# Migration de SQLite vers MySQL - Projet SI

## Résumé de la migration

La base de données du projet SI a été migrée avec succès de SQLite vers MySQL.

## Modifications effectuées

### 1. Configuration de la base de données

- **Fichier `.env.local`** : Mise à jour de la variable `DATABASE_URL` pour utiliser MySQL
  ```
  DATABASE_URL="mysql://root:@127.0.0.1:3306/si_project?serverVersion=8.0&charset=utf8mb4"
  ```

- **Fichier `.env`** : Commenté l'ancienne configuration pour éviter les conflits

### 2. Configuration Doctrine

- **Fichier `config/packages/doctrine.yaml`** : 
  - Mise à jour des fonctions DQL pour utiliser les fonctions MySQL au lieu de SQLite
  - Changement de `DoctrineExtensions\Query\Sqlite\Year` vers `DoctrineExtensions\Query\Mysql\Year`
  - Changement de `DoctrineExtensions\Query\Sqlite\Month` vers `DoctrineExtensions\Query\Mysql\Month`

### 3. Corrections des entités

- **Suppression des entités en double** :
  - Supprimé `src/Entity/Stock/StockAlert.php` (conflit avec `src/Entity/StockAlert.php`)
  - Supprimé `src/Entity/StockItem.php` (conflit avec `src/Entity/Stock/StockItem.php`)
  - Supprimé les repositories correspondants

- **Correction de l'entité SystemSetting** :
  - Réduit la longueur de la colonne `key` de 255 à 191 caractères pour éviter l'erreur d'index MySQL

- **Mise à jour des imports** :
  - Corrigé l'import de `StockItem` dans `src/Entity/StockAlert.php`

### 4. Service StockService

- Commenté temporairement les méthodes utilisant les alertes de stock pour permettre la création du schéma
- Les méthodes suivantes nécessitent une mise à jour pour être compatibles avec la nouvelle entité StockAlert :
  - `checkAndUpdateLowStockAlert()`
  - `createDiscrepancyAlert()`
  - `checkForExpiringProducts()`

## Base de données créée

- **Nom de la base** : `si_project`
- **Charset** : `utf8mb4`
- **Collation** : `utf8mb4_unicode_ci`
- **Nombre de tables** : 94 tables créées avec succès

## Utilisateur administrateur

Un utilisateur administrateur a été créé pour tester l'application :
- **Email** : <EMAIL>
- **Mot de passe** : admin123
- **Rôles** : ROLE_ADMIN, ROLE_USER

## Prochaines étapes

1. **Restaurer le service StockService** : Décommenter et adapter les méthodes pour la nouvelle entité StockAlert
2. **Migration des données** : Si nécessaire, migrer les données importantes de l'ancienne base SQLite
3. **Tests** : Tester toutes les fonctionnalités pour s'assurer qu'elles fonctionnent correctement avec MySQL
4. **Optimisation** : Optimiser les requêtes et les index si nécessaire

## Commandes utiles

```bash
# Vérifier la connexion à la base de données
php bin/console doctrine:query:sql "SELECT 1"

# Voir les tables créées
php bin/console doctrine:query:sql "SHOW TABLES"

# Démarrer le serveur de développement
php -S localhost:8000 -t public

# Vider le cache si nécessaire
php bin/console cache:clear
```

## Notes importantes

- L'ancienne base de données SQLite (`var/data.db`) est conservée en cas de besoin
- La configuration MySQL utilise le serveur WAMP local sur le port 3306
- Aucun mot de passe n'est configuré pour l'utilisateur MySQL `root`
