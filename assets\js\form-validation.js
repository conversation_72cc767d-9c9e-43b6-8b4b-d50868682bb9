// Script pour la validation des formulaires

document.addEventListener('DOMContentLoaded', function() {
    // Récupérer tous les formulaires avec la classe needs-validation
    const forms = document.querySelectorAll('.needs-validation');

    // Boucler sur les formulaires et empêcher la soumission si non valides
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }

            form.classList.add('was-validated');
        }, false);
    });

    // Ajouter des classes pour les champs obligatoires
    const requiredFields = document.querySelectorAll('[required]');
    Array.from(requiredFields).forEach(field => {
        const formGroup = field.closest('.form-group') || field.closest('.mb-3');
        if (formGroup) {
            formGroup.classList.add('required-field');
        }
    });

    // Améliorer l'expérience utilisateur pour les champs de date
    const dateFields = document.querySelectorAll('input[type="date"]');
    Array.from(dateFields).forEach(field => {
        field.addEventListener('focus', function() {
            this.showPicker();
        });
    });

    // Améliorer l'expérience utilisateur pour les champs de fichier
    const fileFields = document.querySelectorAll('input[type="file"]');
    Array.from(fileFields).forEach(field => {
        const formGroup = field.closest('.form-group') || field.closest('.mb-3');
        if (formGroup) {
            const label = formGroup.querySelector('label');
            const fileNameDisplay = document.createElement('div');
            fileNameDisplay.className = 'file-name-display mt-1 small text-muted';
            formGroup.appendChild(fileNameDisplay);

            field.addEventListener('change', function() {
                if (this.files.length > 0) {
                    const fileNames = Array.from(this.files).map(file => file.name).join(', ');
                    fileNameDisplay.textContent = fileNames;
                } else {
                    fileNameDisplay.textContent = '';
                }
            });
        }
    });

    // Améliorer l'expérience utilisateur pour les champs de texte avec compteur de caractères
    const textareas = document.querySelectorAll('textarea[maxlength]');
    Array.from(textareas).forEach(textarea => {
        const formGroup = textarea.closest('.form-group') || textarea.closest('.mb-3');
        if (formGroup) {
            const maxLength = textarea.getAttribute('maxlength');
            const counter = document.createElement('div');
            counter.className = 'char-counter mt-1 small text-muted text-end';
            counter.textContent = `0 / ${maxLength} caractères`;
            formGroup.appendChild(counter);

            textarea.addEventListener('input', function() {
                const currentLength = this.value.length;
                counter.textContent = `${currentLength} / ${maxLength} caractères`;
                
                if (currentLength >= maxLength * 0.9) {
                    counter.classList.add('text-warning');
                } else {
                    counter.classList.remove('text-warning');
                }
            });
        }
    });

    // Améliorer l'expérience utilisateur pour les onglets de formulaire
    const formTabs = document.getElementById('formTabs');
    if (formTabs) {
        const tabLinks = formTabs.querySelectorAll('.nav-link');
        const tabContents = document.querySelectorAll('.tab-pane');

        // Vérifier si un onglet a des erreurs de validation
        function checkTabForErrors() {
            tabContents.forEach((tabContent, index) => {
                const hasErrors = tabContent.querySelectorAll('.is-invalid').length > 0;
                if (hasErrors) {
                    tabLinks[index].classList.add('text-danger');
                } else {
                    tabLinks[index].classList.remove('text-danger');
                }
            });
        }

        // Vérifier les erreurs lors de la validation du formulaire
        const form = formTabs.closest('form');
        if (form) {
            form.addEventListener('submit', function() {
                setTimeout(checkTabForErrors, 100);
            });
        }

        // Vérifier les erreurs lors du changement d'onglet
        tabLinks.forEach(link => {
            link.addEventListener('shown.bs.tab', checkTabForErrors);
        });
    }
});
