/* Styles pour les formulaires */

/* Style général des formulaires */
form {
    margin-bottom: 1.5rem;
}

/* Amélioration des champs de formulaire */
.form-control, .form-select {
    border-radius: 0.375rem;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Style pour les labels */
label.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #212529;
}

/* Style pour les messages d'aide */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Style pour les messages d'erreur */
.invalid-feedback {
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 0.25rem;
}

/* Style pour les boutons */
.btn {
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5c636a;
    border-color: #565e64;
}

/* Style pour les sections de formulaire */
.form-section-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: #212529;
    margin-bottom: 1rem;
}

.form-section-description {
    margin-bottom: 1.5rem;
}

/* Style pour les onglets de formulaire */
.nav-tabs .nav-link {
    color: #6c757d;
    font-weight: 500;
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
    padding: 0.5rem 1rem;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    isolation: isolate;
}

/* Style pour les groupes d'entrée avec icônes */
.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

/* Style pour les champs obligatoires */
.required-field label::after {
    content: " *";
    color: #dc3545;
}

/* Style pour les champs désactivés */
.form-control:disabled, .form-select:disabled {
    background-color: #e9ecef;
    opacity: 1;
}

/* Style pour les cases à cocher et boutons radio */
.form-check-input {
    width: 1.25em;
    height: 1.25em;
    margin-top: 0.125em;
    margin-right: 0.5em;
    vertical-align: top;
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: 1px solid rgba(0, 0, 0, 0.25);
    appearance: none;
    color-adjust: exact;
    transition: background-color 0.2s ease-in-out, background-position 0.2s ease-in-out, border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-check-input:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Style pour les cartes de formulaire */
.card-form {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-form .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1rem;
}

.card-form .card-body {
    padding: 1.5rem;
}

/* Style pour les formulaires en colonnes */
@media (min-width: 768px) {
    .form-columns {
        column-count: 2;
        column-gap: 2rem;
    }
    
    .form-columns .form-group {
        break-inside: avoid;
    }
}
