# Configuration pour l'API mobile
parameters:
    mobile_api.version: '1.0.0'
    mobile_api.prefix: '/api/mobile'
    
    # Configuration de l'authentification
    mobile_api.security.token_ttl: 3600
    mobile_api.security.refresh_token_ttl: 2592000 # 30 jours
    mobile_api.security.two_factor_auth: false
    mobile_api.security.max_login_attempts: 5
    mobile_api.security.login_throttling_delay: 300 # 5 minutes
    
    # Configuration des fonctionnalités
    mobile_api.features.offline_mode: true
    mobile_api.features.offline_cache_size: 50
    mobile_api.features.push_notifications: true
    mobile_api.features.document_scanning: true
    mobile_api.features.data_sync: true
    mobile_api.features.auto_sync_interval: 900 # 15 minutes
    
    # Configuration des limites
    mobile_api.limits.rate_limit: 60
    mobile_api.limits.max_upload_size: 10
    mobile_api.limits.max_items_per_page: 100
    mobile_api.limits.max_search_results: 50
    
    # Configuration des modules disponibles
    mobile_api.modules.partners.enabled: true
    mobile_api.modules.partners.read_only: false
    mobile_api.modules.projects.enabled: true
    mobile_api.modules.projects.read_only: false
    mobile_api.modules.hr.enabled: true
    mobile_api.modules.hr.read_only: true
    mobile_api.modules.finance.enabled: true
    mobile_api.modules.finance.read_only: true
    mobile_api.modules.purchasing.enabled: true
    mobile_api.modules.purchasing.read_only: false
    mobile_api.modules.messages.enabled: true
    mobile_api.modules.messages.read_only: false
    mobile_api.modules.notifications.enabled: true
    mobile_api.modules.notifications.read_only: false
    
    # Configuration des services externes
    mobile_api.external_services.file_storage.provider: 'local'
    mobile_api.external_services.file_storage.base_url: '%env(APP_URL)%/uploads'
    mobile_api.external_services.push_service.provider: 'firebase'
    mobile_api.external_services.push_service.api_key: '%env(default::FIREBASE_API_KEY)%'
    mobile_api.external_services.geolocation.enabled: false
    mobile_api.external_services.geolocation.provider: 'google'
    mobile_api.external_services.geolocation.api_key: '%env(default::MAPS_API_KEY)%'
    
    # Configuration des formats de réponse
    mobile_api.response_formats.default: 'json'
    mobile_api.response_formats.supported: ['json', 'xml']
    mobile_api.response_formats.compression: true
    mobile_api.response_formats.include_metadata: true
    mobile_api.response_formats.date_format: 'Y-m-d\TH:i:s\Z'
    
    # Configuration du logging
    mobile_api.logging.level: 'info'
    mobile_api.logging.log_requests: true
    mobile_api.logging.log_responses: false
    mobile_api.logging.log_errors: true
