security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        # used to reload user from session & other features (e.g. switch_user)
        app_user_provider:
            entity:
                class: App\Entity\User
                property: email
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        main:
            lazy: true
            provider: app_user_provider

            # Custom authenticator
            custom_authenticator: App\Security\LoginFormAuthenticator

            # Entry point configuration
            entry_point: App\Security\LoginFormAuthenticator

            # Form login configuration
            form_login:
                login_path: app_login
                check_path: app_login
                enable_csrf: true
                default_target_path: app_home
                username_parameter: _username
                password_parameter: _password

            # Logout configuration
            logout:
                path: app_logout
                target: app_login

            # Remember me configuration
            remember_me:
                secret: '%kernel.secret%'
                lifetime: 604800 # 1 week in seconds
                path: /

            # User checker
            user_checker: App\Security\UserChecker

            # Impersonation configuration
            switch_user: { role: ROLE_ADMIN, parameter: _switch_user }

    # Role hierarchy
    role_hierarchy:
        ROLE_ADMIN: ROLE_USER
        ROLE_SUPER_ADMIN: [ROLE_ADMIN, ROLE_ALLOWED_TO_SWITCH]
        ROLE_FINANCE: ROLE_USER
        ROLE_ACCOUNTING: ROLE_USER
        ROLE_ACCOUNTING_ADMIN: [ROLE_ACCOUNTING]

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/login, roles: PUBLIC_ACCESS }
        - { path: ^/register, roles: PUBLIC_ACCESS }
        - { path: ^/reset-password, roles: PUBLIC_ACCESS }
        - { path: ^/admin, roles: ROLE_ADMIN }
        - { path: ^/user, roles: ROLE_USER }
        - { path: ^/partner, roles: ROLE_USER }
        - { path: ^/message, roles: ROLE_USER }
        - { path: ^/task, roles: ROLE_USER }
        - { path: ^/notification, roles: ROLE_USER }
        - { path: ^/config, roles: ROLE_ADMIN }
        - { path: ^/dashboard, roles: ROLE_USER }
        - { path: ^/project, roles: ROLE_USER }
        - { path: ^/project/report, roles: ROLE_USER }
        - { path: ^/hr, roles: ROLE_USER }
        - { path: ^/accounting, roles: ROLE_ACCOUNTING }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
