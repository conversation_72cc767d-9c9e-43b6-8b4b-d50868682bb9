# Goods Receipt routes
app_goods_receipt_index:
    path: /goods/receipt/
    controller: App\Controller\GoodsReceiptController::index
    methods: [GET]

app_goods_receipt_new:
    path: /goods/receipt/new
    controller: App\Controller\GoodsReceiptController::new
    methods: [GET, POST]

app_goods_receipt_new_from_order:
    path: /goods/receipt/new-from-order/{id}
    controller: App\Controller\GoodsReceiptController::newFromOrder
    methods: [GET, POST]

app_goods_receipt_show:
    path: /goods/receipt/{id}
    controller: App\Controller\GoodsReceiptController::show
    methods: [GET]

app_goods_receipt_delete:
    path: /goods/receipt/{id}/delete
    controller: App\Controller\GoodsReceiptController::delete
    methods: [POST]

app_goods_receipt_by_order:
    path: /goods/receipt/by-order/{id}
    controller: App\Controller\GoodsReceiptController::byOrder
    methods: [GET]

app_goods_receipt_print:
    path: /goods/receipt/print/{id}
    controller: App\Controller\GoodsReceiptController::print
    methods: [GET]
