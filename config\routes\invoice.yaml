# Invoice routes
app_invoice_index:
    path: /invoice/
    controller: App\Controller\InvoiceController::index
    methods: [GET]

app_invoice_new:
    path: /invoice/new
    controller: App\Controller\InvoiceController::new
    methods: [GET, POST]

app_invoice_new_from_purchase_order:
    path: /invoice/from-purchase-order/{id}
    controller: App\Controller\InvoiceController::newFromPurchaseOrder
    methods: [GET, POST]

app_invoice_show:
    path: /invoice/{id}
    controller: App\Controller\InvoiceController::show
    methods: [GET]

app_invoice_edit:
    path: /invoice/{id}/edit
    controller: App\Controller\InvoiceController::edit
    methods: [GET, POST]

app_invoice_add_item:
    path: /invoice/{id}/add-item
    controller: App\Controller\InvoiceController::addItem
    methods: [GET, POST]

app_invoice_remove_item:
    path: /invoice/{id}/remove-item/{itemId}
    controller: App\Controller\InvoiceController::removeItem
    methods: [POST]

app_invoice_submit:
    path: /invoice/{id}/submit
    controller: App\Controller\InvoiceController::submit
    methods: [POST]

app_invoice_approve:
    path: /invoice/{id}/approve
    controller: App\Controller\InvoiceController::approve
    methods: [GET, POST]

app_invoice_record_payment:
    path: /invoice/{id}/record-payment
    controller: App\Controller\InvoiceController::recordPayment
    methods: [GET, POST]

app_invoice_cancel:
    path: /invoice/{id}/cancel
    controller: App\Controller\InvoiceController::cancel
    methods: [POST]

app_invoice_dispute:
    path: /invoice/{id}/dispute
    controller: App\Controller\InvoiceController::dispute
    methods: [POST]

app_invoice_upload_document:
    path: /invoice/{id}/upload-document
    controller: App\Controller\InvoiceController::uploadDocument
    methods: [POST]

app_invoice_sign:
    path: /invoice/{id}/sign
    controller: App\Controller\InvoiceController::sign
    methods: [POST]

app_invoice_record_supplier_signature:
    path: /invoice/{id}/record-supplier-signature
    controller: App\Controller\InvoiceController::recordSupplierSignature
    methods: [POST]

app_invoice_pending_approval:
    path: /invoice/pending-approval
    controller: App\Controller\InvoiceController::pendingApproval
    methods: [GET]

app_invoice_overdue:
    path: /invoice/overdue
    controller: App\Controller\InvoiceController::overdue
    methods: [GET]

app_invoice_due_soon:
    path: /invoice/due-soon
    controller: App\Controller\InvoiceController::dueSoon
    methods: [GET]
