# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    upload_directory: '%kernel.project_dir%/public/uploads'
    contract_documents_directory: '%kernel.project_dir%/public/uploads/contracts'
    delivery_notes_directory: '%kernel.project_dir%/public/uploads/delivery_notes'
    invoices_directory: '%kernel.project_dir%/public/uploads/invoices'
    tax_declarations_directory: '%kernel.project_dir%/public/uploads/tax_declarations'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    App\Service\ProjectDocumentService:
        arguments:
            $uploadDirectory: '%upload_directory%'

    App\Service\EmployeeDocumentService:
        arguments:
            $uploadDirectory: '%upload_directory%'

    App\Service\ExpenseReportService:
        arguments:
            $uploadDirectory: '%upload_directory%'

    App\Service\MedicalDocumentService:
        arguments:
            $uploadDirectory: '%upload_directory%'

    App\Controller\MedicalDocumentController:
        arguments:
            $uploadDirectory: '%upload_directory%'

    # Medical services
    App\Service\MedicalStatisticsService:
        arguments:
            $entityManager: '@doctrine.orm.entity_manager'
            $medicalRecordRepository: '@App\Repository\MedicalRecordRepository'
            $medicalExaminationRepository: '@App\Repository\MedicalExaminationRepository'
            $employeeRepository: '@App\Repository\EmployeeRepository'
            $familyMemberRepository: '@App\Repository\FamilyMemberRepository'

    App\Service\MedicalReminderService:
        arguments:
            $entityManager: '@doctrine.orm.entity_manager'
            $medicalExaminationRepository: '@App\Repository\MedicalExaminationRepository'
            $mailer: '@mailer.mailer'
            $activityLogService: '@App\Service\ActivityLogService'
            $senderEmail: '%env(MAILER_FROM_ADDRESS)%'

    # Register the Twig extension
    App\Twig\JsonExtension:
        tags: ['twig.extension']


