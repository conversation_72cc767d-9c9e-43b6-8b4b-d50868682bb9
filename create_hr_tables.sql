-- Script pour créer les tables du module RH
-- Système de demandes employés

-- Table principale des demandes
CREATE TABLE IF NOT EXISTS employee_request (
    id INT AUTO_INCREMENT NOT NULL,
    employee_id INT NOT NULL,
    approved_by_id INT DEFAULT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description LONGTEXT NOT NULL,
    status VARCHAR(30) NOT NULL DEFAULT 'pending',
    rejection_reason LONGTEXT DEFAULT NULL,
    priority VARCHAR(20) NOT NULL DEFAULT 'medium',
    requested_date DATETIME DEFAULT NULL,
    approved_at DATETIME DEFAULT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME DEFAULT NULL,
    PRIMARY KEY(id),
    INDEX IDX_employee_request_employee (employee_id),
    INDEX IDX_employee_request_approved_by (approved_by_id),
    INDEX IDX_employee_request_status (status),
    INDEX IDX_employee_request_type (type),
    INDEX IDX_employee_request_priority (priority),
    INDEX IDX_employee_request_created_at (created_at)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;

-- Table des avances sur salaire
CREATE TABLE IF NOT EXISTS salary_advance (
    id INT AUTO_INCREMENT NOT NULL,
    request_id INT NOT NULL,
    employee_id INT NOT NULL,
    paid_by_id INT DEFAULT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    approved_amount DECIMAL(10, 2) DEFAULT NULL,
    reason LONGTEXT NOT NULL,
    repayment_months INT NOT NULL,
    monthly_deduction DECIMAL(10, 2) DEFAULT NULL,
    first_deduction_date DATETIME DEFAULT NULL,
    status VARCHAR(30) NOT NULL DEFAULT 'pending',
    paid_at DATETIME DEFAULT NULL,
    remaining_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    created_at DATETIME NOT NULL,
    updated_at DATETIME DEFAULT NULL,
    PRIMARY KEY(id),
    UNIQUE INDEX UNIQ_salary_advance_request (request_id),
    INDEX IDX_salary_advance_employee (employee_id),
    INDEX IDX_salary_advance_paid_by (paid_by_id),
    INDEX IDX_salary_advance_status (status),
    INDEX IDX_salary_advance_first_deduction_date (first_deduction_date)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;

-- Table des demandes de documents
CREATE TABLE IF NOT EXISTS document_request (
    id INT AUTO_INCREMENT NOT NULL,
    request_id INT NOT NULL,
    employee_id INT NOT NULL,
    processed_by_id INT DEFAULT NULL,
    document_type VARCHAR(100) NOT NULL,
    purpose LONGTEXT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    language VARCHAR(10) NOT NULL DEFAULT 'fr',
    needed_by DATETIME DEFAULT NULL,
    delivery_method VARCHAR(50) NOT NULL DEFAULT 'email',
    delivery_address LONGTEXT DEFAULT NULL,
    status VARCHAR(30) NOT NULL DEFAULT 'pending',
    processed_at DATETIME DEFAULT NULL,
    delivered_at DATETIME DEFAULT NULL,
    notes LONGTEXT DEFAULT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME DEFAULT NULL,
    PRIMARY KEY(id),
    UNIQUE INDEX UNIQ_document_request_request (request_id),
    INDEX IDX_document_request_employee (employee_id),
    INDEX IDX_document_request_processed_by (processed_by_id),
    INDEX IDX_document_request_document_type (document_type),
    INDEX IDX_document_request_status (status),
    INDEX IDX_document_request_needed_by (needed_by),
    INDEX IDX_document_request_delivery_method (delivery_method)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;

-- Table des commentaires sur les demandes
CREATE TABLE IF NOT EXISTS request_comment (
    id INT AUTO_INCREMENT NOT NULL,
    request_id INT NOT NULL,
    author_id INT NOT NULL,
    comment LONGTEXT NOT NULL,
    type VARCHAR(30) NOT NULL DEFAULT 'comment',
    is_internal TINYINT(1) NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME DEFAULT NULL,
    PRIMARY KEY(id),
    INDEX IDX_request_comment_request (request_id),
    INDEX IDX_request_comment_author (author_id),
    INDEX IDX_request_comment_type (type),
    INDEX IDX_request_comment_is_internal (is_internal),
    INDEX IDX_request_comment_created_at (created_at)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;

-- Table des pièces jointes
CREATE TABLE IF NOT EXISTS request_attachment (
    id INT AUTO_INCREMENT NOT NULL,
    request_id INT NOT NULL,
    uploaded_by_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    mime_type VARCHAR(100) DEFAULT NULL,
    file_size INT NOT NULL,
    uploaded_at DATETIME NOT NULL,
    description VARCHAR(500) DEFAULT NULL,
    PRIMARY KEY(id),
    INDEX IDX_request_attachment_request (request_id),
    INDEX IDX_request_attachment_uploaded_by (uploaded_by_id),
    INDEX IDX_request_attachment_uploaded_at (uploaded_at)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;

-- Table des remboursements d'avances
CREATE TABLE IF NOT EXISTS salary_advance_repayment (
    id INT AUTO_INCREMENT NOT NULL,
    salary_advance_id INT NOT NULL,
    processed_by_id INT DEFAULT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    deduction_date DATETIME NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
    notes LONGTEXT DEFAULT NULL,
    processed_at DATETIME DEFAULT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME DEFAULT NULL,
    PRIMARY KEY(id),
    INDEX IDX_salary_advance_repayment_salary_advance (salary_advance_id),
    INDEX IDX_salary_advance_repayment_processed_by (processed_by_id),
    INDEX IDX_salary_advance_repayment_deduction_date (deduction_date),
    INDEX IDX_salary_advance_repayment_status (status)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ENGINE = InnoDB;

-- Ajout des contraintes de clés étrangères
ALTER TABLE employee_request 
ADD CONSTRAINT FK_employee_request_employee 
FOREIGN KEY (employee_id) REFERENCES employee (id);

ALTER TABLE employee_request 
ADD CONSTRAINT FK_employee_request_approved_by 
FOREIGN KEY (approved_by_id) REFERENCES user (id);

ALTER TABLE salary_advance 
ADD CONSTRAINT FK_salary_advance_request 
FOREIGN KEY (request_id) REFERENCES employee_request (id);

ALTER TABLE salary_advance 
ADD CONSTRAINT FK_salary_advance_employee 
FOREIGN KEY (employee_id) REFERENCES employee (id);

ALTER TABLE salary_advance 
ADD CONSTRAINT FK_salary_advance_paid_by 
FOREIGN KEY (paid_by_id) REFERENCES user (id);

ALTER TABLE document_request 
ADD CONSTRAINT FK_document_request_request 
FOREIGN KEY (request_id) REFERENCES employee_request (id);

ALTER TABLE document_request 
ADD CONSTRAINT FK_document_request_employee 
FOREIGN KEY (employee_id) REFERENCES employee (id);

ALTER TABLE document_request 
ADD CONSTRAINT FK_document_request_processed_by 
FOREIGN KEY (processed_by_id) REFERENCES user (id);

ALTER TABLE request_comment 
ADD CONSTRAINT FK_request_comment_request 
FOREIGN KEY (request_id) REFERENCES employee_request (id);

ALTER TABLE request_comment 
ADD CONSTRAINT FK_request_comment_author 
FOREIGN KEY (author_id) REFERENCES user (id);

ALTER TABLE request_attachment 
ADD CONSTRAINT FK_request_attachment_request 
FOREIGN KEY (request_id) REFERENCES employee_request (id);

ALTER TABLE request_attachment 
ADD CONSTRAINT FK_request_attachment_uploaded_by 
FOREIGN KEY (uploaded_by_id) REFERENCES user (id);

ALTER TABLE salary_advance_repayment 
ADD CONSTRAINT FK_salary_advance_repayment_salary_advance 
FOREIGN KEY (salary_advance_id) REFERENCES salary_advance (id);

ALTER TABLE salary_advance_repayment 
ADD CONSTRAINT FK_salary_advance_repayment_processed_by 
FOREIGN KEY (processed_by_id) REFERENCES user (id);
