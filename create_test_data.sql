-- Script pour créer des données de test pour le module RH
-- Demandes d'exemple pour tester l'interface

-- Ins<PERSON>rer quelques demandes d'avance sur salaire
INSERT INTO employee_request (employee_id, type, title, description, status, priority, created_at, updated_at) VALUES
(4, 'salary_advance', 'Avance pour achat véhicule', 'Demande d\'avance de 3000€ pour l\'achat d\'un véhicule nécessaire pour mes déplacements professionnels.', 'pending', 'high', NOW(), NOW()),
(5, 'salary_advance', 'Avance pour frais médicaux', 'Demande d\'avance de 1500€ pour couvrir des frais médicaux urgents non remboursés.', 'pending', 'urgent', NOW(), NOW()),
(2, 'salary_advance', 'Avance pour travaux domicile', 'Demande d\'avance de 2000€ pour des travaux de rénovation de mon domicile.', 'approved', 'medium', DATE_SUB(NOW(), INTERVAL 5 DAY), NOW());

-- Insérer les détails des avances correspondantes
INSERT INTO salary_advance (request_id, employee_id, amount, reason, repayment_months, status, created_at, updated_at) VALUES
(1, 4, 3000.00, 'Achat véhicule pour déplacements professionnels', 12, 'pending', NOW(), NOW()),
(2, 5, 1500.00, 'Frais médicaux urgents non remboursés', 6, 'pending', NOW(), NOW()),
(3, 2, 2000.00, 'Travaux de rénovation domicile', 10, 'approved', DATE_SUB(NOW(), INTERVAL 5 DAY), NOW());

-- Mettre à jour l'avance approuvée avec les détails d'approbation
UPDATE salary_advance SET 
    approved_amount = 2000.00, 
    monthly_deduction = 200.00, 
    first_deduction_date = DATE_ADD(NOW(), INTERVAL 1 MONTH)
WHERE id = 3;

-- Insérer quelques demandes de documents
INSERT INTO employee_request (employee_id, type, title, description, status, priority, created_at, updated_at) VALUES
(4, 'document', 'Certificat de travail', 'Demande de certificat de travail pour dossier de crédit immobilier.', 'pending', 'medium', NOW(), NOW()),
(5, 'document', 'Attestation de salaire urgente', 'Demande d\'attestation de salaire pour dossier CAF urgent.', 'pending', 'urgent', NOW(), NOW()),
(2, 'document', 'Bulletin de paie duplicata', 'Demande de duplicata des 3 derniers bulletins de paie.', 'approved', 'low', DATE_SUB(NOW(), INTERVAL 2 DAY), NOW());

-- Insérer les détails des demandes de documents
INSERT INTO document_request (request_id, employee_id, document_type, purpose, quantity, language, delivery_method, status, created_at, updated_at) VALUES
(4, 4, 'work_certificate', 'Dossier de crédit immobilier', 1, 'fr', 'email', 'pending', NOW(), NOW()),
(5, 5, 'salary_certificate', 'Dossier CAF urgent', 1, 'fr', 'pickup', 'pending', NOW(), NOW()),
(6, 2, 'payslip_duplicate', 'Archives personnelles', 3, 'fr', 'email', 'approved', DATE_SUB(NOW(), INTERVAL 2 DAY), NOW());

-- Mettre à jour la demande de document urgente avec date limite
UPDATE document_request SET 
    needed_by = DATE_ADD(NOW(), INTERVAL 2 DAY)
WHERE id = 2;

-- Insérer quelques demandes de congés (pour les statistiques)
INSERT INTO employee_request (employee_id, type, title, description, status, priority, requested_date, created_at, updated_at) VALUES
(4, 'leave', 'Congés d\'été', 'Demande de congés payés du 15 au 30 juillet 2025.', 'approved', 'medium', '2025-07-15', DATE_SUB(NOW(), INTERVAL 10 DAY), NOW()),
(5, 'leave', 'Congé maladie', 'Arrêt maladie pour intervention chirurgicale.', 'pending', 'high', '2025-07-20', DATE_SUB(NOW(), INTERVAL 1 DAY), NOW());

-- Insérer quelques commentaires pour l'historique
INSERT INTO request_comment (request_id, author_id, comment, type, created_at, updated_at) VALUES
(3, 1, 'Demande approuvée après vérification des justificatifs.', 'approval', DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 4 DAY)),
(6, 1, 'Documents générés et envoyés par email.', 'approval', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
(1, 4, 'Justificatifs de l\'achat du véhicule fournis.', 'comment', DATE_SUB(NOW(), INTERVAL 2 HOUR), DATE_SUB(NOW(), INTERVAL 2 HOUR));

-- Mettre à jour les demandes approuvées avec les informations d'approbation
UPDATE employee_request SET 
    approved_by_id = 1, 
    approved_at = DATE_SUB(NOW(), INTERVAL 4 DAY)
WHERE id IN (3, 6, 7);
