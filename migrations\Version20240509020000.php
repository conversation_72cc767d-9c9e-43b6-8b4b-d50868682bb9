<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Migration pour ajouter les colonnes manquantes à la table user
 */
final class Version20240509020000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute les colonnes manquantes à la table user pour l\'authentification à deux facteurs';
    }

    public function up(Schema $schema): void
    {
        // Pour SQLite, nous devons vérifier si les colonnes existent avant de les ajouter
        // Nous utilisons PRAGMA table_info pour obtenir les informations sur les colonnes
        $columns = $this->connection->fetchAllAssociative('PRAGMA table_info("user")');
        $columnNames = array_column($columns, 'name');

        if (!in_array('is_two_factor_enabled', $columnNames)) {
            $this->addSql('ALTER TABLE "user" ADD COLUMN is_two_factor_enabled BOOLEAN DEFAULT FALSE');
        }

        if (!in_array('totp_secret', $columnNames)) {
            $this->addSql('ALTER TABLE "user" ADD COLUMN totp_secret VARCHAR(255) DEFAULT NULL');
        }

        if (!in_array('backup_codes', $columnNames)) {
            $this->addSql('ALTER TABLE "user" ADD COLUMN backup_codes VARCHAR(255) DEFAULT NULL');
        }

        if (!in_array('preferred_two_factor_method', $columnNames)) {
            $this->addSql('ALTER TABLE "user" ADD COLUMN preferred_two_factor_method VARCHAR(20) DEFAULT NULL');
        }

        if (!in_array('mobile_refresh_token', $columnNames)) {
            $this->addSql('ALTER TABLE "user" ADD COLUMN mobile_refresh_token VARCHAR(255) DEFAULT NULL');
        }

        if (!in_array('mobile_refresh_token_expires_at', $columnNames)) {
            $this->addSql('ALTER TABLE "user" ADD COLUMN mobile_refresh_token_expires_at TIMESTAMP DEFAULT NULL');
        }
    }

    public function down(Schema $schema): void
    {
        // SQLite ne supporte pas DROP COLUMN directement
        // Pour supprimer des colonnes dans SQLite, il faut recréer la table
        // Cette opération est complexe et risquée, donc nous ne l'implémentons pas ici
        $this->write('Skipping down migration for SQLite - cannot drop columns directly');
    }
}
