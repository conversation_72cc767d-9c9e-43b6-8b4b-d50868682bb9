<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Migration pour ajouter les champs d'authentification à deux facteurs et la table d'intégration
 */
final class Version20240601000000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute les champs d\'authentification à deux facteurs et la table d\'intégration';
    }

    public function up(Schema $schema): void
    {
        // Ajouter les champs d'authentification à deux facteurs à la table user
        $this->addSql('ALTER TABLE user ADD is_two_factor_enabled BOOLEAN DEFAULT FALSE');
        $this->addSql('ALTER TABLE user ADD totp_secret VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE user ADD backup_codes VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE user ADD preferred_two_factor_method VARCHAR(20) DEFAULT NULL');
        
        // Créer la table integration
        $this->addSql('CREATE TABLE integration (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            type VARCHAR(50) NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT FALSE,
            last_sync_at DATETIME DEFAULT NULL,
            created_at DATETIME DEFAULT NULL,
            updated_at DATETIME DEFAULT NULL,
            api_key VARCHAR(255) DEFAULT NULL,
            api_secret VARCHAR(255) DEFAULT NULL,
            access_token VARCHAR(255) DEFAULT NULL,
            refresh_token VARCHAR(255) DEFAULT NULL,
            webhook_url VARCHAR(255) DEFAULT NULL,
            config TEXT DEFAULT NULL,
            CONSTRAINT FK_integration_user FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE
        )');
        
        // Créer un index sur le type d'intégration
        $this->addSql('CREATE INDEX IDX_integration_type ON integration (type)');
        
        // Créer un index sur l'utilisateur et le type d'intégration
        $this->addSql('CREATE UNIQUE INDEX UNIQ_integration_user_type ON integration (user_id, type)');
    }

    public function down(Schema $schema): void
    {
        // Supprimer la table integration
        $this->addSql('DROP TABLE integration');
        
        // Supprimer les champs d'authentification à deux facteurs de la table user
        $this->addSql('ALTER TABLE user DROP COLUMN is_two_factor_enabled');
        $this->addSql('ALTER TABLE user DROP COLUMN totp_secret');
        $this->addSql('ALTER TABLE user DROP COLUMN backup_codes');
        $this->addSql('ALTER TABLE user DROP COLUMN preferred_two_factor_method');
    }
}
