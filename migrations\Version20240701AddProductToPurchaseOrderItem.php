<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240701AddProductToPurchaseOrderItem extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute la relation entre PurchaseOrderItem et Product';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE purchase_order_item ADD product_id INTEGER DEFAULT NULL');
        $this->addSql('CREATE INDEX IDX_F44C7CC04584665A ON purchase_order_item (product_id)');
        $this->addSql('CREATE TEMPORARY TABLE __temp_purchase_order_item AS SELECT id, purchase_order_id, request_item_id, description, quantity, unit, unit_price, tax_rate, reference, notes FROM purchase_order_item');
        $this->addSql('DROP TABLE purchase_order_item');
        $this->addSql('CREATE TABLE purchase_order_item (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, purchase_order_id INTEGER NOT NULL, request_item_id INTEGER DEFAULT NULL, product_id INTEGER DEFAULT NULL, description VARCHAR(255) NOT NULL, quantity DOUBLE PRECISION NOT NULL, unit VARCHAR(50) DEFAULT NULL, unit_price DOUBLE PRECISION NOT NULL, tax_rate DOUBLE PRECISION DEFAULT NULL, reference VARCHAR(100) DEFAULT NULL, notes CLOB DEFAULT NULL, CONSTRAINT FK_F44C7CC0A45D7E6A FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_F44C7CC06F19A0A8 FOREIGN KEY (request_item_id) REFERENCES purchase_request_item (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_F44C7CC04584665A FOREIGN KEY (product_id) REFERENCES product (id) NOT DEFERRABLE INITIALLY IMMEDIATE)');
        $this->addSql('INSERT INTO purchase_order_item (id, purchase_order_id, request_item_id, description, quantity, unit, unit_price, tax_rate, reference, notes) SELECT id, purchase_order_id, request_item_id, description, quantity, unit, unit_price, tax_rate, reference, notes FROM __temp_purchase_order_item');
        $this->addSql('DROP TABLE __temp_purchase_order_item');
        $this->addSql('CREATE INDEX IDX_F44C7CC0A45D7E6A ON purchase_order_item (purchase_order_id)');
        $this->addSql('CREATE INDEX IDX_F44C7CC06F19A0A8 ON purchase_order_item (request_item_id)');
        $this->addSql('CREATE INDEX IDX_F44C7CC04584665A ON purchase_order_item (product_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TEMPORARY TABLE __temp_purchase_order_item AS SELECT id, purchase_order_id, request_item_id, description, quantity, unit, unit_price, tax_rate, reference, notes FROM purchase_order_item');
        $this->addSql('DROP TABLE purchase_order_item');
        $this->addSql('CREATE TABLE purchase_order_item (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, purchase_order_id INTEGER NOT NULL, request_item_id INTEGER DEFAULT NULL, description VARCHAR(255) NOT NULL, quantity DOUBLE PRECISION NOT NULL, unit VARCHAR(50) DEFAULT NULL, unit_price DOUBLE PRECISION NOT NULL, tax_rate DOUBLE PRECISION DEFAULT NULL, reference VARCHAR(100) DEFAULT NULL, notes CLOB DEFAULT NULL, CONSTRAINT FK_F44C7CC0A45D7E6A FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_F44C7CC06F19A0A8 FOREIGN KEY (request_item_id) REFERENCES purchase_request_item (id) NOT DEFERRABLE INITIALLY IMMEDIATE)');
        $this->addSql('INSERT INTO purchase_order_item (id, purchase_order_id, request_item_id, description, quantity, unit, unit_price, tax_rate, reference, notes) SELECT id, purchase_order_id, request_item_id, description, quantity, unit, unit_price, tax_rate, reference, notes FROM __temp_purchase_order_item');
        $this->addSql('DROP TABLE __temp_purchase_order_item');
        $this->addSql('CREATE INDEX IDX_F44C7CC0A45D7E6A ON purchase_order_item (purchase_order_id)');
        $this->addSql('CREATE INDEX IDX_F44C7CC06F19A0A8 ON purchase_order_item (request_item_id)');
    }
}
