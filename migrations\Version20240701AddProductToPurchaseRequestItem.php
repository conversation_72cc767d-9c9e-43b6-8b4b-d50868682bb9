<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240701AddProductToPurchaseRequestItem extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute la relation entre PurchaseRequestItem et Product';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE purchase_request_item ADD product_id INTEGER DEFAULT NULL');
        $this->addSql('CREATE INDEX IDX_C4E9F4C94584665A ON purchase_request_item (product_id)');
        $this->addSql('CREATE TEMPORARY TABLE __temp_purchase_request_item AS SELECT id, purchase_request_id, description, quantity, unit, unit_price, tax_rate, reference, notes FROM purchase_request_item');
        $this->addSql('DROP TABLE purchase_request_item');
        $this->addSql('CREATE TABLE purchase_request_item (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, purchase_request_id INTEGER NOT NULL, product_id INTEGER DEFAULT NULL, description VARCHAR(255) NOT NULL, quantity DOUBLE PRECISION NOT NULL, unit VARCHAR(50) DEFAULT NULL, unit_price DOUBLE PRECISION NOT NULL, tax_rate DOUBLE PRECISION DEFAULT NULL, reference VARCHAR(100) DEFAULT NULL, notes CLOB DEFAULT NULL, CONSTRAINT FK_C4E9F4C9A4522A07 FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_C4E9F4C94584665A FOREIGN KEY (product_id) REFERENCES product (id) NOT DEFERRABLE INITIALLY IMMEDIATE)');
        $this->addSql('INSERT INTO purchase_request_item (id, purchase_request_id, description, quantity, unit, unit_price, tax_rate, reference, notes) SELECT id, purchase_request_id, description, quantity, unit, unit_price, tax_rate, reference, notes FROM __temp_purchase_request_item');
        $this->addSql('DROP TABLE __temp_purchase_request_item');
        $this->addSql('CREATE INDEX IDX_C4E9F4C9A4522A07 ON purchase_request_item (purchase_request_id)');
        $this->addSql('CREATE INDEX IDX_C4E9F4C94584665A ON purchase_request_item (product_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TEMPORARY TABLE __temp_purchase_request_item AS SELECT id, purchase_request_id, description, quantity, unit, unit_price, tax_rate, reference, notes FROM purchase_request_item');
        $this->addSql('DROP TABLE purchase_request_item');
        $this->addSql('CREATE TABLE purchase_request_item (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, purchase_request_id INTEGER NOT NULL, description VARCHAR(255) NOT NULL, quantity DOUBLE PRECISION NOT NULL, unit VARCHAR(50) DEFAULT NULL, unit_price DOUBLE PRECISION NOT NULL, tax_rate DOUBLE PRECISION DEFAULT NULL, reference VARCHAR(100) DEFAULT NULL, notes CLOB DEFAULT NULL, CONSTRAINT FK_C4E9F4C9A4522A07 FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id) NOT DEFERRABLE INITIALLY IMMEDIATE)');
        $this->addSql('INSERT INTO purchase_request_item (id, purchase_request_id, description, quantity, unit, unit_price, tax_rate, reference, notes) SELECT id, purchase_request_id, description, quantity, unit, unit_price, tax_rate, reference, notes FROM __temp_purchase_request_item');
        $this->addSql('DROP TABLE __temp_purchase_request_item');
        $this->addSql('CREATE INDEX IDX_C4E9F4C9A4522A07 ON purchase_request_item (purchase_request_id)');
    }
}
