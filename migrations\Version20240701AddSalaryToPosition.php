<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240701AddSalaryToPosition extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute les champs minSalary et maxSalary à la table position';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE position ADD min_salary REAL DEFAULT NULL');
        $this->addSql('ALTER TABLE position ADD max_salary REAL DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE position DROP min_salary, DROP max_salary');
    }
}
