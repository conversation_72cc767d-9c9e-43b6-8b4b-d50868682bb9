<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Migration pour améliorer la table notification avec les nouvelles fonctionnalités
 */
final class Version20241209_NotificationEnhancements extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajout des nouvelles colonnes pour les notifications push avancées';
    }

    public function up(Schema $schema): void
    {
        // Ajouter les nouvelles colonnes à la table notification
        $this->addSql('ALTER TABLE notification ADD priority VARCHAR(20) DEFAULT NULL');
        $this->addSql('ALTER TABLE notification ADD data JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE notification ADD action_url VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE notification ADD action_label VARCHAR(100) DEFAULT NULL');
        $this->addSql('ALTER TABLE notification ADD icon VARCHAR(50) DEFAULT NULL');
        $this->addSql('ALTER TABLE notification ADD color VARCHAR(20) DEFAULT NULL');
        $this->addSql('ALTER TABLE notification ADD is_push_enabled TINYINT(1) DEFAULT 1 NOT NULL');
        $this->addSql('ALTER TABLE notification ADD is_email_enabled TINYINT(1) DEFAULT 1 NOT NULL');
        $this->addSql('ALTER TABLE notification ADD email_sent_at DATETIME DEFAULT NULL');
        $this->addSql('ALTER TABLE notification ADD push_sent_at DATETIME DEFAULT NULL');
        $this->addSql('ALTER TABLE notification ADD expires_at DATETIME DEFAULT NULL');
        
        // Ajouter des index pour les performances
        $this->addSql('CREATE INDEX idx_notification_priority ON notification (priority)');
        $this->addSql('CREATE INDEX idx_notification_expires_at ON notification (expires_at)');
    }

    public function down(Schema $schema): void
    {
        // Supprimer les index
        $this->addSql('DROP INDEX idx_notification_priority ON notification');
        $this->addSql('DROP INDEX idx_notification_expires_at ON notification');
        
        // Supprimer les colonnes
        $this->addSql('ALTER TABLE notification DROP priority');
        $this->addSql('ALTER TABLE notification DROP data');
        $this->addSql('ALTER TABLE notification DROP action_url');
        $this->addSql('ALTER TABLE notification DROP action_label');
        $this->addSql('ALTER TABLE notification DROP icon');
        $this->addSql('ALTER TABLE notification DROP color');
        $this->addSql('ALTER TABLE notification DROP is_push_enabled');
        $this->addSql('ALTER TABLE notification DROP is_email_enabled');
        $this->addSql('ALTER TABLE notification DROP email_sent_at');
        $this->addSql('ALTER TABLE notification DROP push_sent_at');
        $this->addSql('ALTER TABLE notification DROP expires_at');
    }
}
