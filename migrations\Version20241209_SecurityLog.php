<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Migration pour créer la table security_log
 */
final class Version20241209_SecurityLog extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Création de la table security_log pour les logs de sécurité';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE security_log (
            id INT AUTO_INCREMENT NOT NULL, 
            user_id INT DEFAULT NULL, 
            event_type VARCHAR(50) NOT NULL, 
            ip_address VARCHAR(45) DEFAULT NULL, 
            user_agent VARCHAR(500) DEFAULT NULL, 
            details LONGTEXT DEFAULT NULL, 
            severity VARCHAR(20) NOT NULL, 
            status VARCHAR(20) NOT NULL, 
            resource VARCHAR(255) DEFAULT NULL, 
            method VARCHAR(10) DEFAULT NULL, 
            response_code INT DEFAULT NULL, 
            metadata JSON DEFAULT NULL, 
            created_at DATETIME NOT NULL, 
            session_id VARCHAR(100) DEFAULT NULL, 
            is_blocked TINYINT(1) DEFAULT 0 NOT NULL, 
            INDEX idx_security_log_event_type (event_type), 
            INDEX idx_security_log_user_id (user_id), 
            INDEX idx_security_log_ip_address (ip_address), 
            INDEX idx_security_log_created_at (created_at), 
            INDEX IDX_6F1F1A0FA76ED395 (user_id), 
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        
        $this->addSql('ALTER TABLE security_log ADD CONSTRAINT FK_6F1F1A0FA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE security_log DROP FOREIGN KEY FK_6F1F1A0FA76ED395');
        $this->addSql('DROP TABLE security_log');
    }
}
