<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250503000000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add ActivityLog, Notification and Task entities';
    }

    public function up(Schema $schema): void
    {
        // Create activity_log table
        $this->addSql('CREATE TABLE activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
            action VARCHAR(50) NOT NULL,
            entity_type VARCHAR(50) NOT NULL,
            entity_id INTEGER NOT NULL,
            details CLOB DEFAULT NULL,
            user_id VARCHAR(50) DEFAULT NULL,
            created_at DATETIME NOT NULL
        )');
        $this->addSql('CREATE INDEX idx_activity_entity ON activity_log (entity_type, entity_id)');
        $this->addSql('CREATE INDEX idx_activity_created ON activity_log (created_at)');

        // Create notification table
        $this->addSql('CREATE TABLE notification (
            id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
            title VARCHAR(255) NOT NULL,
            content CLOB DEFAULT NULL,
            type VARCHAR(50) NOT NULL,
            user_id VARCHAR(50) DEFAULT NULL,
            is_read BOOLEAN NOT NULL,
            related_entity_type VARCHAR(50) DEFAULT NULL,
            related_entity_id INTEGER DEFAULT NULL,
            created_at DATETIME NOT NULL,
            read_at DATETIME DEFAULT NULL
        )');
        $this->addSql('CREATE INDEX idx_notification_user ON notification (user_id)');
        $this->addSql('CREATE INDEX idx_notification_created ON notification (created_at)');

        // Create task table
        $this->addSql('CREATE TABLE task (
            id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description CLOB DEFAULT NULL,
            status VARCHAR(50) NOT NULL,
            priority VARCHAR(50) NOT NULL,
            due_date DATETIME DEFAULT NULL,
            assigned_to VARCHAR(50) DEFAULT NULL,
            created_by VARCHAR(50) DEFAULT NULL,
            related_entity_type VARCHAR(50) DEFAULT NULL,
            related_entity_id INTEGER DEFAULT NULL,
            created_at DATETIME NOT NULL,
            completed_at DATETIME DEFAULT NULL,
            updated_at DATETIME DEFAULT NULL
        )');
        $this->addSql('CREATE INDEX idx_task_assigned ON task (assigned_to)');
        $this->addSql('CREATE INDEX idx_task_due_date ON task (due_date)');
        $this->addSql('CREATE INDEX idx_task_status ON task (status)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE activity_log');
        $this->addSql('DROP TABLE notification');
        $this->addSql('DROP TABLE task');
    }
}
