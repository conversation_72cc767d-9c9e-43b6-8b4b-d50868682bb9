<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250503002105 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE partner_scope (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(100) NOT NULL, code VARCHAR(50) NOT NULL, description VARCHAR(255) DEFAULT NULL, display_order INTEGER NOT NULL, is_active BOOLEAN NOT NULL, is_default BOOLEAN NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TEMPORARY TABLE __temp__partner AS SELECT id, name, email, phone, custom_attributes, created_at, updated_at, status_id, type_id, nature_id FROM partner
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE partner
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE partner (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(255) NOT NULL, email VARCHAR(255) NOT NULL, phone VARCHAR(255) DEFAULT NULL, custom_attributes CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, status_id INTEGER DEFAULT NULL, type_id INTEGER DEFAULT NULL, nature_id INTEGER DEFAULT NULL, scope_id INTEGER DEFAULT NULL, CONSTRAINT FK_312B3E166BF700BD FOREIGN KEY (status_id) REFERENCES partner_status (id) ON UPDATE NO ACTION ON DELETE NO ACTION NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_312B3E16C54C8C93 FOREIGN KEY (type_id) REFERENCES partner_type (id) ON UPDATE NO ACTION ON DELETE NO ACTION NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_312B3E163BCB2E4B FOREIGN KEY (nature_id) REFERENCES partner_nature (id) ON UPDATE NO ACTION ON DELETE NO ACTION NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_312B3E16682B5931 FOREIGN KEY (scope_id) REFERENCES partner_scope (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            INSERT INTO partner (id, name, email, phone, custom_attributes, created_at, updated_at, status_id, type_id, nature_id) SELECT id, name, email, phone, custom_attributes, created_at, updated_at, status_id, type_id, nature_id FROM __temp__partner
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE __temp__partner
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_312B3E163BCB2E4B ON partner (nature_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_312B3E16C54C8C93 ON partner (type_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_312B3E166BF700BD ON partner (status_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_312B3E16682B5931 ON partner (scope_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE partner_scope
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TEMPORARY TABLE __temp__partner AS SELECT id, name, email, phone, custom_attributes, created_at, updated_at, status_id, type_id, nature_id FROM partner
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE partner
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE partner (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(255) NOT NULL, email VARCHAR(255) NOT NULL, phone VARCHAR(255) DEFAULT NULL, custom_attributes CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, status_id INTEGER DEFAULT NULL, type_id INTEGER DEFAULT NULL, nature_id INTEGER DEFAULT NULL, CONSTRAINT FK_312B3E166BF700BD FOREIGN KEY (status_id) REFERENCES partner_status (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_312B3E16C54C8C93 FOREIGN KEY (type_id) REFERENCES partner_type (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_312B3E163BCB2E4B FOREIGN KEY (nature_id) REFERENCES partner_nature (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            INSERT INTO partner (id, name, email, phone, custom_attributes, created_at, updated_at, status_id, type_id, nature_id) SELECT id, name, email, phone, custom_attributes, created_at, updated_at, status_id, type_id, nature_id FROM __temp__partner
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE __temp__partner
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_312B3E166BF700BD ON partner (status_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_312B3E16C54C8C93 ON partner (type_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_312B3E163BCB2E4B ON partner (nature_id)
        SQL);
    }
}
