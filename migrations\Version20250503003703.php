<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250503003703 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE partner_status_history (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, comment VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, created_by VARCHAR(255) DEFAULT NULL, partner_id INTEGER NOT NULL, old_status_id INTEGER NOT NULL, new_status_id INTEGER NOT NULL, CONSTRAINT FK_2EFD8EF59393F8FE FOREIGN KEY (partner_id) REFERENCES partner (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_2EFD8EF52E43440C FOREIGN KEY (old_status_id) REFERENCES partner_status (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_2EFD8EF5596805D2 FOREIGN KEY (new_status_id) REFERENCES partner_status (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2EFD8EF59393F8FE ON partner_status_history (partner_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2EFD8EF52E43440C ON partner_status_history (old_status_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_2EFD8EF5596805D2 ON partner_status_history (new_status_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE partner_status_history
        SQL);
    }
}
