<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250503184153 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE notification ADD COLUMN link VARCHAR(255) DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TEMPORARY TABLE __temp__notification AS SELECT id, title, content, type, user_id, is_read, related_entity_type, related_entity_id, created_at, read_at FROM notification
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE notification
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE notification (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, title VARCHAR(255) NOT NULL, content CLOB DEFAULT NULL, type VARCHAR(50) NOT NULL, user_id VARCHAR(50) DEFAULT NULL, is_read BOOLEAN NOT NULL, related_entity_type VARCHAR(50) DEFAULT NULL, related_entity_id INTEGER DEFAULT NULL, created_at DATETIME NOT NULL, read_at DATETIME DEFAULT NULL)
        SQL);
        $this->addSql(<<<'SQL'
            INSERT INTO notification (id, title, content, type, user_id, is_read, related_entity_type, related_entity_id, created_at, read_at) SELECT id, title, content, type, user_id, is_read, related_entity_type, related_entity_id, created_at, read_at FROM __temp__notification
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE __temp__notification
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_notification_user ON notification (user_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_notification_created ON notification (created_at)
        SQL);
    }
}
