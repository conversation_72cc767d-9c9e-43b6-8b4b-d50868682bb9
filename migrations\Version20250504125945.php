<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250504125945 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE expense_item (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, expense_date DATETIME NOT NULL, category VARCHAR(50) NOT NULL, description VARCHAR(255) NOT NULL, amount DOUBLE PRECISION NOT NULL, receipt_filename VARCHAR(255) DEFAULT NULL, has_receipt BOOLEAN NOT NULL, expense_report_id INTEGER NOT NULL, CONSTRAINT FK_ABBC6B7C8F758FBA FOREIGN KEY (expense_report_id) REFERENCES expense_report (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_ABBC6B7C8F758FBA ON expense_item (expense_report_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE expense_report (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, report_number VARCHAR(20) NOT NULL, title VARCHAR(255) NOT NULL, report_date DATETIME NOT NULL, total_amount DOUBLE PRECISION NOT NULL, status VARCHAR(20) NOT NULL, notes CLOB DEFAULT NULL, validated_at DATETIME DEFAULT NULL, rejection_reason CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, has_receipts BOOLEAN NOT NULL, mission_order_id INTEGER NOT NULL, validated_by_id INTEGER DEFAULT NULL, created_by_id INTEGER NOT NULL, CONSTRAINT FK_280A69185636C81 FOREIGN KEY (mission_order_id) REFERENCES mission_order (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_280A691C69DE5E5 FOREIGN KEY (validated_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_280A691B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_280A691BF63CACB ON expense_report (report_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_280A69185636C81 ON expense_report (mission_order_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_280A691C69DE5E5 ON expense_report (validated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_280A691B03A8386 ON expense_report (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE mission_order (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, order_number VARCHAR(20) NOT NULL, title VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, start_date DATETIME NOT NULL, end_date DATETIME NOT NULL, destination VARCHAR(255) NOT NULL, transport_mode VARCHAR(50) NOT NULL, advance_requested BOOLEAN NOT NULL, advance_amount DOUBLE PRECISION NOT NULL, status VARCHAR(20) NOT NULL, validated_at DATETIME DEFAULT NULL, finance_validated_at DATETIME DEFAULT NULL, rejection_reason CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, project_id INTEGER NOT NULL, employee_id INTEGER NOT NULL, validated_by_id INTEGER DEFAULT NULL, finance_validated_by_id INTEGER DEFAULT NULL, created_by_id INTEGER NOT NULL, CONSTRAINT FK_65BFCCA5166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_65BFCCA58C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_65BFCCA5C69DE5E5 FOREIGN KEY (validated_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_65BFCCA5AC7074D6 FOREIGN KEY (finance_validated_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_65BFCCA5B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_65BFCCA5551F0F81 ON mission_order (order_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_65BFCCA5166D1F9C ON mission_order (project_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_65BFCCA58C03F15C ON mission_order (employee_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_65BFCCA5C69DE5E5 ON mission_order (validated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_65BFCCA5AC7074D6 ON mission_order (finance_validated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_65BFCCA5B03A8386 ON mission_order (created_by_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE expense_item
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE expense_report
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE mission_order
        SQL);
    }
}
