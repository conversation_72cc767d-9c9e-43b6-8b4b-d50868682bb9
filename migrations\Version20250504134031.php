<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250504134031 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE family_member (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, first_name VARCHAR(50) NOT NULL, last_name VARCHAR(50) NOT NULL, relationship VARCHAR(20) NOT NULL, birth_date DATE DEFAULT NULL, gender VARCHAR(20) DEFAULT NULL, email VARCHAR(255) DEFAULT NULL, phone VARCHAR(20) DEFAULT NULL, is_dependent BOOLEAN NOT NULL, notes CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, employee_id INTEGER NOT NULL, CONSTRAINT FK_B9D4AD6D8C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_B9D4AD6D8C03F15C ON family_member (employee_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE medical_document (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, title VARCHAR(100) NOT NULL, document_type VARCHAR(50) NOT NULL, document_date DATE NOT NULL, filename VARCHAR(255) NOT NULL, issued_by VARCHAR(255) DEFAULT NULL, description CLOB DEFAULT NULL, is_confidential BOOLEAN NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, medical_record_id INTEGER NOT NULL, created_by_id INTEGER NOT NULL, CONSTRAINT FK_A4F36721B88E2BB6 FOREIGN KEY (medical_record_id) REFERENCES medical_record (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_A4F36721B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A4F36721B88E2BB6 ON medical_document (medical_record_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A4F36721B03A8386 ON medical_document (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE medical_examination (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, title VARCHAR(100) NOT NULL, examination_type VARCHAR(50) NOT NULL, examination_date DATE DEFAULT NULL, scheduled_date DATE DEFAULT NULL, results CLOB DEFAULT NULL, recommendations CLOB DEFAULT NULL, location VARCHAR(255) DEFAULT NULL, doctor VARCHAR(100) DEFAULT NULL, status VARCHAR(20) NOT NULL, reminder_sent BOOLEAN NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, medical_record_id INTEGER NOT NULL, created_by_id INTEGER NOT NULL, CONSTRAINT FK_5E236477B88E2BB6 FOREIGN KEY (medical_record_id) REFERENCES medical_record (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_5E236477B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_5E236477B88E2BB6 ON medical_examination (medical_record_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_5E236477B03A8386 ON medical_examination (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE medical_record (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, title VARCHAR(100) NOT NULL, record_type VARCHAR(50) NOT NULL, record_date DATE NOT NULL, description CLOB DEFAULT NULL, blood_group VARCHAR(50) DEFAULT NULL, allergies VARCHAR(255) DEFAULT NULL, chronic_diseases VARCHAR(255) DEFAULT NULL, current_medications VARCHAR(255) DEFAULT NULL, status VARCHAR(50) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, employee_id INTEGER DEFAULT NULL, family_member_id INTEGER DEFAULT NULL, created_by_id INTEGER NOT NULL, CONSTRAINT FK_F06A283E8C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_F06A283EBC594993 FOREIGN KEY (family_member_id) REFERENCES family_member (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_F06A283EB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F06A283E8C03F15C ON medical_record (employee_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F06A283EBC594993 ON medical_record (family_member_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_F06A283EB03A8386 ON medical_record (created_by_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE family_member
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE medical_document
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE medical_examination
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE medical_record
        SQL);
    }
}
