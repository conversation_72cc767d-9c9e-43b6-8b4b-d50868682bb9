<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250504190000 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create department and position tables';
    }

    public function up(Schema $schema): void
    {
        // Create department table
        $this->addSql('CREATE TABLE department (
            id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, 
            parent_department_id INTEGER DEFAULT NULL, 
            name VARCHAR(255) NOT NULL, 
            code VARCHAR(50) NOT NULL, 
            description CLOB DEFAULT NULL, 
            is_active BOOLEAN NOT NULL, 
            created_at DATETIME NOT NULL, 
            updated_at DATETIME DEFAULT NULL, 
            CONSTRAINT FK_CD1DE18A7206964D FOREIGN KEY (parent_department_id) REFERENCES department (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        )');
        $this->addSql('CREATE INDEX IDX_CD1DE18A7206964D ON department (parent_department_id)');

        // Create position table
        $this->addSql('CREATE TABLE position (
            id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, 
            department_id INTEGER NOT NULL, 
            title VARCHAR(255) NOT NULL, 
            code VARCHAR(50) NOT NULL, 
            description CLOB DEFAULT NULL, 
            is_active BOOLEAN NOT NULL, 
            created_at DATETIME NOT NULL, 
            updated_at DATETIME DEFAULT NULL, 
            CONSTRAINT FK_462CE4F5AE80F5DF FOREIGN KEY (department_id) REFERENCES department (id) NOT DEFERRABLE INITIALLY IMMEDIATE
        )');
        $this->addSql('CREATE INDEX IDX_462CE4F5AE80F5DF ON position (department_id)');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE position');
        $this->addSql('DROP TABLE department');
    }
}
