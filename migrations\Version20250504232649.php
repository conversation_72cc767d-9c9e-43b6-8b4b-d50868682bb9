<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250504232649 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE project_budget_line (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(100) NOT NULL, type VARCHAR(20) NOT NULL, project_id INTEGER NOT NULL, CONSTRAINT FK_631F415D166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_631F415D166D1F9C ON project_budget_line (project_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE project_budget_sub_line (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(100) NOT NULL, allocated_amount DOUBLE PRECISION NOT NULL, spent_amount DOUBLE PRECISION NOT NULL, budget_line_id INTEGER NOT NULL, CONSTRAINT FK_197C0F638FF83FA3 FOREIGN KEY (budget_line_id) REFERENCES project_budget_line (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_197C0F638FF83FA3 ON project_budget_sub_line (budget_line_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE project_budget_line
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE project_budget_sub_line
        SQL);
    }
}
