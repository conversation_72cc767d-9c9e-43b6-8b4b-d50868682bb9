<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250505143834 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE contract (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, contract_number VARCHAR(20) NOT NULL, title VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, start_date DATETIME NOT NULL, end_date DATETIME NOT NULL, signature_date DATETIME DEFAULT NULL, total_amount DOUBLE PRECISION NOT NULL, status VARCHAR(20) NOT NULL, terms CLOB DEFAULT NULL, payment_terms CLOB DEFAULT NULL, document_filename VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, supplier_id INTEGER NOT NULL, project_id INTEGER NOT NULL, purchase_request_id INTEGER NOT NULL, created_by_id INTEGER NOT NULL, signed_by_id INTEGER DEFAULT NULL, CONSTRAINT FK_E98F28592ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_E98F2859166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_E98F28594E4DEF6F FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_E98F2859B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_E98F2859D2EDD3FB FOREIGN KEY (signed_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_E98F2859AAD0FA19 ON contract (contract_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E98F28592ADD6D8C ON contract (supplier_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E98F2859166D1F9C ON contract (project_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E98F28594E4DEF6F ON contract (purchase_request_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E98F2859B03A8386 ON contract (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E98F2859D2EDD3FB ON contract (signed_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE goods_receipt (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, receipt_number VARCHAR(20) NOT NULL, receipt_date DATETIME NOT NULL, notes CLOB DEFAULT NULL, delivery_note_number VARCHAR(100) DEFAULT NULL, delivery_note_filename VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, purchase_order_id INTEGER NOT NULL, created_by_id INTEGER NOT NULL, received_by_id INTEGER NOT NULL, CONSTRAINT FK_B6CFD848A45D7E6A FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_B6CFD848B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_B6CFD8486F8DDD17 FOREIGN KEY (received_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_B6CFD848B0ADB74C ON goods_receipt (receipt_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_B6CFD848A45D7E6A ON goods_receipt (purchase_order_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_B6CFD848B03A8386 ON goods_receipt (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_B6CFD8486F8DDD17 ON goods_receipt (received_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE goods_receipt_item (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, quantity DOUBLE PRECISION NOT NULL, notes CLOB DEFAULT NULL, lot_number VARCHAR(50) DEFAULT NULL, expiry_date DATETIME DEFAULT NULL, condition VARCHAR(50) DEFAULT NULL, goods_receipt_id INTEGER NOT NULL, purchase_order_item_id INTEGER NOT NULL, CONSTRAINT FK_E1310FB85AF289C8 FOREIGN KEY (goods_receipt_id) REFERENCES goods_receipt (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_E1310FB83207420A FOREIGN KEY (purchase_order_item_id) REFERENCES purchase_order_item (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E1310FB85AF289C8 ON goods_receipt_item (goods_receipt_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_E1310FB83207420A ON goods_receipt_item (purchase_order_item_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE purchase_order (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, order_number VARCHAR(20) NOT NULL, order_date DATETIME NOT NULL, expected_delivery_date DATETIME DEFAULT NULL, status VARCHAR(20) NOT NULL, total_amount DOUBLE PRECISION NOT NULL, tax_amount DOUBLE PRECISION DEFAULT NULL, notes CLOB DEFAULT NULL, delivery_instructions CLOB DEFAULT NULL, payment_terms CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, sent_at DATETIME DEFAULT NULL, supplier_id INTEGER NOT NULL, project_id INTEGER NOT NULL, purchase_request_id INTEGER NOT NULL, created_by_id INTEGER NOT NULL, sent_by_id INTEGER DEFAULT NULL, CONSTRAINT FK_21E210B22ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_21E210B2166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_21E210B24E4DEF6F FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_21E210B2B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_21E210B2A45BB98C FOREIGN KEY (sent_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_21E210B2551F0F81 ON purchase_order (order_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_21E210B22ADD6D8C ON purchase_order (supplier_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_21E210B2166D1F9C ON purchase_order (project_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_21E210B24E4DEF6F ON purchase_order (purchase_request_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_21E210B2B03A8386 ON purchase_order (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_21E210B2A45BB98C ON purchase_order (sent_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE purchase_order_item (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, description VARCHAR(255) NOT NULL, quantity DOUBLE PRECISION NOT NULL, unit VARCHAR(50) DEFAULT NULL, unit_price DOUBLE PRECISION NOT NULL, tax_rate DOUBLE PRECISION DEFAULT NULL, reference VARCHAR(100) DEFAULT NULL, notes CLOB DEFAULT NULL, purchase_order_id INTEGER NOT NULL, request_item_id INTEGER DEFAULT NULL, CONSTRAINT FK_5ED948C3A45D7E6A FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_5ED948C3384B2009 FOREIGN KEY (request_item_id) REFERENCES purchase_request_item (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_5ED948C3A45D7E6A ON purchase_order_item (purchase_order_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_5ED948C3384B2009 ON purchase_order_item (request_item_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE purchase_request (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, request_number VARCHAR(20) NOT NULL, title VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, request_date DATETIME NOT NULL, needed_by_date DATETIME DEFAULT NULL, estimated_amount DOUBLE PRECISION NOT NULL, status VARCHAR(20) NOT NULL, approved_at DATETIME DEFAULT NULL, rejection_reason CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, purchase_type VARCHAR(50) DEFAULT NULL, purchase_id INTEGER DEFAULT NULL, project_id INTEGER NOT NULL, requested_by_id INTEGER NOT NULL, approved_by_id INTEGER DEFAULT NULL, budget_line_id INTEGER DEFAULT NULL, budget_sub_line_id INTEGER DEFAULT NULL, created_by_id INTEGER NOT NULL, CONSTRAINT FK_204D45E6166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_204D45E64DA1E751 FOREIGN KEY (requested_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_204D45E62D234F6A FOREIGN KEY (approved_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_204D45E68FF83FA3 FOREIGN KEY (budget_line_id) REFERENCES project_budget_line (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_204D45E644A96FE9 FOREIGN KEY (budget_sub_line_id) REFERENCES project_budget_sub_line (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_204D45E6B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_204D45E6DDC9DCAF ON purchase_request (request_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E6166D1F9C ON purchase_request (project_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E64DA1E751 ON purchase_request (requested_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E62D234F6A ON purchase_request (approved_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E68FF83FA3 ON purchase_request (budget_line_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E644A96FE9 ON purchase_request (budget_sub_line_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E6B03A8386 ON purchase_request (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE purchase_request_item (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, description VARCHAR(255) NOT NULL, quantity DOUBLE PRECISION NOT NULL, unit VARCHAR(50) DEFAULT NULL, unit_price DOUBLE PRECISION NOT NULL, tax_rate DOUBLE PRECISION DEFAULT NULL, reference VARCHAR(100) DEFAULT NULL, notes CLOB DEFAULT NULL, purchase_request_id INTEGER NOT NULL, CONSTRAINT FK_A4A6F5444E4DEF6F FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_A4A6F5444E4DEF6F ON purchase_request_item (purchase_request_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE contract
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE goods_receipt
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE goods_receipt_item
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE purchase_order
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE purchase_order_item
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE purchase_request
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE purchase_request_item
        SQL);
    }
}
