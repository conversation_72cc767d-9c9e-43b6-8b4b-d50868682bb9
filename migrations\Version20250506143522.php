<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250506143522 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE project_deliverable (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(100) NOT NULL, description CLOB DEFAULT NULL, due_date DATETIME DEFAULT NULL, status VARCHAR(20) NOT NULL, type VARCHAR(20) NOT NULL, version VARCHAR(50) DEFAULT NULL, approved_at DATETIME DEFAULT NULL, client_feedback CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, project_id INTEGER NOT NULL, assigned_to_id INTEGER DEFAULT NULL, approved_by_id INTEGER DEFAULT NULL, document_id INTEGER DEFAULT NULL, CONSTRAINT FK_69B253BA166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_69B253BAF4BD7827 FOREIGN KEY (assigned_to_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_69B253BA2D234F6A FOREIGN KEY (approved_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_69B253BAC33F7837 FOREIGN KEY (document_id) REFERENCES project_document (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_69B253BA166D1F9C ON project_deliverable (project_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_69B253BAF4BD7827 ON project_deliverable (assigned_to_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_69B253BA2D234F6A ON project_deliverable (approved_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_69B253BAC33F7837 ON project_deliverable (document_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE project_metric (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(50) NOT NULL, type VARCHAR(20) NOT NULL, unit VARCHAR(20) NOT NULL, description CLOB DEFAULT NULL, target_value DOUBLE PRECISION NOT NULL, actual_value DOUBLE PRECISION NOT NULL, measurement_date DATETIME NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, project_id INTEGER NOT NULL, created_by_id INTEGER DEFAULT NULL, CONSTRAINT FK_9072C5A9166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_9072C5A9B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9072C5A9166D1F9C ON project_metric (project_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9072C5A9B03A8386 ON project_metric (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE project_resource (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, role VARCHAR(50) NOT NULL, daily_rate NUMERIC(10, 2) NOT NULL, allocated_days NUMERIC(10, 2) NOT NULL, used_days NUMERIC(10, 2) DEFAULT NULL, start_date DATETIME DEFAULT NULL, end_date DATETIME DEFAULT NULL, status VARCHAR(20) NOT NULL, notes CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, project_id INTEGER NOT NULL, user_id INTEGER NOT NULL, CONSTRAINT FK_81DF7FCD166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_81DF7FCDA76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_81DF7FCD166D1F9C ON project_resource (project_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_81DF7FCDA76ED395 ON project_resource (user_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE project_risk (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, title VARCHAR(100) NOT NULL, description CLOB DEFAULT NULL, probability VARCHAR(20) NOT NULL, impact VARCHAR(20) NOT NULL, status VARCHAR(20) NOT NULL, mitigation_plan CLOB DEFAULT NULL, contingency_plan CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, closed_at DATETIME DEFAULT NULL, project_id INTEGER NOT NULL, owner_id INTEGER DEFAULT NULL, CONSTRAINT FK_40971D59166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_40971D597E3C61F9 FOREIGN KEY (owner_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_40971D59166D1F9C ON project_risk (project_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_40971D597E3C61F9 ON project_risk (owner_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE project_deliverable
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE project_metric
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE project_resource
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE project_risk
        SQL);
    }
}
