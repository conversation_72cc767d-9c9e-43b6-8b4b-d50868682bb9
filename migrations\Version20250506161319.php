<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250506161319 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE project_template (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, is_active BOOLEAN NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, category VARCHAR(50) DEFAULT NULL, estimated_duration INTEGER DEFAULT NULL, estimated_budget DOUBLE PRECISION DEFAULT NULL, created_by_id INTEGER DEFAULT NULL, CONSTRAINT FK_AA2E9458B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_AA2E9458B03A8386 ON project_template (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE project_template_deliverable (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, type VARCHAR(50) NOT NULL, day_offset INTEGER DEFAULT NULL, role VARCHAR(50) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, template_id INTEGER NOT NULL, related_task_id INTEGER DEFAULT NULL, CONSTRAINT FK_8C67838A5DA0FB8 FOREIGN KEY (template_id) REFERENCES project_template (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_8C67838AB2AFCB23 FOREIGN KEY (related_task_id) REFERENCES project_template_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8C67838A5DA0FB8 ON project_template_deliverable (template_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8C67838AB2AFCB23 ON project_template_deliverable (related_task_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE project_template_risk (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, title VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, probability VARCHAR(50) NOT NULL, impact VARCHAR(50) NOT NULL, mitigation_plan CLOB DEFAULT NULL, contingency_plan CLOB DEFAULT NULL, role VARCHAR(50) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, template_id INTEGER NOT NULL, CONSTRAINT FK_7859682A5DA0FB8 FOREIGN KEY (template_id) REFERENCES project_template (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_7859682A5DA0FB8 ON project_template_risk (template_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE project_template_task (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, title VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, estimated_hours INTEGER DEFAULT NULL, priority VARCHAR(50) DEFAULT NULL, day_offset INTEGER DEFAULT NULL, duration INTEGER DEFAULT NULL, role VARCHAR(50) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, template_id INTEGER NOT NULL, predecessor_id INTEGER DEFAULT NULL, CONSTRAINT FK_5321664E5DA0FB8 FOREIGN KEY (template_id) REFERENCES project_template (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_5321664E68C90015 FOREIGN KEY (predecessor_id) REFERENCES project_template_task (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_5321664E5DA0FB8 ON project_template_task (template_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_5321664E68C90015 ON project_template_task (predecessor_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE project_template
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE project_template_deliverable
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE project_template_risk
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE project_template_task
        SQL);
    }
}
