<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250506164009 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE project_budget_line ADD COLUMN category VARCHAR(50) DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TEMPORARY TABLE __temp__project_budget_line AS SELECT id, name, type, allocated_amount, spent_amount, project_id FROM project_budget_line
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE project_budget_line
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE project_budget_line (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(100) NOT NULL, type VARCHAR(20) NOT NULL, allocated_amount DOUBLE PRECISION NOT NULL, spent_amount DOUBLE PRECISION NOT NULL, project_id INTEGER NOT NULL, CONSTRAINT FK_631F415D166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            INSERT INTO project_budget_line (id, name, type, allocated_amount, spent_amount, project_id) SELECT id, name, type, allocated_amount, spent_amount, project_id FROM __temp__project_budget_line
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE __temp__project_budget_line
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_631F415D166D1F9C ON project_budget_line (project_id)
        SQL);
    }
}
