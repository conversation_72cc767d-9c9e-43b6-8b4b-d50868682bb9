<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250506203510 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE purchase_request_approval (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, approved_at DATETIME NOT NULL, approval_level INTEGER DEFAULT NULL, comments CLOB DEFAULT NULL, purchase_request_id INTEGER NOT NULL, approved_by_id INTEGER NOT NULL, CONSTRAINT FK_3201F52C4E4DEF6F FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_3201F52C2D234F6A FOREIGN KEY (approved_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_3201F52C4E4DEF6F ON purchase_request_approval (purchase_request_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_3201F52C2D234F6A ON purchase_request_approval (approved_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request ADD COLUMN required_approval_level INTEGER DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request ADD COLUMN current_approval_level INTEGER DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE purchase_request_approval
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TEMPORARY TABLE __temp__purchase_request AS SELECT id, request_number, title, description, request_date, needed_by_date, estimated_amount, status, approved_at, rejection_reason, created_at, updated_at, purchase_type, purchase_id, project_id, requested_by_id, approved_by_id, budget_line_id, budget_sub_line_id, created_by_id FROM purchase_request
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE purchase_request
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE purchase_request (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, request_number VARCHAR(20) NOT NULL, title VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, request_date DATETIME NOT NULL, needed_by_date DATETIME DEFAULT NULL, estimated_amount DOUBLE PRECISION NOT NULL, status VARCHAR(20) NOT NULL, approved_at DATETIME DEFAULT NULL, rejection_reason CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, purchase_type VARCHAR(50) DEFAULT NULL, purchase_id INTEGER DEFAULT NULL, project_id INTEGER NOT NULL, requested_by_id INTEGER NOT NULL, approved_by_id INTEGER DEFAULT NULL, budget_line_id INTEGER DEFAULT NULL, budget_sub_line_id INTEGER DEFAULT NULL, created_by_id INTEGER NOT NULL, CONSTRAINT FK_204D45E6166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_204D45E64DA1E751 FOREIGN KEY (requested_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_204D45E62D234F6A FOREIGN KEY (approved_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_204D45E68FF83FA3 FOREIGN KEY (budget_line_id) REFERENCES project_budget_line (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_204D45E644A96FE9 FOREIGN KEY (budget_sub_line_id) REFERENCES project_budget_sub_line (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_204D45E6B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            INSERT INTO purchase_request (id, request_number, title, description, request_date, needed_by_date, estimated_amount, status, approved_at, rejection_reason, created_at, updated_at, purchase_type, purchase_id, project_id, requested_by_id, approved_by_id, budget_line_id, budget_sub_line_id, created_by_id) SELECT id, request_number, title, description, request_date, needed_by_date, estimated_amount, status, approved_at, rejection_reason, created_at, updated_at, purchase_type, purchase_id, project_id, requested_by_id, approved_by_id, budget_line_id, budget_sub_line_id, created_by_id FROM __temp__purchase_request
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE __temp__purchase_request
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_204D45E6DDC9DCAF ON purchase_request (request_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E6166D1F9C ON purchase_request (project_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E64DA1E751 ON purchase_request (requested_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E62D234F6A ON purchase_request (approved_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E68FF83FA3 ON purchase_request (budget_line_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E644A96FE9 ON purchase_request (budget_sub_line_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_204D45E6B03A8386 ON purchase_request (created_by_id)
        SQL);
    }
}
