<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250506211217 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE price_history (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, price DOUBLE PRECISION NOT NULL, price_date DATETIME NOT NULL, currency VARCHAR(50) DEFAULT NULL, source_type VARCHAR(50) NOT NULL, source_id INTEGER DEFAULT NULL, notes CLOB DEFAULT NULL, created_at DATETIME NOT NULL, product_id INTEGER NOT NULL, supplier_id INTEGER DEFAULT NULL, created_by_id INTEGER DEFAULT NULL, CONSTRAINT FK_4C9CB8174584665A FOREIGN KEY (product_id) REFERENCES product (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_4C9CB8172ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_4C9CB817B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_4C9CB8174584665A ON price_history (product_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_4C9CB8172ADD6D8C ON price_history (supplier_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_4C9CB817B03A8386 ON price_history (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE product (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, code VARCHAR(50) NOT NULL, name VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, unit VARCHAR(50) DEFAULT NULL, reference_price DOUBLE PRECISION DEFAULT NULL, last_purchase_price DOUBLE PRECISION DEFAULT NULL, last_purchase_date DATETIME DEFAULT NULL, min_stock_level INTEGER DEFAULT NULL, current_stock INTEGER DEFAULT NULL, is_active BOOLEAN NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, category_id INTEGER DEFAULT NULL, created_by_id INTEGER DEFAULT NULL, CONSTRAINT FK_D34A04AD12469DE2 FOREIGN KEY (category_id) REFERENCES product_category (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_D34A04ADB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_D34A04AD77153098 ON product (code)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D34A04AD12469DE2 ON product (category_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_D34A04ADB03A8386 ON product (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE product_category (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(100) NOT NULL, description CLOB DEFAULT NULL, display_order INTEGER NOT NULL, is_active BOOLEAN NOT NULL, parent_id INTEGER DEFAULT NULL, CONSTRAINT FK_CDFC7356727ACA70 FOREIGN KEY (parent_id) REFERENCES product_category (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_CDFC7356727ACA70 ON product_category (parent_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE quote (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, quote_number VARCHAR(50) NOT NULL, title VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, quote_date DATETIME NOT NULL, valid_until DATETIME DEFAULT NULL, status VARCHAR(20) NOT NULL, total_amount DOUBLE PRECISION NOT NULL, tax_amount DOUBLE PRECISION DEFAULT NULL, notes CLOB DEFAULT NULL, terms_and_conditions CLOB DEFAULT NULL, supplier_reference VARCHAR(255) DEFAULT NULL, document_filename VARCHAR(255) DEFAULT NULL, delivery_lead_time INTEGER DEFAULT NULL, payment_terms VARCHAR(255) DEFAULT NULL, is_selected BOOLEAN NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, received_at DATETIME DEFAULT NULL, supplier_id INTEGER NOT NULL, purchase_request_id INTEGER NOT NULL, created_by_id INTEGER NOT NULL, received_by_id INTEGER DEFAULT NULL, CONSTRAINT FK_6B71CBF42ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_6B71CBF44E4DEF6F FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_6B71CBF4B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_6B71CBF46F8DDD17 FOREIGN KEY (received_by_id) REFERENCES employee (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_6B71CBF4AC28B117 ON quote (quote_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6B71CBF42ADD6D8C ON quote (supplier_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6B71CBF44E4DEF6F ON quote (purchase_request_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6B71CBF4B03A8386 ON quote (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6B71CBF46F8DDD17 ON quote (received_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE quote_item (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, description VARCHAR(255) NOT NULL, quantity DOUBLE PRECISION NOT NULL, unit VARCHAR(50) DEFAULT NULL, unit_price DOUBLE PRECISION NOT NULL, tax_rate DOUBLE PRECISION DEFAULT NULL, reference VARCHAR(100) DEFAULT NULL, notes CLOB DEFAULT NULL, delivery_lead_time INTEGER DEFAULT NULL, quote_id INTEGER NOT NULL, product_id INTEGER DEFAULT NULL, request_item_id INTEGER DEFAULT NULL, CONSTRAINT FK_8DFC7A94DB805178 FOREIGN KEY (quote_id) REFERENCES quote (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_8DFC7A944584665A FOREIGN KEY (product_id) REFERENCES product (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_8DFC7A94384B2009 FOREIGN KEY (request_item_id) REFERENCES purchase_request_item (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8DFC7A94DB805178 ON quote_item (quote_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8DFC7A944584665A ON quote_item (product_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8DFC7A94384B2009 ON quote_item (request_item_id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD COLUMN is_supplier BOOLEAN NOT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD COLUMN contact_person VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD COLUMN contact_email VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD COLUMN contact_phone VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD COLUMN tax_id VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD COLUMN payment_terms VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD COLUMN delivery_lead_time INTEGER DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD COLUMN rating DOUBLE PRECISION DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD COLUMN is_approved BOOLEAN DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE price_history
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE product
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE product_category
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE quote
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE quote_item
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TEMPORARY TABLE __temp__partner AS SELECT id, name, email, phone, website, custom_attributes, created_at, updated_at, status_id, type_id, nature_id, scope_id FROM partner
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE partner
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE partner (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(255) NOT NULL, email VARCHAR(255) NOT NULL, phone VARCHAR(255) DEFAULT NULL, website VARCHAR(255) DEFAULT NULL, custom_attributes CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, status_id INTEGER DEFAULT NULL, type_id INTEGER DEFAULT NULL, nature_id INTEGER DEFAULT NULL, scope_id INTEGER DEFAULT NULL, CONSTRAINT FK_312B3E166BF700BD FOREIGN KEY (status_id) REFERENCES partner_status (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_312B3E16C54C8C93 FOREIGN KEY (type_id) REFERENCES partner_type (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_312B3E163BCB2E4B FOREIGN KEY (nature_id) REFERENCES partner_nature (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_312B3E16682B5931 FOREIGN KEY (scope_id) REFERENCES partner_scope (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            INSERT INTO partner (id, name, email, phone, website, custom_attributes, created_at, updated_at, status_id, type_id, nature_id, scope_id) SELECT id, name, email, phone, website, custom_attributes, created_at, updated_at, status_id, type_id, nature_id, scope_id FROM __temp__partner
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE __temp__partner
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_312B3E166BF700BD ON partner (status_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_312B3E16C54C8C93 ON partner (type_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_312B3E163BCB2E4B ON partner (nature_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_312B3E16682B5931 ON partner (scope_id)
        SQL);
    }
}
