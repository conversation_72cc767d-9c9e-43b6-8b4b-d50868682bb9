<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250506223426 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE invoice (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, invoice_number VARCHAR(50) NOT NULL, supplier_invoice_number VARCHAR(50) DEFAULT NULL, invoice_date DATETIME NOT NULL, due_date DATETIME NOT NULL, status VARCHAR(20) NOT NULL, total_amount DOUBLE PRECISION NOT NULL, tax_amount DOUBLE PRECISION DEFAULT NULL, paid_amount DOUBLE PRECISION DEFAULT NULL, notes CLOB DEFAULT NULL, document_filename VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, approved_at DATETIME DEFAULT NULL, paid_at DATETIME DEFAULT NULL, payment_reference VARCHAR(255) DEFAULT NULL, payment_method VARCHAR(50) DEFAULT NULL, rejection_reason CLOB DEFAULT NULL, dispute_reason CLOB DEFAULT NULL, is_recurring BOOLEAN DEFAULT NULL, recurring_frequency VARCHAR(20) DEFAULT NULL, next_recurring_date DATETIME DEFAULT NULL, is_electronically_signed_by_supplier BOOLEAN DEFAULT NULL, is_electronically_signed_by_company BOOLEAN DEFAULT NULL, supplier_signature_date DATETIME DEFAULT NULL, company_signature_date DATETIME DEFAULT NULL, accounting_code VARCHAR(255) DEFAULT NULL, budget_line VARCHAR(255) DEFAULT NULL, supplier_id INTEGER NOT NULL, project_id INTEGER DEFAULT NULL, purchase_order_id INTEGER DEFAULT NULL, created_by_id INTEGER NOT NULL, updated_by_id INTEGER DEFAULT NULL, approved_by_id INTEGER DEFAULT NULL, paid_by_id INTEGER DEFAULT NULL, CONSTRAINT FK_906517442ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_90651744166D1F9C FOREIGN KEY (project_id) REFERENCES project (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_90651744A45D7E6A FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_90651744B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_90651744896DBBDE FOREIGN KEY (updated_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_906517442D234F6A FOREIGN KEY (approved_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_906517447F9BC654 FOREIGN KEY (paid_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_906517442DA68207 ON invoice (invoice_number)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_906517442ADD6D8C ON invoice (supplier_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_90651744166D1F9C ON invoice (project_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_90651744A45D7E6A ON invoice (purchase_order_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_90651744B03A8386 ON invoice (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_90651744896DBBDE ON invoice (updated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_906517442D234F6A ON invoice (approved_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_906517447F9BC654 ON invoice (paid_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE invoice_approval (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, created_at DATETIME NOT NULL, "action" VARCHAR(20) NOT NULL, comments CLOB DEFAULT NULL, approval_level INTEGER DEFAULT NULL, is_electronically_signed BOOLEAN DEFAULT NULL, signature_reference VARCHAR(255) DEFAULT NULL, invoice_id INTEGER NOT NULL, user_id INTEGER NOT NULL, delegated_to_id INTEGER DEFAULT NULL, CONSTRAINT FK_9C8CF732989F1FD FOREIGN KEY (invoice_id) REFERENCES invoice (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_9C8CF73A76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_9C8CF7335EF05C1 FOREIGN KEY (delegated_to_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9C8CF732989F1FD ON invoice_approval (invoice_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9C8CF73A76ED395 ON invoice_approval (user_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9C8CF7335EF05C1 ON invoice_approval (delegated_to_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE invoice_item (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, description VARCHAR(255) NOT NULL, quantity DOUBLE PRECISION NOT NULL, unit VARCHAR(20) DEFAULT NULL, unit_price DOUBLE PRECISION NOT NULL, total_price DOUBLE PRECISION NOT NULL, tax_rate DOUBLE PRECISION DEFAULT NULL, tax_amount DOUBLE PRECISION DEFAULT NULL, accounting_code VARCHAR(255) DEFAULT NULL, budget_line VARCHAR(255) DEFAULT NULL, notes CLOB DEFAULT NULL, invoice_id INTEGER NOT NULL, product_id INTEGER DEFAULT NULL, purchase_order_item_id INTEGER DEFAULT NULL, CONSTRAINT FK_1DDE477B2989F1FD FOREIGN KEY (invoice_id) REFERENCES invoice (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_1DDE477B4584665A FOREIGN KEY (product_id) REFERENCES product (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_1DDE477B3207420A FOREIGN KEY (purchase_order_item_id) REFERENCES purchase_order_item (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1DDE477B2989F1FD ON invoice_item (invoice_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1DDE477B4584665A ON invoice_item (product_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1DDE477B3207420A ON invoice_item (purchase_order_item_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE invoice_payment (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, payment_date DATETIME NOT NULL, amount DOUBLE PRECISION NOT NULL, payment_method VARCHAR(50) NOT NULL, reference VARCHAR(255) DEFAULT NULL, notes CLOB DEFAULT NULL, created_at DATETIME NOT NULL, bank_account VARCHAR(255) DEFAULT NULL, transaction_id VARCHAR(255) DEFAULT NULL, is_reconciled BOOLEAN DEFAULT NULL, reconciled_at DATETIME DEFAULT NULL, invoice_id INTEGER NOT NULL, created_by_id INTEGER NOT NULL, reconciled_by_id INTEGER DEFAULT NULL, CONSTRAINT FK_9FF1B2DE2989F1FD FOREIGN KEY (invoice_id) REFERENCES invoice (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_9FF1B2DEB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_9FF1B2DEF5E43AE9 FOREIGN KEY (reconciled_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9FF1B2DE2989F1FD ON invoice_payment (invoice_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9FF1B2DEB03A8386 ON invoice_payment (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_9FF1B2DEF5E43AE9 ON invoice_payment (reconciled_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE supplier_rating (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, category VARCHAR(20) NOT NULL, rating INTEGER NOT NULL, comment CLOB DEFAULT NULL, created_at DATETIME NOT NULL, supplier_id INTEGER NOT NULL, purchase_order_id INTEGER DEFAULT NULL, contract_id INTEGER DEFAULT NULL, created_by_id INTEGER NOT NULL, CONSTRAINT FK_3EBF4F62ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_3EBF4F6A45D7E6A FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_3EBF4F62576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_3EBF4F6B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_3EBF4F62ADD6D8C ON supplier_rating (supplier_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_3EBF4F6A45D7E6A ON supplier_rating (purchase_order_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_3EBF4F62576E0FD ON supplier_rating (contract_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_3EBF4F6B03A8386 ON supplier_rating (created_by_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE invoice
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE invoice_approval
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE invoice_item
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE invoice_payment
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE supplier_rating
        SQL);
    }
}
