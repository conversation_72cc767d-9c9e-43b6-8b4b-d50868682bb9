<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250507212729 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE accounting_account (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, number VARCHAR(10) NOT NULL, name VARCHAR(255) NOT NULL, type VARCHAR(50) NOT NULL, category VARCHAR(50) NOT NULL, is_active BOOLEAN NOT NULL, description VARCHAR(255) DEFAULT NULL, is_debit BOOLEAN NOT NULL, is_tax_account BOOLEAN NOT NULL, tax_rate DOUBLE PRECISION DEFAULT NULL, parent_id INTEGER DEFAULT NULL, CONSTRAINT FK_44BEAB93727ACA70 FOREIGN KEY (parent_id) REFERENCES accounting_account (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_44BEAB93727ACA70 ON accounting_account (parent_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE accounting_fiscal_year (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(50) NOT NULL, start_date DATE NOT NULL, end_date DATE NOT NULL, status VARCHAR(50) NOT NULL, is_current BOOLEAN NOT NULL, notes VARCHAR(255) DEFAULT NULL, closed_at DATETIME DEFAULT NULL, locked_at DATETIME DEFAULT NULL)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE accounting_journal (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, code VARCHAR(50) NOT NULL, name VARCHAR(255) NOT NULL, type VARCHAR(50) NOT NULL, description VARCHAR(255) DEFAULT NULL, is_active BOOLEAN NOT NULL, is_system BOOLEAN NOT NULL)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE accounting_journal_entry (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, entry_date DATE NOT NULL, description VARCHAR(255) NOT NULL, amount DOUBLE PRECISION NOT NULL, is_debit BOOLEAN NOT NULL, status VARCHAR(50) NOT NULL, reference_type VARCHAR(50) DEFAULT NULL, reference_id INTEGER DEFAULT NULL, reference_number VARCHAR(255) DEFAULT NULL, entry_number INTEGER DEFAULT NULL, line_number INTEGER DEFAULT NULL, created_at DATETIME NOT NULL, validated_at DATETIME DEFAULT NULL, notes VARCHAR(255) DEFAULT NULL, tax_amount DOUBLE PRECISION DEFAULT NULL, tax_rate DOUBLE PRECISION DEFAULT NULL, currency VARCHAR(50) DEFAULT NULL, currency_rate DOUBLE PRECISION DEFAULT NULL, amount_in_currency DOUBLE PRECISION DEFAULT NULL, due_date DATE DEFAULT NULL, external_reference VARCHAR(255) DEFAULT NULL, journal_id INTEGER NOT NULL, account_id INTEGER NOT NULL, created_by_id INTEGER DEFAULT NULL, validated_by_id INTEGER DEFAULT NULL, CONSTRAINT FK_62764D68478E8802 FOREIGN KEY (journal_id) REFERENCES accounting_journal (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_62764D689B6B5FBA FOREIGN KEY (account_id) REFERENCES accounting_account (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_62764D68B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_62764D68C69DE5E5 FOREIGN KEY (validated_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_62764D68478E8802 ON accounting_journal_entry (journal_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_62764D689B6B5FBA ON accounting_journal_entry (account_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_62764D68B03A8386 ON accounting_journal_entry (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_62764D68C69DE5E5 ON accounting_journal_entry (validated_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_journal_entry_date ON accounting_journal_entry (entry_date)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX idx_journal_entry_reference ON accounting_journal_entry (reference_type, reference_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE accounting_tax_declaration (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, type VARCHAR(50) NOT NULL, period VARCHAR(50) NOT NULL, start_date DATE NOT NULL, end_date DATE NOT NULL, due_date DATE NOT NULL, taxable_amount DOUBLE PRECISION NOT NULL, tax_amount DOUBLE PRECISION NOT NULL, deductible_amount DOUBLE PRECISION NOT NULL, net_amount DOUBLE PRECISION NOT NULL, status VARCHAR(50) NOT NULL, submission_date DATE DEFAULT NULL, payment_date DATE DEFAULT NULL, reference_number VARCHAR(255) DEFAULT NULL, notes VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL, period_number INTEGER DEFAULT NULL, attachment_path VARCHAR(255) DEFAULT NULL, created_by_id INTEGER DEFAULT NULL, submitted_by_id INTEGER DEFAULT NULL, fiscal_year_id INTEGER DEFAULT NULL, CONSTRAINT FK_216E9EF2B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_216E9EF279F7D87D FOREIGN KEY (submitted_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_216E9EF263F9139E FOREIGN KEY (fiscal_year_id) REFERENCES accounting_fiscal_year (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_216E9EF2B03A8386 ON accounting_tax_declaration (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_216E9EF279F7D87D ON accounting_tax_declaration (submitted_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_216E9EF263F9139E ON accounting_tax_declaration (fiscal_year_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE accounting_account
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE accounting_fiscal_year
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE accounting_journal
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE accounting_journal_entry
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE accounting_tax_declaration
        SQL);
    }
}
