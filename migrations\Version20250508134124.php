<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250508134124 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE stock_alert (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, type VARCHAR(50) NOT NULL, title VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, status VARCHAR(50) NOT NULL, created_at DATETIME NOT NULL, acknowledged_at DATETIME DEFAULT NULL, resolved_at DATETIME DEFAULT NULL, resolution_notes CLOB DEFAULT NULL, product_id INTEGER DEFAULT NULL, location_id INTEGER DEFAULT NULL, stock_item_id INTEGER DEFAULT NULL, CONSTRAINT FK_8BED5A304584665A FOREIGN KEY (product_id) REFERENCES product (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_8BED5A3064D218E FOREIGN KEY (location_id) REFERENCES stock_location (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_8BED5A30BC942FD FOREIGN KEY (stock_item_id) REFERENCES stock_item (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8BED5A304584665A ON stock_alert (product_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8BED5A3064D218E ON stock_alert (location_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_8BED5A30BC942FD ON stock_alert (stock_item_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE stock_inventory (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, inventory_number VARCHAR(50) NOT NULL, name VARCHAR(255) NOT NULL, start_date DATETIME NOT NULL, end_date DATETIME DEFAULT NULL, status VARCHAR(50) NOT NULL, notes CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, created_by_id INTEGER NOT NULL, completed_by_id INTEGER DEFAULT NULL, CONSTRAINT FK_29B0ACDEB03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_29B0ACDE85ECDE76 FOREIGN KEY (completed_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_29B0ACDEB03A8386 ON stock_inventory (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_29B0ACDE85ECDE76 ON stock_inventory (completed_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE stock_inventory_locations (inventory_id INTEGER NOT NULL, stock_location_id INTEGER NOT NULL, PRIMARY KEY(inventory_id, stock_location_id), CONSTRAINT FK_95E18F4D9EEA759 FOREIGN KEY (inventory_id) REFERENCES stock_inventory (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_95E18F4DD98387BA FOREIGN KEY (stock_location_id) REFERENCES stock_location (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_95E18F4D9EEA759 ON stock_inventory_locations (inventory_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_95E18F4DD98387BA ON stock_inventory_locations (stock_location_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE stock_inventory_line (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, expected_quantity DOUBLE PRECISION NOT NULL, counted_quantity DOUBLE PRECISION DEFAULT NULL, difference DOUBLE PRECISION DEFAULT NULL, lot_number VARCHAR(50) DEFAULT NULL, expiry_date DATETIME DEFAULT NULL, notes CLOB DEFAULT NULL, is_completed BOOLEAN NOT NULL, counted_at DATETIME DEFAULT NULL, inventory_id INTEGER NOT NULL, product_id INTEGER NOT NULL, location_id INTEGER NOT NULL, stock_item_id INTEGER DEFAULT NULL, CONSTRAINT FK_3C51C2939EEA759 FOREIGN KEY (inventory_id) REFERENCES stock_inventory (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_3C51C2934584665A FOREIGN KEY (product_id) REFERENCES product (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_3C51C29364D218E FOREIGN KEY (location_id) REFERENCES stock_location (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_3C51C293BC942FD FOREIGN KEY (stock_item_id) REFERENCES stock_item (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_3C51C2939EEA759 ON stock_inventory_line (inventory_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_3C51C2934584665A ON stock_inventory_line (product_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_3C51C29364D218E ON stock_inventory_line (location_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_3C51C293BC942FD ON stock_inventory_line (stock_item_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE stock_item (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, quantity DOUBLE PRECISION NOT NULL, reserved_quantity DOUBLE PRECISION DEFAULT NULL, lot_number VARCHAR(50) DEFAULT NULL, expiry_date DATETIME DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, product_id INTEGER NOT NULL, location_id INTEGER NOT NULL, CONSTRAINT FK_6017DDA4584665A FOREIGN KEY (product_id) REFERENCES product (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_6017DDA64D218E FOREIGN KEY (location_id) REFERENCES stock_location (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6017DDA4584665A ON stock_item (product_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6017DDA64D218E ON stock_item (location_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE stock_location (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, code VARCHAR(50) NOT NULL, name VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, is_active BOOLEAN NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, parent_id INTEGER DEFAULT NULL, CONSTRAINT FK_1158DD89727ACA70 FOREIGN KEY (parent_id) REFERENCES stock_location (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_1158DD89727ACA70 ON stock_location (parent_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE stock_movement (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, type VARCHAR(50) NOT NULL, source VARCHAR(50) NOT NULL, movement_date DATETIME NOT NULL, quantity DOUBLE PRECISION NOT NULL, reason CLOB DEFAULT NULL, reference_number VARCHAR(50) DEFAULT NULL, reference_type VARCHAR(50) DEFAULT NULL, created_at DATETIME NOT NULL, stock_item_id INTEGER NOT NULL, created_by_id INTEGER NOT NULL, source_location_id INTEGER DEFAULT NULL, destination_location_id INTEGER DEFAULT NULL, CONSTRAINT FK_BB1BC1B5BC942FD FOREIGN KEY (stock_item_id) REFERENCES stock_item (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_BB1BC1B5B03A8386 FOREIGN KEY (created_by_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_BB1BC1B53A32712E FOREIGN KEY (source_location_id) REFERENCES stock_location (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_BB1BC1B5237FCAB5 FOREIGN KEY (destination_location_id) REFERENCES stock_location (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_BB1BC1B5BC942FD ON stock_movement (stock_item_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_BB1BC1B5B03A8386 ON stock_movement (created_by_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_BB1BC1B53A32712E ON stock_movement (source_location_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_BB1BC1B5237FCAB5 ON stock_movement (destination_location_id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE stock_alert
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE stock_inventory
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE stock_inventory_locations
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE stock_inventory_line
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE stock_item
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE stock_location
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE stock_movement
        SQL);
    }
}
