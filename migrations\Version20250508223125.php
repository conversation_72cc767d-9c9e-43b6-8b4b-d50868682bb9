<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250508223125 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE department_permission (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, can_view BOOLEAN NOT NULL, can_edit BOOLEAN NOT NULL, can_delete BOOLEAN NOT NULL, can_manage_employees BOOLEAN NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, user_id INTEGER NOT NULL, department_id INTEGER NOT NULL, CONSTRAINT FK_72409E46A76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_72409E46AE80F5DF FOREIGN KEY (department_id) REFERENCES department (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_72409E46A76ED395 ON department_permission (user_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_72409E46AE80F5DF ON department_permission (department_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TEMPORARY TABLE __temp__role_permission AS SELECT id, can_view, can_create, can_edit, can_delete, created_at, updated_at, role_id, permission_id, can_approve, can_reject, can_export, can_import, can_print FROM role_permission
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE role_permission
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE role_permission (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, can_view BOOLEAN NOT NULL, can_create BOOLEAN NOT NULL, can_edit BOOLEAN NOT NULL, can_delete BOOLEAN NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, role_id INTEGER NOT NULL, permission_id INTEGER NOT NULL, can_approve BOOLEAN NOT NULL, can_reject BOOLEAN NOT NULL, can_export BOOLEAN NOT NULL, can_import BOOLEAN NOT NULL, can_print BOOLEAN NOT NULL, CONSTRAINT FK_6F7DF886D60322AC FOREIGN KEY (role_id) REFERENCES role (id) ON UPDATE NO ACTION ON DELETE NO ACTION NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_6F7DF886FED90CCA FOREIGN KEY (permission_id) REFERENCES permission (id) ON UPDATE NO ACTION ON DELETE NO ACTION NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            INSERT INTO role_permission (id, can_view, can_create, can_edit, can_delete, created_at, updated_at, role_id, permission_id, can_approve, can_reject, can_export, can_import, can_print) SELECT id, can_view, can_create, can_edit, can_delete, created_at, updated_at, role_id, permission_id, can_approve, can_reject, can_export, can_import, can_print FROM __temp__role_permission
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE __temp__role_permission
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6F7DF886FED90CCA ON role_permission (permission_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6F7DF886D60322AC ON role_permission (role_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TEMPORARY TABLE __temp__system_setting AS SELECT id, name, "key", value, category, type, description, options, created_at, updated_at FROM system_setting
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE system_setting
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE system_setting (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(255) NOT NULL, "key" VARCHAR(255) NOT NULL, value CLOB DEFAULT NULL, category VARCHAR(255) NOT NULL, type VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, options CLOB DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL)
        SQL);
        $this->addSql(<<<'SQL'
            INSERT INTO system_setting (id, name, "key", value, category, type, description, options, created_at, updated_at) SELECT id, name, "key", value, category, type, description, options, created_at, updated_at FROM __temp__system_setting
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE __temp__system_setting
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_7307C40B8A90ABA9 ON system_setting ("key")
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user ADD COLUMN is_two_factor_enabled BOOLEAN DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user ADD COLUMN totp_secret VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user ADD COLUMN backup_codes VARCHAR(255) DEFAULT NULL
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user ADD COLUMN preferred_two_factor_method VARCHAR(20) DEFAULT NULL
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            DROP TABLE department_permission
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TEMPORARY TABLE __temp__role_permission AS SELECT id, can_view, can_create, can_edit, can_delete, can_approve, can_reject, can_export, can_import, can_print, created_at, updated_at, role_id, permission_id FROM role_permission
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE role_permission
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE role_permission (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, can_view BOOLEAN NOT NULL, can_create BOOLEAN NOT NULL, can_edit BOOLEAN NOT NULL, can_delete BOOLEAN NOT NULL, can_approve BOOLEAN DEFAULT 0 NOT NULL, can_reject BOOLEAN DEFAULT 0 NOT NULL, can_export BOOLEAN DEFAULT 0 NOT NULL, can_import BOOLEAN DEFAULT 0 NOT NULL, can_print BOOLEAN DEFAULT 0 NOT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, role_id INTEGER NOT NULL, permission_id INTEGER NOT NULL, CONSTRAINT FK_6F7DF886D60322AC FOREIGN KEY (role_id) REFERENCES "role" (id) NOT DEFERRABLE INITIALLY IMMEDIATE, CONSTRAINT FK_6F7DF886FED90CCA FOREIGN KEY (permission_id) REFERENCES permission (id) NOT DEFERRABLE INITIALLY IMMEDIATE)
        SQL);
        $this->addSql(<<<'SQL'
            INSERT INTO role_permission (id, can_view, can_create, can_edit, can_delete, can_approve, can_reject, can_export, can_import, can_print, created_at, updated_at, role_id, permission_id) SELECT id, can_view, can_create, can_edit, can_delete, can_approve, can_reject, can_export, can_import, can_print, created_at, updated_at, role_id, permission_id FROM __temp__role_permission
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE __temp__role_permission
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6F7DF886D60322AC ON role_permission (role_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE INDEX IDX_6F7DF886FED90CCA ON role_permission (permission_id)
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TEMPORARY TABLE __temp__system_setting AS SELECT id, name, "key", value, category, type, description, options, created_at, updated_at FROM system_setting
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE system_setting
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE system_setting (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, name VARCHAR(255) NOT NULL, "key" VARCHAR(255) NOT NULL, value CLOB DEFAULT NULL, category VARCHAR(255) NOT NULL, type VARCHAR(255) NOT NULL, description CLOB DEFAULT NULL, options CLOB DEFAULT NULL, created_at DATETIME NOT NULL --(DC2Type:datetime_immutable)
            , updated_at DATETIME DEFAULT NULL --(DC2Type:datetime_immutable)
            )
        SQL);
        $this->addSql(<<<'SQL'
            INSERT INTO system_setting (id, name, "key", value, category, type, description, options, created_at, updated_at) SELECT id, name, "key", value, category, type, description, options, created_at, updated_at FROM __temp__system_setting
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE __temp__system_setting
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_8F7A1D858A90ABA9 ON system_setting ("key")
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TEMPORARY TABLE __temp__user AS SELECT id, email, password, name, first_name, last_name, phone, roles, is_active, last_login, created_at, updated_at, reset_token, reset_token_expires_at FROM "user"
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE "user"
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE "user" (id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, email VARCHAR(180) NOT NULL, password VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, first_name VARCHAR(255) DEFAULT NULL, last_name VARCHAR(255) DEFAULT NULL, phone VARCHAR(20) DEFAULT NULL, roles CLOB NOT NULL, is_active BOOLEAN NOT NULL, last_login DATETIME DEFAULT NULL, created_at DATETIME DEFAULT NULL, updated_at DATETIME DEFAULT NULL, reset_token VARCHAR(100) DEFAULT NULL, reset_token_expires_at DATETIME DEFAULT NULL)
        SQL);
        $this->addSql(<<<'SQL'
            INSERT INTO "user" (id, email, password, name, first_name, last_name, phone, roles, is_active, last_login, created_at, updated_at, reset_token, reset_token_expires_at) SELECT id, email, password, name, first_name, last_name, phone, roles, is_active, last_login, created_at, updated_at, reset_token, reset_token_expires_at FROM __temp__user
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE __temp__user
        SQL);
        $this->addSql(<<<'SQL'
            CREATE UNIQUE INDEX UNIQ_8D93D649E7927C74 ON "user" (email)
        SQL);
    }
}
