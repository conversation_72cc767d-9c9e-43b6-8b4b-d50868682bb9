<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250510000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add new permission columns to role_permission table with default values';
    }

    public function up(Schema $schema): void
    {
        // Add new columns to role_permission table with default values
        $this->addSql('ALTER TABLE role_permission ADD COLUMN can_approve BOOLEAN NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE role_permission ADD COLUMN can_reject BOOLEAN NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE role_permission ADD COLUMN can_export BOOLEAN NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE role_permission ADD COLUMN can_import BOOLEAN NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE role_permission ADD COLUMN can_print BOOLEAN NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        // Remove columns from role_permission table
        $this->addSql('ALTER TABLE role_permission DROP COLUMN can_approve');
        $this->addSql('ALTER TABLE role_permission DROP COLUMN can_reject');
        $this->addSql('ALTER TABLE role_permission DROP COLUMN can_export');
        $this->addSql('ALTER TABLE role_permission DROP COLUMN can_import');
        $this->addSql('ALTER TABLE role_permission DROP COLUMN can_print');
    }
}
