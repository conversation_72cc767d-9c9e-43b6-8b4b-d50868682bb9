<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250510000002 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create system_setting table';
    }

    public function up(Schema $schema): void
    {
        // Create system_setting table
        $this->addSql('CREATE TABLE system_setting (
            id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
            name VARCHAR(255) NOT NULL,
            key VARCHAR(255) NOT NULL,
            value CLOB DEFAULT NULL,
            category VARCHAR(255) NOT NULL,
            type VARCHAR(255) NOT NULL,
            description CLOB DEFAULT NULL,
            options CLOB DEFAULT NULL,
            created_at DATETIME NOT NULL --(DC2Type:datetime_immutable)
            ,
            updated_at DATETIME DEFAULT NULL --(DC2Type:datetime_immutable)
        )');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_8F7A1D858A90ABA9 ON system_setting (key)');

        // Insert default system settings
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Nom du site', 'app.site_name', 'SI - Système d''Information', 'general', 'text', 'Nom du site affiché dans le titre et le pied de page', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Email de contact', 'app.contact_email', '<EMAIL>', 'general', 'text', 'Adresse email de contact', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Téléphone de contact', 'app.contact_phone', '+212 5 XX XX XX XX', 'general', 'text', 'Numéro de téléphone de contact', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Adresse', 'app.address', 'Adresse de l''entreprise', 'general', 'textarea', 'Adresse de l''entreprise', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Logo', 'app.logo', '/images/logo.png', 'general', 'text', 'Chemin vers le logo du site', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Favicon', 'app.favicon', '/favicon.ico', 'general', 'text', 'Chemin vers le favicon du site', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Version', 'app.version', '1.0.0', 'general', 'text', 'Version actuelle de l''application', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Devise par défaut', 'app.default_currency', 'MAD', 'general', 'text', 'Devise par défaut utilisée dans l''application', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Format de date', 'app.date_format', 'd/m/Y', 'general', 'text', 'Format de date utilisé dans l''application', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Format d''heure', 'app.time_format', 'H:i', 'general', 'text', 'Format d''heure utilisé dans l''application', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Fuseau horaire', 'app.timezone', 'Africa/Casablanca', 'general', 'text', 'Fuseau horaire utilisé dans l''application', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Langue par défaut', 'app.default_locale', 'fr', 'general', 'text', 'Langue par défaut utilisée dans l''application', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Nombre d''éléments par page', 'app.items_per_page', '10', 'general', 'text', 'Nombre d''éléments affichés par page dans les listes', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Activer les notifications par email', 'app.email_notifications', '1', 'notification', 'boolean', 'Activer ou désactiver les notifications par email', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Expéditeur des emails', 'app.email_sender', '<EMAIL>', 'notification', 'text', 'Adresse email utilisée comme expéditeur pour les notifications', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Nom de l''expéditeur des emails', 'app.email_sender_name', 'SI - Système d''Information', 'notification', 'text', 'Nom affiché comme expéditeur pour les notifications par email', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Délai d''expiration de session (minutes)', 'app.session_timeout', '30', 'security', 'text', 'Délai d''expiration de la session en minutes', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Nombre de tentatives de connexion avant blocage', 'app.login_attempts', '5', 'security', 'text', 'Nombre de tentatives de connexion échouées avant blocage du compte', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Durée de blocage après tentatives échouées (minutes)', 'app.login_block_duration', '15', 'security', 'text', 'Durée de blocage du compte après tentatives de connexion échouées (en minutes)', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, options, description, created_at) VALUES ('Complexité minimale du mot de passe', 'app.password_complexity', 'medium', 'security', 'select', 'low,medium,high', 'Niveau de complexité minimal requis pour les mots de passe', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Durée de validité du mot de passe (jours)', 'app.password_expiry', '90', 'security', 'text', 'Durée de validité du mot de passe en jours (0 = pas d''expiration)', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Activer la journalisation des activités', 'app.activity_logging', '1', 'security', 'boolean', 'Activer ou désactiver la journalisation des activités des utilisateurs', datetime('now'))");
        $this->addSql("INSERT INTO system_setting (name, key, value, category, type, description, created_at) VALUES ('Durée de conservation des journaux (jours)', 'app.log_retention', '90', 'security', 'text', 'Durée de conservation des journaux d''activité en jours', datetime('now'))");
    }

    public function down(Schema $schema): void
    {
        // Drop system_setting table
        $this->addSql('DROP TABLE system_setting');
    }
}
