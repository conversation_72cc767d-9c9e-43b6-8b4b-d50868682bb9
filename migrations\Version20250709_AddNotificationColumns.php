<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Migration pour ajouter les colonnes manquantes à la table notification
 */
final class Version20250709_AddNotificationColumns extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Ajoute les colonnes manquantes à la table notification pour les fonctionnalités avancées';
    }

    public function up(Schema $schema): void
    {
        // Vérifier si la table notification existe
        if (!$schema->hasTable('notification')) {
            // Créer la table complète si elle n'existe pas
            $table = $schema->createTable('notification');
            $table->addColumn('id', 'integer', ['autoincrement' => true, 'notnull' => true]);
            $table->addColumn('title', 'string', ['length' => 255, 'notnull' => true]);
            $table->addColumn('content', 'text', ['notnull' => false]);
            $table->addColumn('type', 'string', ['length' => 50, 'notnull' => true]);
            $table->addColumn('user_id', 'string', ['length' => 50, 'notnull' => false]);
            $table->addColumn('is_read', 'boolean', ['notnull' => true, 'default' => false]);
            $table->addColumn('related_entity_type', 'string', ['length' => 50, 'notnull' => false]);
            $table->addColumn('related_entity_id', 'integer', ['notnull' => false]);
            $table->addColumn('created_at', 'datetime_immutable', ['notnull' => true]);
            $table->addColumn('read_at', 'datetime_immutable', ['notnull' => false]);
            $table->addColumn('link', 'string', ['length' => 255, 'notnull' => false]);
            $table->addColumn('priority', 'string', ['length' => 20, 'notnull' => false]);
            $table->addColumn('data', 'json', ['notnull' => false]);
            $table->addColumn('action_url', 'string', ['length' => 255, 'notnull' => false]);
            $table->addColumn('action_label', 'string', ['length' => 100, 'notnull' => false]);
            $table->addColumn('icon', 'string', ['length' => 50, 'notnull' => false]);
            $table->addColumn('color', 'string', ['length' => 20, 'notnull' => false]);
            $table->addColumn('is_push_enabled', 'boolean', ['notnull' => true, 'default' => false]);
            $table->addColumn('is_email_enabled', 'boolean', ['notnull' => true, 'default' => false]);
            $table->addColumn('email_sent_at', 'datetime_immutable', ['notnull' => false]);
            $table->addColumn('push_sent_at', 'datetime_immutable', ['notnull' => false]);
            $table->addColumn('expires_at', 'datetime_immutable', ['notnull' => false]);
            
            $table->setPrimaryKey(['id']);
            $table->addIndex(['user_id'], 'idx_notification_user');
            $table->addIndex(['created_at'], 'idx_notification_created');
            $table->addIndex(['type'], 'idx_notification_type');
            $table->addIndex(['is_read'], 'idx_notification_read');
        } else {
            // Ajouter les colonnes manquantes si la table existe déjà
            $table = $schema->getTable('notification');
            
            // Vérifier et ajouter chaque colonne si elle n'existe pas
            if (!$table->hasColumn('priority')) {
                $table->addColumn('priority', 'string', ['length' => 20, 'notnull' => false]);
            }
            
            if (!$table->hasColumn('data')) {
                $table->addColumn('data', 'json', ['notnull' => false]);
            }
            
            if (!$table->hasColumn('action_url')) {
                $table->addColumn('action_url', 'string', ['length' => 255, 'notnull' => false]);
            }
            
            if (!$table->hasColumn('action_label')) {
                $table->addColumn('action_label', 'string', ['length' => 100, 'notnull' => false]);
            }
            
            if (!$table->hasColumn('icon')) {
                $table->addColumn('icon', 'string', ['length' => 50, 'notnull' => false]);
            }
            
            if (!$table->hasColumn('color')) {
                $table->addColumn('color', 'string', ['length' => 20, 'notnull' => false]);
            }
            
            if (!$table->hasColumn('is_push_enabled')) {
                $table->addColumn('is_push_enabled', 'boolean', ['notnull' => true, 'default' => false]);
            }
            
            if (!$table->hasColumn('is_email_enabled')) {
                $table->addColumn('is_email_enabled', 'boolean', ['notnull' => true, 'default' => false]);
            }
            
            if (!$table->hasColumn('email_sent_at')) {
                $table->addColumn('email_sent_at', 'datetime_immutable', ['notnull' => false]);
            }
            
            if (!$table->hasColumn('push_sent_at')) {
                $table->addColumn('push_sent_at', 'datetime_immutable', ['notnull' => false]);
            }
            
            if (!$table->hasColumn('expires_at')) {
                $table->addColumn('expires_at', 'datetime_immutable', ['notnull' => false]);
            }
            
            // Ajouter les index manquants
            if (!$table->hasIndex('idx_notification_type')) {
                $table->addIndex(['type'], 'idx_notification_type');
            }
            
            if (!$table->hasIndex('idx_notification_read')) {
                $table->addIndex(['is_read'], 'idx_notification_read');
            }
        }
    }

    public function down(Schema $schema): void
    {
        // Supprimer les colonnes ajoutées
        if ($schema->hasTable('notification')) {
            $table = $schema->getTable('notification');
            
            $columnsToRemove = [
                'priority', 'data', 'action_url', 'action_label', 'icon', 'color',
                'is_push_enabled', 'is_email_enabled', 'email_sent_at', 'push_sent_at', 'expires_at'
            ];
            
            foreach ($columnsToRemove as $column) {
                if ($table->hasColumn($column)) {
                    $table->dropColumn($column);
                }
            }
            
            // Supprimer les index ajoutés
            if ($table->hasIndex('idx_notification_type')) {
                $table->dropIndex('idx_notification_type');
            }
            
            if ($table->hasIndex('idx_notification_read')) {
                $table->dropIndex('idx_notification_read');
            }
        }
    }
}
