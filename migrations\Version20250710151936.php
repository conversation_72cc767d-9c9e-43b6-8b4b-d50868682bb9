<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250710151936 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            CREATE TABLE document_request (id INT AUTO_INCREMENT NOT NULL, document_type VARCHAR(100) NOT NULL, purpose LONGTEXT NOT NULL, quantity INT NOT NULL, language VARCHAR(10) NOT NULL, needed_by DATETIME DEFAULT NULL, delivery_method VARCHAR(50) NOT NULL, delivery_address LONGTEXT DEFAULT NULL, status VARCHAR(30) NOT NULL, processed_at DATETIME DEFAULT NULL, delivered_at DATETIME DEFAULT NULL, notes LONGTEXT DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, request_id INT NOT NULL, employee_id INT NOT NULL, processed_by_id INT DEFAULT NULL, UNIQUE INDEX UNIQ_9FF82943427EB8A5 (request_id), INDEX IDX_9FF829438C03F15C (employee_id), INDEX IDX_9FF829432FFD4FD3 (processed_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE employee_request (id INT AUTO_INCREMENT NOT NULL, type VARCHAR(50) NOT NULL, title VARCHAR(200) NOT NULL, description LONGTEXT NOT NULL, status VARCHAR(30) NOT NULL, approved_at DATETIME DEFAULT NULL, rejection_reason LONGTEXT DEFAULT NULL, priority VARCHAR(20) NOT NULL, requested_date DATETIME DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, employee_id INT NOT NULL, approved_by_id INT DEFAULT NULL, INDEX IDX_BBDBD9908C03F15C (employee_id), INDEX IDX_BBDBD9902D234F6A (approved_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE request_attachment (id INT AUTO_INCREMENT NOT NULL, filename VARCHAR(255) NOT NULL, original_name VARCHAR(255) NOT NULL, mime_type VARCHAR(100) NOT NULL, file_size INT NOT NULL, uploaded_at DATETIME NOT NULL, description VARCHAR(500) DEFAULT NULL, request_id INT NOT NULL, uploaded_by_id INT NOT NULL, INDEX IDX_31A61E01427EB8A5 (request_id), INDEX IDX_31A61E01A2B28FE8 (uploaded_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE request_comment (id INT AUTO_INCREMENT NOT NULL, comment LONGTEXT NOT NULL, type VARCHAR(30) NOT NULL, is_internal TINYINT(1) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, request_id INT NOT NULL, author_id INT NOT NULL, INDEX IDX_BE50203B427EB8A5 (request_id), INDEX IDX_BE50203BF675F31B (author_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE salary_advance (id INT AUTO_INCREMENT NOT NULL, amount NUMERIC(10, 2) NOT NULL, approved_amount NUMERIC(10, 2) DEFAULT NULL, reason LONGTEXT NOT NULL, repayment_months INT NOT NULL, monthly_deduction NUMERIC(10, 2) DEFAULT NULL, first_deduction_date DATETIME DEFAULT NULL, status VARCHAR(30) NOT NULL, paid_at DATETIME DEFAULT NULL, remaining_amount NUMERIC(10, 2) NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, request_id INT NOT NULL, employee_id INT NOT NULL, paid_by_id INT DEFAULT NULL, UNIQUE INDEX UNIQ_7085813F427EB8A5 (request_id), INDEX IDX_7085813F8C03F15C (employee_id), INDEX IDX_7085813F7F9BC654 (paid_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            CREATE TABLE salary_advance_repayment (id INT AUTO_INCREMENT NOT NULL, amount NUMERIC(10, 2) NOT NULL, deduction_date DATETIME NOT NULL, status VARCHAR(20) NOT NULL, notes LONGTEXT DEFAULT NULL, processed_at DATETIME DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME DEFAULT NULL, salary_advance_id INT NOT NULL, processed_by_id INT DEFAULT NULL, INDEX IDX_2EA3D60F63CB948 (salary_advance_id), INDEX IDX_2EA3D602FFD4FD3 (processed_by_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document_request ADD CONSTRAINT FK_9FF82943427EB8A5 FOREIGN KEY (request_id) REFERENCES employee_request (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document_request ADD CONSTRAINT FK_9FF829438C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document_request ADD CONSTRAINT FK_9FF829432FFD4FD3 FOREIGN KEY (processed_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_request ADD CONSTRAINT FK_BBDBD9908C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_request ADD CONSTRAINT FK_BBDBD9902D234F6A FOREIGN KEY (approved_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE request_attachment ADD CONSTRAINT FK_31A61E01427EB8A5 FOREIGN KEY (request_id) REFERENCES employee_request (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE request_attachment ADD CONSTRAINT FK_31A61E01A2B28FE8 FOREIGN KEY (uploaded_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE request_comment ADD CONSTRAINT FK_BE50203B427EB8A5 FOREIGN KEY (request_id) REFERENCES employee_request (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE request_comment ADD CONSTRAINT FK_BE50203BF675F31B FOREIGN KEY (author_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE salary_advance ADD CONSTRAINT FK_7085813F427EB8A5 FOREIGN KEY (request_id) REFERENCES employee_request (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE salary_advance ADD CONSTRAINT FK_7085813F8C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE salary_advance ADD CONSTRAINT FK_7085813F7F9BC654 FOREIGN KEY (paid_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE salary_advance_repayment ADD CONSTRAINT FK_2EA3D60F63CB948 FOREIGN KEY (salary_advance_id) REFERENCES salary_advance (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE salary_advance_repayment ADD CONSTRAINT FK_2EA3D602FFD4FD3 FOREIGN KEY (processed_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_account ADD CONSTRAINT FK_44BEAB93727ACA70 FOREIGN KEY (parent_id) REFERENCES accounting_account (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry ADD CONSTRAINT FK_62764D68478E8802 FOREIGN KEY (journal_id) REFERENCES accounting_journal (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry ADD CONSTRAINT FK_62764D689B6B5FBA FOREIGN KEY (account_id) REFERENCES accounting_account (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry ADD CONSTRAINT FK_62764D68B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry ADD CONSTRAINT FK_62764D68C69DE5E5 FOREIGN KEY (validated_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry_line ADD CONSTRAINT FK_4BBA65466A86E4FB FOREIGN KEY (journal_entry_id) REFERENCES accounting_journal_entry (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry_line ADD CONSTRAINT FK_4BBA65469B6B5FBA FOREIGN KEY (account_id) REFERENCES accounting_account (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_tax_declaration ADD CONSTRAINT FK_216E9EF2B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_tax_declaration ADD CONSTRAINT FK_216E9EF279F7D87D FOREIGN KEY (submitted_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_tax_declaration ADD CONSTRAINT FK_216E9EF263F9139E FOREIGN KEY (fiscal_year_id) REFERENCES accounting_fiscal_year (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contract ADD CONSTRAINT FK_E98F28592ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contract ADD CONSTRAINT FK_E98F2859166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contract ADD CONSTRAINT FK_E98F28594E4DEF6F FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contract ADD CONSTRAINT FK_E98F2859B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contract ADD CONSTRAINT FK_E98F2859D2EDD3FB FOREIGN KEY (signed_by_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE department ADD CONSTRAINT FK_CD1DE18A7206964D FOREIGN KEY (parent_department_id) REFERENCES department (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE department_permission ADD CONSTRAINT FK_72409E46A76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE department_permission ADD CONSTRAINT FK_72409E46AE80F5DF FOREIGN KEY (department_id) REFERENCES department (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee ADD CONSTRAINT FK_5D9F75A1A76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee ADD CONSTRAINT FK_5D9F75A1AE80F5DF FOREIGN KEY (department_id) REFERENCES department (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee ADD CONSTRAINT FK_5D9F75A1DD842E46 FOREIGN KEY (position_id) REFERENCES position (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee ADD CONSTRAINT FK_5D9F75A1783E3463 FOREIGN KEY (manager_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_document ADD CONSTRAINT FK_4856DBB18C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_document ADD CONSTRAINT FK_4856DBB1A2B28FE8 FOREIGN KEY (uploaded_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_education ADD CONSTRAINT FK_DE9A36908C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_leave ADD CONSTRAINT FK_73BD8DA98C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_leave ADD CONSTRAINT FK_73BD8DA92D234F6A FOREIGN KEY (approved_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_performance ADD CONSTRAINT FK_3F157FC8C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_performance ADD CONSTRAINT FK_3F157FCFC6B21F1 FOREIGN KEY (reviewed_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_skill ADD CONSTRAINT FK_B630E90E8C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense_item ADD CONSTRAINT FK_ABBC6B7C8F758FBA FOREIGN KEY (expense_report_id) REFERENCES expense_report (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense_report ADD CONSTRAINT FK_280A69185636C81 FOREIGN KEY (mission_order_id) REFERENCES mission_order (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense_report ADD CONSTRAINT FK_280A691C69DE5E5 FOREIGN KEY (validated_by_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense_report ADD CONSTRAINT FK_280A691B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE family_member ADD CONSTRAINT FK_B9D4AD6D8C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE goods_receipt ADD CONSTRAINT FK_B6CFD848A45D7E6A FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE goods_receipt ADD CONSTRAINT FK_B6CFD848B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE goods_receipt ADD CONSTRAINT FK_B6CFD8486F8DDD17 FOREIGN KEY (received_by_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE goods_receipt_item ADD CONSTRAINT FK_E1310FB85AF289C8 FOREIGN KEY (goods_receipt_id) REFERENCES goods_receipt (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE goods_receipt_item ADD CONSTRAINT FK_E1310FB83207420A FOREIGN KEY (purchase_order_item_id) REFERENCES purchase_order_item (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE integration ADD CONSTRAINT FK_FDE96D9BA76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice ADD CONSTRAINT FK_906517442ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice ADD CONSTRAINT FK_90651744166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice ADD CONSTRAINT FK_90651744A45D7E6A FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice ADD CONSTRAINT FK_90651744B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice ADD CONSTRAINT FK_90651744896DBBDE FOREIGN KEY (updated_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice ADD CONSTRAINT FK_906517442D234F6A FOREIGN KEY (approved_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice ADD CONSTRAINT FK_906517447F9BC654 FOREIGN KEY (paid_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_approval ADD CONSTRAINT FK_9C8CF732989F1FD FOREIGN KEY (invoice_id) REFERENCES invoice (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_approval ADD CONSTRAINT FK_9C8CF73A76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_approval ADD CONSTRAINT FK_9C8CF7335EF05C1 FOREIGN KEY (delegated_to_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_item ADD CONSTRAINT FK_1DDE477B2989F1FD FOREIGN KEY (invoice_id) REFERENCES invoice (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_item ADD CONSTRAINT FK_1DDE477B4584665A FOREIGN KEY (product_id) REFERENCES product (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_item ADD CONSTRAINT FK_1DDE477B3207420A FOREIGN KEY (purchase_order_item_id) REFERENCES purchase_order_item (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_payment ADD CONSTRAINT FK_9FF1B2DE2989F1FD FOREIGN KEY (invoice_id) REFERENCES invoice (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_payment ADD CONSTRAINT FK_9FF1B2DEB03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_payment ADD CONSTRAINT FK_9FF1B2DEF5E43AE9 FOREIGN KEY (reconciled_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_document ADD CONSTRAINT FK_A4F36721B88E2BB6 FOREIGN KEY (medical_record_id) REFERENCES medical_record (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_document ADD CONSTRAINT FK_A4F36721B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_examination ADD CONSTRAINT FK_5E236477B88E2BB6 FOREIGN KEY (medical_record_id) REFERENCES medical_record (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_examination ADD CONSTRAINT FK_5E236477B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_record ADD CONSTRAINT FK_F06A283E8C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_record ADD CONSTRAINT FK_F06A283EBC594993 FOREIGN KEY (family_member_id) REFERENCES family_member (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_record ADD CONSTRAINT FK_F06A283EB03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_record_history ADD CONSTRAINT FK_DEBCDBE3B88E2BB6 FOREIGN KEY (medical_record_id) REFERENCES medical_record (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_record_history ADD CONSTRAINT FK_DEBCDBE3A76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mission_order ADD CONSTRAINT FK_65BFCCA5166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mission_order ADD CONSTRAINT FK_65BFCCA58C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mission_order ADD CONSTRAINT FK_65BFCCA5C69DE5E5 FOREIGN KEY (validated_by_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mission_order ADD CONSTRAINT FK_65BFCCA5AC7074D6 FOREIGN KEY (finance_validated_by_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mission_order ADD CONSTRAINT FK_65BFCCA5B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD CONSTRAINT FK_312B3E166BF700BD FOREIGN KEY (status_id) REFERENCES partner_status (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD CONSTRAINT FK_312B3E16C54C8C93 FOREIGN KEY (type_id) REFERENCES partner_type (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD CONSTRAINT FK_312B3E163BCB2E4B FOREIGN KEY (nature_id) REFERENCES partner_nature (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD CONSTRAINT FK_312B3E16682B5931 FOREIGN KEY (scope_id) REFERENCES partner_scope (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD CONSTRAINT FK_312B3E1684A5C6C7 FOREIGN KEY (account_manager_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner ADD CONSTRAINT FK_312B3E16D53503B3 FOREIGN KEY (owner_department_id) REFERENCES department (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner_status_history ADD CONSTRAINT FK_2EFD8EF59393F8FE FOREIGN KEY (partner_id) REFERENCES partner (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner_status_history ADD CONSTRAINT FK_2EFD8EF52E43440C FOREIGN KEY (old_status_id) REFERENCES partner_status (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner_status_history ADD CONSTRAINT FK_2EFD8EF5596805D2 FOREIGN KEY (new_status_id) REFERENCES partner_status (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE position ADD CONSTRAINT FK_462CE4F5AE80F5DF FOREIGN KEY (department_id) REFERENCES department (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE price_history ADD CONSTRAINT FK_4C9CB8174584665A FOREIGN KEY (product_id) REFERENCES product (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE price_history ADD CONSTRAINT FK_4C9CB8172ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE price_history ADD CONSTRAINT FK_4C9CB817B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product ADD CONSTRAINT FK_D34A04AD12469DE2 FOREIGN KEY (category_id) REFERENCES product_category (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product ADD CONSTRAINT FK_D34A04ADB03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_category ADD CONSTRAINT FK_CDFC7356727ACA70 FOREIGN KEY (parent_id) REFERENCES product_category (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project ADD CONSTRAINT FK_2FB3D0EE9393F8FE FOREIGN KEY (partner_id) REFERENCES partner (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project ADD CONSTRAINT FK_2FB3D0EED53503B3 FOREIGN KEY (owner_department_id) REFERENCES department (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project ADD CONSTRAINT FK_2FB3D0EE783E3463 FOREIGN KEY (manager_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project ADD CONSTRAINT FK_2FB3D0EEB03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_budget_line ADD CONSTRAINT FK_631F415D166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_budget_sub_line ADD CONSTRAINT FK_197C0F638FF83FA3 FOREIGN KEY (budget_line_id) REFERENCES project_budget_line (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_deliverable ADD CONSTRAINT FK_69B253BA166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_deliverable ADD CONSTRAINT FK_69B253BAF4BD7827 FOREIGN KEY (assigned_to_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_deliverable ADD CONSTRAINT FK_69B253BA2D234F6A FOREIGN KEY (approved_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_deliverable ADD CONSTRAINT FK_69B253BAC33F7837 FOREIGN KEY (document_id) REFERENCES project_document (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_document ADD CONSTRAINT FK_E52701AD166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_document ADD CONSTRAINT FK_E52701ADA2B28FE8 FOREIGN KEY (uploaded_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_member ADD CONSTRAINT FK_67401132166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_member ADD CONSTRAINT FK_67401132A76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_metric ADD CONSTRAINT FK_9072C5A9166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_metric ADD CONSTRAINT FK_9072C5A9B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_resource ADD CONSTRAINT FK_81DF7FCD166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_resource ADD CONSTRAINT FK_81DF7FCDA76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_risk ADD CONSTRAINT FK_40971D59166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_risk ADD CONSTRAINT FK_40971D597E3C61F9 FOREIGN KEY (owner_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_stock_allocation ADD CONSTRAINT FK_4F8817EF166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_stock_allocation ADD CONSTRAINT FK_4F8817EFBC942FD FOREIGN KEY (stock_item_id) REFERENCES stock_item (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_stock_allocation ADD CONSTRAINT FK_4F8817EF6802B588 FOREIGN KEY (allocated_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_task ADD CONSTRAINT FK_6BEF133D166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_task ADD CONSTRAINT FK_6BEF133DF4BD7827 FOREIGN KEY (assigned_to_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_task ADD CONSTRAINT FK_6BEF133DFFFE75C0 FOREIGN KEY (parent_task_id) REFERENCES project_task (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template ADD CONSTRAINT FK_AA2E9458B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template_deliverable ADD CONSTRAINT FK_8C67838A5DA0FB8 FOREIGN KEY (template_id) REFERENCES project_template (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template_deliverable ADD CONSTRAINT FK_8C67838AB2AFCB23 FOREIGN KEY (related_task_id) REFERENCES project_template_task (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template_risk ADD CONSTRAINT FK_7859682A5DA0FB8 FOREIGN KEY (template_id) REFERENCES project_template (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template_task ADD CONSTRAINT FK_5321664E5DA0FB8 FOREIGN KEY (template_id) REFERENCES project_template (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template_task ADD CONSTRAINT FK_5321664E68C90015 FOREIGN KEY (predecessor_id) REFERENCES project_template_task (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order ADD CONSTRAINT FK_21E210B22ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order ADD CONSTRAINT FK_21E210B2166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order ADD CONSTRAINT FK_21E210B24E4DEF6F FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order ADD CONSTRAINT FK_21E210B2B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order ADD CONSTRAINT FK_21E210B2A45BB98C FOREIGN KEY (sent_by_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order_item ADD CONSTRAINT FK_5ED948C3A45D7E6A FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order_item ADD CONSTRAINT FK_5ED948C3384B2009 FOREIGN KEY (request_item_id) REFERENCES purchase_request_item (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order_item ADD CONSTRAINT FK_5ED948C34584665A FOREIGN KEY (product_id) REFERENCES product (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request ADD CONSTRAINT FK_204D45E6166D1F9C FOREIGN KEY (project_id) REFERENCES project (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request ADD CONSTRAINT FK_204D45E64DA1E751 FOREIGN KEY (requested_by_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request ADD CONSTRAINT FK_204D45E62D234F6A FOREIGN KEY (approved_by_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request ADD CONSTRAINT FK_204D45E68FF83FA3 FOREIGN KEY (budget_line_id) REFERENCES project_budget_line (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request ADD CONSTRAINT FK_204D45E644A96FE9 FOREIGN KEY (budget_sub_line_id) REFERENCES project_budget_sub_line (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request ADD CONSTRAINT FK_204D45E6B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request_approval ADD CONSTRAINT FK_3201F52C4E4DEF6F FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request_approval ADD CONSTRAINT FK_3201F52C2D234F6A FOREIGN KEY (approved_by_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request_item ADD CONSTRAINT FK_A4A6F5444E4DEF6F FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request_item ADD CONSTRAINT FK_A4A6F5444584665A FOREIGN KEY (product_id) REFERENCES product (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote ADD CONSTRAINT FK_6B71CBF42ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote ADD CONSTRAINT FK_6B71CBF44E4DEF6F FOREIGN KEY (purchase_request_id) REFERENCES purchase_request (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote ADD CONSTRAINT FK_6B71CBF4B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote ADD CONSTRAINT FK_6B71CBF46F8DDD17 FOREIGN KEY (received_by_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote_item ADD CONSTRAINT FK_8DFC7A94DB805178 FOREIGN KEY (quote_id) REFERENCES quote (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote_item ADD CONSTRAINT FK_8DFC7A944584665A FOREIGN KEY (product_id) REFERENCES product (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote_item ADD CONSTRAINT FK_8DFC7A94384B2009 FOREIGN KEY (request_item_id) REFERENCES purchase_request_item (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE role_permission ADD CONSTRAINT FK_6F7DF886D60322AC FOREIGN KEY (role_id) REFERENCES `role` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE role_permission ADD CONSTRAINT FK_6F7DF886FED90CCA FOREIGN KEY (permission_id) REFERENCES permission (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE security_log ADD CONSTRAINT FK_FE5C6A69A76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE skill ADD CONSTRAINT FK_5E3DE47712469DE2 FOREIGN KEY (category_id) REFERENCES skill_category (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_alert ADD CONSTRAINT FK_8BED5A30BC942FD FOREIGN KEY (stock_item_id) REFERENCES stock_item (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_alert ADD CONSTRAINT FK_8BED5A306713A32B FOREIGN KEY (resolved_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory ADD CONSTRAINT FK_29B0ACDEB03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory ADD CONSTRAINT FK_29B0ACDE85ECDE76 FOREIGN KEY (completed_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_locations ADD CONSTRAINT FK_95E18F4D9EEA759 FOREIGN KEY (inventory_id) REFERENCES stock_inventory (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_locations ADD CONSTRAINT FK_95E18F4DD98387BA FOREIGN KEY (stock_location_id) REFERENCES stock_location (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_line ADD CONSTRAINT FK_3C51C2939EEA759 FOREIGN KEY (inventory_id) REFERENCES stock_inventory (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_line ADD CONSTRAINT FK_3C51C2934584665A FOREIGN KEY (product_id) REFERENCES product (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_line ADD CONSTRAINT FK_3C51C29364D218E FOREIGN KEY (location_id) REFERENCES stock_location (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_line ADD CONSTRAINT FK_3C51C293BC942FD FOREIGN KEY (stock_item_id) REFERENCES stock_item (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_item ADD CONSTRAINT FK_6017DDA4584665A FOREIGN KEY (product_id) REFERENCES product (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_item ADD CONSTRAINT FK_6017DDA64D218E FOREIGN KEY (location_id) REFERENCES stock_location (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_location ADD CONSTRAINT FK_1158DD89727ACA70 FOREIGN KEY (parent_id) REFERENCES stock_location (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_movement ADD CONSTRAINT FK_BB1BC1B5BC942FD FOREIGN KEY (stock_item_id) REFERENCES stock_item (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_movement ADD CONSTRAINT FK_BB1BC1B5B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_movement ADD CONSTRAINT FK_BB1BC1B53A32712E FOREIGN KEY (source_location_id) REFERENCES stock_location (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_movement ADD CONSTRAINT FK_BB1BC1B5237FCAB5 FOREIGN KEY (destination_location_id) REFERENCES stock_location (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE supplier_rating ADD CONSTRAINT FK_3EBF4F62ADD6D8C FOREIGN KEY (supplier_id) REFERENCES partner (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE supplier_rating ADD CONSTRAINT FK_3EBF4F6A45D7E6A FOREIGN KEY (purchase_order_id) REFERENCES purchase_order (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE supplier_rating ADD CONSTRAINT FK_3EBF4F62576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE supplier_rating ADD CONSTRAINT FK_3EBF4F6B03A8386 FOREIGN KEY (created_by_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_session ADD CONSTRAINT FK_D7A45DABEFD98D1 FOREIGN KEY (training_id) REFERENCES training (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_session ADD CONSTRAINT FK_D7A45DAFB08EDF6 FOREIGN KEY (trainer_id) REFERENCES employee (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_session_participant ADD CONSTRAINT FK_889243C9DB8156B9 FOREIGN KEY (training_session_id) REFERENCES training_session (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_session_participant ADD CONSTRAINT FK_889243C98C03F15C FOREIGN KEY (employee_id) REFERENCES employee (id) ON DELETE CASCADE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_skill ADD CONSTRAINT FK_B1E76E1ABEFD98D1 FOREIGN KEY (training_id) REFERENCES training (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_skill ADD CONSTRAINT FK_B1E76E1A5585C142 FOREIGN KEY (skill_id) REFERENCES skill (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_device ADD CONSTRAINT FK_6C7DADB3A76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_role ADD CONSTRAINT FK_2DE8C6A3A76ED395 FOREIGN KEY (user_id) REFERENCES `user` (id)
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_role ADD CONSTRAINT FK_2DE8C6A3D60322AC FOREIGN KEY (role_id) REFERENCES `role` (id)
        SQL);
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql(<<<'SQL'
            ALTER TABLE document_request DROP FOREIGN KEY FK_9FF82943427EB8A5
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document_request DROP FOREIGN KEY FK_9FF829438C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE document_request DROP FOREIGN KEY FK_9FF829432FFD4FD3
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_request DROP FOREIGN KEY FK_BBDBD9908C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_request DROP FOREIGN KEY FK_BBDBD9902D234F6A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE request_attachment DROP FOREIGN KEY FK_31A61E01427EB8A5
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE request_attachment DROP FOREIGN KEY FK_31A61E01A2B28FE8
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE request_comment DROP FOREIGN KEY FK_BE50203B427EB8A5
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE request_comment DROP FOREIGN KEY FK_BE50203BF675F31B
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE salary_advance DROP FOREIGN KEY FK_7085813F427EB8A5
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE salary_advance DROP FOREIGN KEY FK_7085813F8C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE salary_advance DROP FOREIGN KEY FK_7085813F7F9BC654
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE salary_advance_repayment DROP FOREIGN KEY FK_2EA3D60F63CB948
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE salary_advance_repayment DROP FOREIGN KEY FK_2EA3D602FFD4FD3
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE document_request
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE employee_request
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE request_attachment
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE request_comment
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE salary_advance
        SQL);
        $this->addSql(<<<'SQL'
            DROP TABLE salary_advance_repayment
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_account DROP FOREIGN KEY FK_44BEAB93727ACA70
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry DROP FOREIGN KEY FK_62764D68478E8802
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry DROP FOREIGN KEY FK_62764D689B6B5FBA
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry DROP FOREIGN KEY FK_62764D68B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry DROP FOREIGN KEY FK_62764D68C69DE5E5
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry_line DROP FOREIGN KEY FK_4BBA65466A86E4FB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_journal_entry_line DROP FOREIGN KEY FK_4BBA65469B6B5FBA
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_tax_declaration DROP FOREIGN KEY FK_216E9EF2B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_tax_declaration DROP FOREIGN KEY FK_216E9EF279F7D87D
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE accounting_tax_declaration DROP FOREIGN KEY FK_216E9EF263F9139E
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contract DROP FOREIGN KEY FK_E98F28592ADD6D8C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contract DROP FOREIGN KEY FK_E98F2859166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contract DROP FOREIGN KEY FK_E98F28594E4DEF6F
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contract DROP FOREIGN KEY FK_E98F2859B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE contract DROP FOREIGN KEY FK_E98F2859D2EDD3FB
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE department DROP FOREIGN KEY FK_CD1DE18A7206964D
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE department_permission DROP FOREIGN KEY FK_72409E46A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE department_permission DROP FOREIGN KEY FK_72409E46AE80F5DF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee DROP FOREIGN KEY FK_5D9F75A1A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee DROP FOREIGN KEY FK_5D9F75A1AE80F5DF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee DROP FOREIGN KEY FK_5D9F75A1DD842E46
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee DROP FOREIGN KEY FK_5D9F75A1783E3463
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_document DROP FOREIGN KEY FK_4856DBB18C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_document DROP FOREIGN KEY FK_4856DBB1A2B28FE8
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_education DROP FOREIGN KEY FK_DE9A36908C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_leave DROP FOREIGN KEY FK_73BD8DA98C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_leave DROP FOREIGN KEY FK_73BD8DA92D234F6A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_performance DROP FOREIGN KEY FK_3F157FC8C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_performance DROP FOREIGN KEY FK_3F157FCFC6B21F1
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE employee_skill DROP FOREIGN KEY FK_B630E90E8C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense_item DROP FOREIGN KEY FK_ABBC6B7C8F758FBA
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense_report DROP FOREIGN KEY FK_280A69185636C81
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense_report DROP FOREIGN KEY FK_280A691C69DE5E5
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE expense_report DROP FOREIGN KEY FK_280A691B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE family_member DROP FOREIGN KEY FK_B9D4AD6D8C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE goods_receipt DROP FOREIGN KEY FK_B6CFD848A45D7E6A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE goods_receipt DROP FOREIGN KEY FK_B6CFD848B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE goods_receipt DROP FOREIGN KEY FK_B6CFD8486F8DDD17
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE goods_receipt_item DROP FOREIGN KEY FK_E1310FB85AF289C8
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE goods_receipt_item DROP FOREIGN KEY FK_E1310FB83207420A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE integration DROP FOREIGN KEY FK_FDE96D9BA76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice DROP FOREIGN KEY FK_906517442ADD6D8C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice DROP FOREIGN KEY FK_90651744166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice DROP FOREIGN KEY FK_90651744A45D7E6A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice DROP FOREIGN KEY FK_90651744B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice DROP FOREIGN KEY FK_90651744896DBBDE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice DROP FOREIGN KEY FK_906517442D234F6A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice DROP FOREIGN KEY FK_906517447F9BC654
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_approval DROP FOREIGN KEY FK_9C8CF732989F1FD
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_approval DROP FOREIGN KEY FK_9C8CF73A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_approval DROP FOREIGN KEY FK_9C8CF7335EF05C1
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_item DROP FOREIGN KEY FK_1DDE477B2989F1FD
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_item DROP FOREIGN KEY FK_1DDE477B4584665A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_item DROP FOREIGN KEY FK_1DDE477B3207420A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_payment DROP FOREIGN KEY FK_9FF1B2DE2989F1FD
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_payment DROP FOREIGN KEY FK_9FF1B2DEB03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE invoice_payment DROP FOREIGN KEY FK_9FF1B2DEF5E43AE9
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_document DROP FOREIGN KEY FK_A4F36721B88E2BB6
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_document DROP FOREIGN KEY FK_A4F36721B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_examination DROP FOREIGN KEY FK_5E236477B88E2BB6
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_examination DROP FOREIGN KEY FK_5E236477B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_record DROP FOREIGN KEY FK_F06A283E8C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_record DROP FOREIGN KEY FK_F06A283EBC594993
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_record DROP FOREIGN KEY FK_F06A283EB03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_record_history DROP FOREIGN KEY FK_DEBCDBE3B88E2BB6
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE medical_record_history DROP FOREIGN KEY FK_DEBCDBE3A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mission_order DROP FOREIGN KEY FK_65BFCCA5166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mission_order DROP FOREIGN KEY FK_65BFCCA58C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mission_order DROP FOREIGN KEY FK_65BFCCA5C69DE5E5
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mission_order DROP FOREIGN KEY FK_65BFCCA5AC7074D6
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE mission_order DROP FOREIGN KEY FK_65BFCCA5B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner DROP FOREIGN KEY FK_312B3E166BF700BD
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner DROP FOREIGN KEY FK_312B3E16C54C8C93
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner DROP FOREIGN KEY FK_312B3E163BCB2E4B
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner DROP FOREIGN KEY FK_312B3E16682B5931
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner DROP FOREIGN KEY FK_312B3E1684A5C6C7
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner DROP FOREIGN KEY FK_312B3E16D53503B3
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner_status_history DROP FOREIGN KEY FK_2EFD8EF59393F8FE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner_status_history DROP FOREIGN KEY FK_2EFD8EF52E43440C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE partner_status_history DROP FOREIGN KEY FK_2EFD8EF5596805D2
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE position DROP FOREIGN KEY FK_462CE4F5AE80F5DF
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE price_history DROP FOREIGN KEY FK_4C9CB8174584665A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE price_history DROP FOREIGN KEY FK_4C9CB8172ADD6D8C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE price_history DROP FOREIGN KEY FK_4C9CB817B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product DROP FOREIGN KEY FK_D34A04AD12469DE2
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product DROP FOREIGN KEY FK_D34A04ADB03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE product_category DROP FOREIGN KEY FK_CDFC7356727ACA70
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project DROP FOREIGN KEY FK_2FB3D0EE9393F8FE
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project DROP FOREIGN KEY FK_2FB3D0EED53503B3
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project DROP FOREIGN KEY FK_2FB3D0EE783E3463
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project DROP FOREIGN KEY FK_2FB3D0EEB03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_budget_line DROP FOREIGN KEY FK_631F415D166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_budget_sub_line DROP FOREIGN KEY FK_197C0F638FF83FA3
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_deliverable DROP FOREIGN KEY FK_69B253BA166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_deliverable DROP FOREIGN KEY FK_69B253BAF4BD7827
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_deliverable DROP FOREIGN KEY FK_69B253BA2D234F6A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_deliverable DROP FOREIGN KEY FK_69B253BAC33F7837
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_document DROP FOREIGN KEY FK_E52701AD166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_document DROP FOREIGN KEY FK_E52701ADA2B28FE8
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_member DROP FOREIGN KEY FK_67401132166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_member DROP FOREIGN KEY FK_67401132A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_metric DROP FOREIGN KEY FK_9072C5A9166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_metric DROP FOREIGN KEY FK_9072C5A9B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_resource DROP FOREIGN KEY FK_81DF7FCD166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_resource DROP FOREIGN KEY FK_81DF7FCDA76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_risk DROP FOREIGN KEY FK_40971D59166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_risk DROP FOREIGN KEY FK_40971D597E3C61F9
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_stock_allocation DROP FOREIGN KEY FK_4F8817EF166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_stock_allocation DROP FOREIGN KEY FK_4F8817EFBC942FD
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_stock_allocation DROP FOREIGN KEY FK_4F8817EF6802B588
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_task DROP FOREIGN KEY FK_6BEF133D166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_task DROP FOREIGN KEY FK_6BEF133DF4BD7827
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_task DROP FOREIGN KEY FK_6BEF133DFFFE75C0
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template DROP FOREIGN KEY FK_AA2E9458B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template_deliverable DROP FOREIGN KEY FK_8C67838A5DA0FB8
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template_deliverable DROP FOREIGN KEY FK_8C67838AB2AFCB23
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template_risk DROP FOREIGN KEY FK_7859682A5DA0FB8
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template_task DROP FOREIGN KEY FK_5321664E5DA0FB8
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE project_template_task DROP FOREIGN KEY FK_5321664E68C90015
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order DROP FOREIGN KEY FK_21E210B22ADD6D8C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order DROP FOREIGN KEY FK_21E210B2166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order DROP FOREIGN KEY FK_21E210B24E4DEF6F
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order DROP FOREIGN KEY FK_21E210B2B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order DROP FOREIGN KEY FK_21E210B2A45BB98C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order_item DROP FOREIGN KEY FK_5ED948C3A45D7E6A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order_item DROP FOREIGN KEY FK_5ED948C3384B2009
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_order_item DROP FOREIGN KEY FK_5ED948C34584665A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request DROP FOREIGN KEY FK_204D45E6166D1F9C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request DROP FOREIGN KEY FK_204D45E64DA1E751
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request DROP FOREIGN KEY FK_204D45E62D234F6A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request DROP FOREIGN KEY FK_204D45E68FF83FA3
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request DROP FOREIGN KEY FK_204D45E644A96FE9
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request DROP FOREIGN KEY FK_204D45E6B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request_approval DROP FOREIGN KEY FK_3201F52C4E4DEF6F
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request_approval DROP FOREIGN KEY FK_3201F52C2D234F6A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request_item DROP FOREIGN KEY FK_A4A6F5444E4DEF6F
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE purchase_request_item DROP FOREIGN KEY FK_A4A6F5444584665A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote DROP FOREIGN KEY FK_6B71CBF42ADD6D8C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote DROP FOREIGN KEY FK_6B71CBF44E4DEF6F
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote DROP FOREIGN KEY FK_6B71CBF4B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote DROP FOREIGN KEY FK_6B71CBF46F8DDD17
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote_item DROP FOREIGN KEY FK_8DFC7A94DB805178
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote_item DROP FOREIGN KEY FK_8DFC7A944584665A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE quote_item DROP FOREIGN KEY FK_8DFC7A94384B2009
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE role_permission DROP FOREIGN KEY FK_6F7DF886D60322AC
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE role_permission DROP FOREIGN KEY FK_6F7DF886FED90CCA
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE security_log DROP FOREIGN KEY FK_FE5C6A69A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE skill DROP FOREIGN KEY FK_5E3DE47712469DE2
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_alert DROP FOREIGN KEY FK_8BED5A30BC942FD
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_alert DROP FOREIGN KEY FK_8BED5A306713A32B
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory DROP FOREIGN KEY FK_29B0ACDEB03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory DROP FOREIGN KEY FK_29B0ACDE85ECDE76
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_line DROP FOREIGN KEY FK_3C51C2939EEA759
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_line DROP FOREIGN KEY FK_3C51C2934584665A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_line DROP FOREIGN KEY FK_3C51C29364D218E
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_line DROP FOREIGN KEY FK_3C51C293BC942FD
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_locations DROP FOREIGN KEY FK_95E18F4D9EEA759
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_inventory_locations DROP FOREIGN KEY FK_95E18F4DD98387BA
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_item DROP FOREIGN KEY FK_6017DDA4584665A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_item DROP FOREIGN KEY FK_6017DDA64D218E
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_location DROP FOREIGN KEY FK_1158DD89727ACA70
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_movement DROP FOREIGN KEY FK_BB1BC1B5BC942FD
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_movement DROP FOREIGN KEY FK_BB1BC1B5B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_movement DROP FOREIGN KEY FK_BB1BC1B53A32712E
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE stock_movement DROP FOREIGN KEY FK_BB1BC1B5237FCAB5
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE supplier_rating DROP FOREIGN KEY FK_3EBF4F62ADD6D8C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE supplier_rating DROP FOREIGN KEY FK_3EBF4F6A45D7E6A
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE supplier_rating DROP FOREIGN KEY FK_3EBF4F62576E0FD
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE supplier_rating DROP FOREIGN KEY FK_3EBF4F6B03A8386
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_session DROP FOREIGN KEY FK_D7A45DABEFD98D1
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_session DROP FOREIGN KEY FK_D7A45DAFB08EDF6
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_session_participant DROP FOREIGN KEY FK_889243C9DB8156B9
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_session_participant DROP FOREIGN KEY FK_889243C98C03F15C
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_skill DROP FOREIGN KEY FK_B1E76E1ABEFD98D1
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE training_skill DROP FOREIGN KEY FK_B1E76E1A5585C142
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_device DROP FOREIGN KEY FK_6C7DADB3A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_role DROP FOREIGN KEY FK_2DE8C6A3A76ED395
        SQL);
        $this->addSql(<<<'SQL'
            ALTER TABLE user_role DROP FOREIGN KEY FK_2DE8C6A3D60322AC
        SQL);
    }
}
