/**
 * Styles pour les indicateurs de fonctionnalités
 */

/* Badge pour les fonctionnalités en bêta */
.badge-beta {
    position: relative;
    display: inline-block;
}

.badge-beta::after {
    content: 'BETA';
    position: absolute;
    top: -8px;
    right: -20px;
    background-color: #ff9800;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
    transform: rotate(20deg);
    z-index: 1;
}

/* Badge pour les fonctionnalités à venir */
.badge-coming-soon {
    position: relative;
    display: inline-block;
}

.badge-coming-soon::after {
    content: 'À VENIR';
    position: absolute;
    top: -8px;
    right: -30px;
    background-color: #9c27b0;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
    transform: rotate(20deg);
    z-index: 1;
}

/* Badge pour les fonctionnalités nouvelles */
.badge-new {
    position: relative;
    display: inline-block;
}

.badge-new::after {
    content: 'NOUVEAU';
    position: absolute;
    top: -8px;
    right: -30px;
    background-color: #4caf50;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
    transform: rotate(20deg);
    z-index: 1;
}

/* Badge pour les fonctionnalités mises à jour */
.badge-updated {
    position: relative;
    display: inline-block;
}

.badge-updated::after {
    content: 'MIS À JOUR';
    position: absolute;
    top: -8px;
    right: -35px;
    background-color: #2196f3;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
    transform: rotate(20deg);
    z-index: 1;
}

/* Badge pour les fonctionnalités expérimentales */
.badge-experimental {
    position: relative;
    display: inline-block;
}

.badge-experimental::after {
    content: 'EXPÉRIMENTAL';
    position: absolute;
    top: -8px;
    right: -40px;
    background-color: #f44336;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
    transform: rotate(20deg);
    z-index: 1;
}

/* Tooltip pour les fonctionnalités */
.feature-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.feature-tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
}

.feature-tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

.feature-tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Bannière pour les fonctionnalités en développement */
.feature-banner {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 10px 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.feature-banner.beta {
    border-left-color: #ff9800;
}

.feature-banner.coming-soon {
    border-left-color: #9c27b0;
}

.feature-banner.new {
    border-left-color: #4caf50;
}

.feature-banner.updated {
    border-left-color: #2196f3;
}

.feature-banner.experimental {
    border-left-color: #f44336;
}

.feature-banner h5 {
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 16px;
}

.feature-banner p {
    margin-bottom: 0;
    font-size: 14px;
}

/* Indicateur de progression pour les fonctionnalités en développement */
.feature-progress {
    height: 5px;
    background-color: #e9ecef;
    border-radius: 3px;
    margin-top: 5px;
    margin-bottom: 10px;
    overflow: hidden;
}

.feature-progress-bar {
    height: 100%;
    border-radius: 3px;
    transition: width 0.6s ease;
}

.feature-progress-bar.beta {
    background-color: #ff9800;
}

.feature-progress-bar.coming-soon {
    background-color: #9c27b0;
}

.feature-progress-bar.new {
    background-color: #4caf50;
}

.feature-progress-bar.updated {
    background-color: #2196f3;
}

.feature-progress-bar.experimental {
    background-color: #f44336;
}

/* Styles pour les menus avec indicateurs */
.dropdown-item.beta::after {
    content: 'BETA';
    margin-left: 5px;
    background-color: #ff9800;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

.dropdown-item.coming-soon::after {
    content: 'À VENIR';
    margin-left: 5px;
    background-color: #9c27b0;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

.dropdown-item.new::after {
    content: 'NOUVEAU';
    margin-left: 5px;
    background-color: #4caf50;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

.dropdown-item.updated::after {
    content: 'MIS À JOUR';
    margin-left: 5px;
    background-color: #2196f3;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

.dropdown-item.experimental::after {
    content: 'EXPÉRIMENTAL';
    margin-left: 5px;
    background-color: #f44336;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

/* Styles pour les boutons avec indicateurs */
.btn.beta::after {
    content: 'BETA';
    margin-left: 5px;
    background-color: #ff9800;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

.btn.coming-soon::after {
    content: 'À VENIR';
    margin-left: 5px;
    background-color: #9c27b0;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

.btn.new::after {
    content: 'NOUVEAU';
    margin-left: 5px;
    background-color: #4caf50;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

.btn.updated::after {
    content: 'MIS À JOUR';
    margin-left: 5px;
    background-color: #2196f3;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

.btn.experimental::after {
    content: 'EXPÉRIMENTAL';
    margin-left: 5px;
    background-color: #f44336;
    color: white;
    font-size: 8px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}
