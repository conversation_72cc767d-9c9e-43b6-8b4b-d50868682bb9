/**
 * Styles pour l'optimisation mobile
 */

/* Styles généraux pour les appareils mobiles */
@media (max-width: 767.98px) {
    /* Réduire les marges et paddings */
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    /* Ajuster les titres */
    h1, .h1 {
        font-size: 1.75rem;
    }
    
    h2, .h2 {
        font-size: 1.5rem;
    }
    
    h3, .h3 {
        font-size: 1.25rem;
    }
    
    /* Ajuster les boutons */
    .btn {
        padding: 0.375rem 0.75rem;
    }
    
    /* Cacher le texte des boutons d'action, ne montrer que les icônes */
    .btn-sm .btn-text {
        display: none;
    }
    
    /* Ajuster les tableaux */
    .table {
        font-size: 0.875rem;
    }
    
    /* Rendre les tableaux scrollables horizontalement */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* Ajuster les formulaires */
    .form-label {
        font-size: 0.875rem;
    }
    
    .form-control {
        font-size: 0.875rem;
        padding: 0.25rem 0.5rem;
    }
    
    /* Ajuster les cartes */
    .card-header {
        padding: 0.75rem 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* Ajuster la navigation */
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .navbar-toggler {
        padding: 0.25rem 0.5rem;
        font-size: 1rem;
    }
    
    /* Ajuster les dropdown menus */
    .dropdown-menu {
        font-size: 0.875rem;
    }
    
    .dropdown-item {
        padding: 0.25rem 1rem;
    }
    
    /* Ajuster les modals */
    .modal-header {
        padding: 0.75rem 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-footer {
        padding: 0.75rem 1rem;
    }
    
    /* Ajuster les alertes */
    .alert {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
    
    /* Ajuster les badges */
    .badge {
        font-size: 0.75rem;
    }
    
    /* Ajuster les listes */
    .list-group-item {
        padding: 0.5rem 1rem;
    }
    
    /* Ajuster les paginations */
    .pagination {
        font-size: 0.875rem;
    }
    
    .page-link {
        padding: 0.25rem 0.5rem;
    }
    
    /* Ajuster les tabs */
    .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
    
    /* Ajuster les tooltips */
    .tooltip {
        font-size: 0.75rem;
    }
    
    /* Ajuster les popovers */
    .popover {
        font-size: 0.875rem;
    }
    
    /* Ajuster les toasts */
    .toast {
        font-size: 0.875rem;
    }
    
    /* Ajuster les breadcrumbs */
    .breadcrumb {
        font-size: 0.75rem;
        padding: 0.5rem 0;
    }
    
    /* Ajuster les progress bars */
    .progress {
        height: 0.5rem;
    }
}

/* Styles pour les très petits écrans (téléphones) */
@media (max-width: 575.98px) {
    /* Réduire encore plus les marges et paddings */
    .container {
        padding-left: 5px;
        padding-right: 5px;
    }
    
    /* Ajuster les titres */
    h1, .h1 {
        font-size: 1.5rem;
    }
    
    h2, .h2 {
        font-size: 1.25rem;
    }
    
    h3, .h3 {
        font-size: 1.125rem;
    }
    
    /* Ajuster les boutons */
    .btn-group > .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    /* Cacher certaines colonnes dans les tableaux */
    .table-sm-hide {
        display: none;
    }
    
    /* Ajuster les formulaires */
    .form-group {
        margin-bottom: 0.5rem;
    }
    
    /* Ajuster les cartes */
    .card-header {
        padding: 0.5rem 0.75rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    /* Ajuster les modals */
    .modal-header {
        padding: 0.5rem 0.75rem;
    }
    
    .modal-body {
        padding: 0.75rem;
    }
    
    .modal-footer {
        padding: 0.5rem 0.75rem;
    }
    
    /* Ajuster les alertes */
    .alert {
        padding: 0.5rem 0.75rem;
    }
    
    /* Ajuster les listes */
    .list-group-item {
        padding: 0.375rem 0.75rem;
    }
}

/* Styles pour l'orientation paysage sur mobile */
@media (max-width: 767.98px) and (orientation: landscape) {
    /* Ajuster la hauteur des modals */
    .modal-dialog {
        max-height: 85vh;
    }
    
    .modal-body {
        max-height: 60vh;
        overflow-y: auto;
    }
}

/* Styles pour les gestes tactiles */
@media (pointer: coarse) {
    /* Augmenter la taille des zones cliquables */
    .btn, .nav-link, .dropdown-item, .form-check-label, .page-link {
        min-height: 44px;
        min-width: 44px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Augmenter l'espacement des éléments de liste */
    .list-group-item {
        min-height: 44px;
    }
    
    /* Augmenter la taille des champs de formulaire */
    .form-control, .form-select {
        min-height: 44px;
    }
    
    /* Désactiver les effets de survol */
    .hover-effect {
        display: none;
    }
}
