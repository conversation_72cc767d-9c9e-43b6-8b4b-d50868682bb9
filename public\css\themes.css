/* Thèmes pour l'application */

/* Variables pour le thème clair (par défaut) */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --body-bg: #f8f9fa;
    --body-color: #212529;
    
    --card-bg: #ffffff;
    --card-border: rgba(0, 0, 0, 0.125);
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    
    --input-bg: #ffffff;
    --input-color: #495057;
    --input-border: #ced4da;
    --input-focus-border: #80bdff;
    --input-focus-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    
    --table-bg: #ffffff;
    --table-border: #dee2e6;
    --table-striped-bg: rgba(0, 0, 0, 0.05);
    --table-hover-bg: rgba(0, 0, 0, 0.075);
    
    --navbar-bg: #343a40;
    --navbar-color: #ffffff;
    --navbar-hover-color: rgba(255, 255, 255, 0.75);
    
    --sidebar-bg: #343a40;
    --sidebar-color: #ffffff;
    --sidebar-hover-bg: rgba(255, 255, 255, 0.1);
    --sidebar-active-bg: rgba(255, 255, 255, 0.2);
    
    --modal-bg: #ffffff;
    --modal-border: rgba(0, 0, 0, 0.2);
    
    --dropdown-bg: #ffffff;
    --dropdown-color: #212529;
    --dropdown-border: rgba(0, 0, 0, 0.15);
    --dropdown-hover-bg: #f8f9fa;
    
    --transition-speed: 0.3s;
}

/* Variables pour le thème sombre */
[data-theme="dark"] {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    
    --body-bg: #121212;
    --body-color: #e0e0e0;
    
    --card-bg: #1e1e1e;
    --card-border: rgba(255, 255, 255, 0.125);
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
    
    --input-bg: #2c2c2c;
    --input-color: #e0e0e0;
    --input-border: #444444;
    --input-focus-border: #0d6efd;
    --input-focus-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    
    --table-bg: #1e1e1e;
    --table-border: #444444;
    --table-striped-bg: rgba(255, 255, 255, 0.05);
    --table-hover-bg: rgba(255, 255, 255, 0.075);
    
    --navbar-bg: #1e1e1e;
    --navbar-color: #e0e0e0;
    --navbar-hover-color: rgba(255, 255, 255, 0.9);
    
    --sidebar-bg: #1e1e1e;
    --sidebar-color: #e0e0e0;
    --sidebar-hover-bg: rgba(255, 255, 255, 0.1);
    --sidebar-active-bg: rgba(255, 255, 255, 0.15);
    
    --modal-bg: #2c2c2c;
    --modal-border: rgba(255, 255, 255, 0.2);
    
    --dropdown-bg: #2c2c2c;
    --dropdown-color: #e0e0e0;
    --dropdown-border: rgba(255, 255, 255, 0.15);
    --dropdown-hover-bg: #3c3c3c;
}

/* Application des variables CSS aux éléments */
body {
    background-color: var(--body-bg);
    color: var(--body-color);
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

.card {
    background-color: var(--card-bg);
    border-color: var(--card-border);
    box-shadow: var(--card-shadow);
    transition: background-color var(--transition-speed), border-color var(--transition-speed), box-shadow var(--transition-speed);
}

.card-header, .card-footer {
    background-color: rgba(0, 0, 0, 0.03);
    border-color: var(--card-border);
}

[data-theme="dark"] .card-header, [data-theme="dark"] .card-footer {
    background-color: rgba(255, 255, 255, 0.03);
}

.form-control, .form-select {
    background-color: var(--input-bg);
    color: var(--input-color);
    border-color: var(--input-border);
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}

.form-control:focus, .form-select:focus {
    background-color: var(--input-bg);
    color: var(--input-color);
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
}

.table {
    color: var(--body-color);
    border-color: var(--table-border);
    transition: color var(--transition-speed), border-color var(--transition-speed);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--table-striped-bg);
    transition: background-color var(--transition-speed);
}

.table-hover tbody tr:hover {
    background-color: var(--table-hover-bg);
    transition: background-color var(--transition-speed);
}

.navbar {
    background-color: var(--navbar-bg);
    transition: background-color var(--transition-speed);
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-color);
    transition: color var(--transition-speed);
}

.navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus {
    color: var(--navbar-hover-color);
}

.sidebar {
    background-color: var(--sidebar-bg);
    color: var(--sidebar-color);
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

.sidebar .nav-link {
    color: var(--sidebar-color);
    transition: color var(--transition-speed);
}

.sidebar .nav-link:hover {
    background-color: var(--sidebar-hover-bg);
    transition: background-color var(--transition-speed);
}

.sidebar .nav-link.active {
    background-color: var(--sidebar-active-bg);
    transition: background-color var(--transition-speed);
}

.modal-content {
    background-color: var(--modal-bg);
    border-color: var(--modal-border);
    transition: background-color var(--transition-speed), border-color var(--transition-speed);
}

.dropdown-menu {
    background-color: var(--dropdown-bg);
    color: var(--dropdown-color);
    border-color: var(--dropdown-border);
    transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}

.dropdown-item {
    color: var(--dropdown-color);
    transition: color var(--transition-speed);
}

.dropdown-item:hover, .dropdown-item:focus {
    background-color: var(--dropdown-hover-bg);
    transition: background-color var(--transition-speed);
}

/* Animations et transitions */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-in {
    animation: slideIn 0.5s ease-in-out;
}

.scale-in {
    animation: scaleIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Bouton de basculement du thème */
.theme-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    transition: background-color 0.3s, transform 0.3s;
}

.theme-toggle:hover {
    transform: scale(1.1);
}

.theme-toggle i {
    font-size: 1.5rem;
}

/* Styles spécifiques pour les appareils mobiles */
@media (max-width: 768px) {
    .theme-toggle {
        width: 40px;
        height: 40px;
        bottom: 10px;
        right: 10px;
    }
    
    .theme-toggle i {
        font-size: 1.2rem;
    }
}
