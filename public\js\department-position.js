document.addEventListener('DOMContentLoaded', function() {
    // Fonction pour mettre à jour les postes en fonction du département sélectionné
    function updatePositions() {
        const departmentSelect = document.querySelector('.department-select');
        const positionSelect = document.querySelector('.position-select');
        
        if (!departmentSelect || !positionSelect) {
            return;
        }
        
        const departmentId = departmentSelect.value;
        
        if (!departmentId) {
            // Si aucun département n'est sélectionné, vider la liste des postes
            positionSelect.innerHTML = '<option value="">Sélectionnez d\'abord un département</option>';
            positionSelect.disabled = true;
            return;
        }
        
        // Activer le select des postes
        positionSelect.disabled = true;
        
        // Afficher un message de chargement
        positionSelect.innerHTML = '<option value="">Chargement...</option>';
        
        // Faire une requête AJAX pour récupérer les postes du département
        fetch(`/hr/position/by-department/${departmentId}`)
            .then(response => response.json())
            .then(data => {
                // Vider la liste des postes
                positionSelect.innerHTML = '<option value="">Sélectionnez un poste</option>';
                
                // Ajouter les postes à la liste
                data.positions.forEach(position => {
                    const option = document.createElement('option');
                    option.value = position.id;
                    option.textContent = position.title;
                    positionSelect.appendChild(option);
                });
                
                // Activer le select des postes
                positionSelect.disabled = false;
            })
            .catch(error => {
                console.error('Erreur lors de la récupération des postes:', error);
                positionSelect.innerHTML = '<option value="">Erreur lors du chargement des postes</option>';
            });
    }
    
    // Écouter les changements sur le select des départements
    const departmentSelect = document.querySelector('.department-select');
    if (departmentSelect) {
        departmentSelect.addEventListener('change', updatePositions);
        
        // Mettre à jour les postes au chargement de la page
        updatePositions();
    }
});
