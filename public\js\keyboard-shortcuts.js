/**
 * Gestionnaire de raccourcis clavier pour l'application
 */
class KeyboardShortcutsManager {
    constructor() {
        this.shortcuts = [
            { key: 'h', alt: true, description: 'Aller à l\'accueil', action: () => this.navigateTo('/') },
            { key: 'd', alt: true, description: 'Aller au tableau de bord', action: () => this.navigateTo('/dashboard') },
            { key: 'p', alt: true, description: 'Aller aux partenaires', action: () => this.navigateTo('/partner') },
            { key: 'j', alt: true, description: 'Aller aux projets', action: () => this.navigateTo('/project/dashboard') },
            { key: 't', alt: true, description: 'Aller aux tâches', action: () => this.navigateTo('/task') },
            { key: 'n', alt: true, description: 'Aller aux notifications', action: () => this.navigateTo('/notification') },
            { key: 'r', alt: true, description: 'Aller aux ressources humaines', action: () => this.navigateTo('/employee/dashboard') },
            { key: 'm', alt: true, description: 'Aller aux missions', action: () => this.navigateTo('/mission-order') },
            { key: 'a', alt: true, description: 'Aller aux achats', action: () => this.navigateTo('/purchasing/dashboard') },
            { key: 's', alt: true, description: 'Aller aux stocks', action: () => this.navigateTo('/stock/dashboard') },
            { key: 'f', alt: true, description: 'Aller aux finances', action: () => this.navigateTo('/financial/dashboard') },
            { key: 'c', alt: true, description: 'Aller à la comptabilité', action: () => this.navigateTo('/accounting/dashboard') },
            { key: 'u', alt: true, description: 'Aller à l\'administration', action: () => this.navigateTo('/admin/dashboard') },
            { key: '/', alt: false, description: 'Afficher l\'aide des raccourcis', action: () => this.showHelp() },
            { key: 'Escape', alt: false, description: 'Fermer les modales', action: () => this.closeModals() },
            { key: 'n', alt: true, ctrl: true, description: 'Créer un nouvel élément', action: () => this.createNew() },
            { key: 's', alt: true, ctrl: true, description: 'Sauvegarder', action: () => this.save() },
            { key: 'f', alt: false, ctrl: true, description: 'Rechercher', action: () => this.focusSearch() },
        ];
        
        this.initShortcuts();
        this.createHelpModal();
    }
    
    /**
     * Initialise les raccourcis clavier
     */
    initShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ne pas traiter les raccourcis si l'utilisateur est en train de saisir du texte
            if (this.isUserTyping()) {
                return;
            }
            
            // Parcourir tous les raccourcis
            for (const shortcut of this.shortcuts) {
                if (event.key === shortcut.key && 
                    (!shortcut.alt || event.altKey) && 
                    (!shortcut.ctrl || event.ctrlKey)) {
                    
                    event.preventDefault();
                    shortcut.action();
                    break;
                }
            }
        });
    }
    
    /**
     * Vérifie si l'utilisateur est en train de saisir du texte
     */
    isUserTyping() {
        const activeElement = document.activeElement;
        const tagName = activeElement.tagName.toLowerCase();
        const type = activeElement.type ? activeElement.type.toLowerCase() : '';
        
        return (
            tagName === 'input' && 
            (type === 'text' || type === 'password' || type === 'email' || type === 'number' || type === 'search' || type === 'tel' || type === 'url') ||
            tagName === 'textarea' ||
            activeElement.isContentEditable
        );
    }
    
    /**
     * Navigue vers une URL
     */
    navigateTo(url) {
        window.location.href = url;
    }
    
    /**
     * Affiche l'aide des raccourcis clavier
     */
    showHelp() {
        const helpModal = document.getElementById('keyboardShortcutsModal');
        if (helpModal) {
            const modal = new bootstrap.Modal(helpModal);
            modal.show();
        }
    }
    
    /**
     * Ferme toutes les modales ouvertes
     */
    closeModals() {
        const openModals = document.querySelectorAll('.modal.show');
        openModals.forEach(modalElement => {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        });
    }
    
    /**
     * Tente de créer un nouvel élément en fonction de la page actuelle
     */
    createNew() {
        // Rechercher un bouton "Nouveau" ou "Ajouter" sur la page
        const newButtons = Array.from(document.querySelectorAll('a.btn, button.btn')).filter(btn => {
            const text = btn.textContent.toLowerCase();
            return text.includes('nouveau') || text.includes('nouvel') || text.includes('nouvelle') || text.includes('ajouter') || text.includes('créer');
        });
        
        if (newButtons.length > 0) {
            // Cliquer sur le premier bouton trouvé
            newButtons[0].click();
        }
    }
    
    /**
     * Tente de sauvegarder le formulaire actuel
     */
    save() {
        // Rechercher un bouton de soumission dans le formulaire actif
        const form = document.querySelector('form');
        if (form) {
            const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitButton) {
                submitButton.click();
            }
        }
    }
    
    /**
     * Met le focus sur le champ de recherche
     */
    focusSearch() {
        const searchInput = document.querySelector('input[type="search"], input[name*="search"], input[name*="keyword"], input[placeholder*="recherche"], input[placeholder*="Recherche"]');
        if (searchInput) {
            searchInput.focus();
        }
    }
    
    /**
     * Crée la modale d'aide des raccourcis clavier
     */
    createHelpModal() {
        // Vérifier si la modale existe déjà
        if (document.getElementById('keyboardShortcutsModal')) {
            return;
        }
        
        // Créer la modale
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'keyboardShortcutsModal';
        modal.tabIndex = -1;
        modal.setAttribute('aria-labelledby', 'keyboardShortcutsModalLabel');
        modal.setAttribute('aria-hidden', 'true');
        
        // Contenu de la modale
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="keyboardShortcutsModalLabel">Raccourcis clavier</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Navigation</h6>
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Raccourci</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${this.shortcuts.filter(s => !s.ctrl).map(shortcut => `
                                            <tr>
                                                <td><kbd>${shortcut.alt ? 'Alt' : ''}${shortcut.alt ? ' + ' : ''}${shortcut.key.toUpperCase()}</kbd></td>
                                                <td>${shortcut.description}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6>Actions</h6>
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Raccourci</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${this.shortcuts.filter(s => s.ctrl).map(shortcut => `
                                            <tr>
                                                <td><kbd>${shortcut.ctrl ? 'Ctrl' : ''}${shortcut.ctrl && shortcut.alt ? ' + ' : ''}${shortcut.alt ? 'Alt' : ''}${shortcut.alt || shortcut.ctrl ? ' + ' : ''}${shortcut.key.toUpperCase()}</kbd></td>
                                                <td>${shortcut.description}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle"></i> Appuyez sur <kbd>/</kbd> à tout moment pour afficher cette aide.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    </div>
                </div>
            </div>
        `;
        
        // Ajouter la modale au document
        document.body.appendChild(modal);
    }
}

// Initialiser le gestionnaire de raccourcis clavier au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    window.keyboardShortcutsManager = new KeyboardShortcutsManager();
});
