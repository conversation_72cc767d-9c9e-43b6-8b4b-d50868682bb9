/**
 * JavaScript pour la gestion des éléments de bon de commande
 */
document.addEventListener('DOMContentLoaded', function() {
    // Fonction pour initialiser les écouteurs d'événements sur les sélecteurs de produits
    function initProductSelectors() {
        document.querySelectorAll('.product-select').forEach(function(select) {
            select.addEventListener('change', function() {
                // Si le produit est sélectionné, soumettre le formulaire pour déclencher l'événement POST_SUBMIT
                if (this.value) {
                    // Trouver le formulaire parent
                    const form = this.closest('form');
                    if (form) {
                        // Créer un élément input hidden pour indiquer que c'est une soumission AJAX
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'ajax_submit';
                        input.value = '1';
                        form.appendChild(input);
                        
                        // Soumettre le formulaire via AJAX
                        const formData = new FormData(form);
                        fetch(form.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Mettre à jour les champs avec les données du produit
                                const itemContainer = this.closest('.purchase-order-item');
                                if (itemContainer) {
                                    const descriptionInput = itemContainer.querySelector('[name$="[description]"]');
                                    const unitInput = itemContainer.querySelector('[name$="[unit]"]');
                                    const referenceInput = itemContainer.querySelector('[name$="[reference]"]');
                                    const unitPriceInput = itemContainer.querySelector('[name$="[unitPrice]"]');
                                    
                                    if (descriptionInput) descriptionInput.value = data.product.name;
                                    if (unitInput) unitInput.value = data.product.unit;
                                    if (referenceInput) referenceInput.value = data.product.code;
                                    if (unitPriceInput) unitPriceInput.value = data.product.price;
                                }
                            }
                        })
                        .catch(error => console.error('Error:', error))
                        .finally(() => {
                            // Supprimer l'input hidden
                            form.removeChild(input);
                        });
                    }
                }
            });
        });
    }
    
    // Initialiser les sélecteurs de produits existants
    initProductSelectors();
    
    // Réinitialiser les sélecteurs de produits lorsque de nouveaux éléments sont ajoutés
    document.addEventListener('purchase-order-item-added', function() {
        initProductSelectors();
    });
});
