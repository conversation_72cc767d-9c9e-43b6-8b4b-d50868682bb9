/**
 * Gestionnaire de thèmes pour l'application
 */
class ThemeSwitcher {
    constructor() {
        this.themeKey = 'app_theme';
        this.defaultTheme = 'light';
        this.currentTheme = this.loadTheme();
        
        this.initTheme();
        this.initToggleButton();
        this.initKeyboardShortcuts();
    }
    
    /**
     * Initialise le thème au chargement de la page
     */
    initTheme() {
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        this.updateToggleIcon();
    }
    
    /**
     * Initialise le bouton de basculement du thème
     */
    initToggleButton() {
        // Créer le bouton s'il n'existe pas déjà
        if (!document.querySelector('.theme-toggle')) {
            const toggleButton = document.createElement('div');
            toggleButton.className = 'theme-toggle';
            toggleButton.innerHTML = '<i class="fas fa-moon"></i>';
            toggleButton.setAttribute('title', 'Changer de thème');
            toggleButton.setAttribute('aria-label', 'Changer de thème');
            
            toggleButton.addEventListener('click', () => this.toggleTheme());
            
            document.body.appendChild(toggleButton);
        }
    }
    
    /**
     * Initialise les raccourcis clavier
     */
    initKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Alt+T pour basculer le thème
            if (event.altKey && event.key === 't') {
                this.toggleTheme();
                event.preventDefault();
            }
        });
    }
    
    /**
     * Charge le thème depuis le stockage local
     */
    loadTheme() {
        return localStorage.getItem(this.themeKey) || this.defaultTheme;
    }
    
    /**
     * Sauvegarde le thème dans le stockage local
     */
    saveTheme(theme) {
        localStorage.setItem(this.themeKey, theme);
    }
    
    /**
     * Bascule entre les thèmes clair et sombre
     */
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        this.saveTheme(this.currentTheme);
        this.updateToggleIcon();
        
        // Déclencher un événement personnalisé pour informer d'autres scripts
        const event = new CustomEvent('themeChanged', { detail: { theme: this.currentTheme } });
        document.dispatchEvent(event);
        
        // Ajouter une animation lors du changement de thème
        this.animateThemeChange();
    }
    
    /**
     * Met à jour l'icône du bouton de basculement
     */
    updateToggleIcon() {
        const toggleButton = document.querySelector('.theme-toggle');
        if (toggleButton) {
            toggleButton.innerHTML = this.currentTheme === 'light' 
                ? '<i class="fas fa-moon"></i>' 
                : '<i class="fas fa-sun"></i>';
        }
    }
    
    /**
     * Ajoute une animation lors du changement de thème
     */
    animateThemeChange() {
        const overlay = document.createElement('div');
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = this.currentTheme === 'light' ? '#ffffff' : '#121212';
        overlay.style.opacity = '0';
        overlay.style.zIndex = '9999';
        overlay.style.pointerEvents = 'none';
        overlay.style.transition = 'opacity 0.3s ease-in-out';
        
        document.body.appendChild(overlay);
        
        // Déclencher l'animation
        setTimeout(() => {
            overlay.style.opacity = '0.2';
            
            setTimeout(() => {
                overlay.style.opacity = '0';
                
                setTimeout(() => {
                    document.body.removeChild(overlay);
                }, 300);
            }, 300);
        }, 0);
    }
}

// Initialiser le gestionnaire de thèmes au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    window.themeSwitcher = new ThemeSwitcher();
});
