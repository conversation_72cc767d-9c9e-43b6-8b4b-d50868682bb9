#!/bin/bash

# Script d'installation des dépendances pour le projet SI

# Couleurs pour les messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Fonction pour afficher un message d'information
info() {
    echo -e "${YELLOW}INFO: $1${NC}"
}

# Fonction pour afficher un message de succès
success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

# Fonction pour afficher un message d'erreur
error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# Vérifier si Composer est installé
if ! command -v composer &> /dev/null; then
    error "Composer n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
fi

# Vérifier si PHP est installé
if ! command -v php &> /dev/null; then
    error "PHP n'est pas installé. Veuillez l'installer avant de continuer."
    exit 1
fi

# Vérifier la version de PHP
PHP_VERSION=$(php -r "echo PHP_VERSION;")
info "Version de PHP: $PHP_VERSION"

# Augmenter la limite de mémoire PHP pour Composer
export COMPOSER_MEMORY_LIMIT=-1

# Installer les dépendances pour l'authentification à deux facteurs
info "Installation des dépendances pour l'authentification à deux facteurs..."
composer require scheb/2fa-bundle scheb/2fa-totp scheb/2fa-email

# Installer les dépendances pour les intégrations externes
info "Installation des dépendances pour les intégrations externes..."
composer require symfony/http-client nyholm/psr7 symfony/google-mailer symfony/mailer

# Installer les dépendances pour l'internationalisation
info "Installation des dépendances pour l'internationalisation..."
composer require symfony/translation

# Installer les dépendances pour l'API
info "Installation des dépendances pour l'API..."
composer require api-platform/core

# Installer les dépendances pour le cache
info "Installation des dépendances pour le cache..."
composer require symfony/cache

# Installer les dépendances pour les tests
info "Installation des dépendances pour les tests..."
composer require --dev symfony/test-pack

# Exécuter les migrations de base de données
info "Exécution des migrations de base de données..."
php bin/console doctrine:migrations:migrate --no-interaction

# Vider le cache
info "Vidage du cache..."
php bin/console cache:clear

success "Installation des dépendances terminée avec succès!"
success "Vous pouvez maintenant démarrer le serveur avec la commande: php bin/console server:start"
