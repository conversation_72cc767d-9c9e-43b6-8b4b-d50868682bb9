<?php

// Script pour supprimer les départements en double
// À exécuter avec : php scripts/remove_duplicate_departments.php

require dirname(__DIR__).'/vendor/autoload.php';
require dirname(__DIR__).'/config/bootstrap.php';

use App\Entity\Department;
use App\Entity\Employee;
use App\Entity\Position;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

$kernel = new \App\Kernel($_SERVER['APP_ENV'], (bool) $_SERVER['APP_DEBUG']);
$kernel->boot();
$container = $kernel->getContainer();

/** @var EntityManagerInterface $entityManager */
$entityManager = $container->get('doctrine.orm.entity_manager');

// Récupérer tous les départements
$departmentRepository = $entityManager->getRepository(Department::class);
$departments = $departmentRepository->findAll();

// Grouper les départements par code
$departmentsByCode = [];
foreach ($departments as $department) {
    $code = $department->getCode();
    if (!isset($departmentsByCode[$code])) {
        $departmentsByCode[$code] = [];
    }
    $departmentsByCode[$code][] = $department;
}

// Traiter chaque groupe de départements ayant le même code
foreach ($departmentsByCode as $code => $deptGroup) {
    // S'il n'y a qu'un seul département avec ce code, passer au suivant
    if (count($deptGroup) <= 1) {
        continue;
    }

    echo "Traitement des départements avec le code: $code\n";
    
    // Trouver le département à conserver (celui avec le plus d'employés et de positions)
    $departmentToKeep = null;
    $maxScore = -1;
    
    foreach ($deptGroup as $dept) {
        // Compter les employés
        $employeeCount = $entityManager->createQueryBuilder()
            ->select('COUNT(e.id)')
            ->from(Employee::class, 'e')
            ->where('e.department = :dept')
            ->setParameter('dept', $dept)
            ->getQuery()
            ->getSingleScalarResult();
        
        // Compter les positions
        $positionCount = $entityManager->createQueryBuilder()
            ->select('COUNT(p.id)')
            ->from(Position::class, 'p')
            ->where('p.department = :dept')
            ->setParameter('dept', $dept)
            ->getQuery()
            ->getSingleScalarResult();
        
        // Compter les sous-départements
        $childCount = count($dept->getChildDepartments());
        
        // Calculer un score pour ce département
        $score = $employeeCount * 10 + $positionCount * 5 + $childCount * 2;
        
        echo "  - Département ID {$dept->getId()} ({$dept->getName()}): $employeeCount employés, $positionCount positions, $childCount sous-départements, score: $score\n";
        
        // Si ce département a un meilleur score, le conserver
        if ($score > $maxScore) {
            $maxScore = $score;
            $departmentToKeep = $dept;
        }
    }
    
    if (!$departmentToKeep) {
        echo "  Aucun département à conserver pour le code $code\n";
        continue;
    }
    
    echo "  Département à conserver: ID {$departmentToKeep->getId()} ({$departmentToKeep->getName()})\n";
    
    // Traiter chaque département à supprimer
    foreach ($deptGroup as $dept) {
        if ($dept->getId() === $departmentToKeep->getId()) {
            continue; // Ne pas traiter le département à conserver
        }
        
        echo "  Traitement du département à supprimer: ID {$dept->getId()} ({$dept->getName()})\n";
        
        // Mettre à jour les employés
        $employees = $entityManager->createQueryBuilder()
            ->select('e')
            ->from(Employee::class, 'e')
            ->where('e.department = :dept')
            ->setParameter('dept', $dept)
            ->getQuery()
            ->getResult();
        
        foreach ($employees as $employee) {
            echo "    - Mise à jour de l'employé ID {$employee->getId()}\n";
            $employee->setDepartment($departmentToKeep);
            $entityManager->persist($employee);
        }
        
        // Mettre à jour les positions
        $positions = $entityManager->createQueryBuilder()
            ->select('p')
            ->from(Position::class, 'p')
            ->where('p.department = :dept')
            ->setParameter('dept', $dept)
            ->getQuery()
            ->getResult();
        
        foreach ($positions as $position) {
            echo "    - Mise à jour de la position ID {$position->getId()}\n";
            $position->setDepartment($departmentToKeep);
            $entityManager->persist($position);
        }
        
        // Mettre à jour les sous-départements
        $childDepartments = $dept->getChildDepartments();
        foreach ($childDepartments as $child) {
            echo "    - Mise à jour du sous-département ID {$child->getId()}\n";
            $child->setParentDepartment($departmentToKeep);
            $entityManager->persist($child);
        }
        
        // Appliquer les modifications avant de supprimer le département
        $entityManager->flush();
        
        // Supprimer le département
        try {
            $entityManager->remove($dept);
            $entityManager->flush();
            echo "    Département ID {$dept->getId()} supprimé avec succès\n";
        } catch (\Exception $e) {
            echo "    Erreur lors de la suppression du département ID {$dept->getId()}: " . $e->getMessage() . "\n";
        }
    }
}

echo "Opération terminée\n";
