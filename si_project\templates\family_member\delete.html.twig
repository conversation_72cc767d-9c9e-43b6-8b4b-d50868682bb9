{% extends 'base.html.twig' %}

{% block title %}Supprimer le membre de famille - CRM System{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Supprimer le membre de famille</h1>
        <a href="{{ path('app_family_member_show', {'id': family_member.id}) }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Retour aux détails
        </a>
    </div>

    <div class="card">
        <div class="card-header bg-danger text-white">
            <h5 class="card-title mb-0">Confirmation de suppression</h5>
        </div>
        <div class="card-body">
            <p class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> Attention : Cette action est irréversible. Toutes les données associées à ce membre de famille seront supprimées.
            </p>

            <div class="mb-4">
                <h5>Informations sur le membre de famille :</h5>
                <ul>
                    <li><strong>Nom :</strong> {{ family_member.fullName }}</li>
                    <li><strong>Relation :</strong> {{ family_member.relationshipLabel }}</li>
                    <li><strong>Employé :</strong> {{ family_member.employee.user.fullName }}</li>
                    <li><strong>À charge :</strong> {{ family_member.isDependent ? 'Oui' : 'Non' }}</li>
                </ul>
            </div>

            <div class="mb-4">
                <h5>Données qui seront supprimées :</h5>
                <ul>
                    <li>Informations personnelles du membre de famille</li>
                    <li>{{ family_member.medicalRecords|length }} dossier(s) médical(aux) associé(s)</li>
                </ul>
            </div>

            <form method="post">
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ path('app_family_member_show', {'id': family_member.id}) }}" class="btn btn-secondary me-md-2">
                        <i class="bi bi-x-circle"></i> Annuler
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> Confirmer la suppression
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
