{% extends 'base.html.twig' %}

{% block title %}Modifier le membre de famille - CRM System{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Modifier le membre de famille</h1>
        <div>
            <a href="{{ path('app_family_member_index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Retour à la liste
            </a>
            <a href="{{ path('app_family_member_show', {'id': family_member.id}) }}" class="btn btn-info">
                <i class="bi bi-eye"></i> Voir les détails
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Formulaire de modification</h5>
        </div>
        <div class="card-body">
            {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate'}}) }}
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.firstName) }}
                            {{ form_widget(form.firstName, {'attr': {'class': 'form-control'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.firstName) }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.lastName) }}
                            {{ form_widget(form.lastName, {'attr': {'class': 'form-control'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.lastName) }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.relationship) }}
                            {{ form_widget(form.relationship, {'attr': {'class': 'form-select'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.relationship) }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.gender) }}
                            {{ form_widget(form.gender, {'attr': {'class': 'form-select'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.gender) }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.birthDate) }}
                            {{ form_widget(form.birthDate, {'attr': {'class': 'form-control'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.birthDate) }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3 form-check mt-4">
                            {{ form_widget(form.isDependent, {'attr': {'class': 'form-check-input'}}) }}
                            {{ form_label(form.isDependent, null, {'label_attr': {'class': 'form-check-label'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.isDependent) }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.email) }}
                            {{ form_widget(form.email, {'attr': {'class': 'form-control'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.email) }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.phone) }}
                            {{ form_widget(form.phone, {'attr': {'class': 'form-control'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.phone) }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    {{ form_label(form.notes) }}
                    {{ form_widget(form.notes, {'attr': {'class': 'form-control'}}) }}
                    <div class="invalid-feedback">
                        {{ form_errors(form.notes) }}
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Enregistrer les modifications
                    </button>
                </div>
            {{ form_end(form) }}
        </div>
    </div>
</div>
{% endblock %}
