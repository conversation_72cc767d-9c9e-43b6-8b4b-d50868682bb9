{% extends 'base.html.twig' %}

{% block title %}Membres de famille - CRM System{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Membres de famille</h1>
        <div>
            <a href="{{ path('app_family_member_new') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nouveau membre de famille
            </a>
            <a href="{{ path('app_family_member_dependents') }}" class="btn btn-info">
                <i class="bi bi-people"></i> Personnes à charge
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Liste des membres de famille</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Relation</th>
                            <th>Employé</th>
                            <th>Date de naissance</th>
                            <th>À charge</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for family_member in family_members %}
                            <tr>
                                <td>{{ family_member.fullName }}</td>
                                <td>{{ family_member.relationshipLabel }}</td>
                                <td>
                                    <a href="{{ path('app_employee_show', {'id': family_member.employee.id}) }}">
                                        {{ family_member.employee.user.fullName }}
                                    </a>
                                </td>
                                <td>{{ family_member.birthDate ? family_member.birthDate|date('d/m/Y') : 'Non spécifié' }}</td>
                                <td>
                                    {% if family_member.isDependent %}
                                        <span class="badge bg-success">Oui</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Non</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ path('app_family_member_show', {'id': family_member.id}) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{{ path('app_family_member_edit', {'id': family_member.id}) }}" class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{{ path('app_medical_record_new_for_family_member', {'id': family_member.id}) }}" class="btn btn-sm btn-success">
                                            <i class="bi bi-file-earmark-medical"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center">Aucun membre de famille trouvé</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
