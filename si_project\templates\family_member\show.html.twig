{% extends 'base.html.twig' %}

{% block title %}{{ family_member.fullName }} - CRM System{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ family_member.fullName }}</h1>
        <div>
            <a href="{{ path('app_family_member_index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Retour à la liste
            </a>
            <a href="{{ path('app_family_member_edit', {'id': family_member.id}) }}" class="btn btn-primary">
                <i class="bi bi-pencil"></i> Modifier
            </a>
            <a href="{{ path('app_medical_record_new_for_family_member', {'id': family_member.id}) }}" class="btn btn-success">
                <i class="bi bi-file-earmark-medical"></i> Nouveau dossier médical
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Informations personnelles</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th>Nom complet</th>
                            <td>{{ family_member.fullName }}</td>
                        </tr>
                        <tr>
                            <th>Relation</th>
                            <td>{{ family_member.relationshipLabel }}</td>
                        </tr>
                        <tr>
                            <th>Employé</th>
                            <td>
                                <a href="{{ path('app_employee_show', {'id': family_member.employee.id}) }}">
                                    {{ family_member.employee.user.fullName }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>Date de naissance</th>
                            <td>{{ family_member.birthDate ? family_member.birthDate|date('d/m/Y') : 'Non spécifié' }}</td>
                        </tr>
                        <tr>
                            <th>Âge</th>
                            <td>{{ family_member.age ? family_member.age ~ ' ans' : 'Non spécifié' }}</td>
                        </tr>
                        <tr>
                            <th>Genre</th>
                            <td>{{ family_member.genderLabel }}</td>
                        </tr>
                        <tr>
                            <th>À charge</th>
                            <td>
                                {% if family_member.isDependent %}
                                    <span class="badge bg-success">Oui</span>
                                {% else %}
                                    <span class="badge bg-secondary">Non</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Coordonnées</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th>Email</th>
                            <td>{{ family_member.email ?: 'Non spécifié' }}</td>
                        </tr>
                        <tr>
                            <th>Téléphone</th>
                            <td>{{ family_member.phone ?: 'Non spécifié' }}</td>
                        </tr>
                    </table>

                    {% if family_member.notes %}
                        <div class="mt-3">
                            <h6>Notes</h6>
                            <p>{{ family_member.notes|nl2br }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Dossiers médicaux</h5>
                    <a href="{{ path('app_medical_record_new_for_family_member', {'id': family_member.id}) }}" class="btn btn-sm btn-light">
                        <i class="bi bi-plus-circle"></i> Nouveau dossier
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Titre</th>
                                    <th>Type</th>
                                    <th>Date</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for medical_record in family_member.medicalRecords %}
                                    <tr>
                                        <td>{{ medical_record.title }}</td>
                                        <td>{{ medical_record.recordTypeLabel }}</td>
                                        <td>{{ medical_record.recordDate|date('d/m/Y') }}</td>
                                        <td>
                                            {% if medical_record.status == 'active' %}
                                                <span class="badge bg-success">Actif</span>
                                            {% elseif medical_record.status == 'archived' %}
                                                <span class="badge bg-secondary">Archivé</span>
                                            {% elseif medical_record.status == 'pending' %}
                                                <span class="badge bg-warning">En attente</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ path('app_medical_record_show', {'id': medical_record.id}) }}" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ path('app_medical_record_edit', {'id': medical_record.id}) }}" class="btn btn-sm btn-primary">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center">Aucun dossier médical trouvé</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-between">
        <a href="{{ path('app_family_member_edit', {'id': family_member.id}) }}" class="btn btn-primary">
            <i class="bi bi-pencil"></i> Modifier
        </a>
        <a href="{{ path('app_family_member_delete', {'id': family_member.id}) }}" class="btn btn-danger">
            <i class="bi bi-trash"></i> Supprimer
        </a>
    </div>
</div>
{% endblock %}
