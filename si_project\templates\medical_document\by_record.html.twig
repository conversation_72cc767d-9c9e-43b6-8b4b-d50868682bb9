{% extends 'base.html.twig' %}

{% block title %}Documents médicaux du dossier - CRM System{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Documents médicaux du dossier</h1>
        <div>
            <a href="{{ path('app_medical_record_show', {'id': medical_record.id}) }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Retour au dossier médical
            </a>
            <a href="{{ path('app_medical_document_new_for_record', {'id': medical_record.id}) }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nouveau document
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Documents médicaux de {{ medical_record.title }}</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Titre</th>
                            <th>Type</th>
                            <th>Date</th>
                            <th>Émis par</th>
                            <th>Confidentiel</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in medical_documents %}
                            {% if not document.isConfidential or can_view_confidential %}
                                <tr>
                                    <td>{{ document.title }}</td>
                                    <td>{{ document.documentTypeLabel }}</td>
                                    <td>{{ document.documentDate|date('d/m/Y') }}</td>
                                    <td>{{ document.issuedBy ?: 'Non spécifié' }}</td>
                                    <td>
                                        {% if document.isConfidential %}
                                            <span class="badge bg-danger">Oui</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Non</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ path('app_medical_document_show', {'id': document.id}) }}" class="btn btn-sm btn-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="{{ path('app_medical_document_download', {'id': document.id}) }}" class="btn btn-sm btn-success">
                                                <i class="bi bi-download"></i>
                                            </a>
                                            <a href="{{ path('app_medical_document_edit', {'id': document.id}) }}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endif %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center">Aucun document médical trouvé pour ce dossier</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
