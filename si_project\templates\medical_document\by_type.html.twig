{% extends 'base.html.twig' %}

{% block title %}Documents médicaux par type - CRM System{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Documents médicaux - {{ document_type }}</h1>
        <div>
            <a href="{{ path('app_medical_document_index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Retour à la liste
            </a>
            <a href="{{ path('app_medical_document_new') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nouveau document
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Documents médicaux de type {{ document_type }}</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Titre</th>
                            <th>Personne</th>
                            <th>Date</th>
                            <th>Émis par</th>
                            <th>Confidentiel</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in medical_documents %}
                            {% if not document.isConfidential or can_view_confidential %}
                                <tr>
                                    <td>{{ document.title }}</td>
                                    <td>{{ document.personName }}</td>
                                    <td>{{ document.documentDate|date('d/m/Y') }}</td>
                                    <td>{{ document.issuedBy ?: 'Non spécifié' }}</td>
                                    <td>
                                        {% if document.isConfidential %}
                                            <span class="badge bg-danger">Oui</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Non</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ path('app_medical_document_show', {'id': document.id}) }}" class="btn btn-sm btn-info">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="{{ path('app_medical_document_download', {'id': document.id}) }}" class="btn btn-sm btn-success">
                                                <i class="bi bi-download"></i>
                                            </a>
                                            <a href="{{ path('app_medical_document_edit', {'id': document.id}) }}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endif %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center">Aucun document médical trouvé pour ce type</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
