{% extends 'base.html.twig' %}

{% block title %}Modifier le document médical - CRM System{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Modifier le document médical</h1>
        <div>
            <a href="{{ path('app_medical_document_index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Retour à la liste
            </a>
            <a href="{{ path('app_medical_document_show', {'id': medical_document.id}) }}" class="btn btn-info">
                <i class="bi bi-eye"></i> Voir les détails
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Formulaire de modification</h5>
        </div>
        <div class="card-body">
            {{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate'}}) }}
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.title) }}
                            {{ form_widget(form.title, {'attr': {'class': 'form-control'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.title) }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.documentType) }}
                            {{ form_widget(form.documentType, {'attr': {'class': 'form-select'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.documentType) }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.documentDate) }}
                            {{ form_widget(form.documentDate, {'attr': {'class': 'form-control'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.documentDate) }}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            {{ form_label(form.issuedBy) }}
                            {{ form_widget(form.issuedBy, {'attr': {'class': 'form-control'}}) }}
                            <div class="invalid-feedback">
                                {{ form_errors(form.issuedBy) }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    {{ form_label(form.description) }}
                    {{ form_widget(form.description, {'attr': {'class': 'form-control'}}) }}
                    <div class="invalid-feedback">
                        {{ form_errors(form.description) }}
                    </div>
                </div>

                {% if form.documentFile is defined %}
                <div class="mb-3">
                    {{ form_label(form.documentFile) }}
                    {{ form_widget(form.documentFile, {'attr': {'class': 'form-control'}}) }}
                    <div class="invalid-feedback">
                        {{ form_errors(form.documentFile) }}
                    </div>
                    <small class="form-text text-muted">Formats acceptés : PDF, DOC, DOCX, JPG, PNG. Taille maximale : 10 Mo.</small>
                    <p class="mt-2">Fichier actuel : <strong>{{ medical_document.filename }}</strong></p>
                </div>
                {% endif %}

                <div class="mb-3 form-check">
                    {{ form_widget(form.isConfidential, {'attr': {'class': 'form-check-input'}}) }}
                    {{ form_label(form.isConfidential, null, {'label_attr': {'class': 'form-check-label'}}) }}
                    <div class="invalid-feedback">
                        {{ form_errors(form.isConfidential) }}
                    </div>
                    <small class="form-text text-muted">Les documents confidentiels ne sont visibles que par les utilisateurs autorisés.</small>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Enregistrer les modifications
                    </button>
                </div>
            {{ form_end(form) }}
        </div>
    </div>
</div>
{% endblock %}
