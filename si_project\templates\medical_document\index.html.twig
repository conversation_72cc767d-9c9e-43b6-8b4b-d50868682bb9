{% extends 'base.html.twig' %}

{% block title %}Documents médicaux - CRM System{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Documents médicaux</h1>
        <div>
            <a href="{{ path('app_medical_document_new') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Nouveau document
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Recherche</h5>
                </div>
                <div class="card-body">
                    <form action="{{ path('app_medical_document_search') }}" method="get" class="row g-3">
                        <div class="col-md-8">
                            <input type="text" name="keyword" class="form-control" placeholder="Rechercher par titre, description, émetteur...">
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-search"></i> Rechercher
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <ul class="nav nav-tabs mb-4" id="documentTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true">
                Tous les documents
            </button>
        </li>
        {% if can_view_confidential %}
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="confidential-tab" data-bs-toggle="tab" data-bs-target="#confidential" type="button" role="tab" aria-controls="confidential" aria-selected="false">
                Documents confidentiels ({{ confidential_documents|length }})
            </button>
        </li>
        {% endif %}
    </ul>

    <div class="tab-content" id="documentTabsContent">
        <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Tous les documents médicaux</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Titre</th>
                                    <th>Personne</th>
                                    <th>Type</th>
                                    <th>Date</th>
                                    <th>Émis par</th>
                                    <th>Confidentiel</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for document in medical_documents %}
                                    {% if not document.isConfidential or can_view_confidential %}
                                        <tr>
                                            <td>{{ document.title }}</td>
                                            <td>{{ document.personName }}</td>
                                            <td>{{ document.documentTypeLabel }}</td>
                                            <td>{{ document.documentDate|date('d/m/Y') }}</td>
                                            <td>{{ document.issuedBy ?: 'Non spécifié' }}</td>
                                            <td>
                                                {% if document.isConfidential %}
                                                    <span class="badge bg-danger">Oui</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Non</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="{{ path('app_medical_document_show', {'id': document.id}) }}" class="btn btn-sm btn-info">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="{{ path('app_medical_document_download', {'id': document.id}) }}" class="btn btn-sm btn-success">
                                                        <i class="bi bi-download"></i>
                                                    </a>
                                                    <a href="{{ path('app_medical_document_edit', {'id': document.id}) }}" class="btn btn-sm btn-primary">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endif %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center">Aucun document médical trouvé</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        {% if can_view_confidential %}
        <div class="tab-pane fade" id="confidential" role="tabpanel" aria-labelledby="confidential-tab">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">Documents médicaux confidentiels</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Titre</th>
                                    <th>Personne</th>
                                    <th>Type</th>
                                    <th>Date</th>
                                    <th>Émis par</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for document in confidential_documents %}
                                    <tr>
                                        <td>{{ document.title }}</td>
                                        <td>{{ document.personName }}</td>
                                        <td>{{ document.documentTypeLabel }}</td>
                                        <td>{{ document.documentDate|date('d/m/Y') }}</td>
                                        <td>{{ document.issuedBy ?: 'Non spécifié' }}</td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{{ path('app_medical_document_show', {'id': document.id}) }}" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ path('app_medical_document_download', {'id': document.id}) }}" class="btn btn-sm btn-success">
                                                    <i class="bi bi-download"></i>
                                                </a>
                                                <a href="{{ path('app_medical_document_edit', {'id': document.id}) }}" class="btn btn-sm btn-primary">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% else %}
                                    <tr>
                                        <td colspan="6" class="text-center">Aucun document médical confidentiel trouvé</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
