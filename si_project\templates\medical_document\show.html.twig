{% extends 'base.html.twig' %}

{% block title %}Document médical - CRM System{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ medical_document.title }}</h1>
        <div>
            <a href="{{ path('app_medical_document_index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Retour à la liste
            </a>
            <a href="{{ path('app_medical_record_show', {'id': medical_document.medicalRecord.id}) }}" class="btn btn-info">
                <i class="bi bi-file-earmark-medical"></i> Voir le dossier médical
            </a>
            <a href="{{ path('app_medical_document_download', {'id': medical_document.id}) }}" class="btn btn-success">
                <i class="bi bi-download"></i> Télécharger
            </a>
            <a href="{{ path('app_medical_document_edit', {'id': medical_document.id}) }}" class="btn btn-primary">
                <i class="bi bi-pencil"></i> Modifier
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Informations générales</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th>Titre</th>
                            <td>{{ medical_document.title }}</td>
                        </tr>
                        <tr>
                            <th>Type</th>
                            <td>{{ medical_document.documentTypeLabel }}</td>
                        </tr>
                        <tr>
                            <th>Dossier médical</th>
                            <td>
                                <a href="{{ path('app_medical_record_show', {'id': medical_document.medicalRecord.id}) }}">
                                    {{ medical_document.medicalRecord.title }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>Personne</th>
                            <td>{{ medical_document.personName }}</td>
                        </tr>
                        <tr>
                            <th>Date</th>
                            <td>{{ medical_document.documentDate|date('d/m/Y') }}</td>
                        </tr>
                        <tr>
                            <th>Émis par</th>
                            <td>{{ medical_document.issuedBy ?: 'Non spécifié' }}</td>
                        </tr>
                        <tr>
                            <th>Confidentiel</th>
                            <td>
                                {% if medical_document.isConfidential %}
                                    <span class="badge bg-danger">Oui</span>
                                {% else %}
                                    <span class="badge bg-secondary">Non</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Description</h5>
                </div>
                <div class="card-body">
                    {% if medical_document.description %}
                        <p>{{ medical_document.description|nl2br }}</p>
                    {% else %}
                        <p class="text-muted">Aucune description disponible</p>
                    {% endif %}
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Fichier</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <p class="mb-1"><strong>Nom du fichier :</strong> {{ medical_document.filename }}</p>
                            <p class="mb-1"><strong>Type de fichier :</strong> {{ medical_document.mimeType }}</p>
                            <p class="mb-0"><strong>Taille :</strong> {{ medical_document.fileSize|format_bytes }}</p>
                        </div>
                        <a href="{{ path('app_medical_document_download', {'id': medical_document.id}) }}" class="btn btn-success">
                            <i class="bi bi-download"></i> Télécharger
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Informations complémentaires</h5>
        </div>
        <div class="card-body">
            <table class="table table-bordered">
                <tr>
                    <th>Créé par</th>
                    <td>{{ medical_document.createdBy.fullName }}</td>
                </tr>
                <tr>
                    <th>Date de création</th>
                    <td>{{ medical_document.createdAt|date('d/m/Y H:i') }}</td>
                </tr>
                <tr>
                    <th>Dernière mise à jour</th>
                    <td>{{ medical_document.updatedAt ? medical_document.updatedAt|date('d/m/Y H:i') : 'Jamais' }}</td>
                </tr>
            </table>
        </div>
    </div>

    <div class="d-flex justify-content-between">
        <a href="{{ path('app_medical_document_edit', {'id': medical_document.id}) }}" class="btn btn-primary">
            <i class="bi bi-pencil"></i> Modifier
        </a>
        <a href="{{ path('app_medical_document_delete', {'id': medical_document.id}) }}" class="btn btn-danger">
            <i class="bi bi-trash"></i> Supprimer
        </a>
    </div>
</div>
{% endblock %}
