-- Script pour nettoyer les départements dupliqués

-- 1. Mettre à jour les références des employés vers les départements dupliqués
-- Direction Générale (garder ID 1)
UPDATE employee SET department_id = 1 WHERE department_id IN (18, 34);

-- Direction Financière (garder ID 19)
UPDATE employee SET department_id = 19 WHERE department_id = 35;

-- Direction des Ressources Humaines (garder ID 20)
UPDATE employee SET department_id = 20 WHERE department_id = 36;

-- Direction des Systèmes d'Information (garder ID 21)
UPDATE employee SET department_id = 21 WHERE department_id = 37;

-- Direction Commerciale (garder ID 22)
UPDATE employee SET department_id = 22 WHERE department_id = 38;

-- Direction des Opérations (garder ID 23)
UPDATE employee SET department_id = 23 WHERE department_id = 39;

-- Comptabilité (garder ID 12)
UPDATE employee SET department_id = 12 WHERE department_id IN (24, 40);

-- Recrutement (garder ID 9)
UPDATE employee SET department_id = 9 WHERE department_id IN (26, 42);

-- Développement (garder ID 13)
UPDATE employee SET department_id = 13 WHERE department_id IN (28, 44);

-- Infrastructure (garder ID 14)
UPDATE employee SET department_id = 14 WHERE department_id IN (29, 45);

-- Marketing (garder ID 5)
UPDATE employee SET department_id = 5 WHERE department_id IN (31, 47);

-- Production (garder ID 7)
UPDATE employee SET department_id = 7 WHERE department_id IN (32, 48);

-- Logistique (garder ID 33)
UPDATE employee SET department_id = 33 WHERE department_id = 49;

-- Ventes (garder ID 30)
UPDATE employee SET department_id = 30 WHERE department_id = 46;

-- Trésorerie (garder ID 25)
UPDATE employee SET department_id = 25 WHERE department_id = 41;

-- Formation (garder ID 10)
UPDATE employee SET department_id = 10 WHERE department_id IN (27, 43);

-- 2. Supprimer les départements dupliqués
DELETE FROM department WHERE id IN (
    18, 34, -- Direction Générale
    35,     -- Direction Financière
    36,     -- Direction des Ressources Humaines
    37,     -- Direction des Systèmes d'Information
    38,     -- Direction Commerciale
    39,     -- Direction des Opérations
    24, 40, -- Comptabilité
    26, 42, -- Recrutement
    28, 44, -- Développement
    29, 45, -- Infrastructure
    31, 47, -- Marketing
    32, 48, -- Production
    49,     -- Logistique
    46,     -- Ventes
    41,     -- Trésorerie
    27, 43  -- Formation
);

-- 3. Standardiser les codes des départements
UPDATE department SET code = 'COMPTA' WHERE id = 12;
UPDATE department SET code = 'REC' WHERE id = 9;
