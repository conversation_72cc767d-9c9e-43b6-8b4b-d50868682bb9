-- Données de test pour Finance et Comptabilité

-- 1. Ajouter des comptes comptables
INSERT INTO accounting_account (code, name, type, is_active, created_at) VALUES
('101000', 'Capital social', 'PASSIF', true, NOW()),
('106100', 'Réserve légale', 'PASSIF', true, NOW()),
('106800', 'Autres réserves', 'PASSIF', true, NOW()),
('120000', 'Résultat de l''exercice', 'PASSIF', true, NOW()),
('164000', 'Emprunts auprès des établissements de crédit', 'PASSIF', true, NOW()),
('211000', 'Terrains', 'ACTIF', true, NOW()),
('213500', 'Installations générales, agencements', 'ACTIF', true, NOW()),
('215000', 'Installations techniques, matériel et outillage', 'ACTIF', true, NOW()),
('218300', 'Matériel de bureau et informatique', 'ACTIF', true, NOW()),
('280000', 'Amortissements des immobilisations incorporelles', 'ACTIF', true, NOW()),
('281000', 'Amortissements des immobilisations corporelles', 'ACTIF', true, NOW()),
('401000', 'Fournisseurs', 'PASSIF', true, NOW()),
('411000', 'Clients', 'ACTIF', true, NOW()),
('445660', 'TVA déductible', 'ACTIF', true, NOW()),
('445710', 'TVA collectée', 'PASSIF', true, NOW()),
('512000', 'Banque', 'ACTIF', true, NOW()),
('530000', 'Caisse', 'ACTIF', true, NOW()),
('601000', 'Achats de matières premières', 'CHARGE', true, NOW()),
('607000', 'Achats de marchandises', 'CHARGE', true, NOW()),
('613000', 'Locations', 'CHARGE', true, NOW()),
('615000', 'Entretien et réparations', 'CHARGE', true, NOW()),
('622600', 'Honoraires', 'CHARGE', true, NOW()),
('625100', 'Voyages et déplacements', 'CHARGE', true, NOW()),
('626000', 'Frais postaux et télécommunications', 'CHARGE', true, NOW()),
('627000', 'Services bancaires', 'CHARGE', true, NOW()),
('641000', 'Rémunération du personnel', 'CHARGE', true, NOW()),
('645000', 'Charges de sécurité sociale', 'CHARGE', true, NOW()),
('661000', 'Charges d''intérêts', 'CHARGE', true, NOW()),
('701000', 'Ventes de produits finis', 'PRODUIT', true, NOW()),
('706000', 'Prestations de services', 'PRODUIT', true, NOW()),
('707000', 'Ventes de marchandises', 'PRODUIT', true, NOW());

-- 2. Ajouter des journaux comptables
INSERT INTO accounting_journal (code, name, type, is_active, created_at) VALUES
('ACH', 'Journal des achats', 'ACHAT', true, NOW()),
('VTE', 'Journal des ventes', 'VENTE', true, NOW()),
('BNQ', 'Journal de banque', 'BANQUE', true, NOW()),
('CAIS', 'Journal de caisse', 'CAISSE', true, NOW()),
('OD', 'Journal des opérations diverses', 'OPERATION_DIVERSE', true, NOW());

-- 3. Ajouter un exercice fiscal
INSERT INTO fiscal_year (name, start_date, end_date, is_closed, is_active, created_at) VALUES
('Exercice 2023', '2023-01-01', '2023-12-31', false, true, NOW()),
('Exercice 2024', '2024-01-01', '2024-12-31', false, true, NOW());

-- 4. Ajouter des factures
INSERT INTO invoice (reference, date, due_date, amount, tax_amount, total_amount, status, partner_id, created_at) VALUES
('FACT-2024-001', '2024-01-15', '2024-02-15', 1000.00, 200.00, 1200.00, 'PAID', 1, NOW()),
('FACT-2024-002', '2024-02-10', '2024-03-10', 2500.00, 500.00, 3000.00, 'PENDING', 2, NOW()),
('FACT-2024-003', '2024-03-05', '2024-04-05', 1800.00, 360.00, 2160.00, 'PENDING', 3, NOW()),
('FACT-2024-004', '2024-03-20', '2024-04-20', 3200.00, 640.00, 3840.00, 'DRAFT', 4, NOW()),
('FACT-2024-005', '2024-04-01', '2024-05-01', 950.00, 190.00, 1140.00, 'PENDING', 5, NOW());

-- 5. Ajouter des écritures comptables
INSERT INTO accounting_entry (journal_id, date, reference, description, is_validated, fiscal_year_id, created_at) VALUES
(1, '2024-01-15', 'ACH-2024-001', 'Achat de fournitures', true, 2, NOW()),
(2, '2024-01-20', 'VTE-2024-001', 'Vente de marchandises', true, 2, NOW()),
(3, '2024-02-05', 'BNQ-2024-001', 'Paiement loyer', true, 2, NOW()),
(2, '2024-02-10', 'VTE-2024-002', 'Vente de services', true, 2, NOW()),
(1, '2024-03-01', 'ACH-2024-002', 'Achat de matériel informatique', true, 2, NOW());

-- 6. Ajouter des lignes d'écritures comptables
INSERT INTO accounting_entry_line (entry_id, account_id, debit, credit, description, created_at) VALUES
(1, 18, 500.00, 0.00, 'Achat de fournitures', NOW()),
(1, 14, 100.00, 0.00, 'TVA déductible', NOW()),
(1, 12, 0.00, 600.00, 'Fournisseur', NOW()),
(2, 13, 1200.00, 0.00, 'Client', NOW()),
(2, 15, 0.00, 200.00, 'TVA collectée', NOW()),
(2, 30, 0.00, 1000.00, 'Vente de marchandises', NOW()),
(3, 12, 800.00, 0.00, 'Paiement loyer', NOW()),
(3, 16, 0.00, 800.00, 'Banque', NOW()),
(4, 13, 3000.00, 0.00, 'Client', NOW()),
(4, 15, 0.00, 500.00, 'TVA collectée', NOW()),
(4, 29, 0.00, 2500.00, 'Prestations de services', NOW()),
(5, 9, 2000.00, 0.00, 'Matériel informatique', NOW()),
(5, 14, 400.00, 0.00, 'TVA déductible', NOW()),
(5, 12, 0.00, 2400.00, 'Fournisseur', NOW());

-- 7. Ajouter des déclarations fiscales
INSERT INTO tax_declaration (type, period_start, period_end, submission_date, amount, status, created_at) VALUES
('TVA', '2024-01-01', '2024-01-31', '2024-02-15', 300.00, 'SUBMITTED', NOW()),
('TVA', '2024-02-01', '2024-02-29', '2024-03-15', 450.00, 'SUBMITTED', NOW()),
('TVA', '2024-03-01', '2024-03-31', NULL, 520.00, 'DRAFT', NOW());

-- 8. Ajouter des budgets
INSERT INTO budget (name, start_date, end_date, department_id, amount, status, created_at) VALUES
('Budget Marketing 2024', '2024-01-01', '2024-12-31', 5, 50000.00, 'APPROVED', NOW()),
('Budget R&D 2024', '2024-01-01', '2024-12-31', 8, 75000.00, 'APPROVED', NOW()),
('Budget RH 2024', '2024-01-01', '2024-12-31', 2, 30000.00, 'APPROVED', NOW()),
('Budget IT 2024', '2024-01-01', '2024-12-31', 4, 45000.00, 'APPROVED', NOW()),
('Budget Finance 2024', '2024-01-01', '2024-12-31', 3, 25000.00, 'APPROVED', NOW());

-- 9. Ajouter des lignes budgétaires
INSERT INTO budget_line (budget_id, name, amount, category, created_at) VALUES
(1, 'Campagnes publicitaires', 20000.00, 'FUNCTIONING', NOW()),
(1, 'Événements', 15000.00, 'FUNCTIONING', NOW()),
(1, 'Matériel promotionnel', 10000.00, 'INVESTMENT', NOW()),
(1, 'Études de marché', 5000.00, 'FUNCTIONING', NOW()),
(2, 'Salaires R&D', 40000.00, 'FUNCTIONING', NOW()),
(2, 'Équipement de laboratoire', 25000.00, 'INVESTMENT', NOW()),
(2, 'Licences logicielles', 10000.00, 'FUNCTIONING', NOW()),
(3, 'Formation', 15000.00, 'FUNCTIONING', NOW()),
(3, 'Recrutement', 10000.00, 'FUNCTIONING', NOW()),
(3, 'Événements d''équipe', 5000.00, 'FUNCTIONING', NOW()),
(4, 'Matériel informatique', 20000.00, 'INVESTMENT', NOW()),
(4, 'Licences logicielles', 15000.00, 'FUNCTIONING', NOW()),
(4, 'Maintenance', 10000.00, 'FUNCTIONING', NOW()),
(5, 'Logiciels financiers', 15000.00, 'INVESTMENT', NOW()),
(5, 'Audits', 10000.00, 'FUNCTIONING', NOW());
