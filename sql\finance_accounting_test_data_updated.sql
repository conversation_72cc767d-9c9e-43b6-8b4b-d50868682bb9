-- Données de test pour Finance et Comptabilité

-- 1. Ajouter des comptes comptables
INSERT INTO accounting_account (number, name, type, category, is_debit_balance, is_active, created_at) VALUES
('101000', 'Capital social', 'PASSIF', 'CAPITAL', 0, 1, datetime('now')),
('106100', 'Réserve légale', 'PASSIF', 'CAPITAL', 0, 1, datetime('now')),
('106800', 'Autres réserves', 'PASSIF', 'CAPITAL', 0, 1, datetime('now')),
('120000', 'Résultat de l''exercice', 'PASSIF', 'CAPITAL', 0, 1, datetime('now')),
('164000', 'Emprunts auprès des établissements de crédit', 'PASSIF', 'DETTES', 0, 1, datetime('now')),
('211000', 'Terrains', 'ACTIF', 'IMMOBILISATIONS', 1, 1, datetime('now')),
('213500', 'Installations générales, agencements', 'ACTIF', 'IMMOBILISATIONS', 1, 1, datetime('now')),
('215000', 'Installations techniques, matériel et outillage', 'ACTIF', 'IMMOBILISATIONS', 1, 1, datetime('now')),
('218300', 'Matériel de bureau et informatique', 'ACTIF', 'IMMOBILISATIONS', 1, 1, datetime('now')),
('280000', 'Amortissements des immobilisations incorporelles', 'ACTIF', 'IMMOBILISATIONS', 0, 1, datetime('now')),
('281000', 'Amortissements des immobilisations corporelles', 'ACTIF', 'IMMOBILISATIONS', 0, 1, datetime('now')),
('401000', 'Fournisseurs', 'PASSIF', 'DETTES', 0, 1, datetime('now')),
('411000', 'Clients', 'ACTIF', 'CREANCES', 1, 1, datetime('now')),
('445660', 'TVA déductible', 'ACTIF', 'TAXES', 1, 1, datetime('now')),
('445710', 'TVA collectée', 'PASSIF', 'TAXES', 0, 1, datetime('now')),
('512000', 'Banque', 'ACTIF', 'TRESORERIE', 1, 1, datetime('now')),
('530000', 'Caisse', 'ACTIF', 'TRESORERIE', 1, 1, datetime('now')),
('601000', 'Achats de matières premières', 'CHARGE', 'EXPLOITATION', 1, 1, datetime('now')),
('607000', 'Achats de marchandises', 'CHARGE', 'EXPLOITATION', 1, 1, datetime('now')),
('613000', 'Locations', 'CHARGE', 'EXPLOITATION', 1, 1, datetime('now')),
('615000', 'Entretien et réparations', 'CHARGE', 'EXPLOITATION', 1, 1, datetime('now')),
('622600', 'Honoraires', 'CHARGE', 'EXPLOITATION', 1, 1, datetime('now')),
('625100', 'Voyages et déplacements', 'CHARGE', 'EXPLOITATION', 1, 1, datetime('now')),
('626000', 'Frais postaux et télécommunications', 'CHARGE', 'EXPLOITATION', 1, 1, datetime('now')),
('627000', 'Services bancaires', 'CHARGE', 'EXPLOITATION', 1, 1, datetime('now')),
('641000', 'Rémunération du personnel', 'CHARGE', 'PERSONNEL', 1, 1, datetime('now')),
('645000', 'Charges de sécurité sociale', 'CHARGE', 'PERSONNEL', 1, 1, datetime('now')),
('661000', 'Charges d''intérêts', 'CHARGE', 'FINANCIER', 1, 1, datetime('now')),
('701000', 'Ventes de produits finis', 'PRODUIT', 'EXPLOITATION', 0, 1, datetime('now')),
('706000', 'Prestations de services', 'PRODUIT', 'EXPLOITATION', 0, 1, datetime('now')),
('707000', 'Ventes de marchandises', 'PRODUIT', 'EXPLOITATION', 0, 1, datetime('now'));

-- 2. Ajouter des journaux comptables
INSERT INTO accounting_journal (code, name, type, is_active, is_system, created_at) VALUES
('ACH', 'Journal des achats', 'purchases', 1, 0, datetime('now')),
('VTE', 'Journal des ventes', 'sales', 1, 0, datetime('now')),
('BNQ', 'Journal de banque', 'bank', 1, 0, datetime('now')),
('CAIS', 'Journal de caisse', 'cash', 1, 0, datetime('now')),
('OD', 'Journal des opérations diverses', 'general', 1, 0, datetime('now'));

-- 3. Ajouter un exercice fiscal
INSERT INTO accounting_fiscal_year (name, start_date, end_date, is_closed, is_active, created_at) VALUES
('Exercice 2023', '2023-01-01', '2023-12-31', 0, 1, datetime('now')),
('Exercice 2024', '2024-01-01', '2024-12-31', 0, 1, datetime('now'));

-- 4. Ajouter des déclarations fiscales
INSERT INTO accounting_tax_declaration (type, period, start_date, end_date, due_date, taxable_amount, tax_amount, deductible_amount, net_amount, status, created_at) VALUES
('tva', 'monthly', '2024-01-01', '2024-01-31', '2024-02-20', 50000.00, 10000.00, 5000.00, 5000.00, 'submitted', datetime('now')),
('tva', 'monthly', '2024-02-01', '2024-02-29', '2024-03-20', 60000.00, 12000.00, 6000.00, 6000.00, 'submitted', datetime('now')),
('tva', 'monthly', '2024-03-01', '2024-03-31', '2024-04-20', 70000.00, 14000.00, 7000.00, 7000.00, 'draft', datetime('now'));
