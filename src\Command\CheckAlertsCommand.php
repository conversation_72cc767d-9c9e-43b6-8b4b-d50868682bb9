<?php

namespace App\Command;

use App\Service\AlertService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:check-alerts',
    description: 'Check for low stock products and expiring contracts',
)]
class CheckAlertsCommand extends Command
{
    private AlertService $alertService;

    public function __construct(AlertService $alertService)
    {
        parent::__construct();
        $this->alertService = $alertService;
    }

    protected function configure(): void
    {
        $this
            ->addOption('type', 't', InputOption::VALUE_REQUIRED, 'Type of alerts to check (low_stock, expiring_contracts, all)', 'all')
            ->addOption('days', 'd', InputOption::VALUE_REQUIRED, 'Days threshold for expiring contracts', 30)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $type = $input->getOption('type');
        $days = (int) $input->getOption('days');
        
        $io->title('Checking alerts');
        
        if ($type === 'all' || $type === 'low_stock') {
            $io->section('Checking low stock products');
            $lowStockAlerts = $this->alertService->checkLowStockProducts();
            
            if (empty($lowStockAlerts)) {
                $io->success('No low stock products found');
            } else {
                $io->table(
                    ['Product', 'Current Stock', 'Min Stock', 'Status'],
                    array_map(function($alert) {
                        return [
                            $alert['product']->getName() . ' (' . $alert['product']->getCode() . ')',
                            $alert['product']->getCurrentStock(),
                            $alert['product']->getMinStockLevel(),
                            $alert['status']
                        ];
                    }, $lowStockAlerts)
                );
                
                $io->success(sprintf('Sent %d low stock alerts', count($lowStockAlerts)));
            }
        }
        
        if ($type === 'all' || $type === 'expiring_contracts') {
            $io->section('Checking expiring contracts');
            $expiringContractAlerts = $this->alertService->checkExpiringContracts($days);
            
            if (empty($expiringContractAlerts)) {
                $io->success('No contracts expiring within ' . $days . ' days');
            } else {
                $io->table(
                    ['Contract', 'End Date', 'Days Left', 'Status'],
                    array_map(function($alert) {
                        $daysLeft = $alert['contract']->getEndDate()->diff(new \DateTime())->days;
                        return [
                            $alert['contract']->getTitle() . ' (' . $alert['contract']->getContractNumber() . ')',
                            $alert['contract']->getEndDate()->format('Y-m-d'),
                            $daysLeft,
                            $alert['status']
                        ];
                    }, $expiringContractAlerts)
                );
                
                $io->success(sprintf('Sent %d expiring contract alerts', count($expiringContractAlerts)));
            }
        }
        
        return Command::SUCCESS;
    }
}
