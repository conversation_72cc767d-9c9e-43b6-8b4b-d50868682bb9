<?php

namespace App\Command;

use App\Service\ConfigurationService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:config:init',
    description: 'Initialize default configuration values',
)]
class ConfigInitCommand extends Command
{
    private ConfigurationService $configService;

    public function __construct(ConfigurationService $configService)
    {
        parent::__construct();
        $this->configService = $configService;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Initializing default configuration values');

        try {
            $this->configService->initializeDefaults();
            $io->success('Default configuration values have been initialized successfully.');
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error('An error occurred while initializing default configuration values: ' . $e->getMessage());
            
            return Command::FAILURE;
        }
    }
}
