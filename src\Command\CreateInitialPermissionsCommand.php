<?php

namespace App\Command;

use App\Entity\Permission;
use App\Entity\Role;
use App\Entity\RolePermission;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:create-initial-permissions',
    description: 'Creates initial permissions and roles',
)]
class CreateInitialPermissionsCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        // Create roles
        $roles = [
            [
                'code' => 'ADMIN',
                'name' => 'Administrateur',
                'description' => 'Accès complet au système',
                'displayOrder' => 1,
            ],
            [
                'code' => 'MANAGER',
                'name' => 'Gestionnaire',
                'description' => 'Gestion des partenaires et des utilisateurs',
                'displayOrder' => 2,
            ],
            [
                'code' => 'USER',
                'name' => 'Utilisateur',
                'description' => 'Accès limité au système',
                'displayOrder' => 3,
            ],
        ];

        $createdRoles = [];
        foreach ($roles as $roleData) {
            $existingRole = $this->entityManager->getRepository(Role::class)->findOneBy(['code' => $roleData['code']]);
            if (!$existingRole) {
                $role = new Role();
                $role->setCode($roleData['code']);
                $role->setName($roleData['name']);
                $role->setDescription($roleData['description']);
                $role->setDisplayOrder($roleData['displayOrder']);
                $role->setIsActive(true);
                
                $this->entityManager->persist($role);
                $createdRoles[$roleData['code']] = $role;
                $io->text(sprintf('Created role: %s', $roleData['name']));
            } else {
                $createdRoles[$roleData['code']] = $existingRole;
                $io->text(sprintf('Role already exists: %s', $roleData['name']));
            }
        }

        // Create permissions
        $permissions = [
            // Partner permissions
            [
                'code' => 'PARTNER_VIEW',
                'name' => 'Voir les partenaires',
                'description' => 'Permet de voir la liste des partenaires',
                'category' => 'partner',
            ],
            [
                'code' => 'PARTNER_CREATE',
                'name' => 'Créer des partenaires',
                'description' => 'Permet de créer de nouveaux partenaires',
                'category' => 'partner',
            ],
            [
                'code' => 'PARTNER_EDIT',
                'name' => 'Modifier les partenaires',
                'description' => 'Permet de modifier les partenaires existants',
                'category' => 'partner',
            ],
            [
                'code' => 'PARTNER_DELETE',
                'name' => 'Supprimer les partenaires',
                'description' => 'Permet de supprimer des partenaires',
                'category' => 'partner',
            ],
            
            // User permissions
            [
                'code' => 'USER_VIEW',
                'name' => 'Voir les utilisateurs',
                'description' => 'Permet de voir la liste des utilisateurs',
                'category' => 'user',
            ],
            [
                'code' => 'USER_CREATE',
                'name' => 'Créer des utilisateurs',
                'description' => 'Permet de créer de nouveaux utilisateurs',
                'category' => 'user',
            ],
            [
                'code' => 'USER_EDIT',
                'name' => 'Modifier les utilisateurs',
                'description' => 'Permet de modifier les utilisateurs existants',
                'category' => 'user',
            ],
            [
                'code' => 'USER_DELETE',
                'name' => 'Supprimer les utilisateurs',
                'description' => 'Permet de supprimer des utilisateurs',
                'category' => 'user',
            ],
            
            // Role permissions
            [
                'code' => 'ROLE_VIEW',
                'name' => 'Voir les rôles',
                'description' => 'Permet de voir la liste des rôles',
                'category' => 'user',
            ],
            [
                'code' => 'ROLE_CREATE',
                'name' => 'Créer des rôles',
                'description' => 'Permet de créer de nouveaux rôles',
                'category' => 'user',
            ],
            [
                'code' => 'ROLE_EDIT',
                'name' => 'Modifier les rôles',
                'description' => 'Permet de modifier les rôles existants',
                'category' => 'user',
            ],
            [
                'code' => 'ROLE_DELETE',
                'name' => 'Supprimer les rôles',
                'description' => 'Permet de supprimer des rôles',
                'category' => 'user',
            ],
            
            // Configuration permissions
            [
                'code' => 'CONFIG_VIEW',
                'name' => 'Voir les configurations',
                'description' => 'Permet de voir les configurations du système',
                'category' => 'config',
            ],
            [
                'code' => 'CONFIG_EDIT',
                'name' => 'Modifier les configurations',
                'description' => 'Permet de modifier les configurations du système',
                'category' => 'config',
            ],
        ];

        $createdPermissions = [];
        foreach ($permissions as $permissionData) {
            $existingPermission = $this->entityManager->getRepository(Permission::class)->findOneBy(['code' => $permissionData['code']]);
            if (!$existingPermission) {
                $permission = new Permission();
                $permission->setCode($permissionData['code']);
                $permission->setName($permissionData['name']);
                $permission->setDescription($permissionData['description']);
                $permission->setCategory($permissionData['category']);
                $permission->setIsActive(true);
                
                $this->entityManager->persist($permission);
                $createdPermissions[$permissionData['code']] = $permission;
                $io->text(sprintf('Created permission: %s', $permissionData['name']));
            } else {
                $createdPermissions[$permissionData['code']] = $existingPermission;
                $io->text(sprintf('Permission already exists: %s', $permissionData['name']));
            }
        }

        // Assign permissions to roles
        $rolePermissions = [
            'ADMIN' => [
                'PARTNER_VIEW' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'PARTNER_CREATE' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'PARTNER_EDIT' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'PARTNER_DELETE' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'USER_VIEW' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'USER_CREATE' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'USER_EDIT' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'USER_DELETE' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'ROLE_VIEW' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'ROLE_CREATE' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'ROLE_EDIT' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'ROLE_DELETE' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'CONFIG_VIEW' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
                'CONFIG_EDIT' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => true],
            ],
            'MANAGER' => [
                'PARTNER_VIEW' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => false],
                'PARTNER_CREATE' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => false],
                'PARTNER_EDIT' => ['view' => true, 'create' => true, 'edit' => true, 'delete' => false],
                'PARTNER_DELETE' => ['view' => true, 'create' => false, 'edit' => false, 'delete' => false],
                'USER_VIEW' => ['view' => true, 'create' => false, 'edit' => false, 'delete' => false],
                'CONFIG_VIEW' => ['view' => true, 'create' => false, 'edit' => false, 'delete' => false],
            ],
            'USER' => [
                'PARTNER_VIEW' => ['view' => true, 'create' => false, 'edit' => false, 'delete' => false],
            ],
        ];

        foreach ($rolePermissions as $roleCode => $permissions) {
            $role = $createdRoles[$roleCode] ?? null;
            if (!$role) {
                continue;
            }
            
            foreach ($permissions as $permissionCode => $actions) {
                $permission = $createdPermissions[$permissionCode] ?? null;
                if (!$permission) {
                    continue;
                }
                
                // Check if role permission already exists
                $existingRolePermission = $this->entityManager->getRepository(RolePermission::class)->findOneBy([
                    'role' => $role,
                    'permission' => $permission,
                ]);
                
                if (!$existingRolePermission) {
                    $rolePermission = new RolePermission();
                    $rolePermission->setRole($role);
                    $rolePermission->setPermission($permission);
                    $rolePermission->setCanView($actions['view']);
                    $rolePermission->setCanCreate($actions['create']);
                    $rolePermission->setCanEdit($actions['edit']);
                    $rolePermission->setCanDelete($actions['delete']);
                    
                    $this->entityManager->persist($rolePermission);
                    $io->text(sprintf('Assigned permission %s to role %s', $permissionCode, $roleCode));
                } else {
                    $io->text(sprintf('Permission %s already assigned to role %s', $permissionCode, $roleCode));
                }
            }
        }

        $this->entityManager->flush();

        $io->success('Initial permissions and roles created successfully');

        return Command::SUCCESS;
    }
}
