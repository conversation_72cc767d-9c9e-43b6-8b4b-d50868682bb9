<?php

namespace App\Command;

use App\Entity\Product;
use App\Entity\ProductCategory;
use App\Entity\Stock\StockItem;
use App\Entity\Stock\StockLocation;
use App\Repository\Stock\StockLocationRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:create-products',
    description: 'Create sample products',
)]
class CreateProductsCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private StockLocationRepository $stockLocationRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        StockLocationRepository $stockLocationRepository
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->stockLocationRepository = $stockLocationRepository;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Creating sample products and categories');

        // Create or find main stock location
        $mainLocation = $this->stockLocationRepository->findOneBy(['code' => 'MAIN']);
        if (!$mainLocation) {
            $mainLocation = new StockLocation();
            $mainLocation->setCode('MAIN');
            $mainLocation->setName('Entrepôt principal');
            $mainLocation->setDescription('Emplacement principal de stockage');
            $mainLocation->setIsActive(true);
            $this->entityManager->persist($mainLocation);
            $this->entityManager->flush();
            $io->success('Created main stock location.');
        }

        // Create categories
        $categories = [
            'Fournitures de bureau' => [
                'Papeterie',
                'Classement',
                'Écriture',
                'Accessoires'
            ],
            'Informatique' => [
                'Ordinateurs',
                'Périphériques',
                'Stockage',
                'Réseau'
            ],
            'Mobilier' => [
                'Bureaux',
                'Chaises',
                'Rangement',
                'Accessoires'
            ],
            'Consommables' => [
                'Encre et toner',
                'Papier',
                'Nettoyage',
                'Divers'
            ]
        ];

        $categoryEntities = [];
        foreach ($categories as $mainCategory => $subCategories) {
            $mainCat = new ProductCategory();
            $mainCat->setName($mainCategory);
            $mainCat->setDescription('Catégorie principale: ' . $mainCategory);
            $this->entityManager->persist($mainCat);

            $categoryEntities[$mainCategory] = [
                'main' => $mainCat,
                'sub' => []
            ];

            foreach ($subCategories as $subCategory) {
                $subCat = new ProductCategory();
                $subCat->setName($subCategory);
                $subCat->setDescription('Sous-catégorie de ' . $mainCategory);
                $subCat->setParent($mainCat);
                $this->entityManager->persist($subCat);

                $categoryEntities[$mainCategory]['sub'][] = $subCat;
            }
        }

        $this->entityManager->flush();
        $io->success('Created product categories successfully.');

        // Create products
        $products = [
            [
                'name' => 'Papier A4 80g',
                'description' => 'Ramette de papier A4 80g/m², 500 feuilles',
                'category' => 'Papier',
                'sku' => 'PAP-A4-80G',
                'unit' => 'ramette',
                'referencePrice' => 45.00,
                'minStock' => 10,
                'maxStock' => 100
            ],
            [
                'name' => 'Stylo à bille bleu',
                'description' => 'Stylo à bille pointe moyenne, encre bleue',
                'category' => 'Écriture',
                'sku' => 'STY-BIL-BLU',
                'unit' => 'pièce',
                'referencePrice' => 5.50,
                'minStock' => 20,
                'maxStock' => 200
            ],
            [
                'name' => 'Classeur à levier',
                'description' => 'Classeur à levier dos 75mm, format A4',
                'category' => 'Classement',
                'sku' => 'CLA-LEV-A4',
                'unit' => 'pièce',
                'referencePrice' => 25.00,
                'minStock' => 5,
                'maxStock' => 50
            ],
            [
                'name' => 'Ordinateur portable',
                'description' => 'Ordinateur portable 15", Core i5, 8Go RAM, 256Go SSD',
                'category' => 'Ordinateurs',
                'sku' => 'ORD-POR-I5',
                'unit' => 'pièce',
                'referencePrice' => 6500.00,
                'minStock' => 2,
                'maxStock' => 10
            ],
            [
                'name' => 'Souris sans fil',
                'description' => 'Souris optique sans fil, 3 boutons',
                'category' => 'Périphériques',
                'sku' => 'SOU-SFI-OPT',
                'unit' => 'pièce',
                'referencePrice' => 120.00,
                'minStock' => 5,
                'maxStock' => 30
            ],
            [
                'name' => 'Clé USB 32Go',
                'description' => 'Clé USB 3.0, capacité 32Go',
                'category' => 'Stockage',
                'sku' => 'CLE-USB-32G',
                'unit' => 'pièce',
                'referencePrice' => 85.00,
                'minStock' => 10,
                'maxStock' => 50
            ],
            [
                'name' => 'Bureau droit 160cm',
                'description' => 'Bureau droit 160x80cm, plateau mélaminé',
                'category' => 'Bureaux',
                'sku' => 'BUR-DRO-160',
                'unit' => 'pièce',
                'referencePrice' => 1200.00,
                'minStock' => 1,
                'maxStock' => 5
            ],
            [
                'name' => 'Chaise de bureau ergonomique',
                'description' => 'Chaise de bureau ergonomique, accoudoirs réglables',
                'category' => 'Chaises',
                'sku' => 'CHA-ERG-PRO',
                'unit' => 'pièce',
                'referencePrice' => 850.00,
                'minStock' => 2,
                'maxStock' => 10
            ],
            [
                'name' => 'Armoire haute',
                'description' => 'Armoire haute 180x80x40cm, 4 tablettes',
                'category' => 'Rangement',
                'sku' => 'ARM-HAU-180',
                'unit' => 'pièce',
                'referencePrice' => 1500.00,
                'minStock' => 1,
                'maxStock' => 5
            ],
            [
                'name' => 'Cartouche d\'encre noire',
                'description' => 'Cartouche d\'encre noire compatible HP',
                'category' => 'Encre et toner',
                'sku' => 'CAR-ENC-NOI',
                'unit' => 'pièce',
                'referencePrice' => 180.00,
                'minStock' => 5,
                'maxStock' => 20
            ]
        ];

        $count = 0;
        foreach ($products as $productData) {
            $product = new Product();
            $product->setName($productData['name']);
            $product->setDescription($productData['description']);
            $product->setCode($productData['sku']);
            $product->setUnit($productData['unit']);
            $product->setReferencePrice($productData['referencePrice']);
            $product->setCurrentStock(0);
            $product->setMinStockLevel($productData['minStock']);

            // Find category
            $categoryFound = false;
            foreach ($categoryEntities as $mainCatName => $catData) {
                foreach ($catData['sub'] as $subCat) {
                    if ($subCat->getName() === $productData['category']) {
                        $product->setCategory($subCat);
                        $categoryFound = true;
                        break;
                    }
                }
                if ($categoryFound) break;
            }

            $this->entityManager->persist($product);

            // Create stock item
            $stockItem = new StockItem();
            $stockItem->setProduct($product);
            $stockItem->setQuantity(0);
            $stockItem->setReservedQuantity(0);
            $stockItem->setLocation($mainLocation);

            $this->entityManager->persist($stockItem);

            $count++;
        }

        $this->entityManager->flush();
        $io->success(sprintf('Created %d products successfully.', $count));

        return Command::SUCCESS;
    }
}
