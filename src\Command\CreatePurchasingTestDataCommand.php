<?php

namespace App\Command;

use App\Entity\Contract;
use App\Entity\Employee;
use App\Entity\GoodsReceipt;
use App\Entity\GoodsReceiptItem;
use App\Entity\Partner;
use App\Entity\Project;
use App\Entity\ProjectBudgetLine;
use App\Entity\ProjectBudgetSubLine;
use App\Entity\PurchaseOrder;
use App\Entity\PurchaseOrderItem;
use App\Entity\PurchaseRequest;
use App\Entity\PurchaseRequestItem;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:create-purchasing-test-data',
    description: 'Creates test data for the purchasing module',
)]
class CreatePurchasingTestDataCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Creating test data for the purchasing module');

        // Get existing entities
        $user = $this->entityManager->getRepository(User::class)->findOneBy([]) ??
                $this->createDefaultUser();

        $employees = $this->entityManager->getRepository(Employee::class)->findAll();
        if (empty($employees)) {
            $io->warning('No employees found. Creating default employees...');
            $employees = $this->createDefaultEmployees($user);
        }

        $projects = $this->entityManager->getRepository(Project::class)->findAll();
        if (empty($projects)) {
            $io->warning('No projects found. Creating default projects...');
            $projects = $this->createDefaultProjects($user);
        }

        $partners = $this->entityManager->getRepository(Partner::class)->findAll();
        if (empty($partners)) {
            $io->warning('No partners found. Creating default partners...');
            $partners = $this->createDefaultPartners();
        }

        // Create budget lines and sub-lines
        $io->section('Creating budget lines and sub-lines');
        $budgetLines = [];
        $budgetSubLines = [];

        foreach ($projects as $project) {
            $io->text('Creating budget lines for project: ' . $project->getName());

            // Create main budget lines
            $functioningLine = new ProjectBudgetLine();
            $functioningLine->setName('Fonctionnement');
            $functioningLine->setProject($project);
            $functioningLine->setType('functioning');
            $functioningLine->setAllocatedAmount(50000);
            $functioningLine->setSpentAmount(0);
            $this->entityManager->persist($functioningLine);

            $investmentLine = new ProjectBudgetLine();
            $investmentLine->setName('Investissement');
            $investmentLine->setProject($project);
            $investmentLine->setType('investment');
            $investmentLine->setAllocatedAmount(100000);
            $investmentLine->setSpentAmount(0);
            $this->entityManager->persist($investmentLine);

            $budgetLines[$project->getId()] = [
                'functioning' => $functioningLine,
                'investment' => $investmentLine
            ];

            // Create sub-lines
            $subLines = [
                'functioning' => [
                    'Fournitures de bureau' => 5000,
                    'Déplacements' => 15000,
                    'Services externes' => 20000,
                    'Communication' => 10000
                ],
                'investment' => [
                    'Équipement informatique' => 30000,
                    'Mobilier' => 20000,
                    'Véhicules' => 40000,
                    'Logiciels' => 10000
                ]
            ];

            foreach ($subLines as $type => $lines) {
                $budgetSubLines[$project->getId()][$type] = [];

                foreach ($lines as $name => $budget) {
                    $subLine = new ProjectBudgetSubLine();
                    $subLine->setName($name);
                    $subLine->setBudgetLine($budgetLines[$project->getId()][$type]);
                    $subLine->setAllocatedAmount($budget);
                    $subLine->setSpentAmount(0);
                    $this->entityManager->persist($subLine);

                    $budgetSubLines[$project->getId()][$type][$name] = $subLine;
                }
            }
        }

        $this->entityManager->flush();
        $io->success('Budget lines and sub-lines created successfully');

        // Create purchase requests
        $io->section('Creating purchase requests');
        $purchaseRequests = [];
        $statuses = [
            PurchaseRequest::STATUS_DRAFT,
            PurchaseRequest::STATUS_SUBMITTED,
            PurchaseRequest::STATUS_APPROVED,
            PurchaseRequest::STATUS_REJECTED,
            PurchaseRequest::STATUS_CONVERTED
        ];

        $requestTitles = [
            'Achat de fournitures de bureau',
            'Équipement informatique pour le projet',
            'Mobilier pour les nouveaux bureaux',
            'Services de consultation externe',
            'Matériel de formation',
            'Logiciels spécialisés',
            'Équipement de terrain',
            'Matériel de communication'
        ];

        $itemDescriptions = [
            'Papier A4 (carton de 5 ramettes)',
            'Stylos et crayons (lot de 50)',
            'Classeurs et dossiers',
            'Ordinateur portable Dell XPS 15',
            'Écran 27 pouces Dell UltraSharp',
            'Imprimante multifonction HP LaserJet',
            'Bureau ergonomique réglable',
            'Chaise de bureau ergonomique',
            'Armoire de rangement',
            'Services de consultation en gestion de projet',
            'Formation en développement agile',
            'Licence Microsoft Office 365',
            'Licence Adobe Creative Cloud',
            'Tablette Samsung Galaxy Tab S7',
            'Smartphone Samsung Galaxy S21'
        ];

        // Get the highest request number
        $highestNumber = 0;
        $existingRequests = $this->entityManager->getRepository(PurchaseRequest::class)->findAll();
        foreach ($existingRequests as $existingRequest) {
            $number = $existingRequest->getRequestNumber();
            if (preg_match('/PR-2025-(\d+)/', $number, $matches)) {
                $highestNumber = max($highestNumber, (int)$matches[1]);
            }
        }

        for ($i = 0; $i < 10; $i++) {
            $project = $projects[array_rand($projects)];
            $employee = $employees[array_rand($employees)];
            $status = $statuses[array_rand($statuses)];

            $request = new PurchaseRequest();
            $request->setRequestNumber('PR-2025-' . str_pad($highestNumber + $i + 1, 4, '0', STR_PAD_LEFT));
            $request->setTitle($requestTitles[array_rand($requestTitles)]);
            $request->setDescription('Description détaillée de la demande d\'achat #' . ($i + 1));
            $request->setRequestDate(new \DateTime('-' . rand(1, 30) . ' days'));
            $request->setNeededByDate(new \DateTime('+' . rand(10, 60) . ' days'));
            $request->setProject($project);
            $request->setRequestedBy($employee);
            $request->setCreatedBy($user);
            $request->setStatus($status);

            // Set budget line and sub-line
            $lineType = rand(0, 1) ? 'functioning' : 'investment';
            $request->setBudgetLine($budgetLines[$project->getId()][$lineType]);

            $subLineNames = array_keys($budgetSubLines[$project->getId()][$lineType]);
            $subLineName = $subLineNames[array_rand($subLineNames)];
            $request->setBudgetSubLine($budgetSubLines[$project->getId()][$lineType][$subLineName]);

            // Add approval information if approved or rejected
            if (in_array($status, [PurchaseRequest::STATUS_APPROVED, PurchaseRequest::STATUS_REJECTED, PurchaseRequest::STATUS_CONVERTED])) {
                $request->setApprovedBy($employees[array_rand($employees)]);
                $request->setApprovedAt(new \DateTime('-' . rand(1, 10) . ' days'));

                if ($status === PurchaseRequest::STATUS_REJECTED) {
                    $request->setRejectionReason('Budget insuffisant pour cette demande.');
                }
            }

            // Add items
            $itemCount = rand(1, 5);
            for ($j = 0; $j < $itemCount; $j++) {
                $item = new PurchaseRequestItem();
                $item->setDescription($itemDescriptions[array_rand($itemDescriptions)]);
                $item->setQuantity(rand(1, 10));
                $item->setUnit(rand(0, 1) ? 'pièce' : 'lot');
                $item->setUnitPrice(rand(10, 1000) * 10);
                $item->setTaxRate(rand(0, 1) ? 20 : 0);
                $item->setReference('REF-' . rand(1000, 9999));

                $request->addItem($item);
            }

            $request->updateEstimatedAmount();
            $this->entityManager->persist($request);
            $purchaseRequests[] = $request;

            $io->text('Created purchase request: ' . $request->getRequestNumber() . ' - ' . $request->getTitle());
        }

        $this->entityManager->flush();
        $io->success('Purchase requests created successfully');

        // Create purchase orders
        $io->section('Creating purchase orders');
        $purchaseOrders = [];
        $orderStatuses = [
            PurchaseOrder::STATUS_DRAFT,
            PurchaseOrder::STATUS_SENT,
            PurchaseOrder::STATUS_PARTIALLY_RECEIVED,
            PurchaseOrder::STATUS_FULLY_RECEIVED
        ];

        // Use only approved or converted purchase requests
        $validRequests = array_filter($purchaseRequests, function($request) {
            return $request->getStatus() === PurchaseRequest::STATUS_APPROVED ||
                   $request->getStatus() === PurchaseRequest::STATUS_CONVERTED;
        });

        // Convert array keys to sequential
        $validRequests = array_values($validRequests);

        // Get the highest order number
        $highestOrderNumber = 0;
        $existingOrders = $this->entityManager->getRepository(PurchaseOrder::class)->findAll();
        foreach ($existingOrders as $existingOrder) {
            $number = $existingOrder->getOrderNumber();
            if (preg_match('/PO-2025-(\d+)/', $number, $matches)) {
                $highestOrderNumber = max($highestOrderNumber, (int)$matches[1]);
            }
        }

        for ($i = 0; $i < min(5, count($validRequests)); $i++) {
            $request = $validRequests[$i];
            $project = $request->getProject();
            $partner = $partners[array_rand($partners)];
            $status = $orderStatuses[array_rand($orderStatuses)];

            $order = new PurchaseOrder();
            $order->setOrderNumber('PO-2025-' . str_pad($highestOrderNumber + $i + 1, 4, '0', STR_PAD_LEFT));
            $order->setOrderDate(new \DateTime('-' . rand(1, 20) . ' days'));
            $order->setExpectedDeliveryDate(new \DateTime('+' . rand(5, 30) . ' days'));
            $order->setSupplier($partner);
            $order->setProject($project);
            $order->setPurchaseRequest($request);
            $order->setCreatedBy($user);
            $order->setStatus($status);

            if ($status !== PurchaseOrder::STATUS_DRAFT) {
                $order->setSentAt(new \DateTime('-' . rand(1, 15) . ' days'));
                $order->setSentBy($employees[array_rand($employees)]);
            }

            // Add notes and terms
            $order->setNotes('Notes supplémentaires pour la commande #' . ($i + 1));
            $order->setDeliveryInstructions('Livrer à l\'adresse du projet pendant les heures de bureau.');
            $order->setPaymentTerms('Paiement à 30 jours après réception de la facture.');

            // Add items based on request items
            foreach ($request->getItems() as $requestItem) {
                $item = new PurchaseOrderItem();
                $item->setDescription($requestItem->getDescription());
                $item->setQuantity($requestItem->getQuantity());
                $item->setUnit($requestItem->getUnit());
                $item->setUnitPrice($requestItem->getUnitPrice());
                $item->setTaxRate($requestItem->getTaxRate());
                $item->setReference($requestItem->getReference());
                $item->setRequestItem($requestItem);

                $order->addItem($item);
            }

            $order->updateTotalAmount();
            $this->entityManager->persist($order);
            $purchaseOrders[] = $order;

            // Mark the request as converted
            if ($request->getStatus() === PurchaseRequest::STATUS_APPROVED) {
                $request->setStatus(PurchaseRequest::STATUS_CONVERTED);
                $request->setPurchaseType('po');
                $request->setPurchaseId($i + 1);
                $this->entityManager->persist($request);
            }

            $io->text('Created purchase order: ' . $order->getOrderNumber() . ' for supplier ' . $partner->getName());
        }

        $this->entityManager->flush();
        $io->success('Purchase orders created successfully');

        // Create goods receipts
        $io->section('Creating goods receipts');

        // Use only sent or partially received orders
        $validOrders = array_filter($purchaseOrders, function($order) {
            return $order->getStatus() === PurchaseOrder::STATUS_SENT ||
                   $order->getStatus() === PurchaseOrder::STATUS_PARTIALLY_RECEIVED;
        });

        // Convert array keys to sequential
        $validOrders = array_values($validOrders);

        // Get the highest receipt number
        $highestReceiptNumber = 0;
        $existingReceipts = $this->entityManager->getRepository(GoodsReceipt::class)->findAll();
        foreach ($existingReceipts as $existingReceipt) {
            $number = $existingReceipt->getReceiptNumber();
            if (preg_match('/GR-2025-(\d+)/', $number, $matches)) {
                $highestReceiptNumber = max($highestReceiptNumber, (int)$matches[1]);
            }
        }

        foreach ($validOrders as $index => $order) {
            $receipt = new GoodsReceipt();
            $receipt->setReceiptNumber('GR-2025-' . str_pad($highestReceiptNumber + $index + 1, 4, '0', STR_PAD_LEFT));
            $receipt->setReceiptDate(new \DateTime('-' . rand(1, 10) . ' days'));
            $receipt->setPurchaseOrder($order);
            $receipt->setCreatedBy($user);
            $receipt->setReceivedBy($employees[array_rand($employees)]);
            $receipt->setDeliveryNoteNumber('DN-' . rand(10000, 99999));
            $receipt->setNotes('Notes sur la réception des marchandises.');

            // Add receipt items
            foreach ($order->getItems() as $orderItem) {
                // For partially received orders, receive only some items
                if ($order->getStatus() === PurchaseOrder::STATUS_PARTIALLY_RECEIVED) {
                    // Skip some items randomly
                    if (rand(0, 1) === 0) {
                        continue;
                    }

                    // Receive partial quantity
                    $quantity = rand(1, $orderItem->getQuantity());
                } else {
                    // Receive full quantity
                    $quantity = $orderItem->getQuantity();
                }

                $receiptItem = new GoodsReceiptItem();
                $receiptItem->setPurchaseOrderItem($orderItem);
                $receiptItem->setQuantity($quantity);
                $receiptItem->setCondition(rand(0, 10) > 8 ? 'damaged' : 'good');

                $receipt->addItem($receiptItem);
            }

            $this->entityManager->persist($receipt);
            $io->text('Created goods receipt: ' . $receipt->getReceiptNumber() . ' for order ' . $order->getOrderNumber());
        }

        // Create contracts
        $io->section('Creating contracts');
        $contractStatuses = [
            Contract::STATUS_DRAFT,
            Contract::STATUS_PENDING_SIGNATURE,
            Contract::STATUS_ACTIVE,
            Contract::STATUS_COMPLETED
        ];

        // Get the highest contract number
        $highestContractNumber = 0;
        $existingContracts = $this->entityManager->getRepository(Contract::class)->findAll();
        foreach ($existingContracts as $existingContract) {
            $number = $existingContract->getContractNumber();
            if (preg_match('/CT-2025-(\d+)/', $number, $matches)) {
                $highestContractNumber = max($highestContractNumber, (int)$matches[1]);
            }
        }

        for ($i = 0; $i < 3; $i++) {
            $project = $projects[array_rand($projects)];
            $partner = $partners[array_rand($partners)];
            $status = $contractStatuses[array_rand($contractStatuses)];

            // Find an approved request to convert to contract
            $request = null;
            foreach ($purchaseRequests as $pr) {
                if ($pr->getStatus() === PurchaseRequest::STATUS_APPROVED) {
                    $request = $pr;
                    break;
                }
            }

            // If no approved request found, use any request
            if (!$request) {
                $request = $purchaseRequests[array_rand($purchaseRequests)];
            }

            $contract = new Contract();
            $contract->setContractNumber('CT-2025-' . str_pad($highestContractNumber + $i + 1, 4, '0', STR_PAD_LEFT));
            $contract->setTitle('Contrat pour ' . $request->getTitle());
            $contract->setDescription($request->getDescription());
            $contract->setStartDate(new \DateTime('-' . rand(1, 30) . ' days'));
            $contract->setEndDate(new \DateTime('+' . rand(30, 365) . ' days'));
            $contract->setSupplier($partner);
            $contract->setProject($project);
            $contract->setTotalAmount($request->getEstimatedAmount() * (1 + (rand(-10, 20) / 100))); // Vary by -10% to +20%
            $contract->setCreatedBy($user);
            $contract->setStatus($status);
            $contract->setPurchaseRequest($request);

            // Add terms
            $contract->setTerms('Termes et conditions du contrat #' . ($i + 1));
            $contract->setPaymentTerms('Paiement en 3 tranches : 30% à la signature, 40% à mi-parcours, 30% à la livraison finale.');

            // Add signature information if active or completed
            if (in_array($status, [Contract::STATUS_ACTIVE, Contract::STATUS_COMPLETED])) {
                $contract->setSignatureDate(new \DateTime('-' . rand(1, 30) . ' days'));
                $contract->setSignedBy($employees[array_rand($employees)]);

                // Mark the request as converted
                if ($request->getStatus() === PurchaseRequest::STATUS_APPROVED) {
                    $request->setStatus(PurchaseRequest::STATUS_CONVERTED);
                    $request->setPurchaseType('contract');
                    $request->setPurchaseId($i + 1);
                    $this->entityManager->persist($request);
                }
            }

            $this->entityManager->persist($contract);
            $io->text('Created contract: ' . $contract->getContractNumber() . ' with ' . $partner->getName());
        }

        $this->entityManager->flush();
        $io->success('Contracts created successfully');

        $io->success('All test data for the purchasing module has been created successfully!');

        return Command::SUCCESS;
    }

    private function createDefaultUser(): User
    {
        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setPassword('$2y$13$A.rOQu5BI0UZ.UVsKL3Qx.XZK5KKnZFjP5IWkZeNfZFXHvPQOBEfK'); // 'password'
        $user->setRoles(['ROLE_ADMIN']);
        $user->setName('Admin User');

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $user;
    }

    private function createDefaultEmployees(User $user): array
    {
        $employees = [];

        $employeeData = [
            ['John', 'Doe', 'M', '<EMAIL>', '123456789', 'Project Manager'],
            ['Jane', 'Smith', 'F', '<EMAIL>', '987654321', 'Financial Analyst'],
            ['Robert', 'Johnson', 'M', '<EMAIL>', '456789123', 'Technical Lead'],
            ['Emily', 'Williams', 'F', '<EMAIL>', '789123456', 'HR Manager'],
            ['Michael', 'Brown', 'M', '<EMAIL>', '321654987', 'Operations Manager']
        ];

        foreach ($employeeData as $index => $data) {
            $employee = new Employee();
            $employee->setFirstName($data[0]);
            $employee->setLastName($data[1]);
            $employee->setGender($data[2]);
            $employee->setEmail($data[3]);
            $employee->setPhone($data[4]);
            $employee->setPosition($data[5]);
            $employee->setHireDate(new \DateTime('-' . rand(1, 5) . ' years'));
            $employee->setUser($index === 0 ? $user : null);

            $this->entityManager->persist($employee);
            $employees[] = $employee;
        }

        $this->entityManager->flush();

        return $employees;
    }

    private function createDefaultProjects(User $user): array
    {
        $projects = [];

        $projectData = [
            ['Projet de développement durable', 'Mise en œuvre de pratiques durables dans la région', '2025-01-01', '2026-12-31', 500000],
            ['Projet d\'infrastructure IT', 'Modernisation des systèmes informatiques', '2025-02-15', '2025-12-31', 250000],
            ['Projet de formation', 'Programme de formation pour les jeunes', '2025-03-01', '2026-06-30', 150000],
            ['Projet de recherche', 'Recherche sur les énergies renouvelables', '2025-04-15', '2027-04-14', 750000]
        ];

        foreach ($projectData as $data) {
            $project = new Project();
            $project->setName($data[0]);
            $project->setDescription($data[1]);
            $project->setStartDate(new \DateTime($data[2]));
            $project->setEndDate(new \DateTime($data[3]));
            $project->setBudget($data[4]);
            $project->setStatus('active');
            $project->setCreatedBy($user);

            $this->entityManager->persist($project);
            $projects[] = $project;
        }

        $this->entityManager->flush();

        return $projects;
    }

    private function createDefaultPartners(): array
    {
        $partners = [];

        $partnerData = [
            ['TechSolutions Inc.', 'Fournisseur de solutions technologiques', '<EMAIL>', '+1234567890', 'USA'],
            ['EcoGreen Consulting', 'Consultants en développement durable', '<EMAIL>', '+2345678901', 'France'],
            ['Global Training Services', 'Services de formation professionnelle', '<EMAIL>', '+3456789012', 'UK'],
            ['Research Innovations', 'Centre de recherche et développement', '<EMAIL>', '+4567890123', 'Germany'],
            ['Office Supplies Co.', 'Fournitures de bureau et équipements', '<EMAIL>', '+5678901234', 'Canada']
        ];

        foreach ($partnerData as $data) {
            $partner = new Partner();
            $partner->setName($data[0]);
            $partner->setDescription($data[1]);
            $partner->setEmail($data[2]);
            $partner->setPhone($data[3]);
            $partner->setCountry($data[4]);
            $partner->setType('supplier');
            $partner->setStatus('active');

            $this->entityManager->persist($partner);
            $partners[] = $partner;
        }

        $this->entityManager->flush();

        return $partners;
    }
}
