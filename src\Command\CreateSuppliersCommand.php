<?php

namespace App\Command;

use App\Entity\Partner;
use App\Entity\PartnerContact;
use App\Entity\PartnerType;
use App\Entity\PartnerNature;
use App\Entity\PartnerScope;
use App\Entity\PartnerStatus;
use App\Repository\PartnerTypeRepository;
use App\Repository\PartnerNatureRepository;
use App\Repository\PartnerScopeRepository;
use App\Repository\PartnerStatusRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:create-suppliers',
    description: 'Create sample suppliers',
)]
class CreateSuppliersCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private PartnerTypeRepository $partnerTypeRepository;
    private PartnerNatureRepository $partnerNatureRepository;
    private PartnerScopeRepository $partnerScopeRepository;
    private PartnerStatusRepository $partnerStatusRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        PartnerTypeRepository $partnerTypeRepository,
        PartnerNatureRepository $partnerNatureRepository,
        PartnerScopeRepository $partnerScopeRepository,
        PartnerStatusRepository $partnerStatusRepository
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->partnerTypeRepository = $partnerTypeRepository;
        $this->partnerNatureRepository = $partnerNatureRepository;
        $this->partnerScopeRepository = $partnerScopeRepository;
        $this->partnerStatusRepository = $partnerStatusRepository;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Creating sample suppliers');

        // Get required entities
        $supplierType = $this->partnerTypeRepository->findOneBy(['code' => 'supplier']);
        if (!$supplierType) {
            $io->error('Supplier type not found. Please run app:init-partner-parameters first.');
            return Command::FAILURE;
        }

        $companyNature = $this->partnerNatureRepository->findOneBy(['code' => 'company']);
        if (!$companyNature) {
            $io->error('Company nature not found. Please run app:init-partner-parameters first.');
            return Command::FAILURE;
        }

        $nationalScope = $this->partnerScopeRepository->findOneBy(['code' => 'national']);
        $internationalScope = $this->partnerScopeRepository->findOneBy(['code' => 'international']);
        if (!$nationalScope || !$internationalScope) {
            $io->error('Partner scopes not found. Please run app:init-partner-parameters first.');
            return Command::FAILURE;
        }

        $activeStatus = $this->partnerStatusRepository->findOneBy(['code' => 'contract_signed']);
        if (!$activeStatus) {
            $io->error('Contract signed status not found. Please run app:init-partner-parameters first.');
            return Command::FAILURE;
        }

        $suppliers = [
            [
                'name' => 'Fournitures Bureau Pro',
                'type' => 'supplier',
                'nature' => 'company',
                'structure' => 'national',
                'address' => '123 Avenue des Fournitures, Casablanca',
                'phone' => '0522123456',
                'email' => '<EMAIL>',
                'website' => 'www.fournituresbureau.ma',
                'contacts' => [
                    [
                        'firstName' => 'Mohammed',
                        'lastName' => 'Alami',
                        'position' => 'Directeur Commercial',
                        'email' => '<EMAIL>',
                        'phone' => '0661234567'
                    ]
                ]
            ],
            [
                'name' => 'Informatique Solutions',
                'type' => 'supplier',
                'nature' => 'company',
                'structure' => 'national',
                'address' => '45 Rue des Ordinateurs, Rabat',
                'phone' => '0537654321',
                'email' => '<EMAIL>',
                'website' => 'www.informatiquesolutions.ma',
                'contacts' => [
                    [
                        'firstName' => 'Samira',
                        'lastName' => 'Bennani',
                        'position' => 'Responsable Ventes',
                        'email' => '<EMAIL>',
                        'phone' => '0662345678'
                    ]
                ]
            ],
            [
                'name' => 'Mobilier Professionnel',
                'type' => 'supplier',
                'nature' => 'company',
                'structure' => 'national',
                'address' => '78 Boulevard des Meubles, Tanger',
                'phone' => '0539876543',
                'email' => '<EMAIL>',
                'website' => 'www.mobilierpro.ma',
                'contacts' => [
                    [
                        'firstName' => 'Karim',
                        'lastName' => 'Tazi',
                        'position' => 'Gérant',
                        'email' => '<EMAIL>',
                        'phone' => '0663456789'
                    ]
                ]
            ],
            [
                'name' => 'Tech Import',
                'type' => 'supplier',
                'nature' => 'company',
                'structure' => 'international',
                'address' => '12 Rue de l\'Innovation, Marrakech',
                'phone' => '0524987654',
                'email' => '<EMAIL>',
                'website' => 'www.techimport.ma',
                'contacts' => [
                    [
                        'firstName' => 'Nadia',
                        'lastName' => 'El Fassi',
                        'position' => 'Directrice',
                        'email' => '<EMAIL>',
                        'phone' => '0664567890'
                    ]
                ]
            ],
            [
                'name' => 'Papeterie Centrale',
                'type' => 'supplier',
                'nature' => 'company',
                'structure' => 'national',
                'address' => '34 Avenue du Papier, Fès',
                'phone' => '0535123456',
                'email' => '<EMAIL>',
                'website' => 'www.papeteriecentrale.ma',
                'contacts' => [
                    [
                        'firstName' => 'Hassan',
                        'lastName' => 'Berrada',
                        'position' => 'Responsable Commercial',
                        'email' => '<EMAIL>',
                        'phone' => '0665678901'
                    ]
                ]
            ]
        ];

        $count = 0;
        foreach ($suppliers as $supplierData) {
            $supplier = new Partner();
            $supplier->setName($supplierData['name']);
            $supplier->setType($supplierType);
            $supplier->setNature($companyNature);
            $supplier->setScope($supplierData['structure'] === 'international' ? $internationalScope : $nationalScope);
            $supplier->setPhone($supplierData['phone']);
            $supplier->setEmail($supplierData['email']);
            $supplier->setWebsite($supplierData['website']);
            $supplier->setStatus($activeStatus);
            $supplier->setIsSupplier(true);

            // Set custom attributes for address
            $supplier->setCustomAttributes([
                'address' => $supplierData['address']
            ]);

            // Set contact information directly on the partner
            if (!empty($supplierData['contacts'])) {
                $contactData = $supplierData['contacts'][0]; // Use the first contact
                $supplier->setContactPerson($contactData['firstName'] . ' ' . $contactData['lastName']);
                $supplier->setContactEmail($contactData['email']);
                $supplier->setContactPhone($contactData['phone']);
            }

            $this->entityManager->persist($supplier);
            $count++;
        }

        $this->entityManager->flush();
        $io->success(sprintf('Created %d suppliers successfully.', $count));

        return Command::SUCCESS;
    }
}
