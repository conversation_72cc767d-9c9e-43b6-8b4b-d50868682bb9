<?php

namespace App\Command;

use App\Entity\Department;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:fix-department-hierarchy',
    description: 'Fix department hierarchy',
)]
class FixDepartmentHierarchyCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Correction de la hiérarchie des départements');

        $departmentRepository = $this->entityManager->getRepository(Department::class);
        
        // 1. Direction Générale doit être le seul département racine
        $directionGenerale = $departmentRepository->findOneBy(['code' => 'DG']);
        if (!$directionGenerale) {
            $io->error('Département "Direction Générale" non trouvé');
            return Command::FAILURE;
        }
        
        $io->section('Mise à jour des départements de premier niveau');
        
        // 2. Les directions doivent être sous la Direction Générale
        $firstLevelDepartments = [
            'DC' => 'Direction Commerciale',
            'DF' => 'Direction Financière',
            'DO' => 'Direction des Opérations',
            'DRH' => 'Direction des Ressources Humaines',
            'DSI' => 'Direction des Systèmes d\'Information'
        ];
        
        foreach ($firstLevelDepartments as $code => $name) {
            $department = $departmentRepository->findOneBy(['code' => $code]);
            if ($department) {
                $io->text("Mise à jour de {$department->getName()} (code: {$department->getCode()})");
                $department->setParentDepartment($directionGenerale);
                $this->entityManager->persist($department);
            } else {
                $io->warning("Département {$name} (code: {$code}) non trouvé");
            }
        }
        
        // 3. Mettre à jour les autres départements racines
        $io->section('Mise à jour des autres départements racines');
        
        $otherDepartments = [
            'COM' => 'DC', // Commercial sous Direction Commerciale
            'FIN' => 'DF', // Finance sous Direction Financière
            'IT' => 'DSI',  // Informatique sous Direction des Systèmes d'Information
            'R&D' => 'DO',  // Recherche et Développement sous Direction des Opérations
            'RH' => 'DRH'   // Ressources Humaines sous Direction des Ressources Humaines
        ];
        
        foreach ($otherDepartments as $deptCode => $parentCode) {
            $department = $departmentRepository->findOneBy(['code' => $deptCode]);
            $parentDepartment = $departmentRepository->findOneBy(['code' => $parentCode]);
            
            if ($department && $parentDepartment) {
                $io->text("Mise à jour de {$department->getName()} (code: {$department->getCode()}) -> parent: {$parentDepartment->getName()}");
                $department->setParentDepartment($parentDepartment);
                $this->entityManager->persist($department);
            } else {
                $io->warning("Département {$deptCode} ou parent {$parentCode} non trouvé");
            }
        }
        
        // 4. Appliquer les modifications
        $this->entityManager->flush();
        
        $io->success('Hiérarchie des départements corrigée avec succès');
        
        return Command::SUCCESS;
    }
}
