<?php

namespace App\Command;

use App\Entity\PartnerStatus;
use App\Entity\PartnerType;
use App\Entity\PartnerNature;
use App\Entity\PartnerScope;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:init-partner-parameters',
    description: 'Initialize partner parameters (status, type, nature)',
)]
class InitPartnerParametersCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Initializing partner parameters');

        // Initialize partner statuses
        $io->section('Initializing partner statuses');
        $this->initializePartnerStatuses($io);

        // Initialize partner types
        $io->section('Initializing partner types');
        $this->initializePartnerTypes($io);

        // Initialize partner natures
        $io->section('Initializing partner natures');
        $this->initializePartnerNatures($io);

        // Initialize partner scopes
        $io->section('Initializing partner scopes');
        $this->initializePartnerScopes($io);

        $io->success('Partner parameters initialized successfully.');

        return Command::SUCCESS;
    }

    private function initializePartnerStatuses(SymfonyStyle $io): void
    {
        $statuses = [
            [
                'name' => 'Prospection',
                'code' => 'prospection',
                'description' => 'Partenaire en phase de prospection',
                'displayOrder' => 10,
                'color' => '#FFC107',
                'isDefault' => true
            ],
            [
                'name' => 'Premier contact par email',
                'code' => 'first_contact_email',
                'description' => 'Premier contact établi par email',
                'displayOrder' => 20,
                'color' => '#17A2B8'
            ],
            [
                'name' => 'Premier contact téléphonique',
                'code' => 'first_contact_phone',
                'description' => 'Premier contact établi par téléphone',
                'displayOrder' => 30,
                'color' => '#007BFF'
            ],
            [
                'name' => 'Rencontre physique',
                'code' => 'physical_meeting',
                'description' => 'Rencontre physique effectuée',
                'displayOrder' => 40,
                'color' => '#6610F2'
            ],
            [
                'name' => 'Proposition de projet',
                'code' => 'project_proposal',
                'description' => 'Proposition de projet envoyée',
                'displayOrder' => 50,
                'color' => '#6F42C1'
            ],
            [
                'name' => 'Négociation',
                'code' => 'negotiation',
                'description' => 'En phase de négociation',
                'displayOrder' => 60,
                'color' => '#FD7E14'
            ],
            [
                'name' => 'Contrat signé',
                'code' => 'contract_signed',
                'description' => 'Contrat signé, partenariat actif',
                'displayOrder' => 70,
                'color' => '#28A745'
            ],
            [
                'name' => 'Inactif',
                'code' => 'inactive',
                'description' => 'Partenariat inactif',
                'displayOrder' => 80,
                'color' => '#DC3545'
            ]
        ];

        $repository = $this->entityManager->getRepository(PartnerStatus::class);
        $count = 0;

        foreach ($statuses as $statusData) {
            $existingStatus = $repository->findOneBy(['code' => $statusData['code']]);

            if (!$existingStatus) {
                $status = new PartnerStatus();
                $status->setName($statusData['name']);
                $status->setCode($statusData['code']);
                $status->setDescription($statusData['description']);
                $status->setDisplayOrder($statusData['displayOrder']);
                $status->setColor($statusData['color']);
                $status->setIsActive(true);
                $status->setIsDefault($statusData['isDefault'] ?? false);

                $this->entityManager->persist($status);
                $count++;
            }
        }

        $this->entityManager->flush();
        $io->success(sprintf('%d partner statuses created.', $count));
    }

    private function initializePartnerTypes(SymfonyStyle $io): void
    {
        $types = [
            [
                'name' => 'Client',
                'code' => 'client',
                'description' => 'Client achetant nos produits ou services',
                'displayOrder' => 10,
                'isDefault' => true
            ],
            [
                'name' => 'Fournisseur',
                'code' => 'supplier',
                'description' => 'Fournisseur de produits ou services',
                'displayOrder' => 20
            ],
            [
                'name' => 'Prestataire',
                'code' => 'service_provider',
                'description' => 'Prestataire de services',
                'displayOrder' => 30
            ],
            [
                'name' => 'Distributeur',
                'code' => 'distributor',
                'description' => 'Distributeur de nos produits',
                'displayOrder' => 40
            ],
            [
                'name' => 'Revendeur',
                'code' => 'reseller',
                'description' => 'Revendeur de nos produits',
                'displayOrder' => 50
            ],
            [
                'name' => 'Partenaire stratégique',
                'code' => 'strategic_partner',
                'description' => 'Partenaire stratégique pour notre développement',
                'displayOrder' => 60
            ]
        ];

        $repository = $this->entityManager->getRepository(PartnerType::class);
        $count = 0;

        foreach ($types as $typeData) {
            $existingType = $repository->findOneBy(['code' => $typeData['code']]);

            if (!$existingType) {
                $type = new PartnerType();
                $type->setName($typeData['name']);
                $type->setCode($typeData['code']);
                $type->setDescription($typeData['description']);
                $type->setDisplayOrder($typeData['displayOrder']);
                $type->setIsActive(true);
                $type->setIsDefault($typeData['isDefault'] ?? false);

                $this->entityManager->persist($type);
                $count++;
            }
        }

        $this->entityManager->flush();
        $io->success(sprintf('%d partner types created.', $count));
    }

    private function initializePartnerNatures(SymfonyStyle $io): void
    {
        $natures = [
            [
                'name' => 'Entreprise',
                'code' => 'company',
                'description' => 'Entreprise (SA, SARL, SAS, etc.)',
                'displayOrder' => 10,
                'isDefault' => true
            ],
            [
                'name' => 'Particulier',
                'code' => 'individual',
                'description' => 'Personne physique',
                'displayOrder' => 20
            ],
            [
                'name' => 'Association',
                'code' => 'association',
                'description' => 'Association à but non lucratif',
                'displayOrder' => 30
            ],
            [
                'name' => 'Organisme public',
                'code' => 'public_organization',
                'description' => 'Organisme public ou administration',
                'displayOrder' => 40
            ],
            [
                'name' => 'Auto-entrepreneur',
                'code' => 'self_employed',
                'description' => 'Travailleur indépendant ou auto-entrepreneur',
                'displayOrder' => 50
            ]
        ];

        $repository = $this->entityManager->getRepository(PartnerNature::class);
        $count = 0;

        foreach ($natures as $natureData) {
            $existingNature = $repository->findOneBy(['code' => $natureData['code']]);

            if (!$existingNature) {
                $nature = new PartnerNature();
                $nature->setName($natureData['name']);
                $nature->setCode($natureData['code']);
                $nature->setDescription($natureData['description']);
                $nature->setDisplayOrder($natureData['displayOrder']);
                $nature->setIsActive(true);
                $nature->setIsDefault($natureData['isDefault'] ?? false);

                $this->entityManager->persist($nature);
                $count++;
            }
        }

        $this->entityManager->flush();
        $io->success(sprintf('%d partner natures created.', $count));
    }

    private function initializePartnerScopes(SymfonyStyle $io): void
    {
        $scopes = [
            [
                'name' => 'National',
                'code' => 'national',
                'description' => 'Partenaire opérant au niveau national',
                'displayOrder' => 10,
                'isDefault' => true
            ],
            [
                'name' => 'International',
                'code' => 'international',
                'description' => 'Partenaire opérant à l\'international',
                'displayOrder' => 20
            ],
            [
                'name' => 'Régional',
                'code' => 'regional',
                'description' => 'Partenaire opérant au niveau régional',
                'displayOrder' => 30
            ],
            [
                'name' => 'Local',
                'code' => 'local',
                'description' => 'Partenaire opérant au niveau local',
                'displayOrder' => 40
            ],
            [
                'name' => 'Européen',
                'code' => 'european',
                'description' => 'Partenaire opérant au niveau européen',
                'displayOrder' => 50
            ],
            [
                'name' => 'Multinational',
                'code' => 'multinational',
                'description' => 'Partenaire multinational avec présence dans plusieurs pays',
                'displayOrder' => 60
            ]
        ];

        $repository = $this->entityManager->getRepository(PartnerScope::class);
        $count = 0;

        foreach ($scopes as $scopeData) {
            $existingScope = $repository->findOneBy(['code' => $scopeData['code']]);

            if (!$existingScope) {
                $scope = new PartnerScope();
                $scope->setName($scopeData['name']);
                $scope->setCode($scopeData['code']);
                $scope->setDescription($scopeData['description']);
                $scope->setDisplayOrder($scopeData['displayOrder']);
                $scope->setIsActive(true);
                $scope->setIsDefault($scopeData['isDefault'] ?? false);

                $this->entityManager->persist($scope);
                $count++;
            }
        }

        $this->entityManager->flush();
        $io->success(sprintf('%d partner scopes created.', $count));
    }
}
