<?php

namespace App\Command;

use App\Service\PermissionService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:init-permissions',
    description: 'Initialize system permissions and create admin role',
)]
class InitPermissionsCommand extends Command
{
    private PermissionService $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        parent::__construct();
        $this->permissionService = $permissionService;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->section('Initializing system permissions');
        $this->permissionService->initializePermissions();
        $io->success('Permissions initialized successfully');

        $io->section('Creating admin role with all permissions');
        $adminRole = $this->permissionService->createAdminRole();
        $io->success(sprintf('Admin role "%s" created successfully', $adminRole->getName()));

        return Command::SUCCESS;
    }
}
