<?php

namespace App\Command;

use App\Entity\Department;
use App\Entity\Position;
use App\Entity\ProductCategory;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:init-test-data',
    description: 'Initialize test data for departments, positions, and product categories',
)]
class InitTestDataCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->section('Initializing test data');

        // Initialiser les départements
        $io->info('Creating departments...');
        $departments = $this->createDepartments();
        $io->success(sprintf('Created %d departments', count($departments)));

        // Initialiser les postes
        $io->info('Creating positions...');
        $positions = $this->createPositions($departments);
        $io->success(sprintf('Created %d positions', count($positions)));

        // Initialiser les catégories de produits
        $io->info('Creating product categories...');
        $categories = $this->createProductCategories();
        $io->success(sprintf('Created %d product categories', count($categories)));

        $io->success('Test data initialized successfully');

        return Command::SUCCESS;
    }

    private function createDepartments(): array
    {
        $departments = [];

        // Fonction pour créer un département
        $createDepartment = function (string $name, string $code, string $description, ?Department $parent = null) use (&$departments) {
            $department = new Department();
            $department->setName($name);
            $department->setCode($code);
            $department->setDescription($description);
            $department->setIsActive(true);
            $department->setCreatedAt(new \DateTime());
            $department->setUpdatedAt(new \DateTime());
            if ($parent) {
                $department->setParentDepartment($parent);
            }
            $this->entityManager->persist($department);
            $departments[$code] = $department;
            return $department;
        };

        // Départements principaux
        $directionGenerale = $createDepartment('Direction Générale', 'DG', 'Direction générale de l\'entreprise');
        $directionFinanciere = $createDepartment('Direction Financière', 'DF', 'Direction financière et comptable', $directionGenerale);

        $directionRH = $createDepartment('Direction des Ressources Humaines', 'DRH', 'Direction des ressources humaines', $directionGenerale);
        $directionIT = $createDepartment('Direction des Systèmes d\'Information', 'DSI', 'Direction des systèmes d\'information', $directionGenerale);
        $directionCommerciale = $createDepartment('Direction Commerciale', 'DC', 'Direction commerciale et marketing', $directionGenerale);
        $directionOperations = $createDepartment('Direction des Opérations', 'DO', 'Direction des opérations et de la production', $directionGenerale);

        // Sous-départements
        $comptabilite = $createDepartment('Comptabilité', 'COMPTA', 'Service comptabilité', $directionFinanciere);
        $tresorerie = $createDepartment('Trésorerie', 'TRESO', 'Service trésorerie', $directionFinanciere);
        $recrutement = $createDepartment('Recrutement', 'RECRU', 'Service recrutement', $directionRH);
        $formation = $createDepartment('Formation', 'FORM', 'Service formation', $directionRH);
        $developpement = $createDepartment('Développement', 'DEV', 'Service développement informatique', $directionIT);
        $infrastructure = $createDepartment('Infrastructure', 'INFRA', 'Service infrastructure informatique', $directionIT);
        $ventes = $createDepartment('Ventes', 'VENTES', 'Service ventes', $directionCommerciale);
        $marketing = $createDepartment('Marketing', 'MKT', 'Service marketing', $directionCommerciale);
        $production = $createDepartment('Production', 'PROD', 'Service production', $directionOperations);
        $logistique = $createDepartment('Logistique', 'LOG', 'Service logistique', $directionOperations);

        $this->entityManager->flush();

        return $departments;
    }

    private function createPositions(array $departments): array
    {
        $positions = [];

        // Fonction pour créer un poste
        $createPosition = function (string $name, string $description, Department $department, ?float $minSalary = null, ?float $maxSalary = null) use (&$positions) {
            $position = new Position();
            $position->setTitle($name);
            $position->setCode(strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $name), 0, 10)));
            $position->setDescription($description);
            $position->setDepartment($department);
            $position->setIsActive(true);
            $position->setCreatedAt(new \DateTime());
            $position->setUpdatedAt(new \DateTime());
            $this->entityManager->persist($position);
            $positions[] = $position;
            return $position;
        };

        // Direction Générale
        $directeurGeneral = $createPosition('Directeur Général', 'Directeur général de l\'entreprise', $departments['DG'], 30000, 50000);

        $assistantDG = $createPosition('Assistant(e) de Direction', 'Assistant(e) de la direction générale', $departments['DG'], 8000, 12000);

        // Direction Financière
        $directeurFinancier = $createPosition('Directeur Financier', 'Directeur financier de l\'entreprise', $departments['DF'], 20000, 35000);
        $comptable = $createPosition('Comptable', 'Comptable', $departments['COMPTA'], 7000, 12000);
        $tresorier = $createPosition('Trésorier', 'Trésorier', $departments['TRESO'], 8000, 14000);

        // Direction RH
        $directeurRH = $createPosition('Directeur des Ressources Humaines', 'Directeur des ressources humaines', $departments['DRH'], 18000, 30000);
        $recruteur = $createPosition('Chargé de Recrutement', 'Chargé de recrutement', $departments['RECRU'], 8000, 15000);
        $formateurRH = $createPosition('Responsable Formation', 'Responsable formation', $departments['FORM'], 9000, 16000);

        // Direction IT
        $directeurIT = $createPosition('Directeur des Systèmes d\'Information', 'Directeur des systèmes d\'information', $departments['DSI'], 20000, 35000);
        $developpeur = $createPosition('Développeur', 'Développeur informatique', $departments['DEV'], 10000, 18000);
        $adminSys = $createPosition('Administrateur Système', 'Administrateur système', $departments['INFRA'], 12000, 20000);

        // Direction Commerciale
        $directeurCommercial = $createPosition('Directeur Commercial', 'Directeur commercial', $departments['DC'], 18000, 30000);
        $commercialVente = $createPosition('Commercial', 'Commercial', $departments['VENTES'], 8000, 15000);
        $responsableMarketing = $createPosition('Responsable Marketing', 'Responsable marketing', $departments['MKT'], 12000, 20000);

        // Direction des Opérations
        $directeurOperations = $createPosition('Directeur des Opérations', 'Directeur des opérations', $departments['DO'], 18000, 30000);
        $chefProduction = $createPosition('Chef de Production', 'Chef de production', $departments['PROD'], 12000, 20000);
        $responsableLogistique = $createPosition('Responsable Logistique', 'Responsable logistique', $departments['LOG'], 10000, 18000);

        $this->entityManager->flush();

        return $positions;
    }

    private function createProductCategories(): array
    {
        $categories = [];

        // Fonction pour créer une catégorie de produit
        $createCategory = function (string $name, string $description, ?ProductCategory $parent = null) use (&$categories) {
            $category = new ProductCategory();
            $category->setName($name);
            $category->setDescription($description);
            $category->setIsActive(true);
            $category->setDisplayOrder(count($categories) + 1);
            if ($parent) {
                $category->setParent($parent);
            }
            $this->entityManager->persist($category);
            $categories[] = $category;
            return $category;
        };

        // Catégories principales
        $informatique = $createCategory('Informatique', 'Matériel et logiciels informatiques');

        $bureautique = $createCategory('Bureautique', 'Fournitures et équipements de bureau');
        $mobilier = $createCategory('Mobilier', 'Mobilier de bureau');
        $consommables = $createCategory('Consommables', 'Consommables divers');

        // Sous-catégories Informatique
        $ordinateurs = $createCategory('Ordinateurs', 'Ordinateurs de bureau et portables', $informatique);
        $peripheriques = $createCategory('Périphériques', 'Périphériques informatiques', $informatique);
        $logiciels = $createCategory('Logiciels', 'Logiciels et licences', $informatique);
        $reseaux = $createCategory('Réseaux', 'Équipements réseaux', $informatique);

        // Sous-catégories Bureautique
        $papeterie = $createCategory('Papeterie', 'Articles de papeterie', $bureautique);
        $classement = $createCategory('Classement', 'Articles de classement', $bureautique);
        $ecriture = $createCategory('Écriture', 'Articles d\'écriture', $bureautique);

        // Sous-catégories Mobilier
        $bureaux = $createCategory('Bureaux', 'Bureaux et tables', $mobilier);
        $sieges = $createCategory('Sièges', 'Chaises et fauteuils', $mobilier);
        $rangement = $createCategory('Rangement', 'Meubles de rangement', $mobilier);

        // Sous-catégories Consommables
        $encre = $createCategory('Encre et toners', 'Cartouches d\'encre et toners', $consommables);
        $papier = $createCategory('Papier', 'Papier pour impression et photocopie', $consommables);

        $this->entityManager->flush();

        return $categories;
    }
}
