<?php

namespace App\Command;

use App\Entity\Department;
use App\Entity\Employee;
use App\Entity\Position;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:merge-recruitment-departments',
    description: 'Merge duplicate recruitment departments',
)]
class MergeRecruitmentDepartmentsCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Fusion des départements de recrutement');

        $departmentRepository = $this->entityManager->getRepository(Department::class);

        // Identifier les deux départements de recrutement
        $recruitmentREC = $departmentRepository->findOneBy(['code' => 'REC']);
        $recruitmentRECRU = $departmentRepository->findOneBy(['code' => 'RECRU']);

        if (!$recruitmentREC || !$recruitmentRECRU) {
            $io->error('Un ou plusieurs départements de recrutement non trouvés');
            return Command::FAILURE;
        }

        $io->section('Informations sur les départements');
        $io->table(
            ['ID', 'Nom', 'Code', 'Parent', 'Employés', 'Positions'],
            [
                [
                    $recruitmentREC->getId(),
                    $recruitmentREC->getName(),
                    $recruitmentREC->getCode(),
                    $recruitmentREC->getParentDepartment() ? $recruitmentREC->getParentDepartment()->getName() : 'Aucun',
                    count($this->entityManager->getRepository(Employee::class)->findBy(['department' => $recruitmentREC])),
                    count($recruitmentREC->getPositions())
                ],
                [
                    $recruitmentRECRU->getId(),
                    $recruitmentRECRU->getName(),
                    $recruitmentRECRU->getCode(),
                    $recruitmentRECRU->getParentDepartment() ? $recruitmentRECRU->getParentDepartment()->getName() : 'Aucun',
                    count($this->entityManager->getRepository(Employee::class)->findBy(['department' => $recruitmentRECRU])),
                    count($recruitmentRECRU->getPositions())
                ]
            ]
        );

        // Déterminer quel département conserver (celui avec le plus de positions)
        $departmentToKeep = count($recruitmentREC->getPositions()) >= count($recruitmentRECRU->getPositions())
            ? $recruitmentREC
            : $recruitmentRECRU;

        $departmentToRemove = $departmentToKeep === $recruitmentREC ? $recruitmentRECRU : $recruitmentREC;

        $io->section('Département à conserver');
        $io->text("ID: {$departmentToKeep->getId()}, Nom: {$departmentToKeep->getName()}, Code: {$departmentToKeep->getCode()}");

        $io->section('Département à supprimer');
        $io->text("ID: {$departmentToRemove->getId()}, Nom: {$departmentToRemove->getName()}, Code: {$departmentToRemove->getCode()}");

        // Mettre à jour les positions du département à supprimer
        $positions = $departmentToRemove->getPositions();
        $io->section('Mise à jour des positions');

        foreach ($positions as $position) {
            $io->text("Mise à jour de la position ID {$position->getId()} ({$position->getTitle()})");
            $position->setDepartment($departmentToKeep);
            $this->entityManager->persist($position);
        }

        // Mettre à jour les employés du département à supprimer
        $employees = $this->entityManager->getRepository(Employee::class)->findBy(['department' => $departmentToRemove]);
        $io->section('Mise à jour des employés');

        foreach ($employees as $employee) {
            $io->text("Mise à jour de l'employé ID {$employee->getId()}");
            $employee->setDepartment($departmentToKeep);
            $this->entityManager->persist($employee);
        }

        // Mettre à jour les sous-départements
        $childDepartments = $departmentToRemove->getChildDepartments();
        $io->section('Mise à jour des sous-départements');

        foreach ($childDepartments as $child) {
            $io->text("Mise à jour du sous-département ID {$child->getId()} ({$child->getName()})");
            $child->setParentDepartment($departmentToKeep);
            $this->entityManager->persist($child);
        }

        // Appliquer les modifications avant de supprimer le département
        $this->entityManager->flush();

        // Supprimer le département redondant
        try {
            $this->entityManager->remove($departmentToRemove);
            $this->entityManager->flush();
            $io->success("Département ID {$departmentToRemove->getId()} supprimé avec succès");
        } catch (\Exception $e) {
            $io->error("Erreur lors de la suppression du département ID {$departmentToRemove->getId()}: " . $e->getMessage());
            return Command::FAILURE;
        }

        $io->success('Fusion des départements de recrutement terminée avec succès');

        return Command::SUCCESS;
    }
}
