<?php

namespace App\Command;

use App\Entity\Department;
use App\Entity\Employee;
use App\Entity\Position;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:migrate-departments',
    description: 'Migrate existing employees to use Department and Position entities',
)]
class MigrateDepartmentsCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Migrating employees to use Department and Position entities');

        // 1. Get all existing employees
        $employees = $this->entityManager->getRepository(Employee::class)->findAll();
        $io->info(sprintf('Found %d employees to migrate', count($employees)));

        // 2. Create a map to store unique departments and positions
        $departmentMap = [];
        $positionMap = [];

        // 3. Collect unique department names and job titles
        foreach ($employees as $employee) {
            $departmentName = $employee->getDepartment();
            $jobTitle = $employee->getJobTitle();

            if (!isset($departmentMap[$departmentName])) {
                $departmentMap[$departmentName] = null;
            }

            $positionKey = $departmentName . '|' . $jobTitle;
            if (!isset($positionMap[$positionKey])) {
                $positionMap[$positionKey] = [
                    'title' => $jobTitle,
                    'department' => $departmentName
                ];
            }
        }

        // 4. Create Department entities
        $io->section('Creating departments');
        $progressBar = $io->createProgressBar(count($departmentMap));
        $progressBar->start();

        foreach ($departmentMap as $name => $value) {
            $department = new Department();
            $department->setName($name);
            $department->setCode(strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $name), 0, 5)));
            $department->setIsActive(true);
            $this->entityManager->persist($department);
            $departmentMap[$name] = $department;
            $progressBar->advance();
        }

        $this->entityManager->flush();
        $progressBar->finish();
        $io->newLine(2);
        $io->success(sprintf('Created %d departments', count($departmentMap)));

        // 5. Create Position entities
        $io->section('Creating positions');
        $progressBar = $io->createProgressBar(count($positionMap));
        $progressBar->start();

        foreach ($positionMap as $key => $data) {
            $position = new Position();
            $position->setTitle($data['title']);
            $position->setCode(strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $data['title']), 0, 5)));
            $position->setIsActive(true);
            $position->setDepartment($departmentMap[$data['department']]);
            $this->entityManager->persist($position);
            $positionMap[$key] = $position;
            $progressBar->advance();
        }

        $this->entityManager->flush();
        $progressBar->finish();
        $io->newLine(2);
        $io->success(sprintf('Created %d positions', count($positionMap)));

        // 6. Update employees to use the new entities
        $io->section('Updating employees');
        $progressBar = $io->createProgressBar(count($employees));
        $progressBar->start();

        foreach ($employees as $employee) {
            $departmentName = $employee->getDepartment();
            $jobTitle = $employee->getJobTitle();
            $positionKey = $departmentName . '|' . $jobTitle;

            // Set the new department and position
            $employee->setDepartment($departmentMap[$departmentName]);
            $employee->setPosition($positionMap[$positionKey]);

            $progressBar->advance();
        }

        $this->entityManager->flush();
        $progressBar->finish();
        $io->newLine(2);
        $io->success('All employees have been updated successfully');

        return Command::SUCCESS;
    }
}
