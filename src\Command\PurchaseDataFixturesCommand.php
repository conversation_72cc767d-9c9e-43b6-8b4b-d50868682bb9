<?php

namespace App\Command;

use App\Entity\Employee;
use App\Entity\GoodsReceipt;
use App\Entity\GoodsReceiptItem;
use App\Entity\Invoice;
use App\Entity\InvoiceItem;
use App\Entity\Partner;
use App\Entity\Product;
use App\Entity\Project;
use App\Entity\PurchaseOrder;
use App\Entity\PurchaseOrderItem;
use App\Entity\PurchaseRequest;
use App\Entity\PurchaseRequestItem;
use App\Entity\Quote;
use App\Entity\QuoteItem;
use App\Entity\User;
use DateTime;
use DateTimeImmutable;
use App\Repository\EmployeeRepository;
use App\Repository\PartnerRepository;
use App\Repository\ProductRepository;
use App\Repository\ProjectRepository;
use App\Repository\UserRepository;
use App\Service\GoodsReceiptService;
use App\Service\InvoiceService;
use App\Service\PurchaseOrderService;
use App\Service\PurchaseRequestService;
use App\Service\QuoteService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:purchase-data:load',
    description: 'Load purchase related fixtures',
)]
class PurchaseDataFixturesCommand extends Command
{
    private EntityManagerInterface $entityManager;
    private UserRepository $userRepository;
    private EmployeeRepository $employeeRepository;
    private ProjectRepository $projectRepository;
    private PartnerRepository $partnerRepository;
    private ProductRepository $productRepository;
    private PurchaseRequestService $purchaseRequestService;
    private PurchaseOrderService $purchaseOrderService;
    private QuoteService $quoteService;
    private GoodsReceiptService $goodsReceiptService;
    private InvoiceService $invoiceService;

    public function __construct(
        EntityManagerInterface $entityManager,
        UserRepository $userRepository,
        EmployeeRepository $employeeRepository,
        ProjectRepository $projectRepository,
        PartnerRepository $partnerRepository,
        ProductRepository $productRepository,
        PurchaseRequestService $purchaseRequestService,
        PurchaseOrderService $purchaseOrderService,
        QuoteService $quoteService,
        GoodsReceiptService $goodsReceiptService,
        InvoiceService $invoiceService
    ) {
        parent::__construct();
        $this->entityManager = $entityManager;
        $this->userRepository = $userRepository;
        $this->employeeRepository = $employeeRepository;
        $this->projectRepository = $projectRepository;
        $this->partnerRepository = $partnerRepository;
        $this->productRepository = $productRepository;
        $this->purchaseRequestService = $purchaseRequestService;
        $this->purchaseOrderService = $purchaseOrderService;
        $this->quoteService = $quoteService;
        $this->goodsReceiptService = $goodsReceiptService;
        $this->invoiceService = $invoiceService;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Loading purchase related fixtures');

        try {
            // Get references
            $admin = $this->userRepository->find(1); // Use the admin user with ID 1
            if (!$admin) {
                $io->error('Admin user with ID 1 not found. Please check your database.');
                return Command::FAILURE;
            }

            $io->text('Using admin user: ' . $admin->getEmail());

            $employees = $this->employeeRepository->findAll();
            if (empty($employees)) {
                $io->error('No employees found. Please add employees first.');
                return Command::FAILURE;
            }

            $projects = $this->projectRepository->findAll();
            if (empty($projects)) {
                $io->error('No projects found. Please add projects first.');
                return Command::FAILURE;
            }

            $suppliers = $this->partnerRepository->findBy(['isSupplier' => true]);
            if (empty($suppliers)) {
                $io->error('No suppliers found. Please add suppliers first.');
                return Command::FAILURE;
            }

            $products = $this->productRepository->findAll();
            if (empty($products)) {
                $io->error('No products found. Please add products first.');
                return Command::FAILURE;
            }

            // Create purchase requests
            $io->section('Creating purchase requests');
            $purchaseRequests = $this->createPurchaseRequests($admin, $employees, $projects, $products);
            $io->success(sprintf('Created %d purchase requests', count($purchaseRequests)));

            // Create quotes
            $io->section('Creating quotes');
            $quotes = $this->createQuotes($admin, $purchaseRequests, $suppliers);
            $io->success(sprintf('Created %d quotes', count($quotes)));

            // Create purchase orders
            $io->section('Creating purchase orders');
            $purchaseOrders = $this->createPurchaseOrders($admin, $purchaseRequests, $suppliers);
            $io->success(sprintf('Created %d purchase orders', count($purchaseOrders)));

            // Create goods receipts
            $io->section('Creating goods receipts');
            $goodsReceipts = $this->createGoodsReceipts($admin, $purchaseOrders, $employees);
            $io->success(sprintf('Created %d goods receipts', count($goodsReceipts)));

            // Create invoices
            $io->section('Creating invoices');
            $invoices = $this->createInvoices($admin, $purchaseOrders, $suppliers);
            $io->success(sprintf('Created %d invoices', count($invoices)));

            $io->success('All purchase related fixtures have been loaded successfully.');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error('An error occurred: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    private function createPurchaseRequests(User $admin, array $employees, array $projects, array $products): array
    {
        $purchaseRequests = [];

        for ($i = 0; $i < 5; $i++) {
            $purchaseRequest = new PurchaseRequest();
            $purchaseRequest->setTitle('Demande d\'achat ' . ($i + 1));
            $purchaseRequest->setDescription('Description de la demande d\'achat ' . ($i + 1));
            $purchaseRequest->setProject($projects[array_rand($projects)]);
            $purchaseRequest->setRequestedBy($employees[array_rand($employees)]);
            $purchaseRequest->setCreatedBy($admin);

            // Add items
            for ($j = 0; $j < rand(1, 3); $j++) {
                $item = new PurchaseRequestItem();
                $product = $products[array_rand($products)];
                $item->setProduct($product);
                $item->setDescription($product->getName());
                $item->setQuantity(rand(1, 10));
                $item->setUnit($product->getUnit());
                $item->setUnitPrice($product->getReferencePrice() ?: rand(10, 100));
                $item->setTaxRate(20);

                // Update product stock level for tracking
                $currentStock = $product->getCurrentStock() ?: 0;
                $product->setCurrentStock($currentStock + $item->getQuantity());
                $purchaseRequest->addItem($item);
            }

            $purchaseRequest->updateEstimatedAmount();
            $purchaseRequest->setCreatedBy($admin);

            // Bypass the service's setCreatedBy method by using the repository directly
            $requestNumber = $this->entityManager->getRepository(PurchaseRequest::class)->generateRequestNumber();
            $purchaseRequest->setRequestNumber($requestNumber);

            $this->entityManager->persist($purchaseRequest);
            $this->entityManager->flush();

            // Submit and approve some requests
            if ($i < 4) {
                $this->purchaseRequestService->submitPurchaseRequest($purchaseRequest);

                if ($i < 3) {
                    $this->purchaseRequestService->approvePurchaseRequest($purchaseRequest, $employees[array_rand($employees)], 1, 'Approuvé pour test');
                }
            }

            $purchaseRequests[] = $purchaseRequest;
        }

        return $purchaseRequests;
    }

    private function createQuotes(User $admin, array $purchaseRequests, array $suppliers): array
    {
        $quotes = [];

        // Create quotes for approved purchase requests
        foreach ($purchaseRequests as $purchaseRequest) {
            if ($purchaseRequest->isApproved()) {
                // Create 2 quotes per approved request
                for ($i = 0; $i < 2; $i++) {
                    $quote = $this->quoteService->createFromPurchaseRequest($purchaseRequest);
                    $quote->setSupplier($suppliers[array_rand($suppliers)]);
                    $quote->setCreatedBy($admin);
                    $quote->setQuoteNumber('QT-' . date('Ymd') . '-' . sprintf('%03d', $i + 1) . '-' . uniqid());

                    // Adjust prices slightly for comparison
                    foreach ($quote->getItems() as $item) {
                        $originalPrice = $item->getUnitPrice();
                        $item->setUnitPrice($originalPrice * (0.9 + (rand(0, 20) / 100)));
                    }

                    // Save directly instead of using the service
                    $this->entityManager->persist($quote);
                    $this->entityManager->flush();

                    $quotes[] = $quote;
                }
            }
        }

        return $quotes;
    }

    private function createPurchaseOrders(User $admin, array $purchaseRequests, array $suppliers): array
    {
        $purchaseOrders = [];

        // Create purchase orders for approved purchase requests
        foreach ($purchaseRequests as $purchaseRequest) {
            if ($purchaseRequest->isApproved() && count($purchaseOrders) < 3) {
                $purchaseOrder = $this->purchaseOrderService->createFromPurchaseRequest($purchaseRequest);
                $purchaseOrder->setSupplier($suppliers[array_rand($suppliers)]);
                $purchaseOrder->setCreatedBy($admin);
                $purchaseOrder->setOrderNumber('PO-' . date('Ymd') . '-' . sprintf('%03d', count($purchaseOrders) + 1) . '-' . uniqid());

                // Make sure each item has a product
                foreach ($purchaseOrder->getItems() as $item) {
                    if (!$item->getProduct() && $item->getRequestItem() && $item->getRequestItem()->getProduct()) {
                        $item->setProduct($item->getRequestItem()->getProduct());
                    }
                }

                // Save directly instead of using the service
                $this->entityManager->persist($purchaseOrder);
                $this->entityManager->flush();

                // Mark the purchase request as converted
                $purchaseRequest->setStatus(PurchaseRequest::STATUS_CONVERTED);
                $purchaseRequest->setPurchaseType('po');
                $purchaseRequest->setPurchaseId($purchaseOrder->getId());
                $this->entityManager->flush();

                // Set the purchase order status to sent
                $purchaseOrder->setStatus(PurchaseOrder::STATUS_SENT);
                $purchaseOrder->setSentAt(new \DateTime());
                $this->entityManager->flush();

                $purchaseOrders[] = $purchaseOrder;
            }
        }

        return $purchaseOrders;
    }

    private function createGoodsReceipts(User $admin, array $purchaseOrders, array $employees): array
    {
        $goodsReceipts = [];

        foreach ($purchaseOrders as $purchaseOrder) {
            $goodsReceipt = $this->goodsReceiptService->createFromPurchaseOrder($purchaseOrder);
            $goodsReceipt->setReceivedBy($employees[array_rand($employees)]);
            $goodsReceipt->setCreatedBy($admin);
            $goodsReceipt->setReceiptNumber('GR-' . date('Ymd') . '-' . sprintf('%03d', count($goodsReceipts) + 1) . '-' . uniqid());

            // Save directly instead of using the service
            $this->entityManager->persist($goodsReceipt);
            $this->entityManager->flush();

            $goodsReceipts[] = $goodsReceipt;
        }

        return $goodsReceipts;
    }

    private function createInvoices(User $admin, array $purchaseOrders, array $suppliers): array
    {
        $invoices = [];

        foreach ($purchaseOrders as $purchaseOrder) {
            // Create invoice manually instead of using the service
            $invoice = new Invoice();
            $invoice->setSupplier($purchaseOrder->getSupplier());
            $invoice->setProject($purchaseOrder->getProject());
            $invoice->setPurchaseOrder($purchaseOrder);
            $invoice->setInvoiceNumber('INV-' . date('Ymd') . '-' . sprintf('%03d', count($invoices) + 1) . '-' . uniqid());
            $invoice->setStatus('pending');
            $invoice->setCreatedBy($admin);
            $invoice->setCreatedAt(new DateTimeImmutable());

            // Set due date
            $dueDate = new DateTime('+30 days');
            $invoice->setDueDate($dueDate);

            // Add items from purchase order
            foreach ($purchaseOrder->getItems() as $orderItem) {
                $invoiceItem = new InvoiceItem();
                $invoiceItem->setPurchaseOrderItem($orderItem);
                $invoiceItem->setDescription($orderItem->getDescription());
                $invoiceItem->setQuantity($orderItem->getQuantity());
                $invoiceItem->setUnit($orderItem->getUnit());
                $invoiceItem->setUnitPrice($orderItem->getUnitPrice());
                $invoiceItem->setProduct($orderItem->getProduct());

                $invoice->addItem($invoiceItem);
                $this->entityManager->persist($invoiceItem);
            }

            // Calculate totals
            $totalAmount = 0;
            $taxAmount = 0;

            foreach ($invoice->getItems() as $item) {
                $itemTotal = $item->getQuantity() * $item->getUnitPrice();
                $itemTax = $itemTotal * 0.2; // Assuming 20% tax rate
                $totalAmount += $itemTotal + $itemTax;
                $taxAmount += $itemTax;
            }

            $invoice->setTotalAmount($totalAmount);
            $invoice->setTaxAmount($taxAmount);

            // Save directly
            $this->entityManager->persist($invoice);
            $this->entityManager->flush();

            $invoices[] = $invoice;
        }

        return $invoices;
    }
}
