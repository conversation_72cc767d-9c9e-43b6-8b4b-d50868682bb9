<?php

namespace App\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:purchase-data:reset',
    description: 'Reset all purchase related data',
)]
class PurchaseDataResetCommand extends Command
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        parent::__construct();
        $this->connection = $connection;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Resetting purchase related data');

        // Disable foreign key checks temporarily
        $this->connection->executeStatement('PRAGMA foreign_keys = OFF');

        try {
            // Delete data in reverse order of dependencies
            $tables = [
                'invoice_payment',
                'invoice_approval',
                'invoice_item',
                'invoice',
                'goods_receipt_item',
                'goods_receipt',
                'purchase_order_item',
                'purchase_order',
                'purchase_request_approval',
                'purchase_request_item',
                'purchase_request',
                'quote_item',
                'quote',
                'stock_movement',
                'product_price_history'
            ];

            foreach ($tables as $table) {
                $io->text("Deleting data from table: $table");
                $this->connection->executeStatement("DELETE FROM $table");
                $this->connection->executeStatement("DELETE FROM sqlite_sequence WHERE name='$table'");
            }

            // Reset product stock quantities
            $io->text("Resetting product stock quantities");
            $this->connection->executeStatement("UPDATE product SET current_stock = 0, last_purchase_price = NULL, last_purchase_date = NULL");

            // Reset stock items
            $io->text("Resetting stock items");
            $this->connection->executeStatement("UPDATE stock_item SET quantity = 0, reserved_quantity = 0");

            $io->success('All purchase related data has been reset successfully.');

            // Re-enable foreign key checks
            $this->connection->executeStatement('PRAGMA foreign_keys = ON');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $io->error('An error occurred: ' . $e->getMessage());

            // Re-enable foreign key checks
            $this->connection->executeStatement('PRAGMA foreign_keys = ON');

            return Command::FAILURE;
        }
    }
}
