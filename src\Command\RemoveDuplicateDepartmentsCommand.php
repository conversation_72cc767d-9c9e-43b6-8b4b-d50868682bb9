<?php

namespace App\Command;

use App\Entity\Department;
use App\Entity\Employee;
use App\Entity\Position;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:remove-duplicate-departments',
    description: 'Remove duplicate departments',
)]
class RemoveDuplicateDepartmentsCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();
        $this->entityManager = $entityManager;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Suppression des départements en double');

        // Récupérer tous les départements
        $departmentRepository = $this->entityManager->getRepository(Department::class);
        $departments = $departmentRepository->findAll();

        // Grouper les départements par code
        $departmentsByCode = [];
        foreach ($departments as $department) {
            $code = $department->getCode();
            if (!isset($departmentsByCode[$code])) {
                $departmentsByCode[$code] = [];
            }
            $departmentsByCode[$code][] = $department;
        }

        $totalRemoved = 0;

        // Traiter chaque groupe de départements ayant le même code
        foreach ($departmentsByCode as $code => $deptGroup) {
            // S'il n'y a qu'un seul département avec ce code, passer au suivant
            if (count($deptGroup) <= 1) {
                continue;
            }

            $io->section("Traitement des départements avec le code: $code");
            
            // Trouver le département à conserver (celui avec le plus d'employés et de positions)
            $departmentToKeep = null;
            $maxScore = -1;
            
            foreach ($deptGroup as $dept) {
                // Compter les employés
                $employeeCount = $this->entityManager->createQueryBuilder()
                    ->select('COUNT(e.id)')
                    ->from(Employee::class, 'e')
                    ->where('e.department = :dept')
                    ->setParameter('dept', $dept)
                    ->getQuery()
                    ->getSingleScalarResult();
                
                // Compter les positions
                $positionCount = $this->entityManager->createQueryBuilder()
                    ->select('COUNT(p.id)')
                    ->from(Position::class, 'p')
                    ->where('p.department = :dept')
                    ->setParameter('dept', $dept)
                    ->getQuery()
                    ->getSingleScalarResult();
                
                // Compter les sous-départements
                $childCount = count($dept->getChildDepartments());
                
                // Calculer un score pour ce département
                $score = $employeeCount * 10 + $positionCount * 5 + $childCount * 2;
                
                $io->text("  - Département ID {$dept->getId()} ({$dept->getName()}): $employeeCount employés, $positionCount positions, $childCount sous-départements, score: $score");
                
                // Si ce département a un meilleur score, le conserver
                if ($score > $maxScore) {
                    $maxScore = $score;
                    $departmentToKeep = $dept;
                }
            }
            
            if (!$departmentToKeep) {
                $io->warning("  Aucun département à conserver pour le code $code");
                continue;
            }
            
            $io->success("  Département à conserver: ID {$departmentToKeep->getId()} ({$departmentToKeep->getName()})");
            
            // Traiter chaque département à supprimer
            foreach ($deptGroup as $dept) {
                if ($dept->getId() === $departmentToKeep->getId()) {
                    continue; // Ne pas traiter le département à conserver
                }
                
                $io->text("  Traitement du département à supprimer: ID {$dept->getId()} ({$dept->getName()})");
                
                // Mettre à jour les employés
                $employees = $this->entityManager->createQueryBuilder()
                    ->select('e')
                    ->from(Employee::class, 'e')
                    ->where('e.department = :dept')
                    ->setParameter('dept', $dept)
                    ->getQuery()
                    ->getResult();
                
                foreach ($employees as $employee) {
                    $io->text("    - Mise à jour de l'employé ID {$employee->getId()}");
                    $employee->setDepartment($departmentToKeep);
                    $this->entityManager->persist($employee);
                }
                
                // Mettre à jour les positions
                $positions = $this->entityManager->createQueryBuilder()
                    ->select('p')
                    ->from(Position::class, 'p')
                    ->where('p.department = :dept')
                    ->setParameter('dept', $dept)
                    ->getQuery()
                    ->getResult();
                
                foreach ($positions as $position) {
                    $io->text("    - Mise à jour de la position ID {$position->getId()}");
                    $position->setDepartment($departmentToKeep);
                    $this->entityManager->persist($position);
                }
                
                // Mettre à jour les sous-départements
                $childDepartments = $dept->getChildDepartments()->toArray();
                foreach ($childDepartments as $child) {
                    $io->text("    - Mise à jour du sous-département ID {$child->getId()}");
                    $child->setParentDepartment($departmentToKeep);
                    $this->entityManager->persist($child);
                }
                
                // Mettre à jour le parent département si nécessaire
                if ($dept->getParentDepartment()) {
                    $io->text("    - Mise à jour du département parent");
                    $departmentToKeep->setParentDepartment($dept->getParentDepartment());
                    $this->entityManager->persist($departmentToKeep);
                }
                
                // Appliquer les modifications avant de supprimer le département
                $this->entityManager->flush();
                
                // Supprimer le département
                try {
                    $this->entityManager->remove($dept);
                    $this->entityManager->flush();
                    $io->success("    Département ID {$dept->getId()} supprimé avec succès");
                    $totalRemoved++;
                } catch (\Exception $e) {
                    $io->error("    Erreur lors de la suppression du département ID {$dept->getId()}: " . $e->getMessage());
                }
            }
        }

        $io->success("Opération terminée. $totalRemoved départements en double ont été supprimés.");

        return Command::SUCCESS;
    }
}
