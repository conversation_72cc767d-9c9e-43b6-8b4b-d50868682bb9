<?php

namespace App\Command;

use App\Service\MedicalReminderService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:send-medical-reminders',
    description: 'Send reminders for upcoming medical examinations',
)]
class SendMedicalRemindersCommand extends Command
{
    private MedicalReminderService $medicalReminderService;

    public function __construct(MedicalReminderService $medicalReminderService)
    {
        parent::__construct();
        $this->medicalReminderService = $medicalReminderService;
    }

    protected function configure(): void
    {
        $this
            ->addOption('days', 'd', InputOption::VALUE_REQUIRED, 'Number of days before the examination to send the reminder', 7)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $days = (int) $input->getOption('days');
        
        $io->title('Sending reminders for upcoming medical examinations');
        $io->text("Sending reminders for examinations scheduled in $days days...");
        
        $count = $this->medicalReminderService->sendReminders($days);
        
        if ($count > 0) {
            $io->success("$count reminder(s) sent successfully.");
        } else {
            $io->info('No reminders to send.');
        }
        
        return Command::SUCCESS;
    }
}
