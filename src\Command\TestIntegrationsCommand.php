<?php

namespace App\Command;

use App\Entity\Integration;
use App\Entity\User;
use App\Repository\IntegrationRepository;
use App\Repository\UserRepository;
use App\Service\GoogleCalendarService;
use App\Service\MicrosoftTeamsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-integrations',
    description: 'Teste les intégrations avec les services externes',
)]
class TestIntegrationsCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private UserRepository $userRepository,
        private IntegrationRepository $integrationRepository,
        private GoogleCalendarService $googleCalendarService,
        private MicrosoftTeamsService $microsoftTeamsService
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('create-test-data', null, InputOption::VALUE_NONE, 'Créer des données de test pour les intégrations')
            ->addOption('google-calendar', null, InputOption::VALUE_NONE, 'Tester l\'intégration avec Google Calendar')
            ->addOption('microsoft-teams', null, InputOption::VALUE_NONE, 'Tester l\'intégration avec Microsoft Teams')
            ->addOption('all', null, InputOption::VALUE_NONE, 'Tester toutes les intégrations');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Test des intégrations avec les services externes');

        // Créer des données de test si demandé
        if ($input->getOption('create-test-data')) {
            $this->createTestData($io);
        }

        // Tester l'intégration avec Google Calendar
        if ($input->getOption('google-calendar') || $input->getOption('all')) {
            $this->testGoogleCalendarIntegration($io);
        }

        // Tester l'intégration avec Microsoft Teams
        if ($input->getOption('microsoft-teams') || $input->getOption('all')) {
            $this->testMicrosoftTeamsIntegration($io);
        }

        $io->success('Tests terminés.');

        return Command::SUCCESS;
    }

    /**
     * Crée des données de test pour les intégrations
     */
    private function createTestData(SymfonyStyle $io): void
    {
        $io->section('Création des données de test pour les intégrations');

        // Récupérer l'utilisateur admin
        $admin = $this->userRepository->findOneBy(['email' => '<EMAIL>']);
        if (!$admin) {
            $io->error('Utilisateur admin non trouvé. Veuillez créer un utilisateur avec l\'email <EMAIL>.');
            return;
        }

        // Créer une intégration Google Calendar
        $googleCalendarIntegration = $this->integrationRepository->findOneBy([
            'user' => $admin,
            'type' => 'google_calendar',
        ]);

        if (!$googleCalendarIntegration) {
            $googleCalendarIntegration = new Integration();
            $googleCalendarIntegration->setUser($admin);
            $googleCalendarIntegration->setType('google_calendar');
            $googleCalendarIntegration->setIsActive(true);
            $googleCalendarIntegration->setApiKey('test_api_key');
            $googleCalendarIntegration->setApiSecret('test_api_secret');
            $googleCalendarIntegration->setAccessToken('test_access_token');
            $googleCalendarIntegration->setRefreshToken('test_refresh_token');
            $googleCalendarIntegration->setCreatedAt(new \DateTimeImmutable());

            $this->entityManager->persist($googleCalendarIntegration);
            $io->text('Intégration Google Calendar créée.');
        } else {
            $io->text('Intégration Google Calendar existante mise à jour.');
            $googleCalendarIntegration->setIsActive(true);
            $googleCalendarIntegration->setApiKey('test_api_key');
            $googleCalendarIntegration->setApiSecret('test_api_secret');
            $googleCalendarIntegration->setAccessToken('test_access_token');
            $googleCalendarIntegration->setRefreshToken('test_refresh_token');
        }

        // Créer une intégration Microsoft Teams
        $microsoftTeamsIntegration = $this->integrationRepository->findOneBy([
            'user' => $admin,
            'type' => 'microsoft_teams',
        ]);

        if (!$microsoftTeamsIntegration) {
            $microsoftTeamsIntegration = new Integration();
            $microsoftTeamsIntegration->setUser($admin);
            $microsoftTeamsIntegration->setType('microsoft_teams');
            $microsoftTeamsIntegration->setIsActive(true);
            $microsoftTeamsIntegration->setWebhookUrl('https://outlook.office.com/webhook/test-webhook-url');
            $microsoftTeamsIntegration->setCreatedAt(new \DateTimeImmutable());

            $this->entityManager->persist($microsoftTeamsIntegration);
            $io->text('Intégration Microsoft Teams créée.');
        } else {
            $io->text('Intégration Microsoft Teams existante mise à jour.');
            $microsoftTeamsIntegration->setIsActive(true);
            $microsoftTeamsIntegration->setWebhookUrl('https://outlook.office.com/webhook/test-webhook-url');
        }

        $this->entityManager->flush();
        $io->success('Données de test créées avec succès.');
    }

    /**
     * Teste l'intégration avec Google Calendar
     */
    private function testGoogleCalendarIntegration(SymfonyStyle $io): void
    {
        $io->section('Test de l\'intégration avec Google Calendar');

        // Récupérer toutes les intégrations Google Calendar actives
        $integrations = $this->integrationRepository->findActiveByType('google_calendar');

        if (count($integrations) === 0) {
            $io->warning('Aucune intégration Google Calendar active trouvée.');
            return;
        }

        $io->text(sprintf('Nombre d\'intégrations trouvées : %d', count($integrations)));

        foreach ($integrations as $integration) {
            $io->text(sprintf('Test de l\'intégration pour l\'utilisateur %s', $integration->getUser()->getEmail()));

            // Tester la connexion
            $isConnected = $this->googleCalendarService->testConnection($integration);

            if ($isConnected) {
                $io->success('Connexion réussie à Google Calendar.');
            } else {
                $io->error('Échec de la connexion à Google Calendar.');
                continue;
            }

            // Simuler la synchronisation des projets
            $io->text('Simulation de la synchronisation des projets...');
            $io->text('Synchronisation réussie (simulation).');
        }
    }

    /**
     * Teste l'intégration avec Microsoft Teams
     */
    private function testMicrosoftTeamsIntegration(SymfonyStyle $io): void
    {
        $io->section('Test de l\'intégration avec Microsoft Teams');

        // Récupérer toutes les intégrations Microsoft Teams actives
        $integrations = $this->integrationRepository->findActiveByType('microsoft_teams');

        if (count($integrations) === 0) {
            $io->warning('Aucune intégration Microsoft Teams active trouvée.');
            return;
        }

        $io->text(sprintf('Nombre d\'intégrations trouvées : %d', count($integrations)));

        foreach ($integrations as $integration) {
            $io->text(sprintf('Test de l\'intégration pour l\'utilisateur %s', $integration->getUser()->getEmail()));

            // Tester la connexion
            $isConnected = $this->microsoftTeamsService->testConnection($integration);

            if ($isConnected) {
                $io->success('Connexion réussie à Microsoft Teams.');
            } else {
                $io->error('Échec de la connexion à Microsoft Teams.');
                continue;
            }

            // Simuler l'envoi d'un message
            $io->text('Simulation de l\'envoi d\'un message...');
            $io->text('Message envoyé avec succès (simulation).');
        }
    }
}
