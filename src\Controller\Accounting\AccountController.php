<?php

namespace App\Controller\Accounting;

use App\Entity\Accounting\Account;
use App\Form\Accounting\AccountType;
use App\Repository\Accounting\AccountRepository;
use App\Repository\Accounting\JournalEntryRepository;
use App\Service\AccountingService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/accounting/account')]
#[IsGranted('ROLE_ACCOUNTING')]
class AccountController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private AccountRepository $accountRepository,
        private JournalEntryRepository $journalEntryRepository,
        private AccountingService $accountingService
    ) {
    }

    #[Route('', name: 'app_accounting_account_index', methods: ['GET'])]
    public function index(): Response
    {
        $accounts = $this->accountRepository->findAll();
        
        return $this->render('accounting/account/index.html.twig', [
            'accounts' => $accounts,
        ]);
    }

    #[Route('/new', name: 'app_accounting_account_new', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_ACCOUNTING_ADMIN')]
    public function new(Request $request): Response
    {
        $account = new Account();
        $form = $this->createForm(AccountType::class, $account);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($account);
            $this->entityManager->flush();

            $this->addFlash('success', 'Le compte a été créé avec succès.');

            return $this->redirectToRoute('app_accounting_account_index');
        }

        return $this->render('accounting/account/new.html.twig', [
            'account' => $account,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_accounting_account_show', methods: ['GET'])]
    public function show(Account $account): Response
    {
        // Get current fiscal year
        $currentDate = new \DateTime();
        $startOfYear = (new \DateTime())->setDate((int)$currentDate->format('Y'), 1, 1);
        
        // Get account entries
        $entries = $this->journalEntryRepository->findByAccount($account);
        
        // Get account balance
        $balance = $this->accountingService->getAccountBalance($account);
        
        // Get monthly balances for chart
        $monthlyBalances = [];
        for ($i = 1; $i <= 12; $i++) {
            $startDate = (new \DateTime())->setDate((int)$currentDate->format('Y'), $i, 1);
            $endDate = (clone $startDate)->modify('last day of this month');
            
            if ($endDate > $currentDate) {
                $endDate = $currentDate;
            }
            
            $monthlyBalances[] = [
                'month' => $startDate->format('M'),
                'balance' => $this->accountingService->getAccountBalance($account, $startDate, $endDate)
            ];
        }
        
        return $this->render('accounting/account/show.html.twig', [
            'account' => $account,
            'entries' => $entries,
            'balance' => $balance,
            'monthlyBalances' => $monthlyBalances
        ]);
    }

    #[Route('/{id}/edit', name: 'app_accounting_account_edit', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_ACCOUNTING_ADMIN')]
    public function edit(Request $request, Account $account): Response
    {
        $form = $this->createForm(AccountType::class, $account);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();

            $this->addFlash('success', 'Le compte a été modifié avec succès.');

            return $this->redirectToRoute('app_accounting_account_index');
        }

        return $this->render('accounting/account/edit.html.twig', [
            'account' => $account,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/ledger', name: 'app_accounting_account_ledger', methods: ['GET'])]
    public function ledger(Account $account, Request $request): Response
    {
        // Get date range from request or use current year
        $currentDate = new \DateTime();
        $startDate = $request->query->get('start_date') 
            ? new \DateTime($request->query->get('start_date')) 
            : (new \DateTime())->setDate((int)$currentDate->format('Y'), 1, 1);
        
        $endDate = $request->query->get('end_date') 
            ? new \DateTime($request->query->get('end_date')) 
            : $currentDate;
        
        // Get account entries
        $entries = $this->journalEntryRepository->findByAccount($account, $startDate, $endDate);
        
        // Get opening balance
        $openingBalance = $this->accountingService->getAccountBalance($account, null, (clone $startDate)->modify('-1 day'));
        
        // Calculate running balance
        $runningBalance = $openingBalance;
        $entriesWithBalance = [];
        
        foreach ($entries as $entry) {
            if ($entry->getStatus() !== 'cancelled') {
                if ($entry->isDebit()) {
                    $runningBalance += $entry->getAmount();
                } else {
                    $runningBalance -= $entry->getAmount();
                }
            }
            
            $entriesWithBalance[] = [
                'entry' => $entry,
                'balance' => $runningBalance
            ];
        }
        
        return $this->render('accounting/account/ledger.html.twig', [
            'account' => $account,
            'entries' => $entriesWithBalance,
            'openingBalance' => $openingBalance,
            'closingBalance' => $runningBalance,
            'startDate' => $startDate,
            'endDate' => $endDate
        ]);
    }

    #[Route('/{id}/delete', name: 'app_accounting_account_delete', methods: ['POST'])]
    #[IsGranted('ROLE_ACCOUNTING_ADMIN')]
    public function delete(Request $request, Account $account): Response
    {
        if ($this->isCsrfTokenValid('delete'.$account->getId(), $request->request->get('_token'))) {
            // Check if the account has entries
            $entries = $this->journalEntryRepository->findByAccount($account);
            
            if (!empty($entries)) {
                $this->addFlash('error', 'Ce compte ne peut pas être supprimé car il contient des écritures.');
                return $this->redirectToRoute('app_accounting_account_index');
            }
            
            // Check if the account has children
            if (!$account->getChildren()->isEmpty()) {
                $this->addFlash('error', 'Ce compte ne peut pas être supprimé car il contient des sous-comptes.');
                return $this->redirectToRoute('app_accounting_account_index');
            }
            
            $this->entityManager->remove($account);
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Le compte a été supprimé avec succès.');
        }

        return $this->redirectToRoute('app_accounting_account_index');
    }

    #[Route('/chart-of-accounts', name: 'app_accounting_account_chart', methods: ['GET'])]
    public function chartOfAccounts(): Response
    {
        // Get accounts by class
        $classes = [];
        
        for ($i = 1; $i <= 7; $i++) {
            $accounts = $this->accountRepository->findByClass($i);
            
            if (!empty($accounts)) {
                $classes[$i] = [
                    'name' => $this->getClassName($i),
                    'accounts' => $accounts
                ];
            }
        }
        
        return $this->render('accounting/account/chart.html.twig', [
            'classes' => $classes
        ]);
    }

    /**
     * Get the name of an account class
     */
    private function getClassName(int $class): string
    {
        return match($class) {
            1 => 'Comptes de financement permanent',
            2 => 'Comptes d\'actif immobilisé',
            3 => 'Comptes d\'actif circulant',
            4 => 'Comptes de passif circulant',
            5 => 'Comptes de trésorerie',
            6 => 'Comptes de charges',
            7 => 'Comptes de produits',
            default => 'Classe ' . $class,
        };
    }
}
