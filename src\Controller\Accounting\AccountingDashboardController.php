<?php

namespace App\Controller\Accounting;

use App\Repository\Accounting\AccountRepository;
use App\Repository\Accounting\FiscalYearRepository;
use App\Repository\Accounting\JournalEntryRepository;
use App\Repository\Accounting\TaxDeclarationRepository;
use App\Service\AccountingService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/accounting/dashboard')]
#[IsGranted('ROLE_ACCOUNTING')]
class AccountingDashboardController extends AbstractController
{
    public function __construct(
        private AccountRepository $accountRepository,
        private JournalEntryRepository $journalEntryRepository,
        private FiscalYearRepository $fiscalYearRepository,
        private TaxDeclarationRepository $taxDeclarationRepository,
        private AccountingService $accountingService
    ) {
    }

    #[Route('', name: 'app_accounting_dashboard_index')]
    public function index(): Response
    {
        // Get current fiscal year
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        
        if (!$currentFiscalYear) {
            $this->addFlash('warning', 'Aucun exercice fiscal en cours n\'a été trouvé.');
            return $this->render('accounting/dashboard/index.html.twig', [
                'currentFiscalYear' => null,
                'overdueTaxDeclarations' => [],
                'upcomingTaxDeclarations' => [],
                'recentEntries' => []
            ]);
        }
        
        // Get overdue tax declarations
        $overdueTaxDeclarations = $this->taxDeclarationRepository->findOverdue();
        
        // Get upcoming tax declarations
        $upcomingTaxDeclarations = $this->taxDeclarationRepository->findDueSoon();
        
        // Get recent journal entries
        $recentEntries = $this->journalEntryRepository->findByDateRange(
            (new \DateTime())->modify('-30 days'),
            new \DateTime()
        );
        
        return $this->render('accounting/dashboard/index.html.twig', [
            'currentFiscalYear' => $currentFiscalYear,
            'overdueTaxDeclarations' => $overdueTaxDeclarations,
            'upcomingTaxDeclarations' => $upcomingTaxDeclarations,
            'recentEntries' => $recentEntries
        ]);
    }

    #[Route('/trial-balance', name: 'app_accounting_dashboard_trial_balance')]
    public function trialBalance(): Response
    {
        // Get current fiscal year
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        
        if (!$currentFiscalYear) {
            $this->addFlash('warning', 'Aucun exercice fiscal en cours n\'a été trouvé.');
            return $this->render('accounting/dashboard/trial_balance.html.twig', [
                'currentFiscalYear' => null,
                'balances' => []
            ]);
        }
        
        // Get trial balance
        $balances = $this->accountingService->getTrialBalance(
            $currentFiscalYear->getStartDate(),
            new \DateTime()
        );
        
        // Calculate totals
        $totalDebit = 0;
        $totalCredit = 0;
        
        foreach ($balances as $balance) {
            $totalDebit += $balance['debit'];
            $totalCredit += $balance['credit'];
        }
        
        return $this->render('accounting/dashboard/trial_balance.html.twig', [
            'currentFiscalYear' => $currentFiscalYear,
            'balances' => $balances,
            'totalDebit' => $totalDebit,
            'totalCredit' => $totalCredit
        ]);
    }

    #[Route('/general-ledger', name: 'app_accounting_dashboard_general_ledger')]
    public function generalLedger(): Response
    {
        // Get current fiscal year
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        
        if (!$currentFiscalYear) {
            $this->addFlash('warning', 'Aucun exercice fiscal en cours n\'a été trouvé.');
            return $this->render('accounting/dashboard/general_ledger.html.twig', [
                'currentFiscalYear' => null,
                'ledger' => []
            ]);
        }
        
        // Get general ledger
        $ledger = $this->accountingService->getGeneralLedger(
            $currentFiscalYear->getStartDate(),
            new \DateTime()
        );
        
        return $this->render('accounting/dashboard/general_ledger.html.twig', [
            'currentFiscalYear' => $currentFiscalYear,
            'ledger' => $ledger
        ]);
    }

    #[Route('/tax-summary', name: 'app_accounting_dashboard_tax_summary')]
    public function taxSummary(): Response
    {
        // Get current fiscal year
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        
        if (!$currentFiscalYear) {
            $this->addFlash('warning', 'Aucun exercice fiscal en cours n\'a été trouvé.');
            return $this->render('accounting/dashboard/tax_summary.html.twig', [
                'currentFiscalYear' => null,
                'taxDeclarations' => [],
                'taxRatesBreakdown' => []
            ]);
        }
        
        // Get tax declarations for current fiscal year
        $taxDeclarations = $this->taxDeclarationRepository->findByFiscalYear($currentFiscalYear);
        
        // Get tax rates breakdown
        $taxRatesBreakdown = $this->accountingService->getTaxRatesBreakdown(
            $currentFiscalYear->getStartDate(),
            new \DateTime()
        );
        
        return $this->render('accounting/dashboard/tax_summary.html.twig', [
            'currentFiscalYear' => $currentFiscalYear,
            'taxDeclarations' => $taxDeclarations,
            'taxRatesBreakdown' => $taxRatesBreakdown
        ]);
    }

    #[Route('/financial-statements', name: 'app_accounting_dashboard_financial_statements')]
    public function financialStatements(): Response
    {
        // Get current fiscal year
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        
        if (!$currentFiscalYear) {
            $this->addFlash('warning', 'Aucun exercice fiscal en cours n\'a été trouvé.');
            return $this->render('accounting/dashboard/financial_statements.html.twig', [
                'currentFiscalYear' => null
            ]);
        }
        
        // Get accounts by class
        $assetsAccounts = $this->accountRepository->findByClass(2);
        $liabilitiesAccounts = $this->accountRepository->findByClass(1);
        $equityAccounts = $this->accountRepository->findByClass(1);
        $revenueAccounts = $this->accountRepository->findByClass(7);
        $expenseAccounts = $this->accountRepository->findByClass(6);
        
        // Calculate balances
        $assets = [];
        $liabilities = [];
        $equity = [];
        $revenue = [];
        $expenses = [];
        
        $totalAssets = 0;
        $totalLiabilities = 0;
        $totalEquity = 0;
        $totalRevenue = 0;
        $totalExpenses = 0;
        
        foreach ($assetsAccounts as $account) {
            $balance = $this->accountingService->getAccountBalance(
                $account,
                $currentFiscalYear->getStartDate(),
                new \DateTime()
            );
            
            if (abs($balance) > 0.01) {
                $assets[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalAssets += $balance;
            }
        }
        
        foreach ($liabilitiesAccounts as $account) {
            if (substr($account->getNumber(), 0, 1) === '1' && substr($account->getNumber(), 0, 2) !== '10' && substr($account->getNumber(), 0, 2) !== '12') {
                $balance = $this->accountingService->getAccountBalance(
                    $account,
                    $currentFiscalYear->getStartDate(),
                    new \DateTime()
                );
                
                if (abs($balance) > 0.01) {
                    $liabilities[] = [
                        'account' => $account,
                        'balance' => $balance
                    ];
                    $totalLiabilities += $balance;
                }
            }
        }
        
        foreach ($equityAccounts as $account) {
            if (substr($account->getNumber(), 0, 2) === '10' || substr($account->getNumber(), 0, 2) === '12') {
                $balance = $this->accountingService->getAccountBalance(
                    $account,
                    $currentFiscalYear->getStartDate(),
                    new \DateTime()
                );
                
                if (abs($balance) > 0.01) {
                    $equity[] = [
                        'account' => $account,
                        'balance' => $balance
                    ];
                    $totalEquity += $balance;
                }
            }
        }
        
        foreach ($revenueAccounts as $account) {
            $balance = $this->accountingService->getAccountBalance(
                $account,
                $currentFiscalYear->getStartDate(),
                new \DateTime()
            );
            
            if (abs($balance) > 0.01) {
                $revenue[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalRevenue += $balance;
            }
        }
        
        foreach ($expenseAccounts as $account) {
            $balance = $this->accountingService->getAccountBalance(
                $account,
                $currentFiscalYear->getStartDate(),
                new \DateTime()
            );
            
            if (abs($balance) > 0.01) {
                $expenses[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalExpenses += $balance;
            }
        }
        
        // Calculate net income
        $netIncome = $totalRevenue - $totalExpenses;
        
        return $this->render('accounting/dashboard/financial_statements.html.twig', [
            'currentFiscalYear' => $currentFiscalYear,
            'assets' => $assets,
            'liabilities' => $liabilities,
            'equity' => $equity,
            'revenue' => $revenue,
            'expenses' => $expenses,
            'totalAssets' => $totalAssets,
            'totalLiabilities' => $totalLiabilities,
            'totalEquity' => $totalEquity,
            'totalRevenue' => $totalRevenue,
            'totalExpenses' => $totalExpenses,
            'netIncome' => $netIncome
        ]);
    }
}
