<?php

namespace App\Controller\Accounting;

use App\Repository\Accounting\AccountRepository;
use App\Repository\Accounting\FiscalYearRepository;
use App\Repository\Accounting\JournalEntryRepository;
use App\Repository\Accounting\JournalRepository;
use App\Repository\Accounting\TaxDeclarationRepository;
use App\Service\AccountingService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/accounting/analytics')]
#[IsGranted('ROLE_ACCOUNTING')]
class AnalyticsController extends AbstractController
{
    public function __construct(
        private AccountRepository $accountRepository,
        private JournalRepository $journalRepository,
        private JournalEntryRepository $journalEntryRepository,
        private FiscalYearRepository $fiscalYearRepository,
        private TaxDeclarationRepository $taxDeclarationRepository,
        private AccountingService $accountingService
    ) {
    }

    #[Route('', name: 'app_accounting_analytics_index')]
    public function index(): Response
    {
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        
        if (!$currentFiscalYear) {
            $this->addFlash('warning', 'Aucun exercice fiscal en cours n\'a été trouvé.');
            return $this->render('accounting/analytics/index.html.twig', [
                'currentFiscalYear' => null
            ]);
        }
        
        // Get accounts count
        $accountsCount = count($this->accountRepository->findAll());
        
        // Get journals count
        $journalsCount = count($this->journalRepository->findAll());
        
        // Get entries count
        $entriesCount = count($this->journalEntryRepository->findByDateRange(
            $currentFiscalYear->getStartDate(),
            $currentFiscalYear->getEndDate()
        ));
        
        // Get declarations count
        $declarationsCount = count($this->taxDeclarationRepository->findByFiscalYear($currentFiscalYear));
        
        // Get overdue declarations
        $overdueDeclarations = $this->taxDeclarationRepository->findOverdue();
        
        // Get upcoming declarations
        $upcomingDeclarations = $this->taxDeclarationRepository->findDueSoon(30);
        
        return $this->render('accounting/analytics/index.html.twig', [
            'currentFiscalYear' => $currentFiscalYear,
            'accountsCount' => $accountsCount,
            'journalsCount' => $journalsCount,
            'entriesCount' => $entriesCount,
            'declarationsCount' => $declarationsCount,
            'overdueDeclarations' => $overdueDeclarations,
            'upcomingDeclarations' => $upcomingDeclarations
        ]);
    }

    #[Route('/revenue-expense', name: 'app_accounting_analytics_revenue_expense')]
    public function revenueExpense(): Response
    {
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        $fiscalYears = $this->fiscalYearRepository->findAllOrdered();
        
        return $this->render('accounting/analytics/revenue_expense.html.twig', [
            'currentFiscalYear' => $currentFiscalYear,
            'fiscalYears' => $fiscalYears
        ]);
    }

    #[Route('/revenue-expense-data', name: 'app_accounting_analytics_revenue_expense_data')]
    public function revenueExpenseData(Request $request): JsonResponse
    {
        $fiscalYearId = $request->query->get('fiscal_year');
        $fiscalYear = null;
        
        if ($fiscalYearId) {
            $fiscalYear = $this->fiscalYearRepository->find($fiscalYearId);
        } else {
            $fiscalYear = $this->fiscalYearRepository->findCurrent();
        }
        
        if (!$fiscalYear) {
            return new JsonResponse(['error' => 'Fiscal year not found'], 404);
        }
        
        $startDate = $fiscalYear->getStartDate();
        $endDate = $fiscalYear->getEndDate();
        
        // Get monthly data
        $months = [];
        $revenue = [];
        $expenses = [];
        $profit = [];
        
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $monthStart = (clone $currentDate)->modify('first day of this month');
            $monthEnd = (clone $currentDate)->modify('last day of this month');
            
            if ($monthEnd > $endDate) {
                $monthEnd = $endDate;
            }
            
            $monthName = $currentDate->format('M Y');
            $months[] = $monthName;
            
            // Get revenue
            $revenueAccounts = $this->accountRepository->findByClass(7);
            $monthRevenue = 0;
            
            foreach ($revenueAccounts as $account) {
                $monthRevenue += $this->accountingService->getAccountBalance($account, $monthStart, $monthEnd);
            }
            
            $revenue[] = $monthRevenue;
            
            // Get expenses
            $expenseAccounts = $this->accountRepository->findByClass(6);
            $monthExpenses = 0;
            
            foreach ($expenseAccounts as $account) {
                $monthExpenses += $this->accountingService->getAccountBalance($account, $monthStart, $monthEnd);
            }
            
            $expenses[] = $monthExpenses;
            
            // Calculate profit
            $profit[] = $monthRevenue - $monthExpenses;
            
            // Move to next month
            $currentDate->modify('+1 month');
        }
        
        return new JsonResponse([
            'months' => $months,
            'revenue' => $revenue,
            'expenses' => $expenses,
            'profit' => $profit
        ]);
    }

    #[Route('/balance-sheet', name: 'app_accounting_analytics_balance_sheet')]
    public function balanceSheet(): Response
    {
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        $fiscalYears = $this->fiscalYearRepository->findAllOrdered();
        
        return $this->render('accounting/analytics/balance_sheet.html.twig', [
            'currentFiscalYear' => $currentFiscalYear,
            'fiscalYears' => $fiscalYears
        ]);
    }

    #[Route('/balance-sheet-data', name: 'app_accounting_analytics_balance_sheet_data')]
    public function balanceSheetData(Request $request): JsonResponse
    {
        $fiscalYearId = $request->query->get('fiscal_year');
        $fiscalYear = null;
        
        if ($fiscalYearId) {
            $fiscalYear = $this->fiscalYearRepository->find($fiscalYearId);
        } else {
            $fiscalYear = $this->fiscalYearRepository->findCurrent();
        }
        
        if (!$fiscalYear) {
            return new JsonResponse(['error' => 'Fiscal year not found'], 404);
        }
        
        $startDate = $fiscalYear->getStartDate();
        $endDate = $fiscalYear->getEndDate();
        
        // Get asset accounts
        $assetAccounts = $this->accountRepository->findByClass(2);
        $assets = [];
        $totalAssets = 0;
        
        foreach ($assetAccounts as $account) {
            $balance = $this->accountingService->getAccountBalance($account, $startDate, $endDate);
            
            if (abs($balance) > 0.01) {
                $assets[] = [
                    'name' => $account->getName(),
                    'balance' => $balance
                ];
                $totalAssets += $balance;
            }
        }
        
        // Get liability accounts
        $liabilityAccounts = $this->accountRepository->findByClass(1);
        $liabilities = [];
        $totalLiabilities = 0;
        
        foreach ($liabilityAccounts as $account) {
            if (substr($account->getNumber(), 0, 1) === '1' && substr($account->getNumber(), 0, 2) !== '10' && substr($account->getNumber(), 0, 2) !== '12') {
                $balance = $this->accountingService->getAccountBalance($account, $startDate, $endDate);
                
                if (abs($balance) > 0.01) {
                    $liabilities[] = [
                        'name' => $account->getName(),
                        'balance' => $balance
                    ];
                    $totalLiabilities += $balance;
                }
            }
        }
        
        // Get equity accounts
        $equityAccounts = $this->accountRepository->findByClass(1);
        $equity = [];
        $totalEquity = 0;
        
        foreach ($equityAccounts as $account) {
            if (substr($account->getNumber(), 0, 2) === '10' || substr($account->getNumber(), 0, 2) === '12') {
                $balance = $this->accountingService->getAccountBalance($account, $startDate, $endDate);
                
                if (abs($balance) > 0.01) {
                    $equity[] = [
                        'name' => $account->getName(),
                        'balance' => $balance
                    ];
                    $totalEquity += $balance;
                }
            }
        }
        
        // Calculate net income
        $revenueAccounts = $this->accountRepository->findByClass(7);
        $expenseAccounts = $this->accountRepository->findByClass(6);
        
        $totalRevenue = 0;
        $totalExpenses = 0;
        
        foreach ($revenueAccounts as $account) {
            $totalRevenue += $this->accountingService->getAccountBalance($account, $startDate, $endDate);
        }
        
        foreach ($expenseAccounts as $account) {
            $totalExpenses += $this->accountingService->getAccountBalance($account, $startDate, $endDate);
        }
        
        $netIncome = $totalRevenue - $totalExpenses;
        
        // Add net income to equity
        $equity[] = [
            'name' => 'Résultat de l\'exercice',
            'balance' => $netIncome
        ];
        $totalEquity += $netIncome;
        
        return new JsonResponse([
            'assets' => $assets,
            'liabilities' => $liabilities,
            'equity' => $equity,
            'totalAssets' => $totalAssets,
            'totalLiabilities' => $totalLiabilities,
            'totalEquity' => $totalEquity,
            'netIncome' => $netIncome
        ]);
    }

    #[Route('/tax-analysis', name: 'app_accounting_analytics_tax')]
    public function taxAnalysis(): Response
    {
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        $fiscalYears = $this->fiscalYearRepository->findAllOrdered();
        
        // Get tax declarations
        $tvaDeclarations = $this->taxDeclarationRepository->findByType('tva');
        $isDeclarations = $this->taxDeclarationRepository->findByType('is');
        $irDeclarations = $this->taxDeclarationRepository->findByType('ir');
        
        return $this->render('accounting/analytics/tax.html.twig', [
            'currentFiscalYear' => $currentFiscalYear,
            'fiscalYears' => $fiscalYears,
            'tvaDeclarations' => $tvaDeclarations,
            'isDeclarations' => $isDeclarations,
            'irDeclarations' => $irDeclarations
        ]);
    }

    #[Route('/tax-data', name: 'app_accounting_analytics_tax_data')]
    public function taxData(Request $request): JsonResponse
    {
        $fiscalYearId = $request->query->get('fiscal_year');
        $fiscalYear = null;
        
        if ($fiscalYearId) {
            $fiscalYear = $this->fiscalYearRepository->find($fiscalYearId);
        } else {
            $fiscalYear = $this->fiscalYearRepository->findCurrent();
        }
        
        if (!$fiscalYear) {
            return new JsonResponse(['error' => 'Fiscal year not found'], 404);
        }
        
        // Get tax declarations for this fiscal year
        $declarations = $this->taxDeclarationRepository->findByFiscalYear($fiscalYear);
        
        // Group by type and period
        $tvaMonthly = [];
        $tvaQuarterly = [];
        $isData = [];
        $irData = [];
        
        foreach ($declarations as $declaration) {
            if ($declaration->getType() === 'tva') {
                if ($declaration->getPeriod() === 'monthly') {
                    $tvaMonthly[] = [
                        'period' => $declaration->getPeriodName(),
                        'amount' => $declaration->getNetAmount()
                    ];
                } elseif ($declaration->getPeriod() === 'quarterly') {
                    $tvaQuarterly[] = [
                        'period' => $declaration->getPeriodName(),
                        'amount' => $declaration->getNetAmount()
                    ];
                }
            } elseif ($declaration->getType() === 'is') {
                $isData[] = [
                    'period' => $declaration->getPeriodName(),
                    'amount' => $declaration->getNetAmount()
                ];
            } elseif ($declaration->getType() === 'ir') {
                $irData[] = [
                    'period' => $declaration->getPeriodName(),
                    'amount' => $declaration->getNetAmount()
                ];
            }
        }
        
        // Get tax rates breakdown
        $taxRatesBreakdown = $this->accountingService->getTaxRatesBreakdown(
            $fiscalYear->getStartDate(),
            $fiscalYear->getEndDate()
        );
        
        $taxRates = [];
        $taxAmounts = [];
        
        foreach ($taxRatesBreakdown as $breakdown) {
            $taxRates[] = $breakdown['taxRate'] . '%';
            $taxAmounts[] = abs($breakdown['amount']);
        }
        
        return new JsonResponse([
            'tvaMonthly' => $tvaMonthly,
            'tvaQuarterly' => $tvaQuarterly,
            'isData' => $isData,
            'irData' => $irData,
            'taxRates' => $taxRates,
            'taxAmounts' => $taxAmounts
        ]);
    }

    #[Route('/cash-flow', name: 'app_accounting_analytics_cash_flow')]
    public function cashFlow(): Response
    {
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        $fiscalYears = $this->fiscalYearRepository->findAllOrdered();
        
        return $this->render('accounting/analytics/cash_flow.html.twig', [
            'currentFiscalYear' => $currentFiscalYear,
            'fiscalYears' => $fiscalYears
        ]);
    }

    #[Route('/cash-flow-data', name: 'app_accounting_analytics_cash_flow_data')]
    public function cashFlowData(Request $request): JsonResponse
    {
        $fiscalYearId = $request->query->get('fiscal_year');
        $fiscalYear = null;
        
        if ($fiscalYearId) {
            $fiscalYear = $this->fiscalYearRepository->find($fiscalYearId);
        } else {
            $fiscalYear = $this->fiscalYearRepository->findCurrent();
        }
        
        if (!$fiscalYear) {
            return new JsonResponse(['error' => 'Fiscal year not found'], 404);
        }
        
        $startDate = $fiscalYear->getStartDate();
        $endDate = $fiscalYear->getEndDate();
        
        // Get cash and bank accounts
        $cashAccounts = $this->accountRepository->findByCategory('cash');
        $bankAccounts = $this->accountRepository->findByCategory('bank');
        
        $accounts = array_merge($cashAccounts, $bankAccounts);
        
        // Get monthly data
        $months = [];
        $inflows = [];
        $outflows = [];
        $balances = [];
        
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $monthStart = (clone $currentDate)->modify('first day of this month');
            $monthEnd = (clone $currentDate)->modify('last day of this month');
            
            if ($monthEnd > $endDate) {
                $monthEnd = $endDate;
            }
            
            $monthName = $currentDate->format('M Y');
            $months[] = $monthName;
            
            // Calculate inflows and outflows
            $monthInflow = 0;
            $monthOutflow = 0;
            
            foreach ($accounts as $account) {
                $entries = $this->journalEntryRepository->findByAccount($account, $monthStart, $monthEnd);
                
                foreach ($entries as $entry) {
                    if ($entry->getStatus() !== 'cancelled') {
                        if ($entry->isDebit()) {
                            $monthInflow += $entry->getAmount();
                        } else {
                            $monthOutflow += $entry->getAmount();
                        }
                    }
                }
            }
            
            $inflows[] = $monthInflow;
            $outflows[] = $monthOutflow;
            
            // Calculate balance
            $monthBalance = 0;
            
            foreach ($accounts as $account) {
                $monthBalance += $this->accountingService->getAccountBalance($account, null, $monthEnd);
            }
            
            $balances[] = $monthBalance;
            
            // Move to next month
            $currentDate->modify('+1 month');
        }
        
        return new JsonResponse([
            'months' => $months,
            'inflows' => $inflows,
            'outflows' => $outflows,
            'balances' => $balances
        ]);
    }
}
