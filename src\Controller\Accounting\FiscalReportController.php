<?php

namespace App\Controller\Accounting;

use App\Repository\Accounting\AccountRepository;
use App\Repository\Accounting\FiscalYearRepository;
use App\Repository\Accounting\JournalEntryRepository;
use App\Repository\Accounting\TaxDeclarationRepository;
use App\Service\AccountingService;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/accounting/fiscal-report')]
#[IsGranted('ROLE_ACCOUNTING')]
class FiscalReportController extends AbstractController
{
    public function __construct(
        private AccountRepository $accountRepository,
        private JournalEntryRepository $journalEntryRepository,
        private FiscalYearRepository $fiscalYearRepository,
        private TaxDeclarationRepository $taxDeclarationRepository,
        private AccountingService $accountingService
    ) {
    }

    #[Route('', name: 'app_accounting_fiscal_report_index')]
    public function index(): Response
    {
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        $fiscalYears = $this->fiscalYearRepository->findAllOrdered();
        
        return $this->render('accounting/fiscal_report/index.html.twig', [
            'currentFiscalYear' => $currentFiscalYear,
            'fiscalYears' => $fiscalYears
        ]);
    }

    #[Route('/tva', name: 'app_accounting_fiscal_report_tva')]
    public function tvaReport(Request $request): Response
    {
        $year = $request->query->getInt('year', (int)(new \DateTime())->format('Y'));
        $period = $request->query->get('period', 'monthly');
        $quarter = $request->query->getInt('quarter', 1);
        $month = $request->query->getInt('month', 1);
        
        // Determine date range
        $startDate = null;
        $endDate = null;
        
        if ($period === 'monthly') {
            $startDate = new \DateTime($year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01');
            $endDate = (clone $startDate)->modify('last day of this month');
        } elseif ($period === 'quarterly') {
            $startMonth = ($quarter - 1) * 3 + 1;
            $startDate = new \DateTime($year . '-' . str_pad($startMonth, 2, '0', STR_PAD_LEFT) . '-01');
            $endDate = (clone $startDate)->modify('+2 months')->modify('last day of this month');
        } elseif ($period === 'annual') {
            $startDate = new \DateTime($year . '-01-01');
            $endDate = new \DateTime($year . '-12-31');
        }
        
        // Get TVA data
        $tvaData = null;
        if ($startDate && $endDate) {
            try {
                $tvaData = $this->accountingService->generateTVADeclarationData($startDate, $endDate);
                
                // Get tax rates breakdown
                $taxRatesBreakdown = $this->accountingService->getTaxRatesBreakdown($startDate, $endDate);
                $tvaData['taxRatesBreakdown'] = $taxRatesBreakdown;
                
                // Get existing declarations for this period
                $declarations = $this->taxDeclarationRepository->findByDateRange($startDate, $endDate);
                $tvaData['declarations'] = $declarations;
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la génération des données TVA : ' . $e->getMessage());
            }
        }
        
        return $this->render('accounting/fiscal_report/tva.html.twig', [
            'year' => $year,
            'period' => $period,
            'quarter' => $quarter,
            'month' => $month,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'tvaData' => $tvaData
        ]);
    }

    #[Route('/tva/export', name: 'app_accounting_fiscal_report_tva_export')]
    public function tvaReportExport(Request $request): Response
    {
        $year = $request->query->getInt('year', (int)(new \DateTime())->format('Y'));
        $period = $request->query->get('period', 'monthly');
        $quarter = $request->query->getInt('quarter', 1);
        $month = $request->query->getInt('month', 1);
        
        // Determine date range
        $startDate = null;
        $endDate = null;
        $periodName = '';
        
        if ($period === 'monthly') {
            $startDate = new \DateTime($year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01');
            $endDate = (clone $startDate)->modify('last day of this month');
            $periodName = $startDate->format('F Y');
        } elseif ($period === 'quarterly') {
            $startMonth = ($quarter - 1) * 3 + 1;
            $startDate = new \DateTime($year . '-' . str_pad($startMonth, 2, '0', STR_PAD_LEFT) . '-01');
            $endDate = (clone $startDate)->modify('+2 months')->modify('last day of this month');
            $periodName = 'T' . $quarter . ' ' . $year;
        } elseif ($period === 'annual') {
            $startDate = new \DateTime($year . '-01-01');
            $endDate = new \DateTime($year . '-12-31');
            $periodName = 'Année ' . $year;
        }
        
        // Get TVA data
        $tvaData = null;
        if ($startDate && $endDate) {
            try {
                $tvaData = $this->accountingService->generateTVADeclarationData($startDate, $endDate);
                $taxRatesBreakdown = $this->accountingService->getTaxRatesBreakdown($startDate, $endDate);
                $tvaData['taxRatesBreakdown'] = $taxRatesBreakdown;
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la génération des données TVA : ' . $e->getMessage());
                return $this->redirectToRoute('app_accounting_fiscal_report_tva', [
                    'year' => $year,
                    'period' => $period,
                    'quarter' => $quarter,
                    'month' => $month
                ]);
            }
        }
        
        // Create Excel file
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Set headers
        $sheet->setCellValue('A1', 'DÉCLARATION DE TVA');
        $sheet->setCellValue('A2', 'Période: ' . $periodName);
        $sheet->setCellValue('A3', 'Du ' . $startDate->format('d/m/Y') . ' au ' . $endDate->format('d/m/Y'));
        
        $sheet->setCellValue('A5', 'TVA COLLECTÉE');
        $sheet->setCellValue('A6', 'Base imposable');
        $sheet->setCellValue('B6', $tvaData['taxableAmount']);
        $sheet->setCellValue('A7', 'Montant TVA');
        $sheet->setCellValue('B7', $tvaData['collectedTax']);
        
        $sheet->setCellValue('A9', 'TVA DÉDUCTIBLE');
        $sheet->setCellValue('A10', 'Base déductible');
        $sheet->setCellValue('B10', $tvaData['deductibleAmount']);
        $sheet->setCellValue('A11', 'Montant TVA');
        $sheet->setCellValue('B11', $tvaData['deductibleTax']);
        
        $sheet->setCellValue('A13', 'TVA À PAYER');
        $sheet->setCellValue('B13', $tvaData['netTax']);
        
        // Add tax rates breakdown
        $sheet->setCellValue('A15', 'DÉTAIL PAR TAUX DE TVA');
        $sheet->setCellValue('A16', 'Taux');
        $sheet->setCellValue('B16', 'Montant');
        
        $row = 17;
        foreach ($tvaData['taxRatesBreakdown'] as $breakdown) {
            $sheet->setCellValue('A' . $row, $breakdown['taxRate'] . '%');
            $sheet->setCellValue('B' . $row, $breakdown['amount']);
            $row++;
        }
        
        // Format cells
        $sheet->getStyle('B6:B13')->getNumberFormat()->setFormatCode('#,##0.00');
        $sheet->getStyle('B17:B' . ($row - 1))->getNumberFormat()->setFormatCode('#,##0.00');
        $sheet->getStyle('A1:A3')->getFont()->setBold(true);
        $sheet->getStyle('A5')->getFont()->setBold(true);
        $sheet->getStyle('A9')->getFont()->setBold(true);
        $sheet->getStyle('A13')->getFont()->setBold(true);
        $sheet->getStyle('A15')->getFont()->setBold(true);
        $sheet->getStyle('A16:B16')->getFont()->setBold(true);
        
        // Auto size columns
        foreach (range('A', 'B') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // Create response
        $writer = new Xlsx($spreadsheet);
        
        $response = new StreamedResponse(
            function () use ($writer) {
                $writer->save('php://output');
            }
        );
        
        $filename = 'declaration_tva_' . str_replace(' ', '_', $periodName) . '.xlsx';
        
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $filename
        ));
        
        return $response;
    }

    #[Route('/liasse-fiscale', name: 'app_accounting_fiscal_report_liasse')]
    public function liasseFiscale(Request $request): Response
    {
        $fiscalYearId = $request->query->get('fiscal_year');
        $fiscalYear = null;
        
        if ($fiscalYearId) {
            $fiscalYear = $this->fiscalYearRepository->find($fiscalYearId);
        } else {
            $fiscalYear = $this->fiscalYearRepository->findCurrent();
        }
        
        if (!$fiscalYear) {
            $this->addFlash('warning', 'Aucun exercice fiscal sélectionné ou en cours.');
            return $this->redirectToRoute('app_accounting_fiscal_report_index');
        }
        
        // Get balance sheet data
        $assets = [];
        $liabilities = [];
        $equity = [];
        
        $assetsAccounts = $this->accountRepository->findByClass(2);
        $liabilitiesAccounts = $this->accountRepository->findByClass(1);
        $equityAccounts = $this->accountRepository->findByClass(1);
        
        $totalAssets = 0;
        $totalLiabilities = 0;
        $totalEquity = 0;
        
        foreach ($assetsAccounts as $account) {
            $balance = $this->accountingService->getAccountBalance(
                $account,
                $fiscalYear->getStartDate(),
                $fiscalYear->getEndDate()
            );
            
            if (abs($balance) > 0.01) {
                $assets[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalAssets += $balance;
            }
        }
        
        foreach ($liabilitiesAccounts as $account) {
            if (substr($account->getNumber(), 0, 1) === '1' && substr($account->getNumber(), 0, 2) !== '10' && substr($account->getNumber(), 0, 2) !== '12') {
                $balance = $this->accountingService->getAccountBalance(
                    $account,
                    $fiscalYear->getStartDate(),
                    $fiscalYear->getEndDate()
                );
                
                if (abs($balance) > 0.01) {
                    $liabilities[] = [
                        'account' => $account,
                        'balance' => $balance
                    ];
                    $totalLiabilities += $balance;
                }
            }
        }
        
        foreach ($equityAccounts as $account) {
            if (substr($account->getNumber(), 0, 2) === '10' || substr($account->getNumber(), 0, 2) === '12') {
                $balance = $this->accountingService->getAccountBalance(
                    $account,
                    $fiscalYear->getStartDate(),
                    $fiscalYear->getEndDate()
                );
                
                if (abs($balance) > 0.01) {
                    $equity[] = [
                        'account' => $account,
                        'balance' => $balance
                    ];
                    $totalEquity += $balance;
                }
            }
        }
        
        // Get income statement data
        $revenue = [];
        $expenses = [];
        
        $revenueAccounts = $this->accountRepository->findByClass(7);
        $expenseAccounts = $this->accountRepository->findByClass(6);
        
        $totalRevenue = 0;
        $totalExpenses = 0;
        
        foreach ($revenueAccounts as $account) {
            $balance = $this->accountingService->getAccountBalance(
                $account,
                $fiscalYear->getStartDate(),
                $fiscalYear->getEndDate()
            );
            
            if (abs($balance) > 0.01) {
                $revenue[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalRevenue += $balance;
            }
        }
        
        foreach ($expenseAccounts as $account) {
            $balance = $this->accountingService->getAccountBalance(
                $account,
                $fiscalYear->getStartDate(),
                $fiscalYear->getEndDate()
            );
            
            if (abs($balance) > 0.01) {
                $expenses[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalExpenses += $balance;
            }
        }
        
        // Calculate net income
        $netIncome = $totalRevenue - $totalExpenses;
        
        return $this->render('accounting/fiscal_report/liasse.html.twig', [
            'fiscalYear' => $fiscalYear,
            'assets' => $assets,
            'liabilities' => $liabilities,
            'equity' => $equity,
            'revenue' => $revenue,
            'expenses' => $expenses,
            'totalAssets' => $totalAssets,
            'totalLiabilities' => $totalLiabilities,
            'totalEquity' => $totalEquity,
            'totalRevenue' => $totalRevenue,
            'totalExpenses' => $totalExpenses,
            'netIncome' => $netIncome
        ]);
    }

    #[Route('/liasse-fiscale/export', name: 'app_accounting_fiscal_report_liasse_export')]
    public function liasseFiscaleExport(Request $request): Response
    {
        $fiscalYearId = $request->query->get('fiscal_year');
        $fiscalYear = null;
        
        if ($fiscalYearId) {
            $fiscalYear = $this->fiscalYearRepository->find($fiscalYearId);
        } else {
            $fiscalYear = $this->fiscalYearRepository->findCurrent();
        }
        
        if (!$fiscalYear) {
            $this->addFlash('warning', 'Aucun exercice fiscal sélectionné ou en cours.');
            return $this->redirectToRoute('app_accounting_fiscal_report_index');
        }
        
        // Get all the data (same as in liasseFiscale method)
        // ... (code omitted for brevity)
        
        // Create Excel file with multiple sheets
        $spreadsheet = new Spreadsheet();
        
        // Bilan sheet
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Bilan');
        
        $sheet->setCellValue('A1', 'BILAN - ' . $fiscalYear->getName());
        $sheet->setCellValue('A2', 'Du ' . $fiscalYear->getStartDate()->format('d/m/Y') . ' au ' . $fiscalYear->getEndDate()->format('d/m/Y'));
        
        $sheet->setCellValue('A4', 'ACTIF');
        $sheet->setCellValue('A5', 'Numéro');
        $sheet->setCellValue('B5', 'Compte');
        $sheet->setCellValue('C5', 'Montant');
        
        $row = 6;
        foreach ($assets as $asset) {
            $sheet->setCellValue('A' . $row, $asset['account']->getNumber());
            $sheet->setCellValue('B' . $row, $asset['account']->getName());
            $sheet->setCellValue('C' . $row, $asset['balance']);
            $row++;
        }
        
        $sheet->setCellValue('A' . $row, 'TOTAL ACTIF');
        $sheet->setCellValue('C' . $row, $totalAssets);
        $row += 2;
        
        $sheet->setCellValue('A' . $row, 'PASSIF');
        $row++;
        $sheet->setCellValue('A' . $row, 'Numéro');
        $sheet->setCellValue('B' . $row, 'Compte');
        $sheet->setCellValue('C' . $row, 'Montant');
        $row++;
        
        foreach ($liabilities as $liability) {
            $sheet->setCellValue('A' . $row, $liability['account']->getNumber());
            $sheet->setCellValue('B' . $row, $liability['account']->getName());
            $sheet->setCellValue('C' . $row, $liability['balance']);
            $row++;
        }
        
        $sheet->setCellValue('A' . $row, 'TOTAL PASSIF');
        $sheet->setCellValue('C' . $row, $totalLiabilities);
        $row += 2;
        
        $sheet->setCellValue('A' . $row, 'CAPITAUX PROPRES');
        $row++;
        $sheet->setCellValue('A' . $row, 'Numéro');
        $sheet->setCellValue('B' . $row, 'Compte');
        $sheet->setCellValue('C' . $row, 'Montant');
        $row++;
        
        foreach ($equity as $item) {
            $sheet->setCellValue('A' . $row, $item['account']->getNumber());
            $sheet->setCellValue('B' . $row, $item['account']->getName());
            $sheet->setCellValue('C' . $row, $item['balance']);
            $row++;
        }
        
        $sheet->setCellValue('A' . $row, 'Résultat de l\'exercice');
        $sheet->setCellValue('C' . $row, $netIncome);
        $row++;
        
        $sheet->setCellValue('A' . $row, 'TOTAL CAPITAUX PROPRES');
        $sheet->setCellValue('C' . $row, $totalEquity + $netIncome);
        $row++;
        
        $sheet->setCellValue('A' . $row, 'TOTAL PASSIF ET CAPITAUX PROPRES');
        $sheet->setCellValue('C' . $row, $totalLiabilities + $totalEquity + $netIncome);
        
        // Format cells
        $sheet->getStyle('A1:A2')->getFont()->setBold(true);
        $sheet->getStyle('A4')->getFont()->setBold(true);
        $sheet->getStyle('A5:C5')->getFont()->setBold(true);
        $sheet->getStyle('C6:C' . ($row))->getNumberFormat()->setFormatCode('#,##0.00');
        
        // Add CPC sheet
        $spreadsheet->createSheet();
        $spreadsheet->setActiveSheetIndex(1);
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('CPC');
        
        $sheet->setCellValue('A1', 'COMPTE DE PRODUITS ET CHARGES - ' . $fiscalYear->getName());
        $sheet->setCellValue('A2', 'Du ' . $fiscalYear->getStartDate()->format('d/m/Y') . ' au ' . $fiscalYear->getEndDate()->format('d/m/Y'));
        
        $sheet->setCellValue('A4', 'PRODUITS');
        $sheet->setCellValue('A5', 'Numéro');
        $sheet->setCellValue('B5', 'Compte');
        $sheet->setCellValue('C5', 'Montant');
        
        $row = 6;
        foreach ($revenue as $item) {
            $sheet->setCellValue('A' . $row, $item['account']->getNumber());
            $sheet->setCellValue('B' . $row, $item['account']->getName());
            $sheet->setCellValue('C' . $row, $item['balance']);
            $row++;
        }
        
        $sheet->setCellValue('A' . $row, 'TOTAL PRODUITS');
        $sheet->setCellValue('C' . $row, $totalRevenue);
        $row += 2;
        
        $sheet->setCellValue('A' . $row, 'CHARGES');
        $row++;
        $sheet->setCellValue('A' . $row, 'Numéro');
        $sheet->setCellValue('B' . $row, 'Compte');
        $sheet->setCellValue('C' . $row, 'Montant');
        $row++;
        
        foreach ($expenses as $item) {
            $sheet->setCellValue('A' . $row, $item['account']->getNumber());
            $sheet->setCellValue('B' . $row, $item['account']->getName());
            $sheet->setCellValue('C' . $row, $item['balance']);
            $row++;
        }
        
        $sheet->setCellValue('A' . $row, 'TOTAL CHARGES');
        $sheet->setCellValue('C' . $row, $totalExpenses);
        $row += 2;
        
        $sheet->setCellValue('A' . $row, 'RÉSULTAT NET');
        $sheet->setCellValue('C' . $row, $netIncome);
        
        // Format cells
        $sheet->getStyle('A1:A2')->getFont()->setBold(true);
        $sheet->getStyle('A4')->getFont()->setBold(true);
        $sheet->getStyle('A5:C5')->getFont()->setBold(true);
        $sheet->getStyle('C6:C' . ($row))->getNumberFormat()->setFormatCode('#,##0.00');
        
        // Auto size columns for all sheets
        foreach ($spreadsheet->getAllSheets() as $sheet) {
            foreach (range('A', 'C') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }
        }
        
        // Set active sheet to first sheet
        $spreadsheet->setActiveSheetIndex(0);
        
        // Create response
        $writer = new Xlsx($spreadsheet);
        
        $response = new StreamedResponse(
            function () use ($writer) {
                $writer->save('php://output');
            }
        );
        
        $filename = 'liasse_fiscale_' . $fiscalYear->getName() . '.xlsx';
        
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $filename
        ));
        
        return $response;
    }

    #[Route('/is', name: 'app_accounting_fiscal_report_is')]
    public function isReport(Request $request): Response
    {
        $fiscalYearId = $request->query->get('fiscal_year');
        $fiscalYear = null;
        
        if ($fiscalYearId) {
            $fiscalYear = $this->fiscalYearRepository->find($fiscalYearId);
        } else {
            $fiscalYear = $this->fiscalYearRepository->findCurrent();
        }
        
        if (!$fiscalYear) {
            $this->addFlash('warning', 'Aucun exercice fiscal sélectionné ou en cours.');
            return $this->redirectToRoute('app_accounting_fiscal_report_index');
        }
        
        // Get income statement data
        $revenue = [];
        $expenses = [];
        
        $revenueAccounts = $this->accountRepository->findByClass(7);
        $expenseAccounts = $this->accountRepository->findByClass(6);
        
        $totalRevenue = 0;
        $totalExpenses = 0;
        
        foreach ($revenueAccounts as $account) {
            $balance = $this->accountingService->getAccountBalance(
                $account,
                $fiscalYear->getStartDate(),
                $fiscalYear->getEndDate()
            );
            
            if (abs($balance) > 0.01) {
                $revenue[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalRevenue += $balance;
            }
        }
        
        foreach ($expenseAccounts as $account) {
            $balance = $this->accountingService->getAccountBalance(
                $account,
                $fiscalYear->getStartDate(),
                $fiscalYear->getEndDate()
            );
            
            if (abs($balance) > 0.01) {
                $expenses[] = [
                    'account' => $account,
                    'balance' => $balance
                ];
                $totalExpenses += $balance;
            }
        }
        
        // Calculate net income
        $netIncome = $totalRevenue - $totalExpenses;
        
        // Calculate IS (corporate tax)
        $isRate = 0.30; // 30% standard rate
        $isAmount = max(0, $netIncome * $isRate);
        
        return $this->render('accounting/fiscal_report/is.html.twig', [
            'fiscalYear' => $fiscalYear,
            'revenue' => $revenue,
            'expenses' => $expenses,
            'totalRevenue' => $totalRevenue,
            'totalExpenses' => $totalExpenses,
            'netIncome' => $netIncome,
            'isRate' => $isRate,
            'isAmount' => $isAmount
        ]);
    }
}
