<?php

namespace App\Controller\Accounting;

use App\Entity\Accounting\FiscalYear;
use App\Form\Accounting\FiscalYearType;
use App\Repository\Accounting\FiscalYearRepository;
use App\Service\AccountingService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/accounting/fiscal-year')]
#[IsGranted('IS_AUTHENTICATED_FULLY')]
class FiscalYearController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private FiscalYearRepository $fiscalYearRepository,
        private AccountingService $accountingService
    ) {
    }

    #[Route('', name: 'app_accounting_fiscal_year_index', methods: ['GET'])]
    public function index(): Response
    {
        $fiscalYears = $this->fiscalYearRepository->findAllOrdered();

        return $this->render('accounting/fiscal_year/index.html.twig', [
            'fiscalYears' => $fiscalYears,
        ]);
    }

    #[Route('/new', name: 'app_accounting_fiscal_year_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $fiscalYear = new FiscalYear();

        // Set default dates (January 1st to December 31st of current year)
        $currentYear = (int)(new \DateTime())->format('Y');
        $fiscalYear->setStartDate(new \DateTime($currentYear . '-01-01'));
        $fiscalYear->setEndDate(new \DateTime($currentYear . '-12-31'));
        $fiscalYear->setName('Exercice ' . $currentYear);

        $form = $this->createForm(FiscalYearType::class, $fiscalYear);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Check if there's already a current fiscal year
            if ($fiscalYear->isCurrent()) {
                $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
                if ($currentFiscalYear) {
                    $currentFiscalYear->setIsCurrent(false);
                }
            }

            $this->entityManager->persist($fiscalYear);
            $this->entityManager->flush();

            $this->addFlash('success', 'L\'exercice fiscal a été créé avec succès.');

            return $this->redirectToRoute('app_accounting_fiscal_year_index');
        }

        return $this->render('accounting/fiscal_year/new.html.twig', [
            'fiscalYear' => $fiscalYear,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_accounting_fiscal_year_show', methods: ['GET'])]
    public function show(FiscalYear $fiscalYear): Response
    {
        return $this->render('accounting/fiscal_year/show.html.twig', [
            'fiscalYear' => $fiscalYear,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_accounting_fiscal_year_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, FiscalYear $fiscalYear): Response
    {
        // Check if the fiscal year is closed
        if ($fiscalYear->isIsClosed()) {
            $this->addFlash('error', 'Un exercice fiscal clôturé ne peut pas être modifié.');
            return $this->redirectToRoute('app_accounting_fiscal_year_show', ['id' => $fiscalYear->getId()]);
        }

        $form = $this->createForm(FiscalYearType::class, $fiscalYear);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Check if there's already a current fiscal year
            if ($fiscalYear->isCurrent()) {
                $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
                if ($currentFiscalYear && $currentFiscalYear->getId() !== $fiscalYear->getId()) {
                    $currentFiscalYear->setIsCurrent(false);
                }
            }

            $this->entityManager->flush();

            $this->addFlash('success', 'L\'exercice fiscal a été modifié avec succès.');

            return $this->redirectToRoute('app_accounting_fiscal_year_index');
        }

        return $this->render('accounting/fiscal_year/edit.html.twig', [
            'fiscalYear' => $fiscalYear,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/close', name: 'app_accounting_fiscal_year_close', methods: ['POST'])]
    public function close(Request $request, FiscalYear $fiscalYear): Response
    {
        if ($this->isCsrfTokenValid('close'.$fiscalYear->getId(), $request->request->get('_token'))) {
            try {
                $fiscalYear->setIsClosed(true);
                $fiscalYear->setClosedAt(new \DateTime());
                $this->entityManager->flush();
                $this->addFlash('success', 'L\'exercice fiscal a été clôturé avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la clôture de l\'exercice fiscal : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_accounting_fiscal_year_show', ['id' => $fiscalYear->getId()]);
    }

    #[Route('/{id}/set-current', name: 'app_accounting_fiscal_year_set_current', methods: ['POST'])]
    public function setCurrent(Request $request, FiscalYear $fiscalYear): Response
    {
        if ($this->isCsrfTokenValid('set_current'.$fiscalYear->getId(), $request->request->get('_token'))) {
            // Check if the fiscal year is closed
            if ($fiscalYear->isIsClosed()) {
                $this->addFlash('error', 'Un exercice fiscal clôturé ne peut pas être défini comme exercice en cours.');
                return $this->redirectToRoute('app_accounting_fiscal_year_show', ['id' => $fiscalYear->getId()]);
            }

            // Set all fiscal years as not current
            $allFiscalYears = $this->fiscalYearRepository->findAll();
            foreach ($allFiscalYears as $year) {
                $year->setIsCurrent(false);
            }

            // Set this fiscal year as current
            $fiscalYear->setIsCurrent(true);

            $this->entityManager->flush();

            $this->addFlash('success', 'L\'exercice fiscal a été défini comme exercice en cours avec succès.');
        }

        return $this->redirectToRoute('app_accounting_fiscal_year_show', ['id' => $fiscalYear->getId()]);
    }

    #[Route('/{id}/delete', name: 'app_accounting_fiscal_year_delete', methods: ['POST'])]
    public function delete(Request $request, FiscalYear $fiscalYear): Response
    {
        if ($this->isCsrfTokenValid('delete'.$fiscalYear->getId(), $request->request->get('_token'))) {
            // Check if the fiscal year is closed
            if ($fiscalYear->isIsClosed()) {
                $this->addFlash('error', 'Un exercice fiscal clôturé ne peut pas être supprimé.');
                return $this->redirectToRoute('app_accounting_fiscal_year_index');
            }

            // Check if the fiscal year is current
            if ($fiscalYear->isIsCurrent()) {
                $this->addFlash('error', 'L\'exercice fiscal en cours ne peut pas être supprimé.');
                return $this->redirectToRoute('app_accounting_fiscal_year_index');
            }

            // Check if the fiscal year has tax declarations
            if (!$fiscalYear->getTaxDeclarations()->isEmpty()) {
                $this->addFlash('error', 'Un exercice fiscal contenant des déclarations fiscales ne peut pas être supprimé.');
                return $this->redirectToRoute('app_accounting_fiscal_year_index');
            }

            $this->entityManager->remove($fiscalYear);
            $this->entityManager->flush();

            $this->addFlash('success', 'L\'exercice fiscal a été supprimé avec succès.');
        }

        return $this->redirectToRoute('app_accounting_fiscal_year_index');
    }
}
