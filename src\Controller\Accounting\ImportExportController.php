<?php

namespace App\Controller\Accounting;

use App\Repository\Accounting\JournalRepository;
use App\Service\AccountingImportExportService;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/accounting/import-export')]
#[IsGranted('ROLE_ACCOUNTING')]
class ImportExportController extends AbstractController
{
    public function __construct(
        private AccountingImportExportService $importExportService,
        private JournalRepository $journalRepository
    ) {
    }

    #[Route('', name: 'app_accounting_import_export_index')]
    public function index(): Response
    {
        $journals = $this->journalRepository->findAll();
        
        return $this->render('accounting/import_export/index.html.twig', [
            'journals' => $journals
        ]);
    }

    #[Route('/export-accounts', name: 'app_accounting_export_accounts')]
    public function exportAccounts(): Response
    {
        $spreadsheet = $this->importExportService->exportAccounts();
        
        $writer = new Xlsx($spreadsheet);
        
        $response = new StreamedResponse(
            function () use ($writer) {
                $writer->save('php://output');
            }
        );
        
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            'plan_comptable_' . date('Y-m-d') . '.xlsx'
        ));
        
        return $response;
    }

    #[Route('/import-accounts', name: 'app_accounting_import_accounts', methods: ['POST'])]
    #[IsGranted('ROLE_ACCOUNTING_ADMIN')]
    public function importAccounts(Request $request): Response
    {
        $file = $request->files->get('accounts_file');
        
        if (!$file) {
            $this->addFlash('error', 'Aucun fichier n\'a été téléchargé.');
            return $this->redirectToRoute('app_accounting_import_export_index');
        }
        
        try {
            $result = $this->importExportService->importAccounts($file);
            
            if (!empty($result['errors'])) {
                foreach ($result['errors'] as $error) {
                    $this->addFlash('warning', $error);
                }
            }
            
            $this->addFlash('success', $result['created'] . ' comptes créés et ' . $result['updated'] . ' comptes mis à jour avec succès.');
        } catch (\Exception $e) {
            $this->addFlash('error', 'Une erreur est survenue lors de l\'import : ' . $e->getMessage());
        }
        
        return $this->redirectToRoute('app_accounting_import_export_index');
    }

    #[Route('/export-journal-entries', name: 'app_accounting_export_journal_entries')]
    public function exportJournalEntries(Request $request): Response
    {
        $journalId = $request->query->get('journal');
        $startDate = $request->query->get('start_date') ? new \DateTime($request->query->get('start_date')) : null;
        $endDate = $request->query->get('end_date') ? new \DateTime($request->query->get('end_date')) : null;
        
        $journal = $journalId ? $this->journalRepository->find($journalId) : null;
        
        $spreadsheet = $this->importExportService->exportJournalEntries($journal, $startDate, $endDate);
        
        $writer = new Xlsx($spreadsheet);
        
        $response = new StreamedResponse(
            function () use ($writer) {
                $writer->save('php://output');
            }
        );
        
        $filename = 'ecritures_comptables';
        if ($journal) {
            $filename .= '_' . $journal->getCode();
        }
        if ($startDate) {
            $filename .= '_du_' . $startDate->format('Y-m-d');
        }
        if ($endDate) {
            $filename .= '_au_' . $endDate->format('Y-m-d');
        }
        $filename .= '.xlsx';
        
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $filename
        ));
        
        return $response;
    }

    #[Route('/import-journal-entries', name: 'app_accounting_import_journal_entries', methods: ['POST'])]
    #[IsGranted('ROLE_ACCOUNTING_ADMIN')]
    public function importJournalEntries(Request $request): Response
    {
        $file = $request->files->get('entries_file');
        
        if (!$file) {
            $this->addFlash('error', 'Aucun fichier n\'a été téléchargé.');
            return $this->redirectToRoute('app_accounting_import_export_index');
        }
        
        try {
            $result = $this->importExportService->importJournalEntries($file, $this->getUser());
            
            if (!empty($result['errors'])) {
                foreach ($result['errors'] as $error) {
                    $this->addFlash('warning', $error);
                }
            }
            
            $this->addFlash('success', $result['created'] . ' écritures comptables créées avec succès.');
        } catch (\Exception $e) {
            $this->addFlash('error', 'Une erreur est survenue lors de l\'import : ' . $e->getMessage());
        }
        
        return $this->redirectToRoute('app_accounting_import_export_index');
    }

    #[Route('/export-accounts-template', name: 'app_accounting_export_accounts_template')]
    public function exportAccountsTemplate(): Response
    {
        $spreadsheet = $this->importExportService->exportChartOfAccountsTemplate();
        
        $writer = new Xlsx($spreadsheet);
        
        $response = new StreamedResponse(
            function () use ($writer) {
                $writer->save('php://output');
            }
        );
        
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            'modele_plan_comptable.xlsx'
        ));
        
        return $response;
    }

    #[Route('/export-journal-entries-template', name: 'app_accounting_export_journal_entries_template')]
    public function exportJournalEntriesTemplate(): Response
    {
        $spreadsheet = $this->importExportService->exportJournalEntriesTemplate();
        
        $writer = new Xlsx($spreadsheet);
        
        $response = new StreamedResponse(
            function () use ($writer) {
                $writer->save('php://output');
            }
        );
        
        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Disposition', $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            'modele_ecritures_comptables.xlsx'
        ));
        
        return $response;
    }
}
