<?php

namespace App\Controller\Accounting;

use App\Entity\Accounting\Journal;
use App\Entity\Accounting\JournalEntry;
use App\Form\Accounting\JournalEntryType;
use App\Form\Accounting\JournalType;
use App\Repository\Accounting\JournalEntryRepository;
use App\Repository\Accounting\JournalRepository;
use App\Service\AccountingService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/accounting/journal')]
#[IsGranted('IS_AUTHENTICATED_FULLY')]
class JournalController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private JournalRepository $journalRepository,
        private JournalEntryRepository $journalEntryRepository,
        private AccountingService $accountingService
    ) {
    }

    #[Route('', name: 'app_accounting_journal_index', methods: ['GET'])]
    public function index(): Response
    {
        $journals = $this->journalRepository->findAll();

        return $this->render('accounting/journal/index.html.twig', [
            'journals' => $journals,
        ]);
    }

    #[Route('/new', name: 'app_accounting_journal_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $journal = new Journal();
        $form = $this->createForm(JournalType::class, $journal);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($journal);
            $this->entityManager->flush();

            $this->addFlash('success', 'Le journal a été créé avec succès.');

            return $this->redirectToRoute('app_accounting_journal_index');
        }

        return $this->render('accounting/journal/new.html.twig', [
            'journal' => $journal,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_accounting_journal_show', methods: ['GET'])]
    public function show(Journal $journal): Response
    {
        // Get journal entries
        $journalEntries = $this->journalEntryRepository->findByJournal($journal);

        // Calculate total amount
        $totalAmount = 0;
        foreach ($journalEntries as $entry) {
            foreach ($entry->getLines() as $line) {
                if ($line->isIsDebit()) {
                    $totalAmount += $line->getAmount();
                }
            }
        }

        return $this->render('accounting/journal/show.html.twig', [
            'journal' => $journal,
            'journalEntries' => $journalEntries,
            'totalAmount' => $totalAmount
        ]);
    }

    #[Route('/{id}/edit', name: 'app_accounting_journal_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Journal $journal): Response
    {
        $form = $this->createForm(JournalType::class, $journal);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();

            $this->addFlash('success', 'Le journal a été modifié avec succès.');

            return $this->redirectToRoute('app_accounting_journal_index');
        }

        return $this->render('accounting/journal/edit.html.twig', [
            'journal' => $journal,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/delete', name: 'app_accounting_journal_delete', methods: ['POST'])]
    #[IsGranted('ROLE_ACCOUNTING_ADMIN')]
    public function delete(Request $request, Journal $journal): Response
    {
        if ($this->isCsrfTokenValid('delete'.$journal->getId(), $request->request->get('_token'))) {
            // Check if the journal has entries
            $entries = $this->journalEntryRepository->findByJournal($journal);

            if (!empty($entries)) {
                $this->addFlash('error', 'Ce journal ne peut pas être supprimé car il contient des écritures.');
                return $this->redirectToRoute('app_accounting_journal_index');
            }

            $this->entityManager->remove($journal);
            $this->entityManager->flush();

            $this->addFlash('success', 'Le journal a été supprimé avec succès.');
        }

        return $this->redirectToRoute('app_accounting_journal_index');
    }

    #[Route('/{id}/new-entry', name: 'app_accounting_journal_entry_new', methods: ['GET', 'POST'])]
    public function newEntry(Request $request, Journal $journal): Response
    {
        $entry = new JournalEntry();
        $entry->setJournal($journal);
        $entry->setDate(new \DateTime());
        $entry->setCreatedBy($this->getUser());

        $form = $this->createForm(JournalEntryType::class, $entry);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($entry);
            $this->entityManager->flush();

            $this->addFlash('success', 'L\'écriture a été créée avec succès.');

            return $this->redirectToRoute('app_accounting_journal_show', ['id' => $journal->getId()]);
        }

        return $this->render('accounting/journal_entry/new.html.twig', [
            'journal' => $journal,
            'entry' => $entry,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/entry/{entryId}', name: 'app_accounting_journal_entry_show', methods: ['GET'])]
    public function showEntry(Journal $journal, int $entryId): Response
    {
        $entry = $this->journalEntryRepository->find($entryId);

        if (!$entry || $entry->getJournal() !== $journal) {
            throw $this->createNotFoundException('L\'écriture demandée n\'existe pas.');
        }

        // Calculate totals
        $totalDebit = 0;
        $totalCredit = 0;

        foreach ($entry->getLines() as $line) {
            if ($line->isIsDebit()) {
                $totalDebit += $line->getAmount();
            } else {
                $totalCredit += $line->getAmount();
            }
        }

        return $this->render('accounting/journal_entry/show.html.twig', [
            'journal' => $journal,
            'entry' => $entry,
            'totalDebit' => $totalDebit,
            'totalCredit' => $totalCredit
        ]);
    }

    #[Route('/{id}/entry/{entryId}/post', name: 'app_accounting_journal_entry_post', methods: ['POST'])]
    public function postEntry(Request $request, Journal $journal, int $entryId): Response
    {
        if ($this->isCsrfTokenValid('post'.$journal->getId().$entryId, $request->request->get('_token'))) {
            try {
                $entry = $this->journalEntryRepository->find($entryId);

                if (!$entry || $entry->getJournal() !== $journal) {
                    throw $this->createNotFoundException('L\'écriture demandée n\'existe pas.');
                }

                $entry->setStatus(JournalEntry::STATUS_POSTED);
                $entry->setPostedAt(new \DateTime());
                $entry->setPostedBy($this->getUser());

                $this->entityManager->flush();

                $this->addFlash('success', 'L\'écriture a été comptabilisée avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la comptabilisation de l\'écriture : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_accounting_journal_entry_show', [
            'id' => $journal->getId(),
            'entryId' => $entryId
        ]);
    }

    #[Route('/{id}/entry/{entryId}/cancel', name: 'app_accounting_journal_entry_cancel', methods: ['POST'])]
    public function cancelEntry(Request $request, Journal $journal, int $entryId): Response
    {
        if ($this->isCsrfTokenValid('cancel'.$journal->getId().$entryId, $request->request->get('_token'))) {
            try {
                $entry = $this->journalEntryRepository->find($entryId);

                if (!$entry || $entry->getJournal() !== $journal) {
                    throw $this->createNotFoundException('L\'écriture demandée n\'existe pas.');
                }

                $entry->setStatus(JournalEntry::STATUS_CANCELLED);
                $entry->setCancelledAt(new \DateTime());
                $entry->setCancelledBy($this->getUser());

                $this->entityManager->flush();

                $this->addFlash('success', 'L\'écriture a été annulée avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'annulation de l\'écriture : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_accounting_journal_entry_show', [
            'id' => $journal->getId(),
            'entryId' => $entryId
        ]);
    }
}
