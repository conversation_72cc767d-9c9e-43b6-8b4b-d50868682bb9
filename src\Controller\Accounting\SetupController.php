<?php

namespace App\Controller\Accounting;

use App\Entity\Accounting\Account;
use App\Entity\Accounting\FiscalYear;
use App\Entity\Accounting\Journal;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/accounting/setup')]
#[IsGranted('IS_AUTHENTICATED_FULLY')]
class SetupController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager
    ) {
    }

    #[Route('/init', name: 'app_accounting_setup_init')]
    public function init(): Response
    {
        // Create fiscal year
        $this->createFiscalYear();
        
        // Create journals
        $this->createJournals();
        
        // Create accounts
        $this->createAccounts();
        
        return $this->render('accounting/setup/init.html.twig', [
            'success' => true
        ]);
    }
    
    private function createFiscalYear(): void
    {
        $currentYear = (int)(new \DateTime())->format('Y');
        
        $fiscalYear = new FiscalYear();
        $fiscalYear->setName('Exercice ' . $currentYear);
        $fiscalYear->setStartDate(new \DateTime($currentYear . '-01-01'));
        $fiscalYear->setEndDate(new \DateTime($currentYear . '-12-31'));
        $fiscalYear->setIsCurrent(true);
        
        $this->entityManager->persist($fiscalYear);
        $this->entityManager->flush();
    }
    
    private function createJournals(): void
    {
        $journals = [
            [
                'code' => 'ACH',
                'name' => 'Journal des achats',
                'description' => 'Journal des achats de biens et services'
            ],
            [
                'code' => 'VTE',
                'name' => 'Journal des ventes',
                'description' => 'Journal des ventes de biens et services'
            ],
            [
                'code' => 'BNQ',
                'name' => 'Journal de banque',
                'description' => 'Journal des opérations bancaires'
            ],
            [
                'code' => 'CAI',
                'name' => 'Journal de caisse',
                'description' => 'Journal des opérations de caisse'
            ],
            [
                'code' => 'OD',
                'name' => 'Journal des opérations diverses',
                'description' => 'Journal des opérations diverses'
            ]
        ];
        
        foreach ($journals as $journalData) {
            $journal = new Journal();
            $journal->setCode($journalData['code']);
            $journal->setName($journalData['name']);
            $journal->setDescription($journalData['description']);
            $journal->setIsActive(true);
            
            $this->entityManager->persist($journal);
        }
        
        $this->entityManager->flush();
    }
    
    private function createAccounts(): void
    {
        $accounts = [
            // Class 1 - Comptes de financement permanent
            ['number' => '101000', 'name' => 'Capital social', 'type' => 'equity', 'isDebitBalance' => false],
            ['number' => '110000', 'name' => 'Report à nouveau', 'type' => 'equity', 'isDebitBalance' => false],
            ['number' => '120000', 'name' => 'Résultat de l\'exercice', 'type' => 'equity', 'isDebitBalance' => false],
            ['number' => '131000', 'name' => 'Subventions d\'investissement', 'type' => 'equity', 'isDebitBalance' => false],
            ['number' => '151000', 'name' => 'Provisions pour risques', 'type' => 'liability', 'isDebitBalance' => false],
            ['number' => '171000', 'name' => 'Dettes de financement', 'type' => 'liability', 'isDebitBalance' => false],
            
            // Class 2 - Comptes d'actif immobilisé
            ['number' => '211000', 'name' => 'Frais de constitution', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '222000', 'name' => 'Brevets, marques et droits similaires', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '231000', 'name' => 'Terrains', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '232000', 'name' => 'Constructions', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '233000', 'name' => 'Installations techniques, matériel et outillage', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '234000', 'name' => 'Matériel de transport', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '235000', 'name' => 'Mobilier, matériel de bureau et aménagements divers', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '251000', 'name' => 'Titres de participation', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '281000', 'name' => 'Amortissements des immobilisations incorporelles', 'type' => 'asset', 'isDebitBalance' => false],
            ['number' => '282000', 'name' => 'Amortissements des immobilisations corporelles', 'type' => 'asset', 'isDebitBalance' => false],
            
            // Class 3 - Comptes d'actif circulant
            ['number' => '311000', 'name' => 'Marchandises', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '321000', 'name' => 'Matières et fournitures consommables', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '341000', 'name' => 'Fournisseurs débiteurs, avances et acomptes', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '342000', 'name' => 'Clients et comptes rattachés', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '343000', 'name' => 'Personnel débiteur', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '345000', 'name' => 'État débiteur', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '350000', 'name' => 'Titres et valeurs de placement', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '370000', 'name' => 'Écarts de conversion - Actif', 'type' => 'asset', 'isDebitBalance' => true],
            ['number' => '391000', 'name' => 'Provisions pour dépréciation des stocks', 'type' => 'asset', 'isDebitBalance' => false],
            ['number' => '394000', 'name' => 'Provisions pour dépréciation des créances', 'type' => 'asset', 'isDebitBalance' => false],
            
            // Class 4 - Comptes de passif circulant
            ['number' => '411000', 'name' => 'Fournisseurs et comptes rattachés', 'type' => 'liability', 'isDebitBalance' => false],
            ['number' => '442000', 'name' => 'Clients créditeurs, avances et acomptes', 'type' => 'liability', 'isDebitBalance' => false],
            ['number' => '443000', 'name' => 'Personnel créditeur', 'type' => 'liability', 'isDebitBalance' => false],
            ['number' => '444000', 'name' => 'Organismes sociaux', 'type' => 'liability', 'isDebitBalance' => false],
            ['number' => '445000', 'name' => 'État créditeur', 'type' => 'liability', 'isDebitBalance' => false],
            ['number' => '445660', 'name' => 'TVA déductible', 'type' => 'liability', 'isDebitBalance' => true],
            ['number' => '445710', 'name' => 'TVA collectée', 'type' => 'liability', 'isDebitBalance' => false],
            ['number' => '447000', 'name' => 'Autres créditeurs', 'type' => 'liability', 'isDebitBalance' => false],
            ['number' => '450000', 'name' => 'Autres provisions pour risques et charges', 'type' => 'liability', 'isDebitBalance' => false],
            ['number' => '470000', 'name' => 'Écarts de conversion - Passif', 'type' => 'liability', 'isDebitBalance' => false],
            
            // Class 5 - Comptes de trésorerie
            ['number' => '511000', 'name' => 'Chèques et valeurs à encaisser', 'type' => 'asset', 'category' => 'bank', 'isDebitBalance' => true],
            ['number' => '512000', 'name' => 'Banques', 'type' => 'asset', 'category' => 'bank', 'isDebitBalance' => true],
            ['number' => '516000', 'name' => 'Caisses', 'type' => 'asset', 'category' => 'cash', 'isDebitBalance' => true],
            ['number' => '552000', 'name' => 'Crédits de trésorerie', 'type' => 'liability', 'isDebitBalance' => false],
            ['number' => '590000', 'name' => 'Provisions pour dépréciation des comptes de trésorerie', 'type' => 'asset', 'isDebitBalance' => false],
            
            // Class 6 - Comptes de charges
            ['number' => '611000', 'name' => 'Achats de marchandises', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '612000', 'name' => 'Achats de matières et fournitures consommables', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '613000', 'name' => 'Autres charges externes', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '616000', 'name' => 'Impôts et taxes', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '617000', 'name' => 'Charges de personnel', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '618000', 'name' => 'Autres charges d\'exploitation', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '619000', 'name' => 'Dotations d\'exploitation', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '631000', 'name' => 'Charges d\'intérêts', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '638000', 'name' => 'Autres charges financières', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '639000', 'name' => 'Dotations financières', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '651000', 'name' => 'Valeurs nettes d\'amortissements des immobilisations cédées', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '658000', 'name' => 'Autres charges non courantes', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '659000', 'name' => 'Dotations non courantes', 'type' => 'expense', 'isDebitBalance' => true],
            ['number' => '670000', 'name' => 'Impôts sur les résultats', 'type' => 'expense', 'isDebitBalance' => true],
            
            // Class 7 - Comptes de produits
            ['number' => '711000', 'name' => 'Ventes de marchandises', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '712000', 'name' => 'Ventes de biens et services produits', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '713000', 'name' => 'Variation des stocks de produits', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '714000', 'name' => 'Immobilisations produites par l\'entreprise pour elle-même', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '716000', 'name' => 'Subventions d\'exploitation', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '718000', 'name' => 'Autres produits d\'exploitation', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '719000', 'name' => 'Reprises d\'exploitation; transferts de charges', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '731000', 'name' => 'Produits d\'intérêts', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '738000', 'name' => 'Autres produits financiers', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '739000', 'name' => 'Reprises financières; transferts de charges', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '751000', 'name' => 'Produits des cessions d\'immobilisations', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '756000', 'name' => 'Subventions d\'équilibre', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '757000', 'name' => 'Reprises sur subventions d\'investissement', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '758000', 'name' => 'Autres produits non courants', 'type' => 'revenue', 'isDebitBalance' => false],
            ['number' => '759000', 'name' => 'Reprises non courantes; transferts de charges', 'type' => 'revenue', 'isDebitBalance' => false],
        ];
        
        foreach ($accounts as $accountData) {
            $account = new Account();
            $account->setNumber($accountData['number']);
            $account->setName($accountData['name']);
            $account->setType($accountData['type'] ?? null);
            $account->setCategory($accountData['category'] ?? null);
            $account->setIsDebitBalance($accountData['isDebitBalance']);
            $account->setIsActive(true);
            
            $this->entityManager->persist($account);
        }
        
        $this->entityManager->flush();
    }
}
