<?php

namespace App\Controller\Accounting;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/accounting/simple')]
class SimpleController extends AbstractController
{
    #[Route('/', name: 'app_accounting_simple_index')]
    public function index(): Response
    {
        return $this->render('accounting/simple/index.html.twig');
    }

    #[Route('/new', name: 'app_accounting_simple_new')]
    public function new(): Response
    {
        return $this->render('accounting/simple/new.html.twig');
    }
}
