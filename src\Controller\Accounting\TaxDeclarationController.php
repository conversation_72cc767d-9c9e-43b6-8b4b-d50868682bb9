<?php

namespace App\Controller\Accounting;

use App\Entity\Accounting\TaxDeclaration;
use App\Form\Accounting\TaxDeclarationType;
use App\Repository\Accounting\FiscalYearRepository;
use App\Repository\Accounting\TaxDeclarationRepository;
use App\Service\AccountingService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\String\Slugger\SluggerInterface;

#[Route('/accounting/tax-declaration')]
#[IsGranted('ROLE_ACCOUNTING')]
class TaxDeclarationController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private TaxDeclarationRepository $taxDeclarationRepository,
        private FiscalYearRepository $fiscalYearRepository,
        private AccountingService $accountingService,
        private SluggerInterface $slugger
    ) {
    }

    #[Route('', name: 'app_accounting_tax_declaration_index', methods: ['GET'])]
    public function index(): Response
    {
        $taxDeclarations = $this->taxDeclarationRepository->findBy([], ['dueDate' => 'DESC']);
        
        return $this->render('accounting/tax_declaration/index.html.twig', [
            'taxDeclarations' => $taxDeclarations,
        ]);
    }

    #[Route('/new', name: 'app_accounting_tax_declaration_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $taxDeclaration = new TaxDeclaration();
        
        // Set default values
        $taxDeclaration->setCreatedBy($this->getUser());
        $taxDeclaration->setCreatedAt(new \DateTime());
        
        // Get current fiscal year
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        if ($currentFiscalYear) {
            $taxDeclaration->setFiscalYear($currentFiscalYear);
        }
        
        $form = $this->createForm(TaxDeclarationType::class, $taxDeclaration);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Handle file upload
            $attachmentFile = $form->get('attachmentFile')->getData();
            
            if ($attachmentFile) {
                $originalFilename = pathinfo($attachmentFile->getClientOriginalName(), PATHINFO_FILENAME);
                $safeFilename = $this->slugger->slug($originalFilename);
                $newFilename = $safeFilename.'-'.uniqid().'.'.$attachmentFile->guessExtension();
                
                try {
                    $attachmentFile->move(
                        $this->getParameter('tax_declarations_directory'),
                        $newFilename
                    );
                    
                    $taxDeclaration->setAttachmentPath($newFilename);
                } catch (FileException $e) {
                    $this->addFlash('error', 'Une erreur est survenue lors du téléchargement du fichier.');
                }
            }
            
            $this->entityManager->persist($taxDeclaration);
            $this->entityManager->flush();

            $this->addFlash('success', 'La déclaration fiscale a été créée avec succès.');

            return $this->redirectToRoute('app_accounting_tax_declaration_index');
        }

        return $this->render('accounting/tax_declaration/new.html.twig', [
            'taxDeclaration' => $taxDeclaration,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/new-tva', name: 'app_accounting_tax_declaration_new_tva', methods: ['GET', 'POST'])]
    public function newTVA(Request $request): Response
    {
        $taxDeclaration = new TaxDeclaration();
        
        // Set default values for TVA declaration
        $taxDeclaration->setType(TaxDeclaration::TYPE_TVA);
        $taxDeclaration->setCreatedBy($this->getUser());
        $taxDeclaration->setCreatedAt(new \DateTime());
        
        // Get current fiscal year
        $currentFiscalYear = $this->fiscalYearRepository->findCurrent();
        if ($currentFiscalYear) {
            $taxDeclaration->setFiscalYear($currentFiscalYear);
        }
        
        // Set default period (monthly)
        $taxDeclaration->setPeriod(TaxDeclaration::PERIOD_MONTHLY);
        
        // Set default dates (previous month)
        $previousMonth = (new \DateTime())->modify('first day of previous month');
        $lastDayOfPreviousMonth = (new \DateTime())->modify('last day of previous month');
        
        $taxDeclaration->setStartDate($previousMonth);
        $taxDeclaration->setEndDate($lastDayOfPreviousMonth);
        $taxDeclaration->setPeriodNumber((int)$previousMonth->format('n'));
        
        // Set default due date (20th of current month)
        $dueDate = (new \DateTime())->modify('first day of this month')->modify('+19 days');
        $taxDeclaration->setDueDate($dueDate);
        
        // Get TVA data from accounting
        try {
            $tvaData = $this->accountingService->generateTVADeclarationData(
                $previousMonth,
                $lastDayOfPreviousMonth
            );
            
            $taxDeclaration->setTaxableAmount($tvaData['taxableAmount']);
            $taxDeclaration->setTaxAmount($tvaData['collectedTax']);
            $taxDeclaration->setDeductibleAmount($tvaData['deductibleTax']);
            $taxDeclaration->setNetAmount($tvaData['netTax']);
        } catch (\Exception $e) {
            $this->addFlash('warning', 'Impossible de calculer automatiquement les montants de TVA : ' . $e->getMessage());
        }
        
        $form = $this->createForm(TaxDeclarationType::class, $taxDeclaration, [
            'type_disabled' => true
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Handle file upload
            $attachmentFile = $form->get('attachmentFile')->getData();
            
            if ($attachmentFile) {
                $originalFilename = pathinfo($attachmentFile->getClientOriginalName(), PATHINFO_FILENAME);
                $safeFilename = $this->slugger->slug($originalFilename);
                $newFilename = $safeFilename.'-'.uniqid().'.'.$attachmentFile->guessExtension();
                
                try {
                    $attachmentFile->move(
                        $this->getParameter('tax_declarations_directory'),
                        $newFilename
                    );
                    
                    $taxDeclaration->setAttachmentPath($newFilename);
                } catch (FileException $e) {
                    $this->addFlash('error', 'Une erreur est survenue lors du téléchargement du fichier.');
                }
            }
            
            $this->entityManager->persist($taxDeclaration);
            $this->entityManager->flush();

            $this->addFlash('success', 'La déclaration de TVA a été créée avec succès.');

            return $this->redirectToRoute('app_accounting_tax_declaration_index');
        }

        return $this->render('accounting/tax_declaration/new_tva.html.twig', [
            'taxDeclaration' => $taxDeclaration,
            'form' => $form->createView(),
            'tvaData' => $tvaData ?? null
        ]);
    }

    #[Route('/{id}', name: 'app_accounting_tax_declaration_show', methods: ['GET'])]
    public function show(TaxDeclaration $taxDeclaration): Response
    {
        return $this->render('accounting/tax_declaration/show.html.twig', [
            'taxDeclaration' => $taxDeclaration,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_accounting_tax_declaration_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, TaxDeclaration $taxDeclaration): Response
    {
        // Check if the declaration is already submitted or paid
        if (in_array($taxDeclaration->getStatus(), [TaxDeclaration::STATUS_SUBMITTED, TaxDeclaration::STATUS_PAID])) {
            $this->addFlash('error', 'Une déclaration soumise ou payée ne peut pas être modifiée.');
            return $this->redirectToRoute('app_accounting_tax_declaration_show', ['id' => $taxDeclaration->getId()]);
        }
        
        $form = $this->createForm(TaxDeclarationType::class, $taxDeclaration, [
            'type_disabled' => true
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Handle file upload
            $attachmentFile = $form->get('attachmentFile')->getData();
            
            if ($attachmentFile) {
                $originalFilename = pathinfo($attachmentFile->getClientOriginalName(), PATHINFO_FILENAME);
                $safeFilename = $this->slugger->slug($originalFilename);
                $newFilename = $safeFilename.'-'.uniqid().'.'.$attachmentFile->guessExtension();
                
                try {
                    $attachmentFile->move(
                        $this->getParameter('tax_declarations_directory'),
                        $newFilename
                    );
                    
                    // Delete old file if exists
                    if ($taxDeclaration->getAttachmentPath()) {
                        $oldFilePath = $this->getParameter('tax_declarations_directory').'/'.$taxDeclaration->getAttachmentPath();
                        if (file_exists($oldFilePath)) {
                            unlink($oldFilePath);
                        }
                    }
                    
                    $taxDeclaration->setAttachmentPath($newFilename);
                } catch (FileException $e) {
                    $this->addFlash('error', 'Une erreur est survenue lors du téléchargement du fichier.');
                }
            }
            
            $this->entityManager->flush();

            $this->addFlash('success', 'La déclaration fiscale a été modifiée avec succès.');

            return $this->redirectToRoute('app_accounting_tax_declaration_index');
        }

        return $this->render('accounting/tax_declaration/edit.html.twig', [
            'taxDeclaration' => $taxDeclaration,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/submit', name: 'app_accounting_tax_declaration_submit', methods: ['POST'])]
    public function submit(Request $request, TaxDeclaration $taxDeclaration): Response
    {
        if ($this->isCsrfTokenValid('submit'.$taxDeclaration->getId(), $request->request->get('_token'))) {
            // Check if the declaration is in draft status
            if ($taxDeclaration->getStatus() !== TaxDeclaration::STATUS_DRAFT) {
                $this->addFlash('error', 'Seules les déclarations en brouillon peuvent être soumises.');
                return $this->redirectToRoute('app_accounting_tax_declaration_show', ['id' => $taxDeclaration->getId()]);
            }
            
            $taxDeclaration->setStatus(TaxDeclaration::STATUS_SUBMITTED);
            $taxDeclaration->setSubmissionDate(new \DateTime());
            $taxDeclaration->setSubmittedBy($this->getUser());
            
            $this->entityManager->flush();
            
            $this->addFlash('success', 'La déclaration fiscale a été soumise avec succès.');
        }

        return $this->redirectToRoute('app_accounting_tax_declaration_show', ['id' => $taxDeclaration->getId()]);
    }

    #[Route('/{id}/mark-as-paid', name: 'app_accounting_tax_declaration_mark_as_paid', methods: ['POST'])]
    public function markAsPaid(Request $request, TaxDeclaration $taxDeclaration): Response
    {
        if ($this->isCsrfTokenValid('mark-as-paid'.$taxDeclaration->getId(), $request->request->get('_token'))) {
            // Check if the declaration is submitted
            if ($taxDeclaration->getStatus() !== TaxDeclaration::STATUS_SUBMITTED && $taxDeclaration->getStatus() !== TaxDeclaration::STATUS_LATE) {
                $this->addFlash('error', 'Seules les déclarations soumises ou en retard peuvent être marquées comme payées.');
                return $this->redirectToRoute('app_accounting_tax_declaration_show', ['id' => $taxDeclaration->getId()]);
            }
            
            $taxDeclaration->setStatus(TaxDeclaration::STATUS_PAID);
            $taxDeclaration->setPaymentDate(new \DateTime());
            
            $this->entityManager->flush();
            
            $this->addFlash('success', 'La déclaration fiscale a été marquée comme payée avec succès.');
        }

        return $this->redirectToRoute('app_accounting_tax_declaration_show', ['id' => $taxDeclaration->getId()]);
    }

    #[Route('/{id}/delete', name: 'app_accounting_tax_declaration_delete', methods: ['POST'])]
    public function delete(Request $request, TaxDeclaration $taxDeclaration): Response
    {
        if ($this->isCsrfTokenValid('delete'.$taxDeclaration->getId(), $request->request->get('_token'))) {
            // Check if the declaration is in draft status
            if ($taxDeclaration->getStatus() !== TaxDeclaration::STATUS_DRAFT) {
                $this->addFlash('error', 'Seules les déclarations en brouillon peuvent être supprimées.');
                return $this->redirectToRoute('app_accounting_tax_declaration_index');
            }
            
            // Delete attachment file if exists
            if ($taxDeclaration->getAttachmentPath()) {
                $filePath = $this->getParameter('tax_declarations_directory').'/'.$taxDeclaration->getAttachmentPath();
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
            }
            
            $this->entityManager->remove($taxDeclaration);
            $this->entityManager->flush();
            
            $this->addFlash('success', 'La déclaration fiscale a été supprimée avec succès.');
        }

        return $this->redirectToRoute('app_accounting_tax_declaration_index');
    }

    #[Route('/overdue', name: 'app_accounting_tax_declaration_overdue', methods: ['GET'])]
    public function overdue(): Response
    {
        $overdueDeclarations = $this->taxDeclarationRepository->findOverdue();
        
        return $this->render('accounting/tax_declaration/overdue.html.twig', [
            'taxDeclarations' => $overdueDeclarations,
        ]);
    }

    #[Route('/upcoming', name: 'app_accounting_tax_declaration_upcoming', methods: ['GET'])]
    public function upcoming(Request $request): Response
    {
        $days = $request->query->getInt('days', 30);
        $upcomingDeclarations = $this->taxDeclarationRepository->findDueSoon($days);
        
        return $this->render('accounting/tax_declaration/upcoming.html.twig', [
            'taxDeclarations' => $upcomingDeclarations,
            'days' => $days
        ]);
    }

    #[Route('/by-type/{type}', name: 'app_accounting_tax_declaration_by_type', methods: ['GET'])]
    public function byType(string $type): Response
    {
        $declarations = $this->taxDeclarationRepository->findByType($type);
        
        return $this->render('accounting/tax_declaration/by_type.html.twig', [
            'taxDeclarations' => $declarations,
            'type' => $type
        ]);
    }
}
