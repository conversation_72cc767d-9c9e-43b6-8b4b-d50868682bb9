<?php

namespace App\Controller\Admin;

use App\Repository\ActivityLogRepository;
use App\Service\ActivityLogService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/activity-log')]
#[IsGranted('ROLE_ADMIN')]
class ActivityLogController extends AbstractController
{
    private ActivityLogService $activityLogService;
    private ActivityLogRepository $activityLogRepository;

    public function __construct(
        ActivityLogService $activityLogService,
        ActivityLogRepository $activityLogRepository
    ) {
        $this->activityLogService = $activityLogService;
        $this->activityLogRepository = $activityLogRepository;
    }

    #[Route('/', name: 'app_admin_activity_log_index', methods: ['GET'])]
    public function index(Request $request): Response
    {
        $page = $request->query->getInt('page', 1);
        $limit = 50;
        $offset = ($page - 1) * $limit;
        
        // Récupérer les logs d'activité avec pagination
        $logs = $this->activityLogRepository->findBy(
            [],
            ['createdAt' => 'DESC'],
            $limit,
            $offset
        );
        
        // Compter le nombre total de logs
        $totalLogs = $this->activityLogRepository->count([]);
        $totalPages = ceil($totalLogs / $limit);
        
        return $this->render('admin/activity_log/index.html.twig', [
            'logs' => $logs,
            'current_page' => $page,
            'total_pages' => $totalPages,
        ]);
    }

    #[Route('/user', name: 'app_admin_activity_log_user', methods: ['GET'])]
    public function userActivity(Request $request): Response
    {
        $page = $request->query->getInt('page', 1);
        $limit = 50;
        $offset = ($page - 1) * $limit;
        
        // Récupérer les logs d'activité liés aux utilisateurs
        $logs = $this->activityLogRepository->findBy(
            ['entityType' => 'user'],
            ['createdAt' => 'DESC'],
            $limit,
            $offset
        );
        
        // Compter le nombre total de logs
        $totalLogs = $this->activityLogRepository->count(['entityType' => 'user']);
        $totalPages = ceil($totalLogs / $limit);
        
        return $this->render('admin/activity_log/user.html.twig', [
            'logs' => $logs,
            'current_page' => $page,
            'total_pages' => $totalPages,
        ]);
    }

    #[Route('/security', name: 'app_admin_activity_log_security', methods: ['GET'])]
    public function securityActivity(Request $request): Response
    {
        $page = $request->query->getInt('page', 1);
        $limit = 50;
        $offset = ($page - 1) * $limit;
        
        // Récupérer les logs d'activité liés à la sécurité (login, logout, etc.)
        $logs = $this->activityLogRepository->findSecurityLogs($limit, $offset);
        
        // Compter le nombre total de logs de sécurité
        $totalLogs = $this->activityLogRepository->countSecurityLogs();
        $totalPages = ceil($totalLogs / $limit);
        
        return $this->render('admin/activity_log/security.html.twig', [
            'logs' => $logs,
            'current_page' => $page,
            'total_pages' => $totalPages,
        ]);
    }

    #[Route('/entity/{type}/{id}', name: 'app_admin_activity_log_entity', methods: ['GET'])]
    public function entityActivity(string $type, int $id): Response
    {
        // Récupérer les logs d'activité pour une entité spécifique
        $logs = $this->activityLogService->getLogsForEntity($type, $id, 100);
        
        return $this->render('admin/activity_log/entity.html.twig', [
            'logs' => $logs,
            'entity_type' => $type,
            'entity_id' => $id,
        ]);
    }
}
