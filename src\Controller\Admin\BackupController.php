<?php

namespace App\Controller\Admin;

use App\Service\BackupService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/backup')]
#[IsGranted('ROLE_ADMIN')]
class BackupController extends AbstractController
{
    private BackupService $backupService;

    public function __construct(BackupService $backupService)
    {
        $this->backupService = $backupService;
    }

    #[Route('/', name: 'app_admin_backup_index')]
    public function index(): Response
    {
        $backups = $this->backupService->listBackups();
        $diskSpace = $this->backupService->checkDiskSpace();
        $estimatedSize = $this->backupService->estimateBackupSize();

        return $this->render('admin/backup/index.html.twig', [
            'backups' => $backups,
            'disk_space' => $diskSpace,
            'estimated_size' => $estimatedSize,
        ]);
    }

    #[Route('/create', name: 'app_admin_backup_create', methods: ['POST'])]
    public function create(): JsonResponse
    {
        try {
            // Vérifier l'espace disque disponible
            $diskSpace = $this->backupService->checkDiskSpace();
            $estimatedSize = $this->backupService->estimateBackupSize();
            
            if ($diskSpace['free'] < ($estimatedSize['total'] * 1.5)) { // Marge de sécurité de 50%
                return $this->json([
                    'success' => false,
                    'message' => 'Espace disque insuffisant pour créer la sauvegarde',
                    'required' => $estimatedSize['total_human'],
                    'available' => $diskSpace['free_human']
                ], 400);
            }

            $result = $this->backupService->createFullBackup();
            
            if ($result['success']) {
                $this->addFlash('success', 'Sauvegarde créée avec succès : ' . $result['name']);
                
                return $this->json([
                    'success' => true,
                    'message' => 'Sauvegarde créée avec succès',
                    'backup' => $result
                ]);
            } else {
                return $this->json([
                    'success' => false,
                    'message' => 'Erreur lors de la création de la sauvegarde',
                    'error' => $result['error'] ?? 'Erreur inconnue'
                ], 500);
            }
            
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Erreur lors de la création de la sauvegarde',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/download/{filename}', name: 'app_admin_backup_download')]
    public function download(string $filename): Response
    {
        $backups = $this->backupService->listBackups();
        $backup = null;
        
        foreach ($backups as $b) {
            if ($b['name'] === $filename) {
                $backup = $b;
                break;
            }
        }
        
        if (!$backup) {
            throw $this->createNotFoundException('Sauvegarde non trouvée');
        }

        $response = new BinaryFileResponse($backup['path']);
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $filename . '.zip'
        );

        return $response;
    }

    #[Route('/restore', name: 'app_admin_backup_restore', methods: ['POST'])]
    public function restore(Request $request): JsonResponse
    {
        $filename = $request->request->get('filename');
        
        if (!$filename) {
            return $this->json([
                'success' => false,
                'message' => 'Nom de fichier requis'
            ], 400);
        }

        $backups = $this->backupService->listBackups();
        $backupPath = null;
        
        foreach ($backups as $backup) {
            if ($backup['name'] === $filename) {
                $backupPath = $backup['path'];
                break;
            }
        }
        
        if (!$backupPath) {
            return $this->json([
                'success' => false,
                'message' => 'Sauvegarde non trouvée'
            ], 404);
        }

        try {
            $result = $this->backupService->restoreBackup($backupPath);
            
            $this->addFlash('success', 'Sauvegarde restaurée avec succès');
            
            return $this->json([
                'success' => true,
                'message' => 'Sauvegarde restaurée avec succès',
                'result' => $result
            ]);
            
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Erreur lors de la restauration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/delete', name: 'app_admin_backup_delete', methods: ['POST'])]
    public function delete(Request $request): JsonResponse
    {
        $filename = $request->request->get('filename');
        
        if (!$filename) {
            return $this->json([
                'success' => false,
                'message' => 'Nom de fichier requis'
            ], 400);
        }

        $backups = $this->backupService->listBackups();
        $backupPath = null;
        
        foreach ($backups as $backup) {
            if ($backup['name'] === $filename) {
                $backupPath = $backup['path'];
                break;
            }
        }
        
        if (!$backupPath || !file_exists($backupPath)) {
            return $this->json([
                'success' => false,
                'message' => 'Sauvegarde non trouvée'
            ], 404);
        }

        try {
            unlink($backupPath);
            
            $this->addFlash('success', 'Sauvegarde supprimée avec succès');
            
            return $this->json([
                'success' => true,
                'message' => 'Sauvegarde supprimée avec succès'
            ]);
            
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Erreur lors de la suppression',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/cleanup', name: 'app_admin_backup_cleanup', methods: ['POST'])]
    public function cleanup(Request $request): JsonResponse
    {
        $keepDays = $request->request->getInt('keep_days', 30);
        
        if ($keepDays < 7) {
            return $this->json([
                'success' => false,
                'message' => 'La période de rétention ne peut pas être inférieure à 7 jours'
            ], 400);
        }

        try {
            $deletedCount = $this->backupService->cleanOldBackups($keepDays);
            
            $this->addFlash('success', "Nettoyage terminé. {$deletedCount} sauvegardes supprimées.");
            
            return $this->json([
                'success' => true,
                'message' => "Nettoyage terminé. {$deletedCount} sauvegardes supprimées.",
                'deleted_count' => $deletedCount
            ]);
            
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Erreur lors du nettoyage',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    #[Route('/status', name: 'app_admin_backup_status', methods: ['GET'])]
    public function status(): JsonResponse
    {
        $diskSpace = $this->backupService->checkDiskSpace();
        $estimatedSize = $this->backupService->estimateBackupSize();
        $backups = $this->backupService->listBackups();
        
        $totalBackupsSize = array_sum(array_column($backups, 'size'));
        
        return $this->json([
            'success' => true,
            'data' => [
                'disk_space' => $diskSpace,
                'estimated_backup_size' => $estimatedSize,
                'backups_count' => count($backups),
                'total_backups_size' => $totalBackupsSize,
                'total_backups_size_human' => $this->formatBytes($totalBackupsSize),
                'can_create_backup' => $diskSpace['free'] > ($estimatedSize['total'] * 1.5),
                'last_backup' => !empty($backups) ? $backups[0] : null
            ]
        ]);
    }

    #[Route('/schedule', name: 'app_admin_backup_schedule')]
    public function schedule(): Response
    {
        // Configuration des sauvegardes programmées
        $scheduleConfig = [
            'daily' => [
                'enabled' => false,
                'time' => '02:00',
                'retention_days' => 7
            ],
            'weekly' => [
                'enabled' => false,
                'day' => 'sunday',
                'time' => '03:00',
                'retention_days' => 30
            ],
            'monthly' => [
                'enabled' => false,
                'day' => 1,
                'time' => '04:00',
                'retention_days' => 365
            ]
        ];

        return $this->render('admin/backup/schedule.html.twig', [
            'schedule_config' => $scheduleConfig,
        ]);
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
