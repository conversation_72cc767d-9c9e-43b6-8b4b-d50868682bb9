<?php

namespace App\Controller\Admin;

use App\Entity\Configuration;
use App\Form\ConfigurationForm;
use App\Repository\ConfigurationRepository;
use App\Service\ActivityLogService;
use App\Service\ConfigurationService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/configuration')]
#[IsGranted('ROLE_ADMIN')]
class ConfigurationController extends AbstractController
{
    private ConfigurationService $configService;
    private ConfigurationRepository $configRepository;
    private ActivityLogService $activityLogService;

    public function __construct(
        ConfigurationService $configService,
        ConfigurationRepository $configRepository,
        ActivityLogService $activityLogService
    ) {
        $this->configService = $configService;
        $this->configRepository = $configRepository;
        $this->activityLogService = $activityLogService;
    }

    #[Route('/', name: 'app_admin_configuration_index', methods: ['GET'])]
    public function index(): Response
    {
        // Get all categories
        $categories = $this->configService->getAllCategories();
        
        // Get configurations by category
        $configurationsByCategory = [];
        foreach ($categories as $category) {
            $configurationsByCategory[$category] = $this->configService->getByCategory($category);
        }
        
        return $this->render('admin/configuration/index.html.twig', [
            'configurations_by_category' => $configurationsByCategory,
        ]);
    }

    #[Route('/new', name: 'app_admin_configuration_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $configuration = new Configuration();
        $form = $this->createForm(ConfigurationForm::class, $configuration);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($configuration);
            $entityManager->flush();
            
            // Log activity
            $this->activityLogService->log(
                'create',
                'configuration',
                $configuration->getId(),
                'Configuration "' . $configuration->getKey() . '" created'
            );
            
            // Clear configuration cache
            $this->configService->clearCache();
            
            $this->addFlash('success', 'Configuration created successfully.');
            return $this->redirectToRoute('app_admin_configuration_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/configuration/new.html.twig', [
            'configuration' => $configuration,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_configuration_show', methods: ['GET'])]
    public function show(Configuration $configuration): Response
    {
        return $this->render('admin/configuration/show.html.twig', [
            'configuration' => $configuration,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_configuration_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Configuration $configuration, EntityManagerInterface $entityManager): Response
    {
        // Check if this is a system configuration
        if ($configuration->isIsSystem()) {
            $this->addFlash('warning', 'System configurations can only be edited with caution.');
        }
        
        $form = $this->createForm(ConfigurationForm::class, $configuration);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();
            
            // Log activity
            $this->activityLogService->log(
                'update',
                'configuration',
                $configuration->getId(),
                'Configuration "' . $configuration->getKey() . '" updated'
            );
            
            // Clear configuration cache
            $this->configService->clearCache();
            
            $this->addFlash('success', 'Configuration updated successfully.');
            return $this->redirectToRoute('app_admin_configuration_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/configuration/edit.html.twig', [
            'configuration' => $configuration,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_configuration_delete', methods: ['POST'])]
    public function delete(Request $request, Configuration $configuration, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$configuration->getId(), $request->getPayload()->getString('_token'))) {
            // Check if this is a system configuration
            if ($configuration->isIsSystem()) {
                $this->addFlash('error', 'System configurations cannot be deleted.');
                return $this->redirectToRoute('app_admin_configuration_index', [], Response::HTTP_SEE_OTHER);
            }
            
            $configKey = $configuration->getKey();
            $configId = $configuration->getId();
            
            $entityManager->remove($configuration);
            $entityManager->flush();
            
            // Log activity
            $this->activityLogService->log(
                'delete',
                'configuration',
                $configId,
                'Configuration "' . $configKey . '" deleted'
            );
            
            // Clear configuration cache
            $this->configService->clearCache();
            
            $this->addFlash('success', 'Configuration deleted successfully.');
        }

        return $this->redirectToRoute('app_admin_configuration_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/category/{category}', name: 'app_admin_configuration_category', methods: ['GET'])]
    public function category(string $category): Response
    {
        $configurations = $this->configService->getByCategory($category);
        
        return $this->render('admin/configuration/category.html.twig', [
            'category' => $category,
            'configurations' => $configurations,
        ]);
    }

    #[Route('/initialize', name: 'app_admin_configuration_initialize', methods: ['GET', 'POST'])]
    public function initialize(Request $request): Response
    {
        if ($request->isMethod('POST') && $this->isCsrfTokenValid('initialize_config', $request->getPayload()->getString('_token'))) {
            try {
                $this->configService->initializeDefaults();
                $this->configService->clearCache();
                
                // Log activity
                $this->activityLogService->log(
                    'initialize',
                    'configuration',
                    0,
                    'Default configurations initialized'
                );
                
                $this->addFlash('success', 'Default configurations have been initialized successfully.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'An error occurred while initializing default configurations: ' . $e->getMessage());
            }
            
            return $this->redirectToRoute('app_admin_configuration_index');
        }
        
        return $this->render('admin/configuration/initialize.html.twig');
    }
}
