<?php

namespace App\Controller\Admin;

use App\Repository\PartnerRepository;
use App\Repository\ProjectRepository;
use App\Repository\UserRepository;
use App\Repository\EmployeeRepository;
use App\Repository\DepartmentRepository;
use App\Repository\ProductRepository;
use App\Repository\PurchaseRequestRepository;
use App\Repository\StockItemRepository;
use App\Repository\StockAlertRepository;
use App\Repository\InvoiceRepository;
use App\Repository\NotificationRepository;
use App\Service\PermissionService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin')]
#[IsGranted('ROLE_ADMIN')]
class DashboardController extends AbstractController
{
    private PermissionService $permissionService;
    
    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }
    
    #[Route('/dashboard', name: 'app_admin_dashboard')]
    public function index(
        UserRepository $userRepository,
        PartnerRepository $partnerRepository,
        ProjectRepository $projectRepository,
        EmployeeRepository $employeeRepository,
        DepartmentRepository $departmentRepository,
        ProductRepository $productRepository,
        PurchaseRequestRepository $purchaseRequestRepository,
        StockItemRepository $stockItemRepository,
        StockAlertRepository $stockAlertRepository,
        InvoiceRepository $invoiceRepository,
        NotificationRepository $notificationRepository
    ): Response
    {
        // Récupérer les statistiques pour le tableau de bord
        $stats = [
            'users' => [
                'total' => $userRepository->count([]),
                'active' => $userRepository->countActive(),
                'inactive' => $userRepository->countInactive(),
                'admins' => $userRepository->countByRole('ROLE_ADMIN'),
            ],
            'partners' => [
                'total' => $partnerRepository->count([]),
                'byStatus' => $partnerRepository->countByStatus(),
            ],
            'projects' => [
                'total' => $projectRepository->count([]),
                'active' => $projectRepository->countActive(),
                'completed' => $projectRepository->countCompleted(),
                'byStatus' => $projectRepository->countByStatus(),
            ],
            'employees' => [
                'total' => $employeeRepository->count([]),
                'byDepartment' => $employeeRepository->countByDepartment(),
            ],
            'departments' => [
                'total' => $departmentRepository->count([]),
            ],
            'products' => [
                'total' => $productRepository->count([]),
                'lowStock' => $productRepository->countLowStock(),
            ],
            'purchases' => [
                'pending' => $purchaseRequestRepository->countPending(),
                'approved' => $purchaseRequestRepository->countApproved(),
                'rejected' => $purchaseRequestRepository->countRejected(),
            ],
            'stock' => [
                'items' => $stockItemRepository->count([]),
                'alerts' => $stockAlertRepository->countActive(),
            ],
            'invoices' => [
                'pending' => $invoiceRepository->countPending(),
                'paid' => $invoiceRepository->countPaid(),
                'overdue' => $invoiceRepository->countOverdue(),
            ],
            'notifications' => [
                'unread' => $notificationRepository->countUnread(),
            ],
        ];
        
        // Récupérer les activités récentes
        $recentActivities = [
            'users' => $userRepository->findBy([], ['createdAt' => 'DESC'], 5),
            'partners' => $partnerRepository->findBy([], ['createdAt' => 'DESC'], 5),
            'projects' => $projectRepository->findBy([], ['createdAt' => 'DESC'], 5),
            'employees' => $employeeRepository->findBy([], ['createdAt' => 'DESC'], 5),
        ];
        
        return $this->render('admin/dashboard/index.html.twig', [
            'stats' => $stats,
            'recentActivities' => $recentActivities,
        ]);
    }
}
