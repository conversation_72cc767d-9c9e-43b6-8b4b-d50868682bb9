<?php

namespace App\Controller\Admin;

use App\Entity\Department;
use App\Form\DepartmentSearchType;
use App\Form\DepartmentType;
use App\Form\ImportCsvType;
use App\Repository\DepartmentRepository;
use App\Repository\EmployeeRepository;
use App\Service\ActivityLogService;
use App\Service\CacheService;
use App\Service\PdfReportService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/department')]
#[IsGranted('ROLE_ADMIN')]
class DepartmentController extends AbstractController
{
    #[Route('/', name: 'app_admin_department_index', methods: ['GET'])]
    public function index(Request $request, DepartmentRepository $departmentRepository, EmployeeRepository $employeeRepository): Response
    {
        $searchForm = $this->createForm(DepartmentSearchType::class);
        $searchForm->handleRequest($request);

        $departments = [];

        if ($searchForm->isSubmitted() && $searchForm->isValid()) {
            $criteria = $searchForm->getData();

            $qb = $departmentRepository->createQueryBuilder('d')
                ->leftJoin('d.manager', 'm')
                ->leftJoin('d.employees', 'e')
                ->leftJoin('d.parentDepartment', 'p');

            // Filtrer par mot-clé
            if (!empty($criteria['keyword'])) {
                $qb->andWhere('d.name LIKE :keyword OR d.code LIKE :keyword OR d.description LIKE :keyword')
                    ->setParameter('keyword', '%' . $criteria['keyword'] . '%');
            }

            // Filtrer par département parent
            if (!empty($criteria['parentDepartment'])) {
                $qb->andWhere('d.parentDepartment = :parent')
                    ->setParameter('parent', $criteria['parentDepartment']);
            }

            // Filtrer par statut
            if (!empty($criteria['status'])) {
                if ($criteria['status'] === 'active') {
                    $qb->andWhere('d.isActive = :active')
                        ->setParameter('active', true);
                } elseif ($criteria['status'] === 'inactive') {
                    $qb->andWhere('d.isActive = :active')
                        ->setParameter('active', false);
                }
            }

            // Filtrer par présence d'un responsable
            if (!empty($criteria['hasManager'])) {
                $qb->andWhere('d.manager IS NOT NULL');
            }

            // Filtrer par présence d'employés
            if (!empty($criteria['hasEmployees'])) {
                $qb->andWhere('SIZE(d.employees) > 0');
            }

            $departments = $qb->getQuery()->getResult();
        } else {
            $departments = $departmentRepository->findAll();
        }

        // Compter le nombre d'employés pour chaque département
        $employeeCounts = [];
        foreach ($departments as $department) {
            $employeeCounts[$department->getId()] = $employeeRepository->count(['department' => $department]);
        }

        return $this->render('admin/department/index.html.twig', [
            'departments' => $departments,
            'employeeCounts' => $employeeCounts,
            'searchForm' => $searchForm->createView(),
        ]);
    }

    #[Route('/new', name: 'app_admin_department_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $department = new Department();
        $form = $this->createForm(DepartmentType::class, $department);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($department);
            $entityManager->flush();

            $this->addFlash('success', 'Le département a été créé avec succès.');

            return $this->redirectToRoute('app_admin_department_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/department/new.html.twig', [
            'department' => $department,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_department_show', methods: ['GET'])]
    public function show(Department $department, EmployeeRepository $employeeRepository): Response
    {
        // Récupérer les employés de ce département
        $employees = $employeeRepository->findBy(['department' => $department]);

        return $this->render('admin/department/show.html.twig', [
            'department' => $department,
            'employees' => $employees,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_department_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Department $department, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(DepartmentType::class, $department);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'Le département a été modifié avec succès.');

            return $this->redirectToRoute('app_admin_department_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/department/edit.html.twig', [
            'department' => $department,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_department_delete', methods: ['POST'])]
    public function delete(Request $request, Department $department, EntityManagerInterface $entityManager, EmployeeRepository $employeeRepository): Response
    {
        if ($this->isCsrfTokenValid('delete'.$department->getId(), $request->request->get('_token'))) {
            // Vérifier si des employés sont dans ce département
            $employees = $employeeRepository->findBy(['department' => $department]);

            if (count($employees) > 0) {
                $this->addFlash('error', 'Ce département ne peut pas être supprimé car il contient ' . count($employees) . ' employé(s).');
                return $this->redirectToRoute('app_admin_department_index', [], Response::HTTP_SEE_OTHER);
            }

            $entityManager->remove($department);
            $entityManager->flush();

            $this->addFlash('success', 'Le département a été supprimé avec succès.');
        }

        return $this->redirectToRoute('app_admin_department_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/export', name: 'app_admin_department_export', methods: ['GET'])]
    public function export(Request $request, DepartmentRepository $departmentRepository, EmployeeRepository $employeeRepository): Response
    {
        $searchForm = $this->createForm(DepartmentSearchType::class);
        $searchForm->handleRequest($request);

        $departments = [];

        if ($searchForm->isSubmitted() && $searchForm->isValid()) {
            $criteria = $searchForm->getData();

            $qb = $departmentRepository->createQueryBuilder('d')
                ->leftJoin('d.manager', 'm')
                ->leftJoin('d.employees', 'e')
                ->leftJoin('d.parentDepartment', 'p');

            // Filtrer par mot-clé
            if (!empty($criteria['keyword'])) {
                $qb->andWhere('d.name LIKE :keyword OR d.code LIKE :keyword OR d.description LIKE :keyword')
                    ->setParameter('keyword', '%' . $criteria['keyword'] . '%');
            }

            // Filtrer par département parent
            if (!empty($criteria['parentDepartment'])) {
                $qb->andWhere('d.parentDepartment = :parent')
                    ->setParameter('parent', $criteria['parentDepartment']);
            }

            // Filtrer par statut
            if (!empty($criteria['status'])) {
                if ($criteria['status'] === 'active') {
                    $qb->andWhere('d.isActive = :active')
                        ->setParameter('active', true);
                } elseif ($criteria['status'] === 'inactive') {
                    $qb->andWhere('d.isActive = :active')
                        ->setParameter('active', false);
                }
            }

            // Filtrer par présence d'un responsable
            if (!empty($criteria['hasManager'])) {
                $qb->andWhere('d.manager IS NOT NULL');
            }

            // Filtrer par présence d'employés
            if (!empty($criteria['hasEmployees'])) {
                $qb->andWhere('SIZE(d.employees) > 0');
            }

            $departments = $qb->getQuery()->getResult();
        } else {
            $departments = $departmentRepository->findAll();
        }

        // Créer le contenu CSV
        $csvContent = "ID;Code;Nom;Description;Département parent;Responsable;Nombre d'employés;Statut\n";

        foreach ($departments as $department) {
            $parentName = $department->getParentDepartment() ? $department->getParentDepartment()->getName() : '';
            $managerName = $department->getManager() ? $department->getManager()->getUser()->getFirstName() . ' ' . $department->getManager()->getUser()->getLastName() : '';
            $employeeCount = $employeeRepository->count(['department' => $department]);
            $status = $department->getIsActive() ? 'Actif' : 'Inactif';

            $csvContent .= sprintf(
                "%d;%s;%s;%s;%s;%s;%d;%s\n",
                $department->getId(),
                $department->getCode(),
                $department->getName(),
                str_replace(';', ',', $department->getDescription() ?? ''),
                str_replace(';', ',', $parentName),
                str_replace(';', ',', $managerName),
                $employeeCount,
                $status
            );
        }

        // Créer la réponse avec le contenu CSV
        $response = new Response($csvContent);
        $disposition = $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            'departments_' . date('Y-m-d_H-i-s') . '.csv'
        );

        $response->headers->set('Content-Disposition', $disposition);
        $response->headers->set('Content-Type', 'text/csv; charset=UTF-8');

        return $response;
    }

    #[Route('/dashboard', name: 'app_admin_department_dashboard', methods: ['GET'])]
    public function dashboard(DepartmentRepository $departmentRepository, EmployeeRepository $employeeRepository): Response
    {
        // Récupérer tous les départements
        $departments = $departmentRepository->findAll();

        // Statistiques générales
        $stats = [
            'totalDepartments' => count($departments),
            'activeDepartments' => 0,
            'inactiveDepartments' => 0,
            'topLevelDepartments' => 0,
            'subDepartments' => 0,
            'departmentsWithManager' => 0,
            'departmentsWithoutManager' => 0,
            'totalEmployees' => $employeeRepository->count([]),
        ];

        // Données pour les graphiques
        $employeesByDepartment = [];
        $subDepartmentsByDepartment = [];
        $departmentHierarchy = [];

        foreach ($departments as $department) {
            // Statistiques générales
            if ($department->getIsActive()) {
                $stats['activeDepartments']++;
            } else {
                $stats['inactiveDepartments']++;
            }

            if ($department->getParentDepartment() === null) {
                $stats['topLevelDepartments']++;

                // Construire la hiérarchie des départements
                $departmentHierarchy[] = [
                    'id' => $department->getId(),
                    'name' => $department->getName(),
                    'children' => $this->getChildDepartmentsForHierarchy($department),
                ];
            } else {
                $stats['subDepartments']++;
            }

            if ($department->getManager()) {
                $stats['departmentsWithManager']++;
            } else {
                $stats['departmentsWithoutManager']++;
            }

            // Nombre d'employés par département
            $employeeCount = $employeeRepository->count(['department' => $department]);
            $employeesByDepartment[] = [
                'name' => $department->getName(),
                'count' => $employeeCount,
            ];

            // Nombre de sous-départements par département principal
            if ($department->getParentDepartment() === null) {
                $subDepartmentsByDepartment[] = [
                    'name' => $department->getName(),
                    'count' => count($department->getChildDepartments()),
                ];
            }
        }

        // Trier les départements par nombre d'employés (décroissant)
        usort($employeesByDepartment, function($a, $b) {
            return $b['count'] - $a['count'];
        });

        // Limiter à 10 départements pour le graphique
        $employeesByDepartment = array_slice($employeesByDepartment, 0, 10);

        return $this->render('admin/department/dashboard.html.twig', [
            'stats' => $stats,
            'employeesByDepartment' => $employeesByDepartment,
            'subDepartmentsByDepartment' => $subDepartmentsByDepartment,
            'departmentHierarchy' => $departmentHierarchy,
        ]);
    }

    /**
     * Récupère récursivement les départements enfants pour la hiérarchie
     */
    private function getChildDepartmentsForHierarchy(Department $department): array
    {
        $children = [];

        foreach ($department->getChildDepartments() as $child) {
            $children[] = [
                'id' => $child->getId(),
                'name' => $child->getName(),
                'children' => $this->getChildDepartmentsForHierarchy($child),
            ];
        }

        return $children;
    }

    #[Route('/import', name: 'app_admin_department_import', methods: ['GET', 'POST'])]
    public function import(Request $request, EntityManagerInterface $entityManager, DepartmentRepository $departmentRepository): Response
    {
        $form = $this->createForm(ImportCsvType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            /** @var UploadedFile $csvFile */
            $csvFile = $form->get('file')->getData();

            if ($csvFile) {
                $fileContent = file_get_contents($csvFile->getPathname());
                $rows = array_map('str_getcsv', explode("\n", $fileContent));

                // Supprimer la ligne d'en-tête si elle existe
                if (count($rows) > 0 && is_array($rows[0]) && count($rows[0]) > 0) {
                    $header = $rows[0];
                    if (strtolower($header[0]) === 'code' || strtolower($header[0]) === '"code"') {
                        array_shift($rows);
                    }
                }

                $importCount = 0;
                $errors = [];

                foreach ($rows as $index => $row) {
                    // Ignorer les lignes vides
                    if (empty($row) || count($row) <= 1 || empty($row[0])) {
                        continue;
                    }

                    // Nettoyer les données
                    $code = trim($row[0]);
                    $name = isset($row[1]) ? trim($row[1]) : '';
                    $description = isset($row[2]) ? trim($row[2]) : null;
                    $parentCode = isset($row[3]) ? trim($row[3]) : null;

                    // Vérifier si le département existe déjà
                    $existingDepartment = $departmentRepository->findOneBy(['code' => $code]);

                    if ($existingDepartment) {
                        $errors[] = sprintf('Ligne %d: Le département avec le code "%s" existe déjà.', $index + 1, $code);
                        continue;
                    }

                    // Vérifier si le nom est vide
                    if (empty($name)) {
                        $errors[] = sprintf('Ligne %d: Le nom du département est obligatoire.', $index + 1);
                        continue;
                    }

                    // Créer le nouveau département
                    $department = new Department();
                    $department->setCode($code);
                    $department->setName($name);
                    $department->setDescription($description);
                    $department->setIsActive(true);
                    $department->setCreatedAt(new \DateTime());
                    $department->setUpdatedAt(new \DateTime());

                    // Associer au département parent si spécifié
                    if (!empty($parentCode)) {
                        $parentDepartment = $departmentRepository->findOneBy(['code' => $parentCode]);
                        if ($parentDepartment) {
                            $department->setParentDepartment($parentDepartment);
                        } else {
                            $errors[] = sprintf('Ligne %d: Le département parent avec le code "%s" n\'existe pas.', $index + 1, $parentCode);
                        }
                    }

                    $entityManager->persist($department);
                    $importCount++;
                }

                if ($importCount > 0) {
                    $entityManager->flush();
                    $this->addFlash('success', sprintf('%d département(s) importé(s) avec succès.', $importCount));
                }

                foreach ($errors as $error) {
                    $this->addFlash('error', $error);
                }

                return $this->redirectToRoute('app_admin_department_index');
            }
        }

        return $this->render('admin/department/import.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/report', name: 'app_admin_department_report', methods: ['GET'])]
    public function report(Department $department, EmployeeRepository $employeeRepository, PdfReportService $pdfReportService, ActivityLogService $activityLogService): Response
    {
        // Récupérer les employés du département
        $employees = $employeeRepository->findBy(['department' => $department]);

        // Journaliser l'action
        $activityLogService->logView('department', $department->getId(), [
            'action' => 'generate_report',
            'department_id' => $department->getId(),
            'department_name' => $department->getName(),
        ]);

        // Générer le rapport PDF
        return $pdfReportService->generateDepartmentReport([
            'id' => $department->getId(),
            'code' => $department->getCode(),
            'name' => $department->getName(),
            'description' => $department->getDescription(),
            'parentDepartment' => $department->getParentDepartment(),
            'manager' => $department->getManager(),
            'isActive' => $department->isActive(),
            'createdAt' => $department->getCreatedAt(),
            'childDepartments' => $department->getChildDepartments(),
        ], $employees, 'department_' . $department->getCode() . '_' . date('Y-m-d') . '.pdf');
    }
}
