<?php

namespace App\Controller\Admin;

use App\Entity\Department;
use App\Entity\DepartmentPermission;
use App\Form\DepartmentPermissionType;
use App\Repository\DepartmentPermissionRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/department-permission')]
#[IsGranted('ROLE_ADMIN')]
class DepartmentPermissionController extends AbstractController
{
    #[Route('/', name: 'app_admin_department_permission_index', methods: ['GET'])]
    public function index(DepartmentPermissionRepository $departmentPermissionRepository): Response
    {
        return $this->render('admin/department_permission/index.html.twig', [
            'department_permissions' => $departmentPermissionRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_admin_department_permission_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $departmentPermission = new DepartmentPermission();
        $form = $this->createForm(DepartmentPermissionType::class, $departmentPermission);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $departmentPermission->setCreatedAt(new \DateTime());
            $departmentPermission->setUpdatedAt(new \DateTime());
            
            $entityManager->persist($departmentPermission);
            $entityManager->flush();

            $this->addFlash('success', 'La permission a été créée avec succès.');

            return $this->redirectToRoute('app_admin_department_permission_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/department_permission/new.html.twig', [
            'department_permission' => $departmentPermission,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_department_permission_show', methods: ['GET'])]
    public function show(DepartmentPermission $departmentPermission): Response
    {
        return $this->render('admin/department_permission/show.html.twig', [
            'department_permission' => $departmentPermission,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_department_permission_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, DepartmentPermission $departmentPermission, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(DepartmentPermissionType::class, $departmentPermission);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $departmentPermission->setUpdatedAt(new \DateTime());
            
            $entityManager->flush();

            $this->addFlash('success', 'La permission a été modifiée avec succès.');

            return $this->redirectToRoute('app_admin_department_permission_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/department_permission/edit.html.twig', [
            'department_permission' => $departmentPermission,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_department_permission_delete', methods: ['POST'])]
    public function delete(Request $request, DepartmentPermission $departmentPermission, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$departmentPermission->getId(), $request->request->get('_token'))) {
            $entityManager->remove($departmentPermission);
            $entityManager->flush();
            
            $this->addFlash('success', 'La permission a été supprimée avec succès.');
        }

        return $this->redirectToRoute('app_admin_department_permission_index', [], Response::HTTP_SEE_OTHER);
    }
    
    #[Route('/department/{id}', name: 'app_admin_department_permission_by_department', methods: ['GET'])]
    public function byDepartment(Department $department, DepartmentPermissionRepository $departmentPermissionRepository): Response
    {
        $permissions = $departmentPermissionRepository->findByDepartment($department);
        
        return $this->render('admin/department_permission/by_department.html.twig', [
            'department' => $department,
            'permissions' => $permissions,
        ]);
    }
}
