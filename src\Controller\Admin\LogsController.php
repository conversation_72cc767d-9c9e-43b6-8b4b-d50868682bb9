<?php

namespace App\Controller\Admin;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin')]
#[IsGranted('ROLE_ADMIN')]
class LogsController extends AbstractController
{
    #[Route('/logs', name: 'app_admin_logs')]
    public function index(): Response
    {
        // Rediriger vers la page des journaux d'activité
        return $this->redirectToRoute('app_admin_activity_log_index');
    }
}
