<?php

namespace App\Controller\Admin;

use App\Entity\PartnerNature;
use App\Form\PartnerNatureType;
use App\Repository\PartnerNatureRepository;
use App\Repository\PartnerRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/partner-nature')]
#[IsGranted('ROLE_ADMIN')]
class PartnerNatureController extends AbstractController
{
    #[Route('/', name: 'app_admin_partner_nature_index', methods: ['GET'])]
    public function index(PartnerNatureRepository $partnerNatureRepository): Response
    {
        return $this->render('admin/partner_nature/index.html.twig', [
            'partner_natures' => $partnerNatureRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_admin_partner_nature_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $partnerNature = new PartnerNature();
        $form = $this->createForm(PartnerNatureType::class, $partnerNature);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($partnerNature);
            $entityManager->flush();

            $this->addFlash('success', 'La nature de partenaire a été créée avec succès.');

            return $this->redirectToRoute('app_admin_partner_nature_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/partner_nature/new.html.twig', [
            'partner_nature' => $partnerNature,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_partner_nature_show', methods: ['GET'])]
    public function show(PartnerNature $partnerNature, PartnerRepository $partnerRepository): Response
    {
        // Récupérer les partenaires avec cette nature
        $partners = $partnerRepository->findBy(['nature' => $partnerNature]);
        
        return $this->render('admin/partner_nature/show.html.twig', [
            'partner_nature' => $partnerNature,
            'partners' => $partners,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_partner_nature_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, PartnerNature $partnerNature, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PartnerNatureType::class, $partnerNature);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'La nature de partenaire a été modifiée avec succès.');

            return $this->redirectToRoute('app_admin_partner_nature_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/partner_nature/edit.html.twig', [
            'partner_nature' => $partnerNature,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_partner_nature_delete', methods: ['POST'])]
    public function delete(Request $request, PartnerNature $partnerNature, EntityManagerInterface $entityManager, PartnerRepository $partnerRepository): Response
    {
        if ($this->isCsrfTokenValid('delete'.$partnerNature->getId(), $request->request->get('_token'))) {
            // Vérifier si des partenaires utilisent cette nature
            $partners = $partnerRepository->findBy(['nature' => $partnerNature]);
            
            if (count($partners) > 0) {
                $this->addFlash('error', 'Cette nature ne peut pas être supprimée car elle est utilisée par ' . count($partners) . ' partenaire(s).');
                return $this->redirectToRoute('app_admin_partner_nature_index', [], Response::HTTP_SEE_OTHER);
            }
            
            $entityManager->remove($partnerNature);
            $entityManager->flush();
            
            $this->addFlash('success', 'La nature de partenaire a été supprimée avec succès.');
        }

        return $this->redirectToRoute('app_admin_partner_nature_index', [], Response::HTTP_SEE_OTHER);
    }
}
