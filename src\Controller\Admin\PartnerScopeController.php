<?php

namespace App\Controller\Admin;

use App\Entity\PartnerScope;
use App\Form\PartnerScopeType;
use App\Repository\PartnerRepository;
use App\Repository\PartnerScopeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/partner-scope')]
#[IsGranted('ROLE_ADMIN')]
class PartnerScopeController extends AbstractController
{
    #[Route('/', name: 'app_admin_partner_scope_index', methods: ['GET'])]
    public function index(PartnerScopeRepository $partnerScopeRepository): Response
    {
        return $this->render('admin/partner_scope/index.html.twig', [
            'partner_scopes' => $partnerScopeRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_admin_partner_scope_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $partnerScope = new PartnerScope();
        $form = $this->createForm(PartnerScopeType::class, $partnerScope);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($partnerScope);
            $entityManager->flush();

            $this->addFlash('success', 'La structure de partenaire a été créée avec succès.');

            return $this->redirectToRoute('app_admin_partner_scope_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/partner_scope/new.html.twig', [
            'partner_scope' => $partnerScope,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_partner_scope_show', methods: ['GET'])]
    public function show(PartnerScope $partnerScope, PartnerRepository $partnerRepository): Response
    {
        // Récupérer les partenaires avec cette structure
        $partners = $partnerRepository->findBy(['scope' => $partnerScope]);
        
        return $this->render('admin/partner_scope/show.html.twig', [
            'partner_scope' => $partnerScope,
            'partners' => $partners,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_partner_scope_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, PartnerScope $partnerScope, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PartnerScopeType::class, $partnerScope);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'La structure de partenaire a été modifiée avec succès.');

            return $this->redirectToRoute('app_admin_partner_scope_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/partner_scope/edit.html.twig', [
            'partner_scope' => $partnerScope,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_partner_scope_delete', methods: ['POST'])]
    public function delete(Request $request, PartnerScope $partnerScope, EntityManagerInterface $entityManager, PartnerRepository $partnerRepository): Response
    {
        if ($this->isCsrfTokenValid('delete'.$partnerScope->getId(), $request->request->get('_token'))) {
            // Vérifier si des partenaires utilisent cette structure
            $partners = $partnerRepository->findBy(['scope' => $partnerScope]);
            
            if (count($partners) > 0) {
                $this->addFlash('error', 'Cette structure ne peut pas être supprimée car elle est utilisée par ' . count($partners) . ' partenaire(s).');
                return $this->redirectToRoute('app_admin_partner_scope_index', [], Response::HTTP_SEE_OTHER);
            }
            
            $entityManager->remove($partnerScope);
            $entityManager->flush();
            
            $this->addFlash('success', 'La structure de partenaire a été supprimée avec succès.');
        }

        return $this->redirectToRoute('app_admin_partner_scope_index', [], Response::HTTP_SEE_OTHER);
    }
}
