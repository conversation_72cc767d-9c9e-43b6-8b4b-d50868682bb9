<?php

namespace App\Controller\Admin;

use App\Entity\PartnerStatus;
use App\Form\PartnerStatusType;
use App\Repository\PartnerRepository;
use App\Repository\PartnerStatusRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/partner-status')]
#[IsGranted('ROLE_ADMIN')]
class PartnerStatusController extends AbstractController
{
    #[Route('/', name: 'app_admin_partner_status_index', methods: ['GET'])]
    public function index(PartnerStatusRepository $partnerStatusRepository): Response
    {
        return $this->render('admin/partner_status/index.html.twig', [
            'partner_statuses' => $partnerStatusRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_admin_partner_status_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $partnerStatus = new PartnerStatus();
        $form = $this->createForm(PartnerStatusType::class, $partnerStatus);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($partnerStatus);
            $entityManager->flush();

            $this->addFlash('success', 'Le statut de partenaire a été créé avec succès.');

            return $this->redirectToRoute('app_admin_partner_status_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/partner_status/new.html.twig', [
            'partner_status' => $partnerStatus,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_partner_status_show', methods: ['GET'])]
    public function show(PartnerStatus $partnerStatus, PartnerRepository $partnerRepository): Response
    {
        // Récupérer les partenaires avec ce statut
        $partners = $partnerRepository->findBy(['status' => $partnerStatus]);
        
        return $this->render('admin/partner_status/show.html.twig', [
            'partner_status' => $partnerStatus,
            'partners' => $partners,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_partner_status_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, PartnerStatus $partnerStatus, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PartnerStatusType::class, $partnerStatus);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'Le statut de partenaire a été modifié avec succès.');

            return $this->redirectToRoute('app_admin_partner_status_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/partner_status/edit.html.twig', [
            'partner_status' => $partnerStatus,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_partner_status_delete', methods: ['POST'])]
    public function delete(Request $request, PartnerStatus $partnerStatus, EntityManagerInterface $entityManager, PartnerRepository $partnerRepository): Response
    {
        if ($this->isCsrfTokenValid('delete'.$partnerStatus->getId(), $request->request->get('_token'))) {
            // Vérifier si des partenaires utilisent ce statut
            $partners = $partnerRepository->findBy(['status' => $partnerStatus]);
            
            if (count($partners) > 0) {
                $this->addFlash('error', 'Ce statut ne peut pas être supprimé car il est utilisé par ' . count($partners) . ' partenaire(s).');
                return $this->redirectToRoute('app_admin_partner_status_index', [], Response::HTTP_SEE_OTHER);
            }
            
            $entityManager->remove($partnerStatus);
            $entityManager->flush();
            
            $this->addFlash('success', 'Le statut de partenaire a été supprimé avec succès.');
        }

        return $this->redirectToRoute('app_admin_partner_status_index', [], Response::HTTP_SEE_OTHER);
    }
}
