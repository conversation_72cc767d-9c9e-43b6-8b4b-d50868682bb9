<?php

namespace App\Controller\Admin;

use App\Entity\PartnerType;
use App\Form\PartnerTypeType;
use App\Repository\PartnerRepository;
use App\Repository\PartnerTypeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/partner-type')]
#[IsGranted('ROLE_ADMIN')]
class PartnerTypeController extends AbstractController
{
    #[Route('/', name: 'app_admin_partner_type_index', methods: ['GET'])]
    public function index(PartnerTypeRepository $partnerTypeRepository): Response
    {
        return $this->render('admin/partner_type/index.html.twig', [
            'partner_types' => $partnerTypeRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_admin_partner_type_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $partnerType = new PartnerType();
        $form = $this->createForm(PartnerTypeType::class, $partnerType);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($partnerType);
            $entityManager->flush();

            $this->addFlash('success', 'Le type de partenaire a été créé avec succès.');

            return $this->redirectToRoute('app_admin_partner_type_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/partner_type/new.html.twig', [
            'partner_type' => $partnerType,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_partner_type_show', methods: ['GET'])]
    public function show(PartnerType $partnerType, PartnerRepository $partnerRepository): Response
    {
        // Récupérer les partenaires avec ce type
        $partners = $partnerRepository->findBy(['type' => $partnerType]);
        
        return $this->render('admin/partner_type/show.html.twig', [
            'partner_type' => $partnerType,
            'partners' => $partners,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_partner_type_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, PartnerType $partnerType, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PartnerTypeType::class, $partnerType);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'Le type de partenaire a été modifié avec succès.');

            return $this->redirectToRoute('app_admin_partner_type_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/partner_type/edit.html.twig', [
            'partner_type' => $partnerType,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_partner_type_delete', methods: ['POST'])]
    public function delete(Request $request, PartnerType $partnerType, EntityManagerInterface $entityManager, PartnerRepository $partnerRepository): Response
    {
        if ($this->isCsrfTokenValid('delete'.$partnerType->getId(), $request->request->get('_token'))) {
            // Vérifier si des partenaires utilisent ce type
            $partners = $partnerRepository->findBy(['type' => $partnerType]);
            
            if (count($partners) > 0) {
                $this->addFlash('error', 'Ce type ne peut pas être supprimé car il est utilisé par ' . count($partners) . ' partenaire(s).');
                return $this->redirectToRoute('app_admin_partner_type_index', [], Response::HTTP_SEE_OTHER);
            }
            
            $entityManager->remove($partnerType);
            $entityManager->flush();
            
            $this->addFlash('success', 'Le type de partenaire a été supprimé avec succès.');
        }

        return $this->redirectToRoute('app_admin_partner_type_index', [], Response::HTTP_SEE_OTHER);
    }
}
