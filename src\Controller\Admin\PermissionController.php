<?php

namespace App\Controller\Admin;

use App\Entity\Permission;
use App\Form\PermissionType;
use App\Repository\PermissionRepository;
use App\Service\PermissionService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/permission')]
#[IsGranted('ROLE_ADMIN')]
class PermissionController extends AbstractController
{
    private PermissionService $permissionService;

    public function __construct(PermissionService $permissionService)
    {
        $this->permissionService = $permissionService;
    }
    #[Route('/', name: 'app_admin_permission_index', methods: ['GET'])]
    public function index(PermissionRepository $permissionRepository): Response
    {
        // Group permissions by category
        $permissionsByCategory = [];
        $permissions = $permissionRepository->findAll();

        foreach ($permissions as $permission) {
            $category = $permission->getCategory();
            if (!isset($permissionsByCategory[$category])) {
                $permissionsByCategory[$category] = [];
            }
            $permissionsByCategory[$category][] = $permission;
        }

        return $this->render('admin/permission/index.html.twig', [
            'permissionsByCategory' => $permissionsByCategory,
        ]);
    }

    #[Route('/new', name: 'app_admin_permission_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $permission = new Permission();
        $form = $this->createForm(PermissionType::class, $permission);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($permission);
            $entityManager->flush();

            $this->addFlash('success', 'La permission a été créée avec succès.');
            return $this->redirectToRoute('app_admin_permission_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/permission/new.html.twig', [
            'permission' => $permission,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_permission_show', methods: ['GET'])]
    public function show(Permission $permission): Response
    {
        return $this->render('admin/permission/show.html.twig', [
            'permission' => $permission,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_permission_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Permission $permission, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PermissionType::class, $permission);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'La permission a été modifiée avec succès.');
            return $this->redirectToRoute('app_admin_permission_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/permission/edit.html.twig', [
            'permission' => $permission,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_permission_delete', methods: ['POST'])]
    public function delete(Request $request, Permission $permission, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$permission->getId(), $request->request->get('_token'))) {
            $entityManager->remove($permission);
            $entityManager->flush();

            $this->addFlash('success', 'La permission a été supprimée avec succès.');
        }

        return $this->redirectToRoute('app_admin_permission_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/initialize', name: 'app_admin_permission_initialize', methods: ['GET'])]
    public function initialize(): Response
    {
        // Initialiser les permissions
        $this->permissionService->initializePermissions();

        // Créer le rôle administrateur
        $this->permissionService->createAdminRole();

        $this->addFlash('success', 'Les permissions ont été initialisées avec succès.');

        return $this->redirectToRoute('app_admin_permission_index');
    }
}
