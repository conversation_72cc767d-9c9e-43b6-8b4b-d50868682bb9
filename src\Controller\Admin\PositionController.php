<?php

namespace App\Controller\Admin;

use App\Entity\Position;
use App\Form\PositionImportType;
use App\Form\PositionSearchType;
use App\Form\PositionType;
use App\Repository\DepartmentRepository;
use App\Repository\EmployeeRepository;
use App\Repository\PositionRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/position')]
#[IsGranted('ROLE_ADMIN')]
class PositionController extends AbstractController
{
    #[Route('/dashboard', name: 'app_admin_position_dashboard', methods: ['GET'])]
    public function dashboard(PositionRepository $positionRepository, EmployeeRepository $employeeRepository, DepartmentRepository $departmentRepository): Response
    {
        // Récupérer tous les postes
        $positions = $positionRepository->findAll();
        $departments = $departmentRepository->findAll();

        // Statistiques générales
        $stats = [
            'totalPositions' => count($positions),
            'activePositions' => 0,
            'inactivePositions' => 0,
            'positionsWithDepartment' => 0,
            'positionsWithoutDepartment' => 0,
            'totalEmployees' => $employeeRepository->count([]),
            'employeesWithPosition' => $employeeRepository->count(['position' => $positions]),
            'employeesWithoutPosition' => 0,
            'averageMinSalary' => 0,
            'averageMaxSalary' => 0,
        ];

        // Calculer les statistiques
        $totalMinSalary = 0;
        $totalMaxSalary = 0;
        $countMinSalary = 0;
        $countMaxSalary = 0;

        foreach ($positions as $position) {
            // Statistiques générales
            if ($position->isActive()) {
                $stats['activePositions']++;
            } else {
                $stats['inactivePositions']++;
            }

            if ($position->getDepartment()) {
                $stats['positionsWithDepartment']++;
            } else {
                $stats['positionsWithoutDepartment']++;
            }

            // Calculer les salaires moyens
            if ($position->getMinSalary()) {
                $totalMinSalary += $position->getMinSalary();
                $countMinSalary++;
            }

            if ($position->getMaxSalary()) {
                $totalMaxSalary += $position->getMaxSalary();
                $countMaxSalary++;
            }
        }

        // Calculer les moyennes
        if ($countMinSalary > 0) {
            $stats['averageMinSalary'] = $totalMinSalary / $countMinSalary;
        }

        if ($countMaxSalary > 0) {
            $stats['averageMaxSalary'] = $totalMaxSalary / $countMaxSalary;
        }

        // Calculer le nombre d'employés sans poste
        $stats['employeesWithoutPosition'] = $stats['totalEmployees'] - $stats['employeesWithPosition'];

        // Données pour les graphiques
        $positionsByDepartment = [];
        $employeesByPosition = [];
        $salaryRangesByPosition = [];

        // Postes par département
        $departmentCounts = [];
        foreach ($departments as $department) {
            $departmentCounts[$department->getId()] = 0;
        }

        foreach ($positions as $position) {
            if ($position->getDepartment()) {
                $departmentId = $position->getDepartment()->getId();
                if (isset($departmentCounts[$departmentId])) {
                    $departmentCounts[$departmentId]++;
                }
            }
        }

        foreach ($departments as $department) {
            if ($departmentCounts[$department->getId()] > 0) {
                $positionsByDepartment[] = [
                    'name' => $department->getName(),
                    'count' => $departmentCounts[$department->getId()],
                ];
            }
        }

        // Trier les départements par nombre de postes (décroissant)
        usort($positionsByDepartment, function($a, $b) {
            return $b['count'] - $a['count'];
        });

        // Limiter à 10 départements pour le graphique
        $positionsByDepartment = array_slice($positionsByDepartment, 0, 10);

        // Employés par poste
        foreach ($positions as $position) {
            $employeeCount = $employeeRepository->count(['position' => $position]);
            if ($employeeCount > 0) {
                $employeesByPosition[] = [
                    'name' => $position->getTitle(),
                    'count' => $employeeCount,
                ];
            }
        }

        // Trier les postes par nombre d'employés (décroissant)
        usort($employeesByPosition, function($a, $b) {
            return $b['count'] - $a['count'];
        });

        // Limiter à 10 postes pour le graphique
        $employeesByPosition = array_slice($employeesByPosition, 0, 10);

        // Plages de salaires par poste
        foreach ($positions as $position) {
            if ($position->getMinSalary() && $position->getMaxSalary()) {
                $salaryRangesByPosition[] = [
                    'name' => $position->getTitle(),
                    'min' => $position->getMinSalary(),
                    'max' => $position->getMaxSalary(),
                ];
            }
        }

        // Trier les postes par salaire maximum (décroissant)
        usort($salaryRangesByPosition, function($a, $b) {
            return $b['max'] - $a['max'];
        });

        // Limiter à 10 postes pour le graphique
        $salaryRangesByPosition = array_slice($salaryRangesByPosition, 0, 10);

        return $this->render('admin/position/dashboard.html.twig', [
            'stats' => $stats,
            'positionsByDepartment' => $positionsByDepartment,
            'employeesByPosition' => $employeesByPosition,
            'salaryRangesByPosition' => $salaryRangesByPosition,
        ]);
    }

    #[Route('/export', name: 'app_admin_position_export', methods: ['GET'])]
    public function export(Request $request, PositionRepository $positionRepository, EmployeeRepository $employeeRepository): Response
    {
        $searchForm = $this->createForm(PositionSearchType::class);
        $searchForm->handleRequest($request);

        $positions = [];

        if ($searchForm->isSubmitted() && $searchForm->isValid()) {
            $criteria = $searchForm->getData();

            $qb = $positionRepository->createQueryBuilder('p')
                ->leftJoin('p.department', 'd');

            // Filtrer par mot-clé
            if (!empty($criteria['keyword'])) {
                $qb->andWhere('p.title LIKE :keyword OR p.code LIKE :keyword OR p.description LIKE :keyword')
                    ->setParameter('keyword', '%' . $criteria['keyword'] . '%');
            }

            // Filtrer par département
            if (!empty($criteria['department'])) {
                $qb->andWhere('p.department = :department')
                    ->setParameter('department', $criteria['department']);
            }

            // Filtrer par statut
            if (!empty($criteria['status'])) {
                if ($criteria['status'] === 'active') {
                    $qb->andWhere('p.isActive = :active')
                        ->setParameter('active', true);
                } elseif ($criteria['status'] === 'inactive') {
                    $qb->andWhere('p.isActive = :active')
                        ->setParameter('active', false);
                }
            }

            // Filtrer par plage de salaire minimum
            if (!empty($criteria['minSalaryRange'])) {
                $qb->andWhere('p.minSalary >= :minSalaryRange')
                    ->setParameter('minSalaryRange', $criteria['minSalaryRange']);
            }

            // Filtrer par plage de salaire maximum
            if (!empty($criteria['maxSalaryRange'])) {
                $qb->andWhere('p.maxSalary <= :maxSalaryRange')
                    ->setParameter('maxSalaryRange', $criteria['maxSalaryRange']);
            }

            // Filtrer par présence d'employés
            if (!empty($criteria['hasEmployees'])) {
                $positionsWithEmployees = $employeeRepository->createQueryBuilder('e')
                    ->select('IDENTITY(e.position)')
                    ->getQuery()
                    ->getResult();

                $positionIds = array_map(function($item) {
                    return $item[1];
                }, $positionsWithEmployees);

                if ($criteria['hasEmployees'] === 'with') {
                    if (empty($positionIds)) {
                        // Si aucun poste n'a d'employés, forcer un résultat vide
                        $qb->andWhere('p.id = 0');
                    } else {
                        $qb->andWhere('p.id IN (:positionIds)')
                            ->setParameter('positionIds', $positionIds);
                    }
                } elseif ($criteria['hasEmployees'] === 'without') {
                    if (!empty($positionIds)) {
                        $qb->andWhere('p.id NOT IN (:positionIds)')
                            ->setParameter('positionIds', $positionIds);
                    }
                }
            }

            $positions = $qb->getQuery()->getResult();
        } else {
            $positions = $positionRepository->findAll();
        }

        // Créer le contenu CSV
        $csvContent = "ID;Code;Titre;Description;Département;Salaire minimum;Salaire maximum;Nombre d'employés;Statut\n";

        foreach ($positions as $position) {
            $departmentName = $position->getDepartment() ? $position->getDepartment()->getName() : '';
            $employeeCount = $employeeRepository->count(['position' => $position]);
            $status = $position->isActive() ? 'Actif' : 'Inactif';
            $minSalary = $position->getMinSalary() ? number_format($position->getMinSalary(), 2, ',', ' ') . ' MAD' : '-';
            $maxSalary = $position->getMaxSalary() ? number_format($position->getMaxSalary(), 2, ',', ' ') . ' MAD' : '-';

            $csvContent .= sprintf(
                "%d;%s;%s;%s;%s;%s;%s;%d;%s\n",
                $position->getId(),
                $position->getCode(),
                str_replace(';', ',', $position->getTitle()),
                str_replace(';', ',', $position->getDescription() ?? ''),
                str_replace(';', ',', $departmentName),
                $minSalary,
                $maxSalary,
                $employeeCount,
                $status
            );
        }

        // Créer la réponse avec le contenu CSV
        $response = new Response($csvContent);
        $disposition = $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            'positions_' . date('Y-m-d_H-i-s') . '.csv'
        );

        $response->headers->set('Content-Disposition', $disposition);
        $response->headers->set('Content-Type', 'text/csv; charset=UTF-8');

        return $response;
    }

    #[Route('/import', name: 'app_admin_position_import', methods: ['GET', 'POST'])]
    public function import(Request $request, EntityManagerInterface $entityManager, PositionRepository $positionRepository, DepartmentRepository $departmentRepository): Response
    {
        $form = $this->createForm(PositionImportType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            /** @var UploadedFile $csvFile */
            $csvFile = $form->get('file')->getData();

            if ($csvFile) {
                $fileContent = file_get_contents($csvFile->getPathname());
                $rows = array_map('str_getcsv', explode("\n", $fileContent));

                // Supprimer la ligne d'en-tête si elle existe
                if (count($rows) > 0 && is_array($rows[0]) && count($rows[0]) > 0) {
                    $header = $rows[0];
                    if (strtolower($header[0]) === 'code' || strtolower($header[0]) === '"code"') {
                        array_shift($rows);
                    }
                }

                $importCount = 0;
                $errors = [];

                foreach ($rows as $index => $row) {
                    // Ignorer les lignes vides
                    if (empty($row) || count($row) <= 1 || empty($row[0])) {
                        continue;
                    }

                    // Nettoyer les données
                    $code = trim($row[0]);
                    $title = isset($row[1]) ? trim($row[1]) : '';
                    $description = isset($row[2]) ? trim($row[2]) : null;
                    $departmentCode = isset($row[3]) ? trim($row[3]) : null;
                    $minSalary = isset($row[4]) && !empty($row[4]) ? (float)str_replace([' ', ','], ['', '.'], $row[4]) : null;
                    $maxSalary = isset($row[5]) && !empty($row[5]) ? (float)str_replace([' ', ','], ['', '.'], $row[5]) : null;

                    // Vérifier si le poste existe déjà
                    $existingPosition = $positionRepository->findOneBy(['code' => $code]);

                    if ($existingPosition) {
                        $errors[] = sprintf('Ligne %d: Le poste avec le code "%s" existe déjà.', $index + 1, $code);
                        continue;
                    }

                    // Vérifier si le titre est vide
                    if (empty($title)) {
                        $errors[] = sprintf('Ligne %d: Le titre du poste est obligatoire.', $index + 1);
                        continue;
                    }

                    // Créer le nouveau poste
                    $position = new Position();
                    $position->setCode($code);
                    $position->setTitle($title);
                    $position->setDescription($description);
                    $position->setIsActive(true);
                    $position->setCreatedAt(new \DateTime());
                    $position->setUpdatedAt(new \DateTime());

                    if ($minSalary !== null) {
                        $position->setMinSalary($minSalary);
                    }

                    if ($maxSalary !== null) {
                        $position->setMaxSalary($maxSalary);
                    }

                    // Associer au département si spécifié
                    if (!empty($departmentCode)) {
                        $department = $departmentRepository->findOneBy(['code' => $departmentCode]);
                        if ($department) {
                            $position->setDepartment($department);
                        } else {
                            $errors[] = sprintf('Ligne %d: Le département avec le code "%s" n\'existe pas.', $index + 1, $departmentCode);
                        }
                    }

                    $entityManager->persist($position);
                    $importCount++;
                }

                if ($importCount > 0) {
                    $entityManager->flush();
                    $this->addFlash('success', sprintf('%d poste(s) importé(s) avec succès.', $importCount));
                }

                foreach ($errors as $error) {
                    $this->addFlash('error', $error);
                }

                return $this->redirectToRoute('app_admin_position_index');
            }
        }

        return $this->render('admin/position/import.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/', name: 'app_admin_position_index', methods: ['GET'])]
    public function index(Request $request, PositionRepository $positionRepository, EmployeeRepository $employeeRepository): Response
    {
        $searchForm = $this->createForm(PositionSearchType::class);
        $searchForm->handleRequest($request);

        $positions = [];

        if ($searchForm->isSubmitted() && $searchForm->isValid()) {
            $criteria = $searchForm->getData();

            $qb = $positionRepository->createQueryBuilder('p')
                ->leftJoin('p.department', 'd');

            // Filtrer par mot-clé
            if (!empty($criteria['keyword'])) {
                $qb->andWhere('p.title LIKE :keyword OR p.code LIKE :keyword OR p.description LIKE :keyword')
                    ->setParameter('keyword', '%' . $criteria['keyword'] . '%');
            }

            // Filtrer par département
            if (!empty($criteria['department'])) {
                $qb->andWhere('p.department = :department')
                    ->setParameter('department', $criteria['department']);
            }

            // Filtrer par statut
            if (!empty($criteria['status'])) {
                if ($criteria['status'] === 'active') {
                    $qb->andWhere('p.isActive = :active')
                        ->setParameter('active', true);
                } elseif ($criteria['status'] === 'inactive') {
                    $qb->andWhere('p.isActive = :active')
                        ->setParameter('active', false);
                }
            }

            // Filtrer par plage de salaire minimum
            if (!empty($criteria['minSalaryRange'])) {
                $qb->andWhere('p.minSalary >= :minSalaryRange')
                    ->setParameter('minSalaryRange', $criteria['minSalaryRange']);
            }

            // Filtrer par plage de salaire maximum
            if (!empty($criteria['maxSalaryRange'])) {
                $qb->andWhere('p.maxSalary <= :maxSalaryRange')
                    ->setParameter('maxSalaryRange', $criteria['maxSalaryRange']);
            }

            // Filtrer par présence d'employés
            if (!empty($criteria['hasEmployees'])) {
                $positionsWithEmployees = $employeeRepository->createQueryBuilder('e')
                    ->select('IDENTITY(e.position)')
                    ->getQuery()
                    ->getResult();

                $positionIds = array_map(function($item) {
                    return $item[1];
                }, $positionsWithEmployees);

                if ($criteria['hasEmployees'] === 'with') {
                    if (empty($positionIds)) {
                        // Si aucun poste n'a d'employés, forcer un résultat vide
                        $qb->andWhere('p.id = 0');
                    } else {
                        $qb->andWhere('p.id IN (:positionIds)')
                            ->setParameter('positionIds', $positionIds);
                    }
                } elseif ($criteria['hasEmployees'] === 'without') {
                    if (!empty($positionIds)) {
                        $qb->andWhere('p.id NOT IN (:positionIds)')
                            ->setParameter('positionIds', $positionIds);
                    }
                }
            }

            $positions = $qb->getQuery()->getResult();
        } else {
            $positions = $positionRepository->findAll();
        }

        // Compter le nombre d'employés pour chaque poste
        $employeeCounts = [];
        foreach ($positions as $position) {
            $employeeCounts[$position->getId()] = $employeeRepository->count(['position' => $position]);
        }

        return $this->render('admin/position/index.html.twig', [
            'positions' => $positions,
            'employeeCounts' => $employeeCounts,
            'searchForm' => $searchForm->createView(),
        ]);
    }

    #[Route('/new', name: 'app_admin_position_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $position = new Position();
        $position->setIsActive(true);
        $position->setCreatedAt(new \DateTime());
        $position->setUpdatedAt(new \DateTime());

        $form = $this->createForm(PositionType::class, $position);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($position);
            $entityManager->flush();

            $this->addFlash('success', 'Le poste a été créé avec succès.');

            return $this->redirectToRoute('app_admin_position_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/position/new.html.twig', [
            'position' => $position,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_position_show', methods: ['GET'])]
    public function show(Position $position, EmployeeRepository $employeeRepository): Response
    {
        // Récupérer les employés avec ce poste
        $employees = $employeeRepository->findBy(['position' => $position]);

        return $this->render('admin/position/show.html.twig', [
            'position' => $position,
            'employees' => $employees,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_position_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Position $position, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PositionType::class, $position);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'Le poste a été modifié avec succès.');

            return $this->redirectToRoute('app_admin_position_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/position/edit.html.twig', [
            'position' => $position,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_position_delete', methods: ['POST'])]
    public function delete(Request $request, Position $position, EntityManagerInterface $entityManager, EmployeeRepository $employeeRepository): Response
    {
        if ($this->isCsrfTokenValid('delete'.$position->getId(), $request->request->get('_token'))) {
            // Vérifier si des employés ont ce poste
            $employees = $employeeRepository->findBy(['position' => $position]);

            if (count($employees) > 0) {
                $this->addFlash('error', 'Ce poste ne peut pas être supprimé car il est utilisé par ' . count($employees) . ' employé(s).');
                return $this->redirectToRoute('app_admin_position_index', [], Response::HTTP_SEE_OTHER);
            }

            $entityManager->remove($position);
            $entityManager->flush();

            $this->addFlash('success', 'Le poste a été supprimé avec succès.');
        }

        return $this->redirectToRoute('app_admin_position_index', [], Response::HTTP_SEE_OTHER);
    }


}
