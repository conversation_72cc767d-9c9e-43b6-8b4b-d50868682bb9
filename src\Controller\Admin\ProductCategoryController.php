<?php

namespace App\Controller\Admin;

use App\Entity\ProductCategory;
use App\Form\ProductCategoryType;
use App\Repository\ProductCategoryRepository;
use App\Repository\ProductRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/product-category')]
#[IsGranted('ROLE_ADMIN')]
class ProductCategoryController extends AbstractController
{
    #[Route('/', name: 'app_admin_product_category_index', methods: ['GET'])]
    public function index(ProductCategoryRepository $productCategoryRepository): Response
    {
        return $this->render('admin/product_category/index.html.twig', [
            'product_categories' => $productCategoryRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_admin_product_category_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $productCategory = new ProductCategory();
        $form = $this->createForm(ProductCategoryType::class, $productCategory);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($productCategory);
            $entityManager->flush();

            $this->addFlash('success', 'La catégorie de produit a été créée avec succès.');

            return $this->redirectToRoute('app_admin_product_category_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/product_category/new.html.twig', [
            'product_category' => $productCategory,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_product_category_show', methods: ['GET'])]
    public function show(ProductCategory $productCategory, ProductRepository $productRepository): Response
    {
        // Récupérer les produits de cette catégorie
        $products = $productRepository->findBy(['category' => $productCategory]);
        
        return $this->render('admin/product_category/show.html.twig', [
            'product_category' => $productCategory,
            'products' => $products,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_product_category_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, ProductCategory $productCategory, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(ProductCategoryType::class, $productCategory);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'La catégorie de produit a été modifiée avec succès.');

            return $this->redirectToRoute('app_admin_product_category_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/product_category/edit.html.twig', [
            'product_category' => $productCategory,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_product_category_delete', methods: ['POST'])]
    public function delete(Request $request, ProductCategory $productCategory, EntityManagerInterface $entityManager, ProductRepository $productRepository): Response
    {
        if ($this->isCsrfTokenValid('delete'.$productCategory->getId(), $request->request->get('_token'))) {
            // Vérifier si des produits utilisent cette catégorie
            $products = $productRepository->findBy(['category' => $productCategory]);
            
            if (count($products) > 0) {
                $this->addFlash('error', 'Cette catégorie ne peut pas être supprimée car elle est utilisée par ' . count($products) . ' produit(s).');
                return $this->redirectToRoute('app_admin_product_category_index', [], Response::HTTP_SEE_OTHER);
            }
            
            $entityManager->remove($productCategory);
            $entityManager->flush();
            
            $this->addFlash('success', 'La catégorie de produit a été supprimée avec succès.');
        }

        return $this->redirectToRoute('app_admin_product_category_index', [], Response::HTTP_SEE_OTHER);
    }
}
