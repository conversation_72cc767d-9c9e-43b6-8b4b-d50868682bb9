<?php

namespace App\Controller\Admin;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/roadmap')]
#[IsGranted('ROLE_ADMIN')]
class RoadmapController extends AbstractController
{
    #[Route('/', name: 'app_admin_roadmap')]
    public function index(): Response
    {
        return $this->render('admin/roadmap.html.twig');
    }
}
