<?php

namespace App\Controller\Admin;

use App\Entity\Role;
use App\Entity\RolePermission;
use App\Form\RoleType;
use App\Repository\PermissionRepository;
use App\Repository\RolePermissionRepository;
use App\Repository\RoleRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/role')]
#[IsGranted('ROLE_ADMIN')]
class RoleController extends AbstractController
{
    #[Route('/', name: 'app_admin_role_index', methods: ['GET'])]
    public function index(RoleRepository $roleRepository): Response
    {
        return $this->render('admin/role/index.html.twig', [
            'roles' => $roleRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_admin_role_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $role = new Role();
        $form = $this->createForm(RoleType::class, $role);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($role);
            $entityManager->flush();

            $this->addFlash('success', 'Le rôle a été créé avec succès.');
            return $this->redirectToRoute('app_admin_role_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/role/new.html.twig', [
            'role' => $role,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_role_show', methods: ['GET'])]
    public function show(Role $role, RolePermissionRepository $rolePermissionRepository): Response
    {
        $permissions = $rolePermissionRepository->findByRole($role);

        return $this->render('admin/role/show.html.twig', [
            'role' => $role,
            'permissions' => $permissions,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_role_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Role $role, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(RoleType::class, $role);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'Le rôle a été modifié avec succès.');
            return $this->redirectToRoute('app_admin_role_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/role/edit.html.twig', [
            'role' => $role,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_admin_role_delete', methods: ['POST'])]
    public function delete(Request $request, Role $role, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$role->getId(), $request->request->get('_token'))) {
            $entityManager->remove($role);
            $entityManager->flush();

            $this->addFlash('success', 'Le rôle a été supprimé avec succès.');
        }

        return $this->redirectToRoute('app_admin_role_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/{id}/permissions', name: 'app_admin_role_permissions', methods: ['GET', 'POST'])]
    public function managePermissions(
        Request $request,
        Role $role,
        PermissionRepository $permissionRepository,
        RolePermissionRepository $rolePermissionRepository,
        EntityManagerInterface $entityManager
    ): Response {
        // Get all permissions by category
        $permissionsByCategory = [];
        $categories = $permissionRepository->findAllCategories();

        foreach ($categories as $category) {
            $permissionsByCategory[$category['category']] = $permissionRepository->findByCategory($category['category']);
        }

        // Get existing role permissions
        $rolePermissions = $rolePermissionRepository->findByRole($role);
        $permissionsMap = [];

        foreach ($rolePermissions as $rolePermission) {
            $permCode = $rolePermission->getPermission()->getCode();
            $permissionsMap[$permCode] = [
                'view' => $rolePermission->isCanView(),
                'create' => $rolePermission->isCanCreate(),
                'edit' => $rolePermission->isCanEdit(),
                'delete' => $rolePermission->isCanDelete(),
                'approve' => $rolePermission->isCanApprove(),
                'reject' => $rolePermission->isCanReject(),
                'export' => $rolePermission->isCanExport(),
                'import' => $rolePermission->isCanImport(),
                'print' => $rolePermission->isCanPrint(),
            ];
        }

        if ($request->isMethod('POST')) {
            // Get permissions from form
            $formPermissions = $request->request->all('permissions') ?? [];

            // Remove all existing permissions
            foreach ($rolePermissions as $rolePermission) {
                $entityManager->remove($rolePermission);
            }

            // Add new permissions
            foreach ($formPermissions as $permissionCode => $actions) {
                $permission = $permissionRepository->findOneBy(['code' => $permissionCode]);

                if ($permission) {
                    $rolePermission = new RolePermission();
                    $rolePermission->setRole($role);
                    $rolePermission->setPermission($permission);
                    $rolePermission->setCanView(isset($actions['view']));
                    $rolePermission->setCanCreate(isset($actions['create']));
                    $rolePermission->setCanEdit(isset($actions['edit']));
                    $rolePermission->setCanDelete(isset($actions['delete']));
                    $rolePermission->setCanApprove(isset($actions['approve']));
                    $rolePermission->setCanReject(isset($actions['reject']));
                    $rolePermission->setCanExport(isset($actions['export']));
                    $rolePermission->setCanImport(isset($actions['import']));
                    $rolePermission->setCanPrint(isset($actions['print']));

                    $entityManager->persist($rolePermission);
                }
            }

            $entityManager->flush();

            $this->addFlash('success', 'Les permissions du rôle ont été mises à jour avec succès.');
            return $this->redirectToRoute('app_admin_role_show', ['id' => $role->getId()], Response::HTTP_SEE_OTHER);
        }

        return $this->render('admin/role/permissions.html.twig', [
            'role' => $role,
            'permissionsByCategory' => $permissionsByCategory,
            'permissionsMap' => $permissionsMap,
        ]);
    }
}
