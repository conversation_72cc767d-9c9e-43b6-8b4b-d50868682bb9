<?php

namespace App\Controller\Admin;

use App\Entity\SecurityLog;
use App\Repository\SecurityLogRepository;
use App\Service\SecurityService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/security')]
#[IsGranted('ROLE_ADMIN')]
class SecurityController extends AbstractController
{
    private SecurityLogRepository $securityLogRepository;
    private SecurityService $securityService;

    public function __construct(
        SecurityLogRepository $securityLogRepository,
        SecurityService $securityService
    ) {
        $this->securityLogRepository = $securityLogRepository;
        $this->securityService = $securityService;
    }

    #[Route('/', name: 'app_admin_security_dashboard')]
    public function dashboard(Request $request): Response
    {
        // Période par défaut : 7 derniers jours
        $period = $request->query->get('period', '7');
        $since = new \DateTime("-{$period} days");

        // Statistiques de sécurité
        $stats = $this->securityService->getSecurityStats($since);

        // Logs récents
        $recentLogs = $this->securityLogRepository->createQueryBuilder('sl')
            ->andWhere('sl.createdAt >= :since')
            ->setParameter('since', $since)
            ->orderBy('sl.createdAt', 'DESC')
            ->setMaxResults(50)
            ->getQuery()
            ->getResult();

        // Activités suspectes
        $suspiciousActivities = $this->securityLogRepository->findSuspiciousActivities($since);

        // Tentatives de connexion échouées récentes
        $failedLogins = $this->securityLogRepository->findByEventType(
            SecurityLog::EVENT_LOGIN_FAILED,
            20
        );

        return $this->render('admin/security/dashboard.html.twig', [
            'stats' => $stats,
            'recent_logs' => $recentLogs,
            'suspicious_activities' => $suspiciousActivities,
            'failed_logins' => $failedLogins,
            'period' => $period,
            'since' => $since,
        ]);
    }

    #[Route('/logs', name: 'app_admin_security_logs')]
    public function logs(Request $request): Response
    {
        $page = $request->query->getInt('page', 1);
        $eventType = $request->query->get('event_type');
        $severity = $request->query->get('severity');
        $ipAddress = $request->query->get('ip_address');
        $userId = $request->query->get('user_id');

        $queryBuilder = $this->securityLogRepository->createQueryBuilder('sl')
            ->leftJoin('sl.user', 'u')
            ->addSelect('u');

        if ($eventType) {
            $queryBuilder->andWhere('sl.eventType = :eventType')
                ->setParameter('eventType', $eventType);
        }

        if ($severity) {
            $queryBuilder->andWhere('sl.severity = :severity')
                ->setParameter('severity', $severity);
        }

        if ($ipAddress) {
            $queryBuilder->andWhere('sl.ipAddress LIKE :ipAddress')
                ->setParameter('ipAddress', '%' . $ipAddress . '%');
        }

        if ($userId) {
            $queryBuilder->andWhere('sl.user = :userId')
                ->setParameter('userId', $userId);
        }

        $queryBuilder->orderBy('sl.createdAt', 'DESC');

        $query = $queryBuilder->getQuery();
        
        // Pagination simple
        $limit = 50;
        $offset = ($page - 1) * $limit;
        $query->setFirstResult($offset)->setMaxResults($limit);

        $logs = $query->getResult();

        // Compter le total pour la pagination
        $totalQuery = $this->securityLogRepository->createQueryBuilder('sl')
            ->select('COUNT(sl.id)');

        if ($eventType) {
            $totalQuery->andWhere('sl.eventType = :eventType')
                ->setParameter('eventType', $eventType);
        }

        if ($severity) {
            $totalQuery->andWhere('sl.severity = :severity')
                ->setParameter('severity', $severity);
        }

        if ($ipAddress) {
            $totalQuery->andWhere('sl.ipAddress LIKE :ipAddress')
                ->setParameter('ipAddress', '%' . $ipAddress . '%');
        }

        if ($userId) {
            $totalQuery->andWhere('sl.user = :userId')
                ->setParameter('userId', $userId);
        }

        $total = $totalQuery->getQuery()->getSingleScalarResult();
        $totalPages = ceil($total / $limit);

        return $this->render('admin/security/logs.html.twig', [
            'logs' => $logs,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total' => $total,
            'filters' => [
                'event_type' => $eventType,
                'severity' => $severity,
                'ip_address' => $ipAddress,
                'user_id' => $userId,
            ],
            'event_types' => [
                SecurityLog::EVENT_LOGIN_SUCCESS => 'Connexion réussie',
                SecurityLog::EVENT_LOGIN_FAILED => 'Connexion échouée',
                SecurityLog::EVENT_LOGOUT => 'Déconnexion',
                SecurityLog::EVENT_PASSWORD_CHANGE => 'Changement mot de passe',
                SecurityLog::EVENT_2FA_ENABLED => '2FA activée',
                SecurityLog::EVENT_2FA_DISABLED => '2FA désactivée',
                SecurityLog::EVENT_PERMISSION_DENIED => 'Accès refusé',
                SecurityLog::EVENT_SUSPICIOUS_ACTIVITY => 'Activité suspecte',
            ],
            'severities' => [
                SecurityLog::SEVERITY_LOW => 'Faible',
                SecurityLog::SEVERITY_MEDIUM => 'Moyen',
                SecurityLog::SEVERITY_HIGH => 'Élevé',
                SecurityLog::SEVERITY_CRITICAL => 'Critique',
            ],
        ]);
    }

    #[Route('/log/{id}', name: 'app_admin_security_log_show')]
    public function showLog(SecurityLog $log): Response
    {
        return $this->render('admin/security/log_show.html.twig', [
            'log' => $log,
        ]);
    }

    #[Route('/blocked-ips', name: 'app_admin_security_blocked_ips')]
    public function blockedIps(): Response
    {
        // Obtenir les IPs avec beaucoup de tentatives échouées
        $since = new \DateTime('-24 hours');
        $suspiciousIps = $this->securityLogRepository->createQueryBuilder('sl')
            ->select('sl.ipAddress, COUNT(sl.id) as attempts')
            ->andWhere('sl.eventType = :eventType')
            ->andWhere('sl.createdAt >= :since')
            ->andWhere('sl.ipAddress IS NOT NULL')
            ->setParameter('eventType', SecurityLog::EVENT_LOGIN_FAILED)
            ->setParameter('since', $since)
            ->groupBy('sl.ipAddress')
            ->having('attempts >= 3')
            ->orderBy('attempts', 'DESC')
            ->getQuery()
            ->getResult();

        return $this->render('admin/security/blocked_ips.html.twig', [
            'suspicious_ips' => $suspiciousIps,
        ]);
    }

    #[Route('/settings', name: 'app_admin_security_settings')]
    public function settings(Request $request): Response
    {
        // Configuration de sécurité (à implémenter avec des paramètres système)
        $settings = [
            'max_login_attempts' => 5,
            'lockout_duration' => 60, // minutes
            'password_min_length' => 8,
            'password_require_uppercase' => true,
            'password_require_lowercase' => true,
            'password_require_numbers' => true,
            'password_require_symbols' => false,
            'session_timeout' => 120, // minutes
            '2fa_required' => false,
            'log_retention_days' => 90,
        ];

        if ($request->isMethod('POST')) {
            // Traitement de la sauvegarde des paramètres
            $this->addFlash('success', 'Paramètres de sécurité mis à jour avec succès.');
            return $this->redirectToRoute('app_admin_security_settings');
        }

        return $this->render('admin/security/settings.html.twig', [
            'settings' => $settings,
        ]);
    }

    #[Route('/cleanup', name: 'app_admin_security_cleanup', methods: ['POST'])]
    public function cleanup(Request $request): Response
    {
        $daysToKeep = $request->request->getInt('days_to_keep', 90);
        
        if ($daysToKeep < 30) {
            $this->addFlash('error', 'La période de rétention ne peut pas être inférieure à 30 jours.');
            return $this->redirectToRoute('app_admin_security_dashboard');
        }

        $deletedCount = $this->securityService->cleanOldLogs($daysToKeep);
        
        $this->addFlash('success', "Nettoyage terminé. {$deletedCount} logs supprimés.");
        
        return $this->redirectToRoute('app_admin_security_dashboard');
    }
}
