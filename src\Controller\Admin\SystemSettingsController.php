<?php

namespace App\Controller\Admin;

use App\Entity\SystemSetting;
use App\Form\SystemSettingType;
use App\Repository\SystemSettingRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/system-settings')]
#[IsGranted('ROLE_ADMIN')]
class SystemSettingsController extends AbstractController
{
    #[Route('/', name: 'app_admin_system_settings', methods: ['GET', 'POST'])]
    public function index(Request $request, SystemSettingRepository $systemSettingRepository, EntityManagerInterface $entityManager): Response
    {
        // Récupérer tous les paramètres système
        $settings = $systemSettingRepository->findAll();
        
        // Grouper les paramètres par catégorie
        $settingsByCategory = [];
        foreach ($settings as $setting) {
            $category = $setting->getCategory();
            if (!isset($settingsByCategory[$category])) {
                $settingsByCategory[$category] = [];
            }
            $settingsByCategory[$category][] = $setting;
        }
        
        // Traiter le formulaire de mise à jour des paramètres
        if ($request->isMethod('POST')) {
            $settingsData = $request->request->all('settings');
            
            foreach ($settingsData as $id => $value) {
                $setting = $systemSettingRepository->find($id);
                if ($setting) {
                    $setting->setValue($value);
                    $entityManager->persist($setting);
                }
            }
            
            $entityManager->flush();
            
            $this->addFlash('success', 'Les paramètres système ont été mis à jour avec succès.');
            
            return $this->redirectToRoute('app_admin_system_settings');
        }
        
        return $this->render('admin/system_settings/test.html.twig', [
            'settingsByCategory' => $settingsByCategory,
        ]);
    }
    
    #[Route('/new', name: 'app_admin_system_settings_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $systemSetting = new SystemSetting();
        $form = $this->createForm(SystemSettingType::class, $systemSetting);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($systemSetting);
            $entityManager->flush();
            
            $this->addFlash('success', 'Le paramètre système a été créé avec succès.');
            
            return $this->redirectToRoute('app_admin_system_settings');
        }
        
        return $this->render('admin/system_settings/new.html.twig', [
            'system_setting' => $systemSetting,
            'form' => $form->createView(),
        ]);
    }
    
    #[Route('/{id}/edit', name: 'app_admin_system_settings_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, SystemSetting $systemSetting, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(SystemSettingType::class, $systemSetting);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();
            
            $this->addFlash('success', 'Le paramètre système a été modifié avec succès.');
            
            return $this->redirectToRoute('app_admin_system_settings');
        }
        
        return $this->render('admin/system_settings/edit.html.twig', [
            'system_setting' => $systemSetting,
            'form' => $form->createView(),
        ]);
    }
    
    #[Route('/{id}', name: 'app_admin_system_settings_delete', methods: ['POST'])]
    public function delete(Request $request, SystemSetting $systemSetting, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$systemSetting->getId(), $request->request->get('_token'))) {
            $entityManager->remove($systemSetting);
            $entityManager->flush();
            
            $this->addFlash('success', 'Le paramètre système a été supprimé avec succès.');
        }
        
        return $this->redirectToRoute('app_admin_system_settings');
    }
}
