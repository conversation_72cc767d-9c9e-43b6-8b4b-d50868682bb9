<?php

namespace App\Controller\Admin;

use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/user-role')]
#[IsGranted('ROLE_ADMIN')]
class UserRoleController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private UserRepository $userRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        UserRepository $userRepository
    ) {
        $this->entityManager = $entityManager;
        $this->userRepository = $userRepository;
    }

    #[Route('/', name: 'app_admin_user_role_index')]
    public function index(): Response
    {
        $users = $this->userRepository->findAll();

        return $this->render('admin/user_role/index.html.twig', [
            'users' => $users,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_admin_user_role_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, User $user): Response
    {
        $availableRoles = [
            'ROLE_USER' => 'Utilisateur',
            'ROLE_ADMIN' => 'Administrateur',
            'ROLE_FINANCE' => 'Finance',
            'ROLE_FINANCE_MANAGER' => 'Responsable Finance',
            'ROLE_ACCOUNTING' => 'Comptabilité',
            'ROLE_ACCOUNTING_ADMIN' => 'Administrateur Comptabilité',
            'ROLE_HR' => 'Ressources Humaines',
            'ROLE_HR_MANAGER' => 'Responsable RH',
            'ROLE_PROJECT_MANAGER' => 'Chef de Projet',
            'ROLE_PURCHASE' => 'Achats',
            'ROLE_PURCHASE_MANAGER' => 'Responsable Achats',
            'ROLE_INVOICE_APPROVER' => 'Approbateur de Factures',
        ];

        $userRoles = $user->getRoles();

        if ($request->isMethod('POST')) {
            $submittedRoles = $request->request->all('roles') ?? [];
            
            // Always include ROLE_USER
            $newRoles = ['ROLE_USER'];
            
            foreach ($submittedRoles as $role => $value) {
                if ($value === 'on' && array_key_exists($role, $availableRoles)) {
                    $newRoles[] = $role;
                }
            }
            
            $user->setRoles($newRoles);
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Les rôles de l\'utilisateur ont été mis à jour avec succès.');
            
            return $this->redirectToRoute('app_admin_user_role_index');
        }

        return $this->render('admin/user_role/edit.html.twig', [
            'user' => $user,
            'availableRoles' => $availableRoles,
            'userRoles' => $userRoles,
        ]);
    }

    #[Route('/{id}/add-accounting-role', name: 'app_admin_user_add_accounting_role', methods: ['GET'])]
    public function addAccountingRole(User $user): Response
    {
        $roles = $user->getRoles();
        
        if (!in_array('ROLE_ACCOUNTING', $roles)) {
            $roles[] = 'ROLE_ACCOUNTING';
            $user->setRoles($roles);
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Le rôle Comptabilité a été ajouté à l\'utilisateur ' . $user->getFullName() . '.');
        } else {
            $this->addFlash('info', 'L\'utilisateur ' . $user->getFullName() . ' a déjà le rôle Comptabilité.');
        }
        
        return $this->redirectToRoute('app_admin_user_role_index');
    }
}
