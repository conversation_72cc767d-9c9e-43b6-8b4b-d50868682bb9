<?php

namespace App\Controller\Admin;

use App\Service\WorkflowAnalysisService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/workflow-analysis')]
#[IsGranted('ROLE_ADMIN')]
class WorkflowAnalysisController extends AbstractController
{
    private WorkflowAnalysisService $workflowAnalysisService;

    public function __construct(WorkflowAnalysisService $workflowAnalysisService)
    {
        $this->workflowAnalysisService = $workflowAnalysisService;
    }

    #[Route('/', name: 'app_admin_workflow_analysis_index')]
    public function index(): Response
    {
        $analysis = $this->workflowAnalysisService->analyzeWorkflows();
        $report = $this->workflowAnalysisService->generateRecommendationReport();

        return $this->render('admin/workflow_analysis/index.html.twig', [
            'analysis' => $analysis,
            'report' => $report,
        ]);
    }

    #[Route('/connections', name: 'app_admin_workflow_analysis_connections')]
    public function connections(): Response
    {
        $analysis = $this->workflowAnalysisService->analyzeWorkflows();

        return $this->render('admin/workflow_analysis/connections.html.twig', [
            'current_connections' => $analysis['current_connections'],
            'missing_connections' => $analysis['missing_connections'],
        ]);
    }

    #[Route('/opportunities', name: 'app_admin_workflow_analysis_opportunities')]
    public function opportunities(): Response
    {
        $analysis = $this->workflowAnalysisService->analyzeWorkflows();

        return $this->render('admin/workflow_analysis/opportunities.html.twig', [
            'opportunities' => $analysis['workflow_opportunities'],
        ]);
    }

    #[Route('/recommendations', name: 'app_admin_workflow_analysis_recommendations')]
    public function recommendations(): Response
    {
        $report = $this->workflowAnalysisService->generateRecommendationReport();

        return $this->render('admin/workflow_analysis/recommendations.html.twig', [
            'report' => $report,
        ]);
    }

    #[Route('/roadmap', name: 'app_admin_workflow_analysis_roadmap')]
    public function roadmap(): Response
    {
        $report = $this->workflowAnalysisService->generateRecommendationReport();

        return $this->render('admin/workflow_analysis/roadmap.html.twig', [
            'immediate_actions' => $report['immediate_actions'],
            'medium_term' => $report['medium_term_roadmap'],
            'long_term' => $report['long_term_vision'],
        ]);
    }
}
