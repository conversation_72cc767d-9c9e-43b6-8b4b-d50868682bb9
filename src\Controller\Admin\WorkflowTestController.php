<?php

namespace App\Controller\Admin;

use App\Service\WorkflowTestService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/admin/workflow-test')]
#[IsGranted('ROLE_ADMIN')]
class WorkflowTestController extends AbstractController
{
    public function __construct(
        private WorkflowTestService $workflowTestService
    ) {
    }
    
    #[Route('/', name: 'app_admin_workflow_test')]
    public function index(): Response
    {
        return $this->render('admin/workflow_test/index.html.twig');
    }
    
    #[Route('/purchase', name: 'app_admin_workflow_test_purchase')]
    public function testPurchaseWorkflow(): Response
    {
        $results = $this->workflowTestService->testPurchaseWorkflow();
        
        if ($results['success']) {
            $this->addFlash('success', 'Le test du workflow d\'achat a été exécuté avec succès.');
        } else {
            $this->addFlash('error', 'Erreur lors du test du workflow d\'achat: ' . $results['error']);
        }
        
        return $this->render('admin/workflow_test/purchase.html.twig', [
            'results' => $results,
        ]);
    }
    
    #[Route('/finance', name: 'app_admin_workflow_test_finance')]
    public function testFinanceWorkflow(): Response
    {
        // À implémenter
        $results = [
            'success' => false,
            'error' => 'Cette fonctionnalité n\'est pas encore implémentée.',
        ];
        
        $this->addFlash('warning', 'Le test du workflow financier n\'est pas encore implémenté.');
        
        return $this->render('admin/workflow_test/finance.html.twig', [
            'results' => $results,
        ]);
    }
    
    #[Route('/accounting', name: 'app_admin_workflow_test_accounting')]
    public function testAccountingWorkflow(): Response
    {
        // À implémenter
        $results = [
            'success' => false,
            'error' => 'Cette fonctionnalité n\'est pas encore implémentée.',
        ];
        
        $this->addFlash('warning', 'Le test du workflow comptable n\'est pas encore implémenté.');
        
        return $this->render('admin/workflow_test/accounting.html.twig', [
            'results' => $results,
        ]);
    }
}
