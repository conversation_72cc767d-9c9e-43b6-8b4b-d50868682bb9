<?php

namespace App\Controller;

use App\Service\AdvancedAnalyticsService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/advanced-dashboard')]
#[IsGranted('ROLE_USER')]
class AdvancedDashboardController extends AbstractController
{
    private AdvancedAnalyticsService $analyticsService;

    public function __construct(AdvancedAnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    #[Route('/', name: 'app_advanced_dashboard')]
    public function index(): Response
    {
        $kpis = $this->analyticsService->getMainKPIs();

        return $this->render('advanced_dashboard/index.html.twig', [
            'kpis' => $kpis,
        ]);
    }

    #[Route('/api/kpis', name: 'app_advanced_dashboard_api_kpis', methods: ['GET'])]
    public function getKPIs(): JsonResponse
    {
        $kpis = $this->analyticsService->getMainKPIs();
        return $this->json($kpis);
    }

    #[Route('/api/chart-data/{metric}', name: 'app_advanced_dashboard_api_chart_data', methods: ['GET'])]
    public function getChartData(string $metric, Request $request): JsonResponse
    {
        $period = $request->query->get('period', '30d');
        $data = $this->analyticsService->getTimeSeriesData($metric, $period);
        
        return $this->json([
            'metric' => $metric,
            'period' => $period,
            'data' => $data
        ]);
    }

    #[Route('/projects', name: 'app_advanced_dashboard_projects')]
    public function projects(): Response
    {
        return $this->render('advanced_dashboard/projects.html.twig');
    }

    #[Route('/financial', name: 'app_advanced_dashboard_financial')]
    public function financial(): Response
    {
        return $this->render('advanced_dashboard/financial.html.twig');
    }

    #[Route('/hr', name: 'app_advanced_dashboard_hr')]
    public function hr(): Response
    {
        return $this->render('advanced_dashboard/hr.html.twig');
    }

    #[Route('/security', name: 'app_advanced_dashboard_security')]
    public function security(): Response
    {
        return $this->render('advanced_dashboard/security.html.twig');
    }

    #[Route('/export', name: 'app_advanced_dashboard_export', methods: ['POST'])]
    public function export(Request $request): Response
    {
        $format = $request->request->get('format', 'pdf');
        $data = $this->analyticsService->getMainKPIs();

        switch ($format) {
            case 'pdf':
                return $this->exportToPDF($data);
            case 'excel':
                return $this->exportToExcel($data);
            case 'csv':
                return $this->exportToCSV($data);
            default:
                throw $this->createNotFoundException('Format non supporté');
        }
    }

    private function exportToPDF(array $data): Response
    {
        // Implémentation de l'export PDF
        $html = $this->renderView('advanced_dashboard/export/pdf.html.twig', [
            'data' => $data,
            'generated_at' => new \DateTime(),
        ]);

        // Ici, vous pourriez utiliser une bibliothèque comme TCPDF ou wkhtmltopdf
        // Pour l'instant, on retourne le HTML
        return new Response($html, 200, [
            'Content-Type' => 'text/html',
            'Content-Disposition' => 'attachment; filename="dashboard-report.html"'
        ]);
    }

    private function exportToExcel(array $data): Response
    {
        // Implémentation de l'export Excel avec PhpSpreadsheet
        $csvContent = $this->generateCSVContent($data);
        
        return new Response($csvContent, 200, [
            'Content-Type' => 'application/vnd.ms-excel',
            'Content-Disposition' => 'attachment; filename="dashboard-report.csv"'
        ]);
    }

    private function exportToCSV(array $data): Response
    {
        $csvContent = $this->generateCSVContent($data);
        
        return new Response($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="dashboard-report.csv"'
        ]);
    }

    private function generateCSVContent(array $data): string
    {
        $output = fopen('php://temp', 'r+');
        
        // En-têtes
        fputcsv($output, ['Catégorie', 'Métrique', 'Valeur']);
        
        // Données des projets
        foreach ($data['projects'] as $key => $value) {
            fputcsv($output, ['Projets', $this->formatMetricName($key), $value]);
        }
        
        // Données financières
        foreach ($data['financial'] as $key => $value) {
            fputcsv($output, ['Financier', $this->formatMetricName($key), $value]);
        }
        
        // Données RH
        foreach ($data['hr'] as $key => $value) {
            fputcsv($output, ['RH', $this->formatMetricName($key), $value]);
        }
        
        // Données de sécurité
        foreach ($data['security'] as $key => $value) {
            fputcsv($output, ['Sécurité', $this->formatMetricName($key), $value]);
        }
        
        // Données des partenaires
        foreach ($data['partners'] as $key => $value) {
            fputcsv($output, ['Partenaires', $this->formatMetricName($key), $value]);
        }
        
        rewind($output);
        $csvContent = stream_get_contents($output);
        fclose($output);
        
        return $csvContent;
    }

    private function formatMetricName(string $key): string
    {
        $translations = [
            'active_projects' => 'Projets actifs',
            'completed_this_month' => 'Projets terminés ce mois',
            'total_budget' => 'Budget total',
            'success_rate' => 'Taux de réussite (%)',
            'monthly_revenue' => 'Chiffre d\'affaires mensuel',
            'pending_invoices' => 'Factures en attente',
            'overdue_invoices' => 'Factures en retard',
            'pending_amount' => 'Montant en attente',
            'total_employees' => 'Total employés',
            'new_employees' => 'Nouveaux employés',
            'active_leaves' => 'Congés en cours',
            'pending_leaves' => 'Congés en attente',
            'failed_logins' => 'Connexions échouées',
            'suspicious_activities' => 'Activités suspectes',
            'successful_logins' => 'Connexions réussies',
            'unique_ips' => 'IPs uniques',
            'total_partners' => 'Total partenaires',
            'new_partners' => 'Nouveaux partenaires',
            'active_partners' => 'Partenaires actifs',
        ];

        return $translations[$key] ?? ucfirst(str_replace('_', ' ', $key));
    }
}
