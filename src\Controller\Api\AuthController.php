<?php

namespace App\Controller\Api;

use App\Entity\User;
use App\Repository\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/auth', name: 'api_auth_')]
class AuthController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private UserPasswordHasherInterface $passwordHasher;
    private ValidatorInterface $validator;

    public function __construct(
        EntityManagerInterface $entityManager,
        UserPasswordHasherInterface $passwordHasher,
        ValidatorInterface $validator
    ) {
        $this->entityManager = $entityManager;
        $this->passwordHasher = $passwordHasher;
        $this->validator = $validator;
    }

    #[Route('/login', name: 'login', methods: ['POST'])]
    public function login(Request $request, UserRepository $userRepository): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        if (!isset($data['email']) || !isset($data['password'])) {
            return $this->json([
                'success' => false,
                'message' => 'Email et mot de passe requis'
            ], 400);
        }

        $user = $userRepository->findOneBy(['email' => $data['email']]);
        
        if (!$user || !$this->passwordHasher->isPasswordValid($user, $data['password'])) {
            return $this->json([
                'success' => false,
                'message' => 'Identifiants invalides'
            ], 401);
        }

        // Ici, vous devriez générer un token JWT
        // Pour l'instant, on simule avec un token simple
        $token = $this->generateSimpleToken($user);

        return $this->json([
            'success' => true,
            'data' => [
                'token' => $token,
                'user' => [
                    'id' => $user->getId(),
                    'email' => $user->getEmail(),
                    'full_name' => $user->getFullName(),
                    'roles' => $user->getRoles()
                ],
                'expires_at' => (new \DateTime('+24 hours'))->format('c')
            ]
        ]);
    }

    #[Route('/register', name: 'register', methods: ['POST'])]
    public function register(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        $requiredFields = ['email', 'password', 'firstName', 'lastName'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return $this->json([
                    'success' => false,
                    'message' => "Le champ {$field} est requis"
                ], 400);
            }
        }

        // Vérifier si l'utilisateur existe déjà
        $existingUser = $this->entityManager->getRepository(User::class)
            ->findOneBy(['email' => $data['email']]);
            
        if ($existingUser) {
            return $this->json([
                'success' => false,
                'message' => 'Un utilisateur avec cet email existe déjà'
            ], 409);
        }

        $user = new User();
        $user->setEmail($data['email']);
        $user->setFirstName($data['firstName']);
        $user->setLastName($data['lastName']);
        $user->setRoles(['ROLE_USER']);
        
        // Hasher le mot de passe
        $hashedPassword = $this->passwordHasher->hashPassword($user, $data['password']);
        $user->setPassword($hashedPassword);

        // Valider l'entité
        $errors = $this->validator->validate($user);
        if (count($errors) > 0) {
            $errorMessages = [];
            foreach ($errors as $error) {
                $errorMessages[] = $error->getMessage();
            }
            
            return $this->json([
                'success' => false,
                'message' => 'Données invalides',
                'errors' => $errorMessages
            ], 400);
        }

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        // Générer un token pour le nouvel utilisateur
        $token = $this->generateSimpleToken($user);

        return $this->json([
            'success' => true,
            'message' => 'Utilisateur créé avec succès',
            'data' => [
                'token' => $token,
                'user' => [
                    'id' => $user->getId(),
                    'email' => $user->getEmail(),
                    'full_name' => $user->getFullName(),
                    'roles' => $user->getRoles()
                ],
                'expires_at' => (new \DateTime('+24 hours'))->format('c')
            ]
        ], 201);
    }

    #[Route('/me', name: 'me', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function me(): JsonResponse
    {
        $user = $this->getUser();
        
        return $this->json([
            'success' => true,
            'data' => [
                'id' => $user->getId(),
                'email' => $user->getEmail(),
                'full_name' => $user->getFullName(),
                'first_name' => $user->getFirstName(),
                'last_name' => $user->getLastName(),
                'roles' => $user->getRoles(),
                'created_at' => $user->getCreatedAt()?->format('c')
            ]
        ]);
    }

    #[Route('/refresh', name: 'refresh', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function refresh(): JsonResponse
    {
        $user = $this->getUser();
        $token = $this->generateSimpleToken($user);

        return $this->json([
            'success' => true,
            'data' => [
                'token' => $token,
                'expires_at' => (new \DateTime('+24 hours'))->format('c')
            ]
        ]);
    }

    #[Route('/logout', name: 'logout', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function logout(): JsonResponse
    {
        // Dans une vraie implémentation JWT, vous pourriez blacklister le token
        return $this->json([
            'success' => true,
            'message' => 'Déconnexion réussie'
        ]);
    }

    #[Route('/change-password', name: 'change_password', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function changePassword(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $user = $this->getUser();
        
        if (!isset($data['current_password']) || !isset($data['new_password'])) {
            return $this->json([
                'success' => false,
                'message' => 'Mot de passe actuel et nouveau mot de passe requis'
            ], 400);
        }

        // Vérifier le mot de passe actuel
        if (!$this->passwordHasher->isPasswordValid($user, $data['current_password'])) {
            return $this->json([
                'success' => false,
                'message' => 'Mot de passe actuel incorrect'
            ], 400);
        }

        // Valider le nouveau mot de passe
        if (strlen($data['new_password']) < 6) {
            return $this->json([
                'success' => false,
                'message' => 'Le nouveau mot de passe doit contenir au moins 6 caractères'
            ], 400);
        }

        // Hasher et sauvegarder le nouveau mot de passe
        $hashedPassword = $this->passwordHasher->hashPassword($user, $data['new_password']);
        $user->setPassword($hashedPassword);
        
        $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Mot de passe modifié avec succès'
        ]);
    }

    #[Route('/forgot-password', name: 'forgot_password', methods: ['POST'])]
    public function forgotPassword(Request $request, UserRepository $userRepository): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        if (!isset($data['email'])) {
            return $this->json([
                'success' => false,
                'message' => 'Email requis'
            ], 400);
        }

        $user = $userRepository->findOneBy(['email' => $data['email']]);
        
        if (!$user) {
            // Pour des raisons de sécurité, on ne révèle pas si l'email existe
            return $this->json([
                'success' => true,
                'message' => 'Si cet email existe, un lien de réinitialisation a été envoyé'
            ]);
        }

        // Ici, vous devriez générer un token de réinitialisation et envoyer un email
        // Pour l'instant, on simule
        $resetToken = bin2hex(random_bytes(32));
        
        // Sauvegarder le token (vous devriez ajouter un champ reset_token à l'entité User)
        // $user->setResetToken($resetToken);
        // $user->setResetTokenExpiresAt(new \DateTime('+1 hour'));
        // $this->entityManager->flush();

        return $this->json([
            'success' => true,
            'message' => 'Si cet email existe, un lien de réinitialisation a été envoyé',
            'debug_token' => $resetToken // À supprimer en production
        ]);
    }

    #[Route('/validate-token', name: 'validate_token', methods: ['POST'])]
    public function validateToken(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        if (!isset($data['token'])) {
            return $this->json([
                'success' => false,
                'message' => 'Token requis'
            ], 400);
        }

        // Ici, vous devriez valider le token JWT
        // Pour l'instant, on simule une validation simple
        $isValid = $this->validateSimpleToken($data['token']);

        if (!$isValid) {
            return $this->json([
                'success' => false,
                'message' => 'Token invalide ou expiré'
            ], 401);
        }

        return $this->json([
            'success' => true,
            'message' => 'Token valide'
        ]);
    }

    /**
     * Génère un token simple (à remplacer par JWT en production)
     */
    private function generateSimpleToken(User $user): string
    {
        $payload = [
            'user_id' => $user->getId(),
            'email' => $user->getEmail(),
            'exp' => time() + (24 * 60 * 60), // 24 heures
            'iat' => time()
        ];
        
        return base64_encode(json_encode($payload));
    }

    /**
     * Valide un token simple (à remplacer par JWT en production)
     */
    private function validateSimpleToken(string $token): bool
    {
        try {
            $payload = json_decode(base64_decode($token), true);
            
            if (!$payload || !isset($payload['exp']) || $payload['exp'] < time()) {
                return false;
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
