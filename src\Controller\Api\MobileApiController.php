<?php

namespace App\Controller\Api;

use App\Entity\User;
use App\Repository\ProjectRepository;
use App\Repository\EmployeeRepository;
use App\Repository\InvoiceRepository;
use App\Repository\NotificationRepository;
use App\Service\AdvancedAnalyticsService;
use App\Service\NotificationService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Serializer\SerializerInterface;

#[Route('/api/mobile', name: 'api_mobile_')]
#[IsGranted('ROLE_USER')]
class MobileApiController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private SerializerInterface $serializer;
    private AdvancedAnalyticsService $analyticsService;
    private NotificationService $notificationService;

    public function __construct(
        EntityManagerInterface $entityManager,
        SerializerInterface $serializer,
        AdvancedAnalyticsService $analyticsService,
        NotificationService $notificationService
    ) {
        $this->entityManager = $entityManager;
        $this->serializer = $serializer;
        $this->analyticsService = $analyticsService;
        $this->notificationService = $notificationService;
    }

    #[Route('/dashboard', name: 'dashboard', methods: ['GET'])]
    public function dashboard(): JsonResponse
    {
        $user = $this->getUser();
        $kpis = $this->analyticsService->getMainKPIs();
        $unreadNotifications = $this->notificationService->getUnreadCount($user);

        return $this->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->getId(),
                    'name' => $user->getFullName(),
                    'email' => $user->getEmail(),
                    'roles' => $user->getRoles()
                ],
                'kpis' => $kpis,
                'unread_notifications' => $unreadNotifications,
                'last_updated' => (new \DateTime())->format('c')
            ]
        ]);
    }

    #[Route('/projects', name: 'projects', methods: ['GET'])]
    public function projects(Request $request, ProjectRepository $projectRepository): JsonResponse
    {
        $page = $request->query->getInt('page', 1);
        $limit = min($request->query->getInt('limit', 20), 100); // Max 100 items
        $status = $request->query->get('status');
        
        $qb = $projectRepository->createQueryBuilder('p')
            ->orderBy('p.createdAt', 'DESC')
            ->setFirstResult(($page - 1) * $limit)
            ->setMaxResults($limit);

        if ($status) {
            $qb->andWhere('p.status = :status')
               ->setParameter('status', $status);
        }

        $projects = $qb->getQuery()->getResult();
        
        $data = [];
        foreach ($projects as $project) {
            $data[] = [
                'id' => $project->getId(),
                'name' => $project->getName(),
                'description' => $project->getDescription(),
                'status' => $project->getStatus(),
                'budget' => $project->getBudget(),
                'start_date' => $project->getStartDate()?->format('Y-m-d'),
                'end_date' => $project->getEndDate()?->format('Y-m-d'),
                'progress' => $project->getProgress() ?? 0,
                'created_at' => $project->getCreatedAt()->format('c')
            ];
        }

        return $this->json([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => count($data)
            ]
        ]);
    }

    #[Route('/projects/{id}', name: 'project_detail', methods: ['GET'])]
    public function projectDetail(int $id, ProjectRepository $projectRepository): JsonResponse
    {
        $project = $projectRepository->find($id);
        
        if (!$project) {
            return $this->json([
                'success' => false,
                'message' => 'Projet non trouvé'
            ], 404);
        }

        return $this->json([
            'success' => true,
            'data' => [
                'id' => $project->getId(),
                'name' => $project->getName(),
                'description' => $project->getDescription(),
                'status' => $project->getStatus(),
                'budget' => $project->getBudget(),
                'start_date' => $project->getStartDate()?->format('Y-m-d'),
                'end_date' => $project->getEndDate()?->format('Y-m-d'),
                'progress' => $project->getProgress() ?? 0,
                'created_at' => $project->getCreatedAt()->format('c'),
                'updated_at' => $project->getUpdatedAt()?->format('c')
            ]
        ]);
    }

    #[Route('/employees', name: 'employees', methods: ['GET'])]
    public function employees(Request $request, EmployeeRepository $employeeRepository): JsonResponse
    {
        $page = $request->query->getInt('page', 1);
        $limit = min($request->query->getInt('limit', 20), 100);
        $search = $request->query->get('search');
        
        $qb = $employeeRepository->createQueryBuilder('e')
            ->orderBy('e.firstName', 'ASC')
            ->setFirstResult(($page - 1) * $limit)
            ->setMaxResults($limit);

        if ($search) {
            $qb->andWhere('e.firstName LIKE :search OR e.lastName LIKE :search OR e.email LIKE :search')
               ->setParameter('search', '%' . $search . '%');
        }

        $employees = $qb->getQuery()->getResult();
        
        $data = [];
        foreach ($employees as $employee) {
            $data[] = [
                'id' => $employee->getId(),
                'first_name' => $employee->getFirstName(),
                'last_name' => $employee->getLastName(),
                'email' => $employee->getEmail(),
                'phone' => $employee->getPhone(),
                'position' => $employee->getPosition()?->getName(),
                'department' => $employee->getDepartment()?->getName(),
                'hire_date' => $employee->getHireDate()?->format('Y-m-d'),
                'status' => $employee->getStatus()
            ];
        }

        return $this->json([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => count($data)
            ]
        ]);
    }

    #[Route('/invoices', name: 'invoices', methods: ['GET'])]
    public function invoices(Request $request, InvoiceRepository $invoiceRepository): JsonResponse
    {
        $page = $request->query->getInt('page', 1);
        $limit = min($request->query->getInt('limit', 20), 100);
        $status = $request->query->get('status');
        
        $qb = $invoiceRepository->createQueryBuilder('i')
            ->orderBy('i.createdAt', 'DESC')
            ->setFirstResult(($page - 1) * $limit)
            ->setMaxResults($limit);

        if ($status) {
            $qb->andWhere('i.status = :status')
               ->setParameter('status', $status);
        }

        $invoices = $qb->getQuery()->getResult();
        
        $data = [];
        foreach ($invoices as $invoice) {
            $data[] = [
                'id' => $invoice->getId(),
                'number' => $invoice->getNumber(),
                'status' => $invoice->getStatus(),
                'total_amount' => $invoice->getTotalAmount(),
                'due_date' => $invoice->getDueDate()?->format('Y-m-d'),
                'paid_at' => $invoice->getPaidAt()?->format('Y-m-d'),
                'created_at' => $invoice->getCreatedAt()->format('c'),
                'is_overdue' => $invoice->getDueDate() && $invoice->getDueDate() < new \DateTime() && $invoice->getStatus() !== 'paid'
            ];
        }

        return $this->json([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => count($data)
            ]
        ]);
    }

    #[Route('/notifications', name: 'notifications', methods: ['GET'])]
    public function notifications(Request $request, NotificationRepository $notificationRepository): JsonResponse
    {
        $user = $this->getUser();
        $page = $request->query->getInt('page', 1);
        $limit = min($request->query->getInt('limit', 20), 100);
        $unreadOnly = $request->query->getBoolean('unread_only', false);
        
        $qb = $notificationRepository->createQueryBuilder('n')
            ->where('n.userId = :userId')
            ->setParameter('userId', (string) $user->getId())
            ->orderBy('n.createdAt', 'DESC')
            ->setFirstResult(($page - 1) * $limit)
            ->setMaxResults($limit);

        if ($unreadOnly) {
            $qb->andWhere('n.isRead = false');
        }

        $notifications = $qb->getQuery()->getResult();
        
        $data = [];
        foreach ($notifications as $notification) {
            $data[] = [
                'id' => $notification->getId(),
                'type' => $notification->getType(),
                'title' => $notification->getTitle(),
                'content' => $notification->getContent(),
                'is_read' => $notification->isIsRead(),
                'created_at' => $notification->getCreatedAt()->format('c'),
                'link' => $notification->getLink()
            ];
        }

        return $this->json([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => count($data)
            ],
            'unread_count' => $this->notificationService->getUnreadCount($user)
        ]);
    }

    #[Route('/notifications/{id}/read', name: 'notification_read', methods: ['POST'])]
    public function markNotificationAsRead(int $id): JsonResponse
    {
        $user = $this->getUser();
        $success = $this->notificationService->markAsReadSimple($id, $user);

        if (!$success) {
            return $this->json([
                'success' => false,
                'message' => 'Notification non trouvée'
            ], 404);
        }

        return $this->json([
            'success' => true,
            'unread_count' => $this->notificationService->getUnreadCount($user)
        ]);
    }

    #[Route('/search', name: 'search', methods: ['GET'])]
    public function search(Request $request): JsonResponse
    {
        $query = $request->query->get('q');
        $type = $request->query->get('type', 'all'); // all, projects, employees, invoices
        $limit = min($request->query->getInt('limit', 10), 50);

        if (!$query || strlen($query) < 2) {
            return $this->json([
                'success' => false,
                'message' => 'La recherche doit contenir au moins 2 caractères'
            ], 400);
        }

        $results = [];

        // Recherche dans les projets
        if ($type === 'all' || $type === 'projects') {
            $projectRepo = $this->entityManager->getRepository(\App\Entity\Project::class);
            $projects = $projectRepo->createQueryBuilder('p')
                ->where('p.name LIKE :query OR p.description LIKE :query')
                ->setParameter('query', '%' . $query . '%')
                ->setMaxResults($limit)
                ->getQuery()
                ->getResult();

            foreach ($projects as $project) {
                $results[] = [
                    'type' => 'project',
                    'id' => $project->getId(),
                    'title' => $project->getName(),
                    'subtitle' => $project->getDescription(),
                    'status' => $project->getStatus()
                ];
            }
        }

        // Recherche dans les employés
        if ($type === 'all' || $type === 'employees') {
            $employeeRepo = $this->entityManager->getRepository(\App\Entity\Employee::class);
            $employees = $employeeRepo->createQueryBuilder('e')
                ->where('e.firstName LIKE :query OR e.lastName LIKE :query OR e.email LIKE :query')
                ->setParameter('query', '%' . $query . '%')
                ->setMaxResults($limit)
                ->getQuery()
                ->getResult();

            foreach ($employees as $employee) {
                $results[] = [
                    'type' => 'employee',
                    'id' => $employee->getId(),
                    'title' => $employee->getFirstName() . ' ' . $employee->getLastName(),
                    'subtitle' => $employee->getEmail(),
                    'department' => $employee->getDepartment()?->getName()
                ];
            }
        }

        return $this->json([
            'success' => true,
            'data' => $results,
            'query' => $query,
            'total' => count($results)
        ]);
    }

    #[Route('/sync', name: 'sync', methods: ['POST'])]
    public function sync(Request $request): JsonResponse
    {
        $user = $this->getUser();
        $lastSync = $request->request->get('last_sync');
        
        $syncData = [
            'timestamp' => (new \DateTime())->format('c'),
            'user_id' => $user->getId(),
            'changes' => []
        ];

        if ($lastSync) {
            $lastSyncDate = new \DateTime($lastSync);
            
            // Récupérer les changements depuis la dernière synchronisation
            // Ici vous pourriez implémenter une logique de synchronisation plus complexe
            
            $syncData['changes'] = [
                'projects' => $this->getChangedProjects($lastSyncDate),
                'notifications' => $this->getChangedNotifications($user, $lastSyncDate),
                'invoices' => $this->getChangedInvoices($lastSyncDate)
            ];
        }

        return $this->json([
            'success' => true,
            'data' => $syncData
        ]);
    }

    private function getChangedProjects(\DateTime $since): array
    {
        $projectRepo = $this->entityManager->getRepository(\App\Entity\Project::class);
        $projects = $projectRepo->createQueryBuilder('p')
            ->where('p.updatedAt > :since OR p.createdAt > :since')
            ->setParameter('since', $since)
            ->getQuery()
            ->getResult();

        $data = [];
        foreach ($projects as $project) {
            $data[] = [
                'id' => $project->getId(),
                'name' => $project->getName(),
                'status' => $project->getStatus(),
                'updated_at' => $project->getUpdatedAt()?->format('c')
            ];
        }

        return $data;
    }

    private function getChangedNotifications(User $user, \DateTime $since): array
    {
        $notificationRepo = $this->entityManager->getRepository(\App\Entity\Notification::class);
        $notifications = $notificationRepo->createQueryBuilder('n')
            ->where('n.userId = :userId')
            ->andWhere('n.createdAt > :since')
            ->setParameter('userId', (string) $user->getId())
            ->setParameter('since', $since)
            ->getQuery()
            ->getResult();

        $data = [];
        foreach ($notifications as $notification) {
            $data[] = [
                'id' => $notification->getId(),
                'title' => $notification->getTitle(),
                'type' => $notification->getType(),
                'is_read' => $notification->isIsRead(),
                'created_at' => $notification->getCreatedAt()->format('c')
            ];
        }

        return $data;
    }

    private function getChangedInvoices(\DateTime $since): array
    {
        $invoiceRepo = $this->entityManager->getRepository(\App\Entity\Invoice::class);
        $invoices = $invoiceRepo->createQueryBuilder('i')
            ->where('i.updatedAt > :since OR i.createdAt > :since')
            ->setParameter('since', $since)
            ->getQuery()
            ->getResult();

        $data = [];
        foreach ($invoices as $invoice) {
            $data[] = [
                'id' => $invoice->getId(),
                'number' => $invoice->getNumber(),
                'status' => $invoice->getStatus(),
                'total_amount' => $invoice->getTotalAmount(),
                'updated_at' => $invoice->getUpdatedAt()?->format('c')
            ];
        }

        return $data;
    }
}
