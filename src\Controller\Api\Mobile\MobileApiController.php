<?php

namespace App\Controller\Api\Mobile;

use App\Entity\User;
use App\Repository\UserRepository;
use App\Repository\ProjectRepository;
use App\Repository\PartnerRepository;
use App\Repository\EmployeeRepository;
use App\Service\MobileApiService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Psr\Log\LoggerInterface;

#[Route('/api/mobile')]
class MobileApiController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SerializerInterface $serializer,
        private ValidatorInterface $validator,
        private LoggerInterface $logger,
        private MobileApiService $mobileApiService
    ) {
    }
    
    #[Route('/status', name: 'app_mobile_api_status', methods: ['GET'])]
    public function status(): JsonResponse
    {
        return $this->json([
            'status' => 'ok',
            'version' => '1.0.0',
            'timestamp' => (new \DateTime())->format('Y-m-d\TH:i:s\Z'),
            'environment' => $this->getParameter('kernel.environment'),
        ]);
    }
    
    #[Route('/login', name: 'app_mobile_api_login', methods: ['POST'])]
    public function login(Request $request, UserRepository $userRepository): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        if (!isset($data['email']) || !isset($data['password'])) {
            return $this->json([
                'status' => 'error',
                'message' => 'Email et mot de passe requis',
            ], Response::HTTP_BAD_REQUEST);
        }
        
        try {
            $result = $this->mobileApiService->authenticateUser($data['email'], $data['password']);
            
            if (!$result['success']) {
                return $this->json([
                    'status' => 'error',
                    'message' => $result['message'],
                ], Response::HTTP_UNAUTHORIZED);
            }
            
            return $this->json([
                'status' => 'success',
                'token' => $result['token'],
                'refresh_token' => $result['refresh_token'],
                'expires_at' => $result['expires_at'],
                'user' => $result['user'],
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de la connexion mobile', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return $this->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la connexion',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    #[Route('/refresh-token', name: 'app_mobile_api_refresh_token', methods: ['POST'])]
    public function refreshToken(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        if (!isset($data['refresh_token'])) {
            return $this->json([
                'status' => 'error',
                'message' => 'Refresh token requis',
            ], Response::HTTP_BAD_REQUEST);
        }
        
        try {
            $result = $this->mobileApiService->refreshToken($data['refresh_token']);
            
            if (!$result['success']) {
                return $this->json([
                    'status' => 'error',
                    'message' => $result['message'],
                ], Response::HTTP_UNAUTHORIZED);
            }
            
            return $this->json([
                'status' => 'success',
                'token' => $result['token'],
                'refresh_token' => $result['refresh_token'],
                'expires_at' => $result['expires_at'],
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors du rafraîchissement du token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return $this->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors du rafraîchissement du token',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    #[Route('/logout', name: 'app_mobile_api_logout', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function logout(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        if (!isset($data['refresh_token'])) {
            return $this->json([
                'status' => 'error',
                'message' => 'Refresh token requis',
            ], Response::HTTP_BAD_REQUEST);
        }
        
        try {
            $result = $this->mobileApiService->invalidateToken($data['refresh_token']);
            
            return $this->json([
                'status' => 'success',
                'message' => 'Déconnexion réussie',
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de la déconnexion', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return $this->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la déconnexion',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    #[Route('/me', name: 'app_mobile_api_me', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function me(): JsonResponse
    {
        $user = $this->getUser();
        
        return $this->json([
            'id' => $user->getId(),
            'email' => $user->getEmail(),
            'roles' => $user->getRoles(),
            'firstName' => $user->getFirstName(),
            'lastName' => $user->getLastName(),
            'fullName' => $user->getFullName(),
            'permissions' => $this->mobileApiService->getUserPermissions($user),
        ]);
    }
    
    #[Route('/sync', name: 'app_mobile_api_sync', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function sync(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        if (!isset($data['last_sync'])) {
            return $this->json([
                'status' => 'error',
                'message' => 'Date de dernière synchronisation requise',
            ], Response::HTTP_BAD_REQUEST);
        }
        
        try {
            $lastSync = new \DateTime($data['last_sync']);
            $modules = $data['modules'] ?? null;
            
            $result = $this->mobileApiService->synchronizeData($this->getUser(), $lastSync, $modules);
            
            return $this->json([
                'status' => 'success',
                'data' => $result['data'],
                'sync_timestamp' => (new \DateTime())->format('Y-m-d\TH:i:s\Z'),
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de la synchronisation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return $this->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la synchronisation',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    #[Route('/projects', name: 'app_mobile_api_projects', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function getProjects(Request $request, ProjectRepository $projectRepository): JsonResponse
    {
        try {
            $page = $request->query->getInt('page', 1);
            $limit = $request->query->getInt('limit', 20);
            $status = $request->query->get('status');
            $search = $request->query->get('search');
            
            $result = $this->mobileApiService->getProjects($this->getUser(), $page, $limit, $status, $search);
            
            return $this->json($result);
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de la récupération des projets', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return $this->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la récupération des projets',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    #[Route('/partners', name: 'app_mobile_api_partners', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function getPartners(Request $request, PartnerRepository $partnerRepository): JsonResponse
    {
        try {
            $page = $request->query->getInt('page', 1);
            $limit = $request->query->getInt('limit', 20);
            $type = $request->query->get('type');
            $search = $request->query->get('search');
            
            $result = $this->mobileApiService->getPartners($this->getUser(), $page, $limit, $type, $search);
            
            return $this->json($result);
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de la récupération des partenaires', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return $this->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la récupération des partenaires',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    #[Route('/employees', name: 'app_mobile_api_employees', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function getEmployees(Request $request, EmployeeRepository $employeeRepository): JsonResponse
    {
        try {
            $page = $request->query->getInt('page', 1);
            $limit = $request->query->getInt('limit', 20);
            $department = $request->query->get('department');
            $search = $request->query->get('search');
            
            $result = $this->mobileApiService->getEmployees($this->getUser(), $page, $limit, $department, $search);
            
            return $this->json($result);
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de la récupération des employés', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return $this->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la récupération des employés',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    #[Route('/notifications', name: 'app_mobile_api_notifications', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function getNotifications(Request $request): JsonResponse
    {
        try {
            $page = $request->query->getInt('page', 1);
            $limit = $request->query->getInt('limit', 20);
            $unreadOnly = $request->query->getBoolean('unread_only', false);
            
            $result = $this->mobileApiService->getNotifications($this->getUser(), $page, $limit, $unreadOnly);
            
            return $this->json($result);
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de la récupération des notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return $this->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de la récupération des notifications',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
    
    #[Route('/device-register', name: 'app_mobile_api_device_register', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function registerDevice(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        
        if (!isset($data['device_token']) || !isset($data['device_type'])) {
            return $this->json([
                'status' => 'error',
                'message' => 'Token et type d\'appareil requis',
            ], Response::HTTP_BAD_REQUEST);
        }
        
        try {
            $result = $this->mobileApiService->registerDevice(
                $this->getUser(),
                $data['device_token'],
                $data['device_type'],
                $data['device_name'] ?? null
            );
            
            return $this->json([
                'status' => 'success',
                'message' => 'Appareil enregistré avec succès',
                'device_id' => $result['device_id'],
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de l\'enregistrement de l\'appareil', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return $this->json([
                'status' => 'error',
                'message' => 'Une erreur est survenue lors de l\'enregistrement de l\'appareil',
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
