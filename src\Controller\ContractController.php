<?php

namespace App\Controller;

use App\Entity\Contract;
use App\Entity\PurchaseRequest;
use App\Form\ContractForm;
use App\Repository\ContractRepository;
use App\Service\ContractService;
use App\Service\PurchaseRequestService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\String\Slugger\SluggerInterface;

#[Route('/contract')]
#[IsGranted('ROLE_USER')]
class ContractController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private ContractRepository $contractRepository;
    private ContractService $contractService;
    private PurchaseRequestService $purchaseRequestService;
    private Security $security;
    private SluggerInterface $slugger;

    public function __construct(
        EntityManagerInterface $entityManager,
        ContractRepository $contractRepository,
        ContractService $contractService,
        PurchaseRequestService $purchaseRequestService,
        Security $security,
        SluggerInterface $slugger
    ) {
        $this->entityManager = $entityManager;
        $this->contractRepository = $contractRepository;
        $this->contractService = $contractService;
        $this->purchaseRequestService = $purchaseRequestService;
        $this->security = $security;
        $this->slugger = $slugger;
    }

    #[Route('/', name: 'app_contract_index', methods: ['GET'])]
    public function index(): Response
    {
        $contracts = $this->contractRepository->findBy([], ['startDate' => 'DESC']);

        return $this->render('contract/index.html.twig', [
            'contracts' => $contracts,
        ]);
    }

    #[Route('/active', name: 'app_contract_active', methods: ['GET'])]
    public function active(): Response
    {
        $contracts = $this->contractRepository->findActiveContracts();

        return $this->render('contract/active.html.twig', [
            'contracts' => $contracts,
        ]);
    }

    #[Route('/expiring-soon', name: 'app_contract_expiring_soon', methods: ['GET'])]
    public function expiringSoon(): Response
    {
        $contracts = $this->contractRepository->findContractsExpiringSoon();

        return $this->render('contract/expiring_soon.html.twig', [
            'contracts' => $contracts,
        ]);
    }

    #[Route('/new', name: 'app_contract_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $contract = new Contract();
        $contract->setCreatedBy($this->security->getUser());

        $form = $this->createForm(ContractForm::class, $contract);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Handle document file upload
                $documentFile = $form->get('documentFile')->getData();
                if ($documentFile) {
                    $originalFilename = pathinfo($documentFile->getClientOriginalName(), PATHINFO_FILENAME);
                    $safeFilename = $this->slugger->slug($originalFilename);
                    $newFilename = $safeFilename.'-'.uniqid().'.'.$documentFile->guessExtension();

                    try {
                        $documentFile->move(
                            $this->getParameter('contract_documents_directory'),
                            $newFilename
                        );

                        $contract->setDocumentFilename($newFilename);
                    } catch (FileException $e) {
                        $this->addFlash('error', 'Erreur lors du téléchargement du document : ' . $e->getMessage());
                    }
                }

                $this->contractService->createContract($contract);

                $this->addFlash('success', 'Contrat créé avec succès.');

                return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du contrat : ' . $e->getMessage());
            }
        }

        return $this->render('contract/new.html.twig', [
            'contract' => $contract,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/new-from-request/{id}', name: 'app_contract_new_from_request', methods: ['GET', 'POST'])]
    public function newFromRequest(Request $request, PurchaseRequest $purchaseRequest): Response
    {
        // Check if the purchase request can be converted
        if (!$purchaseRequest->canBeConverted()) {
            $this->addFlash('error', 'Cette demande d\'achat ne peut pas être convertie en contrat dans son état actuel.');
            return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
        }

        try {
            $contract = $this->contractService->createFromPurchaseRequest($purchaseRequest);

            $form = $this->createForm(ContractForm::class, $contract);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                try {
                    // Handle document file upload
                    $documentFile = $form->get('documentFile')->getData();
                    if ($documentFile) {
                        $originalFilename = pathinfo($documentFile->getClientOriginalName(), PATHINFO_FILENAME);
                        $safeFilename = $this->slugger->slug($originalFilename);
                        $newFilename = $safeFilename.'-'.uniqid().'.'.$documentFile->guessExtension();

                        try {
                            $documentFile->move(
                                $this->getParameter('contract_documents_directory'),
                                $newFilename
                            );

                            $contract->setDocumentFilename($newFilename);
                        } catch (FileException $e) {
                            $this->addFlash('error', 'Erreur lors du téléchargement du document : ' . $e->getMessage());
                        }
                    }

                    $this->contractService->saveContract($contract);

                    // Mark the purchase request as converted
                    $this->purchaseRequestService->markAsConverted($purchaseRequest, 'contract', $contract->getId());

                    $this->addFlash('success', 'Contrat créé avec succès à partir de la demande d\'achat.');

                    return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
                } catch (\Exception $e) {
                    $this->addFlash('error', 'Erreur lors de la création du contrat : ' . $e->getMessage());
                }
            }

            return $this->render('contract/new_from_request.html.twig', [
                'contract' => $contract,
                'purchase_request' => $purchaseRequest,
                'form' => $form->createView(),
            ]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Erreur lors de la création du contrat : ' . $e->getMessage());
            return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
        }
    }

    #[Route('/{id}', name: 'app_contract_show', methods: ['GET'])]
    public function show(Contract $contract): Response
    {
        return $this->render('contract/show.html.twig', [
            'contract' => $contract,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_contract_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Contract $contract): Response
    {
        // Check if the contract can be edited
        if (!$contract->canBeEdited()) {
            $this->addFlash('error', 'Ce contrat ne peut pas être modifié dans son état actuel.');
            return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
        }

        $form = $this->createForm(ContractForm::class, $contract);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Handle document file upload
                $documentFile = $form->get('documentFile')->getData();
                if ($documentFile) {
                    $originalFilename = pathinfo($documentFile->getClientOriginalName(), PATHINFO_FILENAME);
                    $safeFilename = $this->slugger->slug($originalFilename);
                    $newFilename = $safeFilename.'-'.uniqid().'.'.$documentFile->guessExtension();

                    try {
                        $documentFile->move(
                            $this->getParameter('contract_documents_directory'),
                            $newFilename
                        );

                        // Delete old file if exists
                        if ($contract->getDocumentFilename()) {
                            $oldFilePath = $this->getParameter('contract_documents_directory').'/'.$contract->getDocumentFilename();
                            if (file_exists($oldFilePath)) {
                                unlink($oldFilePath);
                            }
                        }

                        $contract->setDocumentFilename($newFilename);
                    } catch (FileException $e) {
                        $this->addFlash('error', 'Erreur lors du téléchargement du document : ' . $e->getMessage());
                    }
                }

                $this->contractService->updateContract($contract);

                $this->addFlash('success', 'Contrat mis à jour avec succès.');

                return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour du contrat : ' . $e->getMessage());
            }
        }

        return $this->render('contract/edit.html.twig', [
            'contract' => $contract,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/sign', name: 'app_contract_sign', methods: ['POST'])]
    #[IsGranted('ROLE_MANAGER')]
    public function sign(Request $request, Contract $contract): Response
    {
        // Check if the contract can be signed
        if (!$contract->canBeSigned()) {
            $this->addFlash('error', 'Ce contrat ne peut pas être signé dans son état actuel.');
            return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
        }

        if ($this->isCsrfTokenValid('sign'.$contract->getId(), $request->request->get('_token'))) {
            try {
                $this->contractService->signContract($contract);

                $this->addFlash('success', 'Contrat signé avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la signature du contrat : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
    }

    #[Route('/{id}/send-for-signature', name: 'app_contract_send_for_signature', methods: ['POST'])]
    public function sendForSignature(Request $request, Contract $contract): Response
    {
        // Check if the contract can be sent for signature
        if (!$contract->canBeSentForSignature()) {
            $this->addFlash('error', 'Ce contrat ne peut pas être envoyé pour signature dans son état actuel.');
            return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
        }

        if ($this->isCsrfTokenValid('send_for_signature'.$contract->getId(), $request->request->get('_token'))) {
            try {
                $this->contractService->sendForSignature($contract);

                $this->addFlash('success', 'Contrat envoyé pour signature avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'envoi du contrat pour signature : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
    }

    #[Route('/{id}/cancel', name: 'app_contract_cancel', methods: ['POST'])]
    #[IsGranted('ROLE_MANAGER')]
    public function cancel(Request $request, Contract $contract): Response
    {
        // Check if the contract can be cancelled
        if (!$contract->canBeCancelled()) {
            $this->addFlash('error', 'Ce contrat ne peut pas être annulé dans son état actuel.');
            return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
        }

        if ($this->isCsrfTokenValid('cancel'.$contract->getId(), $request->request->get('_token'))) {
            try {
                $this->contractService->cancelContract($contract);

                $this->addFlash('success', 'Contrat annulé avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'annulation du contrat : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
    }

    #[Route('/{id}/complete', name: 'app_contract_complete', methods: ['POST'])]
    #[IsGranted('ROLE_MANAGER')]
    public function complete(Request $request, Contract $contract): Response
    {
        // Check if the contract can be completed
        if (!$contract->canBeCompleted()) {
            $this->addFlash('error', 'Ce contrat ne peut pas être marqué comme terminé dans son état actuel.');
            return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
        }

        if ($this->isCsrfTokenValid('complete'.$contract->getId(), $request->request->get('_token'))) {
            try {
                $this->contractService->completeContract($contract);

                $this->addFlash('success', 'Contrat marqué comme terminé avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors du marquage du contrat comme terminé : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
    }

    #[Route('/{id}/delete', name: 'app_contract_delete', methods: ['POST'])]
    public function delete(Request $request, Contract $contract): Response
    {
        // Only draft contracts can be deleted
        if ($contract->getStatus() !== Contract::STATUS_DRAFT) {
            $this->addFlash('error', 'Seuls les contrats en brouillon peuvent être supprimés.');
            return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
        }

        if ($this->isCsrfTokenValid('delete'.$contract->getId(), $request->request->get('_token'))) {
            try {
                $this->contractService->deleteContract($contract);

                $this->addFlash('success', 'Contrat supprimé avec succès.');

                return $this->redirectToRoute('app_contract_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression du contrat : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
    }


}
