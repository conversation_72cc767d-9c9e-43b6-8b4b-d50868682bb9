<?php

namespace App\Controller;

use App\Repository\PartnerRepository;
use App\Repository\MessageRepository;
use App\Repository\DelegationRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class DashboardController extends AbstractController
{
    #[Route('/dashboard', name: 'app_dashboard')]
    public function index(
        PartnerRepository $partnerRepository,
        MessageRepository $messageRepository,
        DelegationRepository $delegationRepository
    ): Response
    {
        // Get counts for dashboard widgets
        $partnerCount = count($partnerRepository->findAll());
        $messageCount = count($messageRepository->findAll());
        $pendingMessageCount = count($messageRepository->findBy(['status' => 'pending']));
        $activeDelegationCount = count($delegationRepository->findBy(['status' => 'active']));
        
        // Get recent partners
        $recentPartners = $partnerRepository->findBy([], ['createdAt' => 'DESC'], 5);
        
        // Get recent messages
        $recentMessages = $messageRepository->findBy([], ['createdAt' => 'DESC'], 5);
        
        return $this->render('dashboard/index.html.twig', [
            'partner_count' => $partnerCount,
            'message_count' => $messageCount,
            'pending_message_count' => $pendingMessageCount,
            'active_delegation_count' => $activeDelegationCount,
            'recent_partners' => $recentPartners,
            'recent_messages' => $recentMessages,
        ]);
    }
}
