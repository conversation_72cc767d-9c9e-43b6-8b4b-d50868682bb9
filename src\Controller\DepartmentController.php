<?php

namespace App\Controller;

use App\Entity\Department;
use App\Form\DepartmentTypeForm;
use App\Repository\DepartmentRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/department')]
#[IsGranted('ROLE_HR')]
class DepartmentController extends AbstractController
{
    #[Route('/', name: 'app_department_index', methods: ['GET'])]
    public function index(DepartmentRepository $departmentRepository): Response
    {
        $departments = $departmentRepository->findAll();
        
        return $this->render('department/index.html.twig', [
            'departments' => $departments,
        ]);
    }

    #[Route('/new', name: 'app_department_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $department = new Department();
        $department->setIsActive(true);
        $form = $this->createForm(DepartmentTypeForm::class, $department);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($department);
            $entityManager->flush();

            $this->addFlash('success', 'Le département a été créé avec succès.');
            return $this->redirectToRoute('app_department_index');
        }

        return $this->render('department/new.html.twig', [
            'department' => $department,
            'form' => $form,
        ]);
    }

    #[Route('/org_chart', name: 'app_department_org_chart', methods: ['GET'])]
    public function orgChart(DepartmentRepository $departmentRepository): Response
    {
        // Récupérer les départements racines (sans parent)
        $rootDepartments = $departmentRepository->findBy(['parentDepartment' => null]);
        
        return $this->render('department/org_chart.html.twig', [
            'rootDepartments' => $rootDepartments,
        ]);
    }

    #[Route('/{id}', name: 'app_department_show', methods: ['GET'])]
    public function show(Department $department): Response
    {
        return $this->render('department/show.html.twig', [
            'department' => $department,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_department_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Department $department, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(DepartmentTypeForm::class, $department);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'Le département a été modifié avec succès.');
            return $this->redirectToRoute('app_department_index');
        }

        return $this->render('department/edit.html.twig', [
            'department' => $department,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/delete', name: 'app_department_delete', methods: ['GET', 'POST'])]
    public function delete(Request $request, Department $department, EntityManagerInterface $entityManager): Response
    {
        // Vérifier si le département a des sous-départements
        if (!$department->getChildDepartments()->isEmpty()) {
            $this->addFlash('error', 'Impossible de supprimer ce département car il contient des sous-départements.');
            return $this->redirectToRoute('app_department_show', ['id' => $department->getId()]);
        }

        // Vérifier si le département a des postes
        if (!$department->getPositions()->isEmpty()) {
            $this->addFlash('error', 'Impossible de supprimer ce département car il contient des postes.');
            return $this->redirectToRoute('app_department_show', ['id' => $department->getId()]);
        }

        // Vérifier si le département a des employés
        // Cette vérification nécessite une requête personnalisée dans le repository
        
        if ($request->isMethod('POST')) {
            $entityManager->remove($department);
            $entityManager->flush();

            $this->addFlash('success', 'Le département a été supprimé avec succès.');
            return $this->redirectToRoute('app_department_index');
        }

        return $this->render('department/delete.html.twig', [
            'department' => $department,
        ]);
    }

    #[Route('/{id}/toggle-status', name: 'app_department_toggle_status', methods: ['GET'])]
    public function toggleStatus(Department $department, EntityManagerInterface $entityManager): Response
    {
        $department->setIsActive(!$department->isIsActive());
        $entityManager->flush();

        $status = $department->isIsActive() ? 'activé' : 'désactivé';
        $this->addFlash('success', "Le département a été $status avec succès.");
        
        return $this->redirectToRoute('app_department_show', ['id' => $department->getId()]);
    }
}
