<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/documentation')]
class DocumentationController extends AbstractController
{
    #[Route('/', name: 'app_documentation_index')]
    public function index(): Response
    {
        return $this->render('documentation/index.html.twig');
    }
    
    #[Route('/two-factor-auth', name: 'app_documentation_two_factor_auth')]
    public function twoFactorAuth(): Response
    {
        return $this->render('documentation/two_factor_auth.html.twig');
    }
    
    #[Route('/integrations', name: 'app_documentation_integrations')]
    public function integrations(): Response
    {
        return $this->render('documentation/integrations.html.twig');
    }
    
    #[Route('/internationalization', name: 'app_documentation_internationalization')]
    public function internationalization(): Response
    {
        return $this->render('documentation/internationalization.html.twig');
    }
    
    #[Route('/api', name: 'app_documentation_api')]
    public function api(): Response
    {
        return $this->render('documentation/api.html.twig');
    }
    
    #[Route('/user-guide', name: 'app_documentation_user_guide')]
    public function userGuide(): Response
    {
        return $this->render('documentation/user_guide.html.twig');
    }
    
    #[Route('/admin-guide', name: 'app_documentation_admin_guide')]
    public function adminGuide(): Response
    {
        return $this->render('documentation/admin_guide.html.twig');
    }
    
    #[Route('/developer-guide', name: 'app_documentation_developer_guide')]
    public function developerGuide(): Response
    {
        return $this->render('documentation/developer_guide.html.twig');
    }
}
