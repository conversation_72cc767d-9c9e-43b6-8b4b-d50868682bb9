<?php

namespace App\Controller;

use App\Entity\Employee;
use App\Form\EmployeeEditForm;
use App\Form\EmployeeForm;
use App\Service\EmployeeService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/employee')]
#[IsGranted('ROLE_HR')]
class EmployeeController extends AbstractController
{
    private EmployeeService $employeeService;

    public function __construct(EmployeeService $employeeService)
    {
        $this->employeeService = $employeeService;
    }

    #[Route('/', name: 'app_employee_index', methods: ['GET'])]
    public function index(): Response
    {
        $dashboardData = $this->employeeService->getEmployeeDashboardData();

        return $this->render('employee/index.html.twig', [
            'dashboard_data' => $dashboardData,
        ]);
    }

    #[Route('/list', name: 'app_employee_list', methods: ['GET'])]
    public function list(): Response
    {
        $employees = $this->employeeService->getEmployeeRepository()->findAll();

        return $this->render('employee/list.html.twig', [
            'employees' => $employees,
        ]);
    }

    #[Route('/new', name: 'app_employee_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $employee = new Employee();
        $form = $this->createForm(EmployeeForm::class, $employee);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->employeeService->createEmployee($employee);

            $this->addFlash('success', 'Employé créé avec succès.');

            return $this->redirectToRoute('app_employee_show', ['id' => $employee->getId()]);
        }

        return $this->render('employee/new.html.twig', [
            'employee' => $employee,
            'form' => $form,
        ]);
    }

    #[Route('/org-chart', name: 'app_employee_org_chart', methods: ['GET'])]
    public function orgChart(): Response
    {
        $employees = $this->employeeService->getEmployeeRepository()->findAll();

        // Organize employees by manager
        $employeesByManager = [];
        $topLevelEmployees = [];

        foreach ($employees as $employee) {
            if ($employee->getManager()) {
                $managerId = $employee->getManager()->getId();
                if (!isset($employeesByManager[$managerId])) {
                    $employeesByManager[$managerId] = [];
                }
                $employeesByManager[$managerId][] = $employee;
            } else {
                $topLevelEmployees[] = $employee;
            }
        }

        return $this->render('employee/org_chart.html.twig', [
            'top_level_employees' => $topLevelEmployees,
            'employees_by_manager' => $employeesByManager,
        ]);
    }

    #[Route('/department/{department}', name: 'app_employee_by_department', methods: ['GET'])]
    public function byDepartment(string $department): Response
    {
        $employees = $this->employeeService->getEmployeesByDepartment($department);

        return $this->render('employee/by_department.html.twig', [
            'employees' => $employees,
            'department' => $department,
        ]);
    }

    #[Route('/search', name: 'app_employee_search', methods: ['GET'])]
    public function search(Request $request): Response
    {
        $keyword = $request->query->get('keyword');

        if (!$keyword) {
            return $this->redirectToRoute('app_employee_list');
        }

        $employees = $this->employeeService->searchEmployees($keyword);

        return $this->render('employee/search.html.twig', [
            'employees' => $employees,
            'keyword' => $keyword,
        ]);
    }

    #[Route('/{id}', name: 'app_employee_show', methods: ['GET'])]
    public function show(Employee $employee): Response
    {
        $employeeDetails = $this->employeeService->getEmployeeDetails($employee);

        return $this->render('employee/show.html.twig', [
            'employee_details' => $employeeDetails,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_employee_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Employee $employee): Response
    {
        $form = $this->createForm(EmployeeEditForm::class, $employee);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->employeeService->updateEmployee($employee);

            $this->addFlash('success', 'Employé mis à jour avec succès.');

            return $this->redirectToRoute('app_employee_show', ['id' => $employee->getId()]);
        }

        return $this->render('employee/edit.html.twig', [
            'employee' => $employee,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/delete', name: 'app_employee_delete', methods: ['POST'])]
    #[IsGranted('ROLE_ADMIN')]
    public function delete(Request $request, Employee $employee): Response
    {
        if ($this->isCsrfTokenValid('delete'.$employee->getId(), $request->request->get('_token'))) {
            $this->employeeService->deleteEmployee($employee);

            $this->addFlash('success', 'Employé supprimé avec succès.');
        }

        return $this->redirectToRoute('app_employee_list');
    }

    #[Route('/{id}/change-status/{status}', name: 'app_employee_change_status', methods: ['GET'])]
    public function changeStatus(Employee $employee, string $status): Response
    {
        $allowedStatuses = ['active', 'on_leave', 'terminated', 'suspended'];

        if (!in_array($status, $allowedStatuses)) {
            $this->addFlash('error', 'Statut non valide.');
            return $this->redirectToRoute('app_employee_show', ['id' => $employee->getId()]);
        }

        $this->employeeService->changeStatus($employee, $status);

        $this->addFlash('success', 'Statut de l\'employé mis à jour avec succès.');

        return $this->redirectToRoute('app_employee_show', ['id' => $employee->getId()]);
    }
}
