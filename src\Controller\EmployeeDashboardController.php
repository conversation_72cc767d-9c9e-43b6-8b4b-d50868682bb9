<?php

namespace App\Controller;

use App\Form\EmployeeSearchForm;
use App\Service\EmployeeSearchService;
use App\Service\EmployeeService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/dashboard')]
#[IsGranted('ROLE_HR')]
class EmployeeDashboardController extends AbstractController
{
    private EmployeeSearchService $employeeSearchService;
    private EmployeeService $employeeService;

    public function __construct(
        EmployeeSearchService $employeeSearchService,
        EmployeeService $employeeService
    ) {
        $this->employeeSearchService = $employeeSearchService;
        $this->employeeService = $employeeService;
    }

    #[Route('/', name: 'app_employee_dashboard')]
    public function index(): Response
    {
        // Get employee statistics
        $statistics = $this->employeeSearchService->getEmployeeStatistics();

        return $this->render('employee_dashboard/index.html.twig', [
            'statistics' => $statistics
        ]);
    }

    #[Route('/search', name: 'app_employee_advanced_search')]
    public function search(Request $request): Response
    {
        $form = $this->createForm(EmployeeSearchForm::class);
        $form->handleRequest($request);

        $page = $request->query->getInt('page', 1);
        $limit = $request->query->getInt('limit', 10);

        $criteria = $form->getData() ?: [];
        
        // Always call the search service, it will handle empty criteria internally
        $results = $this->employeeSearchService->searchEmployees($criteria, $page, $limit);
        
        // If no results and no criteria, set results to null for template
        if ($results['total'] === 0 && !$this->hasSearchCriteria($criteria) && !$form->isSubmitted()) {
            $results = null;
        }

        return $this->render('employee_dashboard/search.html.twig', [
            'form' => $form,
            'results' => $results,
            'criteria' => $criteria
        ]);
    }

    /**
     * Check if there are any search criteria provided
     */
    private function hasSearchCriteria(array $criteria): bool
    {
        foreach ($criteria as $key => $value) {
            if (!empty($value) && $key !== 'sort_field' && $key !== 'sort_direction') {
                return true;
            }
        }
        return false;
    }

    #[Route('/export', name: 'app_employee_export')]
    public function export(Request $request): Response
    {
        // This is a placeholder for an export feature
        // You can implement CSV or Excel export here

        $form = $this->createForm(EmployeeSearchForm::class);
        $form->handleRequest($request);

        $criteria = $form->getData() ?: [];

        // Get all results without pagination
        $results = $this->employeeSearchService->searchEmployees($criteria, 1, 1000);

        // Return a simple CSV for now
        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="employees.csv"');

        $output = fopen('php://output', 'w');

        // Add headers
        fputcsv($output, ['ID', 'Numéro d\'employé', 'Prénom', 'Nom', 'Email', 'Poste', 'Département', 'Statut', 'Type', 'Date d\'embauche', 'Salaire']);

        // Add data
        foreach ($results['employees'] as $employee) {
            fputcsv($output, [
                $employee->getId(),
                $employee->getEmployeeNumber(),
                $employee->getUser()->getFirstName(),
                $employee->getUser()->getLastName(),
                $employee->getUser()->getEmail(),
                $employee->getPosition() ? $employee->getPosition()->getTitle() : 'Non spécifié',
                $employee->getDepartment() ? $employee->getDepartment()->getName() : 'Non spécifié',
                $employee->getEmploymentStatusLabel(),
                $employee->getEmploymentTypeLabel(),
                $employee->getHireDate()->format('Y-m-d'),
                $employee->getFormattedSalary()
            ]);
        }

        $response->setContent(ob_get_clean());

        return $response;
    }
}
