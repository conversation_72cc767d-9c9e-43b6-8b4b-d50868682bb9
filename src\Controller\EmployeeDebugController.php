<?php

namespace App\Controller;

use App\Service\EmployeeService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\VarDumper\VarDumper;

#[Route('/hr/employee/debug')]
class EmployeeDebugController extends AbstractController
{
    private EmployeeService $employeeService;

    public function __construct(EmployeeService $employeeService)
    {
        $this->employeeService = $employeeService;
    }

    #[Route('/', name: 'app_employee_debug')]
    public function debug(): Response
    {
        $dashboardData = $this->employeeService->getEmployeeDashboardData();
        
        return $this->render('employee/debug.html.twig', [
            'dashboard_data' => $dashboardData,
        ]);
    }
}
