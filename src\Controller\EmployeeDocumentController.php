<?php

namespace App\Controller;

use App\Entity\Employee;
use App\Entity\EmployeeDocument;
use App\Form\EmployeeDocumentForm;
use App\Service\EmployeeDocumentService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/employee/{employeeId}/document')]
#[IsGranted('ROLE_HR')]
class EmployeeDocumentController extends AbstractController
{
    private EmployeeDocumentService $documentService;

    public function __construct(EmployeeDocumentService $documentService)
    {
        $this->documentService = $documentService;
    }

    #[Route('/', name: 'app_employee_document_index', methods: ['GET'])]
    public function index(Employee $employeeId): Response
    {
        $documents = $this->documentService->getDocumentsByEmployee($employeeId);

        // Group documents by type
        $documentsByType = [];
        foreach ($documents as $document) {
            $type = $document->getDocumentType();
            if (!isset($documentsByType[$type])) {
                $documentsByType[$type] = [];
            }
            $documentsByType[$type][] = $document;
        }

        return $this->render('employee_document/index.html.twig', [
            'employee' => $employeeId,
            'documents_by_type' => $documentsByType,
            'can_access_confidential' => $this->documentService->canAccessConfidentialDocuments(),
        ]);
    }

    #[Route('/new', name: 'app_employee_document_new', methods: ['GET', 'POST'])]
    public function new(Request $request, int $employeeId): Response
    {
        // Récupérer l'employé manuellement
        $employee = $this->documentService->getEmployeeRepository()->find($employeeId);

        if (!$employee) {
            throw $this->createNotFoundException('Employé non trouvé');
        }

        $document = new EmployeeDocument();
        $document->setEmployee($employee);

        $form = $this->createForm(EmployeeDocumentForm::class, $document);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $file = $form->get('file')->getData();

            if ($file) {
                $this->documentService->uploadDocument(
                    $employee,
                    $file,
                    $document->getTitle(),
                    $document->getDocumentType(),
                    $document->getNotes(),
                    $document->isIsConfidential(),
                    $document->getIssueDate(),
                    $document->getExpirationDate()
                );

                $this->addFlash('success', 'Document ajouté avec succès.');

                return $this->redirectToRoute('app_employee_document_index', ['employeeId' => $employee->getId()]);
            }
        }

        return $this->render('employee_document/new.html.twig', [
            'employee' => $employee,
            'form' => $form,
        ]);
    }

    #[Route('/type/{type}', name: 'app_employee_document_by_type', methods: ['GET'])]
    public function byType(Employee $employeeId, string $type): Response
    {
        $documents = $this->documentService->getDocumentsByEmployeeAndType($employeeId, $type);

        return $this->render('employee_document/by_type.html.twig', [
            'employee' => $employeeId,
            'documents' => $documents,
            'type' => $type,
            'can_access_confidential' => $this->documentService->canAccessConfidentialDocuments(),
        ]);
    }

    #[Route('/{id}/edit', name: 'app_employee_document_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Employee $employeeId, EmployeeDocument $id): Response
    {
        // Security check to ensure the document belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Ce document n\'appartient pas à cet employé.');
        }

        // Check if user can access confidential documents
        if ($id->isIsConfidential() && !$this->documentService->canAccessConfidentialDocuments()) {
            throw $this->createAccessDeniedException('Vous n\'avez pas accès à ce document confidentiel.');
        }

        $form = $this->createForm(EmployeeDocumentForm::class, $id, [
            'include_file' => false,
        ]);
        $form->remove('file'); // Remove file field for edit form
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->documentService->updateDocument($id);

            $this->addFlash('success', 'Document mis à jour avec succès.');

            return $this->redirectToRoute('app_employee_document_index', ['employeeId' => $employeeId->getId()]);
        }

        return $this->render('employee_document/edit.html.twig', [
            'employee' => $employeeId,
            'document' => $id,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/delete', name: 'app_employee_document_delete', methods: ['POST'])]
    public function delete(Request $request, Employee $employeeId, EmployeeDocument $id): Response
    {
        // Security check to ensure the document belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Ce document n\'appartient pas à cet employé.');
        }

        if ($this->isCsrfTokenValid('delete'.$id->getId(), $request->request->get('_token'))) {
            $this->documentService->deleteDocument($id);

            $this->addFlash('success', 'Document supprimé avec succès.');
        }

        return $this->redirectToRoute('app_employee_document_index', ['employeeId' => $employeeId->getId()]);
    }

    #[Route('/{id}/download', name: 'app_employee_document_download', methods: ['GET'])]
    public function download(Employee $employeeId, EmployeeDocument $id): Response
    {
        // Security check to ensure the document belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Ce document n\'appartient pas à cet employé.');
        }

        // Check if user can access confidential documents
        if ($id->isIsConfidential() && !$this->documentService->canAccessConfidentialDocuments()) {
            throw $this->createAccessDeniedException('Vous n\'avez pas accès à ce document confidentiel.');
        }

        $filePath = $this->documentService->getDocumentPath($id);

        if (!file_exists($filePath)) {
            throw $this->createNotFoundException('Le fichier n\'existe pas.');
        }

        $response = new BinaryFileResponse($filePath);
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $id->getOriginalFilename() ?? $id->getFilename()
        );

        return $response;
    }
}
