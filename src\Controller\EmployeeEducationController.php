<?php

namespace App\Controller;

use App\Entity\Employee;
use App\Entity\EmployeeEducation;
use App\Form\EmployeeEducationForm;
use App\Repository\EmployeeEducationRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/employee/{employeeId}/education')]
#[IsGranted('ROLE_HR')]
class EmployeeEducationController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private EmployeeEducationRepository $educationRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        EmployeeEducationRepository $educationRepository
    ) {
        $this->entityManager = $entityManager;
        $this->educationRepository = $educationRepository;
    }

    #[Route('/', name: 'app_employee_education_index', methods: ['GET'])]
    public function index(Employee $employeeId): Response
    {
        $educations = $this->educationRepository->findByEmployee($employeeId);

        // Sort educations by start date (most recent first)
        usort($educations, function($a, $b) {
            return $b->getStartDate() <=> $a->getStartDate();
        });

        return $this->render('employee_education/index.html.twig', [
            'employee' => $employeeId,
            'educations' => $educations,
        ]);
    }

    #[Route('/new', name: 'app_employee_education_new', methods: ['GET', 'POST'])]
    public function new(Request $request, int $employeeId): Response
    {
        // Récupérer l'employé manuellement
        $employee = $this->entityManager->getRepository(Employee::class)->find($employeeId);

        if (!$employee) {
            throw $this->createNotFoundException('Employé non trouvé');
        }

        $education = new EmployeeEducation();
        $education->setEmployee($employee);

        $form = $this->createForm(EmployeeEducationForm::class, $education);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($education);
            $this->entityManager->flush();

            $this->addFlash('success', 'Formation ajoutée avec succès.');

            return $this->redirectToRoute('app_employee_education_index', ['employeeId' => $employee->getId()]);
        }

        return $this->render('employee_education/new.html.twig', [
            'employee' => $employee,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/toggle-completion', name: 'app_employee_education_toggle_completion', methods: ['GET'])]
    public function toggleCompletion(Employee $employeeId, EmployeeEducation $id): Response
    {
        // Security check to ensure the education belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette formation n\'appartient pas à cet employé.');
        }

        $id->setIsCompleted(!$id->isIsCompleted());

        // If marked as completed and no end date, set end date to today
        if ($id->isIsCompleted() && !$id->getEndDate()) {
            $id->setEndDate(new \DateTime());
        }

        $this->entityManager->flush();

        $this->addFlash('success', 'Statut de la formation mis à jour avec succès.');

        return $this->redirectToRoute('app_employee_education_index', ['employeeId' => $employeeId->getId()]);
    }

    #[Route('/{id}/edit', name: 'app_employee_education_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Employee $employeeId, EmployeeEducation $id): Response
    {
        // Security check to ensure the education belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette formation n\'appartient pas à cet employé.');
        }

        $form = $this->createForm(EmployeeEducationForm::class, $id);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();

            $this->addFlash('success', 'Formation mise à jour avec succès.');

            return $this->redirectToRoute('app_employee_education_index', ['employeeId' => $employeeId->getId()]);
        }

        return $this->render('employee_education/edit.html.twig', [
            'employee' => $employeeId,
            'education' => $id,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/delete', name: 'app_employee_education_delete', methods: ['POST'])]
    public function delete(Request $request, Employee $employeeId, EmployeeEducation $id): Response
    {
        // Security check to ensure the education belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette formation n\'appartient pas à cet employé.');
        }

        if ($this->isCsrfTokenValid('delete'.$id->getId(), $request->request->get('_token'))) {
            $this->entityManager->remove($id);
            $this->entityManager->flush();

            $this->addFlash('success', 'Formation supprimée avec succès.');
        }

        return $this->redirectToRoute('app_employee_education_index', ['employeeId' => $employeeId->getId()]);
    }
}
