<?php

namespace App\Controller;

use App\Entity\Employee;
use App\Entity\EmployeeLeave;
use App\Form\EmployeeLeaveForm;
use App\Service\EmployeeLeaveService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/employee/{employeeId}/leave')]
#[IsGranted('ROLE_HR')]
class EmployeeLeaveController extends AbstractController
{
    private EmployeeLeaveService $leaveService;

    public function __construct(EmployeeLeaveService $leaveService)
    {
        $this->leaveService = $leaveService;
    }

    #[Route('/', name: 'app_employee_leave_index', methods: ['GET'])]
    public function index(Employee $employeeId): Response
    {
        $leaves = $this->leaveService->getLeavesByEmployee($employeeId);

        // Group leaves by status
        $leavesByStatus = [
            'pending' => [],
            'approved' => [],
            'rejected' => [],
            'cancelled' => [],
        ];

        foreach ($leaves as $leave) {
            $status = $leave->getStatus();
            if (!isset($leavesByStatus[$status])) {
                $leavesByStatus[$status] = [];
            }
            $leavesByStatus[$status][] = $leave;
        }

        // Get leave balance
        $leaveBalance = $this->leaveService->getLeaveBalance($employeeId);

        // Get upcoming leaves
        $upcomingLeaves = [];
        $today = new \DateTime();
        foreach ($leaves as $leave) {
            if ($leave->getStatus() === 'approved' && $leave->getStartDate() > $today) {
                $upcomingLeaves[] = $leave;
            }
        }

        // Sort upcoming leaves by start date
        usort($upcomingLeaves, function($a, $b) {
            return $a->getStartDate() <=> $b->getStartDate();
        });

        // Limit to 5 upcoming leaves
        $upcomingLeaves = array_slice($upcomingLeaves, 0, 5);

        return $this->render('employee_leave/index.html.twig', [
            'employee' => $employeeId,
            'leaves' => $leaves,
            'leaves_by_status' => $leavesByStatus,
            'leave_balance' => $leaveBalance,
            'upcoming_leaves' => $upcomingLeaves,
            'can_approve' => $this->leaveService->canApproveLeaves(),
        ]);
    }

    #[Route('/new', name: 'app_employee_leave_new', methods: ['GET', 'POST'])]
    public function new(Request $request, int $employeeId): Response
    {
        // Récupérer l'employé manuellement
        $employee = $this->leaveService->getEmployeeRepository()->find($employeeId);

        if (!$employee) {
            throw $this->createNotFoundException('Employé non trouvé');
        }

        $leave = new EmployeeLeave();
        $leave->setEmployee($employee);
        $leave->setStatus('pending');

        $form = $this->createForm(EmployeeLeaveForm::class, $leave);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->leaveService->requestLeave($leave);

            $this->addFlash('success', 'Demande de congé créée avec succès.');

            return $this->redirectToRoute('app_employee_leave_index', ['employeeId' => $employee->getId()]);
        }

        return $this->render('employee_leave/new.html.twig', [
            'employee' => $employee,
            'form' => $form,
        ]);
    }

    #[Route('/calendar', name: 'app_employee_leave_calendar', methods: ['GET'])]
    public function calendar(Employee $employeeId): Response
    {
        $leaves = $this->leaveService->getLeavesByEmployee($employeeId);

        // Format leaves for calendar
        $calendarLeaves = [];
        foreach ($leaves as $leave) {
            if ($leave->getStatus() === 'approved') {
                $calendarLeaves[] = [
                    'id' => $leave->getId(),
                    'title' => $leave->getLeaveTypeLabel(),
                    'start' => $leave->getStartDate()->format('Y-m-d'),
                    'end' => $leave->getEndDate()->modify('+1 day')->format('Y-m-d'), // End date is exclusive in FullCalendar
                    'color' => $this->getLeaveColor($leave->getLeaveType()),
                ];
            }
        }

        return $this->render('employee_leave/calendar.html.twig', [
            'employee' => $employeeId,
            'calendar_leaves' => json_encode($calendarLeaves),
        ]);
    }

    #[Route('/{id}/edit', name: 'app_employee_leave_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Employee $employeeId, EmployeeLeave $id): Response
    {
        // Security check to ensure the leave belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette demande de congé n\'appartient pas à cet employé.');
        }

        // Only pending leaves can be edited
        if ($id->getStatus() !== 'pending') {
            $this->addFlash('error', 'Seules les demandes en attente peuvent être modifiées.');
            return $this->redirectToRoute('app_employee_leave_index', ['employeeId' => $employeeId->getId()]);
        }

        $form = $this->createForm(EmployeeLeaveForm::class, $id);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Recalculate duration
            $id->setDuration($id->calculateDuration());

            $this->leaveService->requestLeave($id);

            $this->addFlash('success', 'Demande de congé mise à jour avec succès.');

            return $this->redirectToRoute('app_employee_leave_index', ['employeeId' => $employeeId->getId()]);
        }

        return $this->render('employee_leave/edit.html.twig', [
            'employee' => $employeeId,
            'leave' => $id,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/approve', name: 'app_employee_leave_approve', methods: ['GET'])]
    public function approve(Employee $employeeId, EmployeeLeave $id): Response
    {
        // Security check to ensure the leave belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette demande de congé n\'appartient pas à cet employé.');
        }

        // Check if user can approve leaves
        if (!$this->leaveService->canApproveLeaves()) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour approuver des congés.');
        }

        // Only pending leaves can be approved
        if ($id->getStatus() !== 'pending') {
            $this->addFlash('error', 'Seules les demandes en attente peuvent être approuvées.');
            return $this->redirectToRoute('app_employee_leave_index', ['employeeId' => $employeeId->getId()]);
        }

        $this->leaveService->approveLeave($id, $this->getUser());

        $this->addFlash('success', 'Demande de congé approuvée avec succès.');

        return $this->redirectToRoute('app_employee_leave_index', ['employeeId' => $employeeId->getId()]);
    }

    #[Route('/{id}/reject', name: 'app_employee_leave_reject', methods: ['GET', 'POST'])]
    public function reject(Request $request, Employee $employeeId, EmployeeLeave $id): Response
    {
        // Security check to ensure the leave belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette demande de congé n\'appartient pas à cet employé.');
        }

        // Check if user can approve leaves
        if (!$this->leaveService->canApproveLeaves()) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour rejeter des congés.');
        }

        // Only pending leaves can be rejected
        if ($id->getStatus() !== 'pending') {
            $this->addFlash('error', 'Seules les demandes en attente peuvent être rejetées.');
            return $this->redirectToRoute('app_employee_leave_index', ['employeeId' => $employeeId->getId()]);
        }

        if ($request->isMethod('POST')) {
            $comments = $request->request->get('comments');

            $this->leaveService->rejectLeave($id, $this->getUser(), $comments);

            $this->addFlash('success', 'Demande de congé rejetée avec succès.');

            return $this->redirectToRoute('app_employee_leave_index', ['employeeId' => $employeeId->getId()]);
        }

        return $this->render('employee_leave/reject.html.twig', [
            'employee' => $employeeId,
            'leave' => $id,
        ]);
    }

    #[Route('/{id}/cancel', name: 'app_employee_leave_cancel', methods: ['GET'])]
    public function cancel(Employee $employeeId, EmployeeLeave $id): Response
    {
        // Security check to ensure the leave belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette demande de congé n\'appartient pas à cet employé.');
        }

        // Only pending or approved leaves can be cancelled
        if ($id->getStatus() !== 'pending' && $id->getStatus() !== 'approved') {
            $this->addFlash('error', 'Seules les demandes en attente ou approuvées peuvent être annulées.');
            return $this->redirectToRoute('app_employee_leave_index', ['employeeId' => $employeeId->getId()]);
        }

        $this->leaveService->cancelLeave($id);

        $this->addFlash('success', 'Demande de congé annulée avec succès.');

        return $this->redirectToRoute('app_employee_leave_index', ['employeeId' => $employeeId->getId()]);
    }

    /**
     * Get color for leave type
     */
    private function getLeaveColor(string $leaveType): string
    {
        return match($leaveType) {
            'vacation' => '#4CAF50', // Green
            'sick' => '#F44336', // Red
            'personal' => '#2196F3', // Blue
            'maternity' => '#9C27B0', // Purple
            'paternity' => '#673AB7', // Deep Purple
            'unpaid' => '#FF9800', // Orange
            default => '#607D8B', // Blue Grey
        };
    }
}
