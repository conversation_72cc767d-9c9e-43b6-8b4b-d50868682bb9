<?php

namespace App\Controller;

use App\Entity\Employee;
use App\Entity\EmployeePerformance;
use App\Form\EmployeePerformanceForm;
use App\Service\EmployeePerformanceService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/employee/{employeeId}/performance')]
#[IsGranted('ROLE_HR')]
class EmployeePerformanceController extends AbstractController
{
    private EmployeePerformanceService $performanceService;

    public function __construct(EmployeePerformanceService $performanceService)
    {
        $this->performanceService = $performanceService;
    }

    #[Route('/', name: 'app_employee_performance_index', methods: ['GET'])]
    public function index(Employee $employeeId): Response
    {
        // Check if user can view performance reviews
        if (!$this->performanceService->canViewPerformanceReviews($employeeId)) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour voir les évaluations de cet employé.');
        }

        $performances = $this->performanceService->getPerformanceReviewsByEmployee($employeeId);

        // Sort performances by review date (most recent first)
        usort($performances, function($a, $b) {
            return $b->getReviewDate() <=> $a->getReviewDate();
        });

        return $this->render('employee_performance/index.html.twig', [
            'employee' => $employeeId,
            'performances' => $performances,
            'can_create' => $this->performanceService->canCreatePerformanceReviews(),
        ]);
    }

    #[Route('/new', name: 'app_employee_performance_new', methods: ['GET', 'POST'])]
    public function new(Request $request, int $employeeId): Response
    {
        // Récupérer l'employé manuellement
        $employee = $this->performanceService->getEmployeeRepository()->find($employeeId);

        if (!$employee) {
            throw $this->createNotFoundException('Employé non trouvé');
        }

        // Check if user can create performance reviews
        if (!$this->performanceService->canCreatePerformanceReviews()) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour créer des évaluations.');
        }

        $performance = new EmployeePerformance();
        $performance->setEmployee($employee);
        $performance->setReviewedBy($this->getUser());
        $performance->setIsAcknowledged(false);

        $form = $this->createForm(EmployeePerformanceForm::class, $performance);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->performanceService->createPerformanceReview($performance);

            $this->addFlash('success', 'Évaluation créée avec succès.');

            return $this->redirectToRoute('app_employee_performance_index', ['employeeId' => $employee->getId()]);
        }

        return $this->render('employee_performance/new.html.twig', [
            'employee' => $employee,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_employee_performance_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Employee $employeeId, EmployeePerformance $id): Response
    {
        // Security check to ensure the performance belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette évaluation n\'appartient pas à cet employé.');
        }

        // Check if user can create performance reviews
        if (!$this->performanceService->canCreatePerformanceReviews()) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour modifier des évaluations.');
        }

        // Only the reviewer or HR/admin can edit
        if ($id->getReviewedBy()->getId() !== $this->getUser()->getId() && !$this->isGranted('ROLE_HR') && !$this->isGranted('ROLE_ADMIN')) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour modifier cette évaluation.');
        }

        $form = $this->createForm(EmployeePerformanceForm::class, $id);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->performanceService->updatePerformanceReview($id);

            $this->addFlash('success', 'Évaluation mise à jour avec succès.');

            return $this->redirectToRoute('app_employee_performance_index', ['employeeId' => $employeeId->getId()]);
        }

        return $this->render('employee_performance/edit.html.twig', [
            'employee' => $employeeId,
            'performance' => $id,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/delete', name: 'app_employee_performance_delete', methods: ['POST'])]
    public function delete(Request $request, Employee $employeeId, EmployeePerformance $id): Response
    {
        // Security check to ensure the performance belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette évaluation n\'appartient pas à cet employé.');
        }

        // Only HR/admin can delete
        if (!$this->isGranted('ROLE_HR') && !$this->isGranted('ROLE_ADMIN')) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour supprimer des évaluations.');
        }

        if ($this->isCsrfTokenValid('delete'.$id->getId(), $request->request->get('_token'))) {
            $this->performanceService->deletePerformanceReview($id);

            $this->addFlash('success', 'Évaluation supprimée avec succès.');
        }

        return $this->redirectToRoute('app_employee_performance_index', ['employeeId' => $employeeId->getId()]);
    }

    #[Route('/{id}/acknowledge', name: 'app_employee_performance_acknowledge', methods: ['GET'])]
    public function acknowledge(Employee $employeeId, EmployeePerformance $id): Response
    {
        // Security check to ensure the performance belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette évaluation n\'appartient pas à cet employé.');
        }

        // Only the employee or HR/admin can acknowledge
        if ($employeeId->getUser()->getId() !== $this->getUser()->getId() && !$this->isGranted('ROLE_HR') && !$this->isGranted('ROLE_ADMIN')) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour accuser réception de cette évaluation.');
        }

        // Only unacknowledged reviews can be acknowledged
        if ($id->isIsAcknowledged()) {
            $this->addFlash('error', 'Cette évaluation a déjà été accusée de réception.');
            return $this->redirectToRoute('app_employee_performance_index', ['employeeId' => $employeeId->getId()]);
        }

        $this->performanceService->acknowledgePerformanceReview($id);

        $this->addFlash('success', 'Accusé de réception enregistré avec succès.');

        return $this->redirectToRoute('app_employee_performance_index', ['employeeId' => $employeeId->getId()]);
    }

    #[Route('/{id}/show', name: 'app_employee_performance_show', methods: ['GET'])]
    public function show(Employee $employeeId, EmployeePerformance $id): Response
    {
        // Security check to ensure the performance belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette évaluation n\'appartient pas à cet employé.');
        }

        // Check if user can view performance reviews
        if (!$this->performanceService->canViewPerformanceReviews($employeeId)) {
            throw $this->createAccessDeniedException('Vous n\'avez pas les droits pour voir les évaluations de cet employé.');
        }

        return $this->render('employee_performance/show.html.twig', [
            'employee' => $employeeId,
            'performance' => $id,
        ]);
    }
}
