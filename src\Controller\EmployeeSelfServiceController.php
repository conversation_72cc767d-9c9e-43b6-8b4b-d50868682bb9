<?php

namespace App\Controller;

use App\Entity\EmployeeRequest;
use App\Entity\SalaryAdvance;
use App\Entity\DocumentRequest;
use App\Service\EmployeeRequestWorkflowService;
use App\Repository\EmployeeRequestRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/employee/self-service')]
#[IsGranted('ROLE_USER')]
class EmployeeSelfServiceController extends AbstractController
{
    private EmployeeRequestWorkflowService $workflowService;
    private EmployeeRequestRepository $requestRepository;

    public function __construct(
        EmployeeRequestWorkflowService $workflowService,
        EmployeeRequestRepository $requestRepository
    ) {
        $this->workflowService = $workflowService;
        $this->requestRepository = $requestRepository;
    }

    #[Route('/', name: 'app_employee_self_service_dashboard', methods: ['GET'])]
    public function dashboard(): Response
    {
        $user = $this->getUser();
        $employee = $user->getEmployee();

        if (!$employee) {
            throw $this->createAccessDeniedException('Vous devez être un employé pour accéder à cette page.');
        }

        // Récupérer les demandes de l'employé
        $recentRequests = $this->requestRepository->findByEmployee($employee);
        $pendingRequests = $this->requestRepository->findByEmployee($employee, 'pending');
        
        // Statistiques personnelles
        $stats = [
            'total_requests' => count($recentRequests),
            'pending_requests' => count($pendingRequests),
            'approved_requests' => count($this->requestRepository->findByEmployee($employee, 'approved')),
            'rejected_requests' => count($this->requestRepository->findByEmployee($employee, 'rejected'))
        ];

        return $this->render('employee_self_service/dashboard.html.twig', [
            'employee' => $employee,
            'recent_requests' => array_slice($recentRequests, 0, 10),
            'pending_requests' => $pendingRequests,
            'stats' => $stats
        ]);
    }

    #[Route('/requests', name: 'app_employee_self_service_requests', methods: ['GET'])]
    public function requests(Request $request): Response
    {
        $user = $this->getUser();
        $employee = $user->getEmployee();

        if (!$employee) {
            throw $this->createAccessDeniedException('Vous devez être un employé pour accéder à cette page.');
        }

        $status = $request->query->get('status');
        $type = $request->query->get('type');

        $requests = $this->requestRepository->findByEmployee($employee, $status);

        // Filtrer par type si spécifié
        if ($type) {
            $requests = array_filter($requests, function($req) use ($type) {
                return $req->getType() === $type;
            });
        }

        return $this->render('employee_self_service/requests.html.twig', [
            'employee' => $employee,
            'requests' => $requests,
            'current_status' => $status,
            'current_type' => $type
        ]);
    }

    #[Route('/request/{id}', name: 'app_employee_self_service_request_show', methods: ['GET'])]
    public function showRequest(EmployeeRequest $request): Response
    {
        $user = $this->getUser();
        $employee = $user->getEmployee();

        // Vérifier que la demande appartient à l'employé connecté
        if ($request->getEmployee() !== $employee) {
            throw $this->createAccessDeniedException('Vous ne pouvez voir que vos propres demandes.');
        }

        return $this->render('employee_self_service/request_show.html.twig', [
            'employee' => $employee,
            'request' => $request
        ]);
    }

    #[Route('/request/new', name: 'app_employee_self_service_request_new', methods: ['GET'])]
    public function newRequestChoice(): Response
    {
        $user = $this->getUser();
        $employee = $user->getEmployee();

        if (!$employee) {
            throw $this->createAccessDeniedException('Vous devez être un employé pour accéder à cette page.');
        }

        return $this->render('employee_self_service/request_new_choice.html.twig', [
            'employee' => $employee
        ]);
    }

    #[Route('/request/salary-advance/new', name: 'app_employee_self_service_salary_advance_new', methods: ['GET', 'POST'])]
    public function newSalaryAdvance(Request $request): Response
    {
        $user = $this->getUser();
        $employee = $user->getEmployee();

        if (!$employee) {
            throw $this->createAccessDeniedException('Vous devez être un employé pour accéder à cette page.');
        }

        $employeeRequest = new EmployeeRequest();
        $employeeRequest->setEmployee($employee);
        $employeeRequest->setType('salary_advance');

        $salaryAdvance = new SalaryAdvance();
        $salaryAdvance->setEmployee($employee);
        $employeeRequest->setSalaryAdvance($salaryAdvance);

        if ($request->isMethod('POST')) {
            // Récupérer les données du formulaire
            $amount = $request->request->get('amount');
            $reason = $request->request->get('reason');
            $repaymentMonths = $request->request->get('repayment_months');

            // Valider les données
            if ($amount && $reason && $repaymentMonths) {
                $employeeRequest->setTitle("Demande d'avance sur salaire - {$amount}€");
                $employeeRequest->setDescription($reason);
                $employeeRequest->setPriority('medium');

                $salaryAdvance->setAmount($amount);
                $salaryAdvance->setReason($reason);
                $salaryAdvance->setRepaymentMonths((int)$repaymentMonths);

                // Soumettre la demande via le service de workflow
                if ($this->workflowService->submitRequest($employeeRequest)) {
                    $this->addFlash('success', 'Votre demande d\'avance sur salaire a été soumise avec succès.');
                    return $this->redirectToRoute('app_employee_self_service_dashboard');
                } else {
                    $this->addFlash('error', 'Une erreur est survenue lors de la soumission de votre demande.');
                }
            } else {
                $this->addFlash('error', 'Veuillez remplir tous les champs obligatoires.');
            }
        }

        return $this->render('employee_self_service/salary_advance_new.html.twig', [
            'employee' => $employee,
            'request' => $employeeRequest
        ]);
    }

    #[Route('/request/document/new', name: 'app_employee_self_service_document_new', methods: ['GET', 'POST'])]
    public function newDocumentRequest(Request $request): Response
    {
        $user = $this->getUser();
        $employee = $user->getEmployee();

        if (!$employee) {
            throw $this->createAccessDeniedException('Vous devez être un employé pour accéder à cette page.');
        }

        $employeeRequest = new EmployeeRequest();
        $employeeRequest->setEmployee($employee);
        $employeeRequest->setType('document');

        $documentRequest = new DocumentRequest();
        $documentRequest->setEmployee($employee);
        $employeeRequest->setDocumentRequest($documentRequest);

        if ($request->isMethod('POST')) {
            // Récupérer les données du formulaire
            $documentType = $request->request->get('document_type');
            $purpose = $request->request->get('purpose');
            $quantity = $request->request->get('quantity', 1);
            $language = $request->request->get('language', 'fr');
            $deliveryMethod = $request->request->get('delivery_method', 'email');
            $neededBy = $request->request->get('needed_by');

            // Valider les données
            if ($documentType && $purpose) {
                $employeeRequest->setTitle("Demande de document - " . $this->getDocumentTypeLabel($documentType));
                $employeeRequest->setDescription($purpose);
                $employeeRequest->setPriority($neededBy && new \DateTime($neededBy) <= new \DateTime('+3 days') ? 'urgent' : 'medium');

                $documentRequest->setDocumentType($documentType);
                $documentRequest->setPurpose($purpose);
                $documentRequest->setQuantity((int)$quantity);
                $documentRequest->setLanguage($language);
                $documentRequest->setDeliveryMethod($deliveryMethod);

                if ($neededBy) {
                    $documentRequest->setNeededBy(new \DateTime($neededBy));
                    $employeeRequest->setRequestedDate(new \DateTime($neededBy));
                }

                // Soumettre la demande via le service de workflow
                if ($this->workflowService->submitRequest($employeeRequest)) {
                    $this->addFlash('success', 'Votre demande de document a été soumise avec succès.');
                    return $this->redirectToRoute('app_employee_self_service_dashboard');
                } else {
                    $this->addFlash('error', 'Une erreur est survenue lors de la soumission de votre demande.');
                }
            } else {
                $this->addFlash('error', 'Veuillez remplir tous les champs obligatoires.');
            }
        }

        return $this->render('employee_self_service/document_request_new.html.twig', [
            'employee' => $employee,
            'request' => $employeeRequest,
            'document_types' => $this->getDocumentTypes()
        ]);
    }

    #[Route('/request/{id}/cancel', name: 'app_employee_self_service_request_cancel', methods: ['POST'])]
    public function cancelRequest(EmployeeRequest $request, Request $httpRequest): Response
    {
        $user = $this->getUser();
        $employee = $user->getEmployee();

        // Vérifier que la demande appartient à l'employé connecté
        if ($request->getEmployee() !== $employee) {
            throw $this->createAccessDeniedException('Vous ne pouvez annuler que vos propres demandes.');
        }

        $reason = $httpRequest->request->get('reason');

        if ($this->workflowService->cancelRequest($request, $user, $reason)) {
            $this->addFlash('success', 'Votre demande a été annulée avec succès.');
        } else {
            $this->addFlash('error', 'Impossible d\'annuler cette demande.');
        }

        return $this->redirectToRoute('app_employee_self_service_dashboard');
    }

    /**
     * Obtenir les types de documents disponibles
     */
    private function getDocumentTypes(): array
    {
        return [
            'work_certificate' => 'Certificat de travail',
            'employment_attestation' => 'Attestation d\'emploi',
            'payslip' => 'Bulletin de paie',
            'contract' => 'Contrat de travail',
            'salary_attestation' => 'Attestation de salaire',
            'career_summary' => 'Relevé de carrière',
            'tax_certificate' => 'Certificat fiscal',
            'other' => 'Autre document'
        ];
    }

    /**
     * Obtenir le libellé d'un type de document
     */
    private function getDocumentTypeLabel(string $type): string
    {
        $types = $this->getDocumentTypes();
        return $types[$type] ?? 'Document inconnu';
    }
}
