<?php

namespace App\Controller;

use App\Entity\Employee;
use App\Entity\EmployeeSkill;
use App\Form\EmployeeSkillForm;
use App\Service\EmployeeSkillService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/employee/{employeeId}/skill')]
#[IsGranted('ROLE_HR')]
class EmployeeSkillController extends AbstractController
{
    private EmployeeSkillService $skillService;

    public function __construct(EmployeeSkillService $skillService)
    {
        $this->skillService = $skillService;
    }

    #[Route('/', name: 'app_employee_skill_index', methods: ['GET'])]
    public function index(Employee $employeeId): Response
    {
        $skills = $this->skillService->getSkillsByEmployee($employeeId);

        // Group skills by category
        $skillsByCategory = [];
        foreach ($skills as $skill) {
            $category = $skill->getCategory();
            if (!isset($skillsByCategory[$category])) {
                $skillsByCategory[$category] = [];
            }
            $skillsByCategory[$category][] = $skill;
        }

        return $this->render('employee_skill/index.html.twig', [
            'employee' => $employeeId,
            'skills_by_category' => $skillsByCategory,
        ]);
    }

    #[Route('/new', name: 'app_employee_skill_new', methods: ['GET', 'POST'])]
    public function new(Request $request, int $employeeId): Response
    {
        // Récupérer l'employé manuellement
        $employee = $this->skillService->getEmployeeRepository()->find($employeeId);

        if (!$employee) {
            throw $this->createNotFoundException('Employé non trouvé');
        }

        $skill = new EmployeeSkill();
        $skill->setEmployee($employee);

        $form = $this->createForm(EmployeeSkillForm::class, $skill);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->skillService->addSkill($skill);

            $this->addFlash('success', 'Compétence ajoutée avec succès.');

            return $this->redirectToRoute('app_employee_skill_index', ['employeeId' => $employee->getId()]);
        }

        return $this->render('employee_skill/new.html.twig', [
            'employee' => $employee,
            'form' => $form,
        ]);
    }

    #[Route('/category/{category}', name: 'app_employee_skill_by_category', methods: ['GET'])]
    public function byCategory(Employee $employeeId, string $category): Response
    {
        $skills = $this->skillService->getSkillsByEmployee($employeeId);

        // Filter skills by category
        $filteredSkills = array_filter($skills, function($skill) use ($category) {
            return $skill->getCategory() === $category;
        });

        return $this->render('employee_skill/by_category.html.twig', [
            'employee' => $employeeId,
            'skills' => $filteredSkills,
            'category' => $category,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_employee_skill_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Employee $employeeId, EmployeeSkill $id): Response
    {
        // Security check to ensure the skill belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette compétence n\'appartient pas à cet employé.');
        }

        $form = $this->createForm(EmployeeSkillForm::class, $id);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->skillService->updateSkill($id);

            $this->addFlash('success', 'Compétence mise à jour avec succès.');

            return $this->redirectToRoute('app_employee_skill_index', ['employeeId' => $employeeId->getId()]);
        }

        return $this->render('employee_skill/edit.html.twig', [
            'employee' => $employeeId,
            'skill' => $id,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/delete', name: 'app_employee_skill_delete', methods: ['POST'])]
    public function delete(Request $request, Employee $employeeId, EmployeeSkill $id): Response
    {
        // Security check to ensure the skill belongs to the employee
        if ($id->getEmployee()->getId() !== $employeeId->getId()) {
            throw $this->createAccessDeniedException('Cette compétence n\'appartient pas à cet employé.');
        }

        if ($this->isCsrfTokenValid('delete'.$id->getId(), $request->request->get('_token'))) {
            $this->skillService->removeSkill($id);

            $this->addFlash('success', 'Compétence supprimée avec succès.');
        }

        return $this->redirectToRoute('app_employee_skill_index', ['employeeId' => $employeeId->getId()]);
    }
}
