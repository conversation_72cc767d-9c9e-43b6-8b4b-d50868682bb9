<?php

namespace App\Controller;

use App\Entity\ExpenseItem;
use App\Entity\ExpenseReport;
use App\Entity\MissionOrder;
use App\Form\ExpenseItemForm;
use App\Form\ExpenseReportApprovalForm;
use App\Form\ExpenseReportForm;
use App\Repository\EmployeeRepository;
use App\Repository\ExpenseReportRepository;
use App\Service\ExpenseReportService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/expense/report')]
#[IsGranted('ROLE_USER')]
class ExpenseReportController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private ExpenseReportService $expenseReportService;
    private EmployeeRepository $employeeRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        ExpenseReportService $expenseReportService,
        EmployeeRepository $employeeRepository
    ) {
        $this->entityManager = $entityManager;
        $this->expenseReportService = $expenseReportService;
        $this->employeeRepository = $employeeRepository;
    }

    #[Route('/', name: 'app_expense_report_index')]
    public function index(ExpenseReportRepository $expenseReportRepository): Response
    {
        $user = $this->getUser();
        
        // Get expense reports
        $expenseReports = [];
        $pendingApproval = [];
        
        // Get expense reports created by user
        $expenseReports = $expenseReportRepository->findByCreatedBy($user);
        
        // If manager, get pending approval
        if ($this->isGranted('ROLE_MANAGER')) {
            $employee = $this->employeeRepository->findOneBy(['user' => $user]);
            if ($employee) {
                $pendingApproval = $this->expenseReportService->getExpenseReportsPendingApprovalByManager($employee);
            }
        }
        
        // If admin or finance, get all pending approval
        if ($this->isGranted('ROLE_ADMIN') || $this->isGranted('ROLE_FINANCE')) {
            $pendingApproval = $this->expenseReportService->getExpenseReportsPendingApproval();
        }
        
        // Get statistics
        $statistics = $this->expenseReportService->getExpenseReportStatistics();
        
        return $this->render('expense_report/index.html.twig', [
            'expense_reports' => $expenseReports,
            'pending_approval' => $pendingApproval,
            'statistics' => $statistics
        ]);
    }

    #[Route('/dashboard', name: 'app_expense_report_dashboard')]
    #[IsGranted('ROLE_FINANCE')]
    public function dashboard(): Response
    {
        $statistics = $this->expenseReportService->getExpenseReportStatistics();
        $pendingApproval = $this->expenseReportService->getExpenseReportsPendingApproval();
        
        return $this->render('expense_report/dashboard.html.twig', [
            'statistics' => $statistics,
            'pending_approval' => $pendingApproval
        ]);
    }

    #[Route('/new', name: 'app_expense_report_new')]
    public function new(Request $request): Response
    {
        $expenseReport = new ExpenseReport();
        
        $form = $this->createForm(ExpenseReportForm::class, $expenseReport);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Create expense report
                $this->expenseReportService->createExpenseReport($expenseReport);
                
                $this->addFlash('success', 'Note de frais créée avec succès');
                return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création de la note de frais : ' . $e->getMessage());
            }
        }
        
        return $this->render('expense_report/new.html.twig', [
            'expense_report' => $expenseReport,
            'form' => $form
        ]);
    }

    #[Route('/new/mission/{id}', name: 'app_expense_report_new_for_mission')]
    public function newForMission(Request $request, MissionOrder $missionOrder): Response
    {
        // Check if mission order is approved
        if ($missionOrder->getStatus() !== 'approved') {
            $this->addFlash('error', 'Vous ne pouvez créer une note de frais que pour un ordre de mission approuvé');
            return $this->redirectToRoute('app_mission_order_show', ['id' => $missionOrder->getId()]);
        }
        
        $expenseReport = new ExpenseReport();
        $expenseReport->setMissionOrder($missionOrder);
        $expenseReport->setTitle('Note de frais - ' . $missionOrder->getTitle());
        
        $form = $this->createForm(ExpenseReportForm::class, $expenseReport, [
            'mission_order' => $missionOrder
        ]);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Create expense report
                $this->expenseReportService->createExpenseReport($expenseReport);
                
                $this->addFlash('success', 'Note de frais créée avec succès');
                return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création de la note de frais : ' . $e->getMessage());
            }
        }
        
        return $this->render('expense_report/new.html.twig', [
            'expense_report' => $expenseReport,
            'form' => $form,
            'mission_order' => $missionOrder
        ]);
    }

    #[Route('/mission/{id}', name: 'app_expense_report_by_mission')]
    public function byMission(MissionOrder $missionOrder): Response
    {
        $expenseReports = $this->expenseReportService->getExpenseReportsByMissionOrder($missionOrder);
        
        return $this->render('expense_report/by_mission.html.twig', [
            'expense_reports' => $expenseReports,
            'mission_order' => $missionOrder
        ]);
    }

    #[Route('/{id}', name: 'app_expense_report_show', methods: ['GET'])]
    public function show(ExpenseReport $expenseReport): Response
    {
        // Check if user can view this expense report
        $this->denyAccessUnlessGranted('view', $expenseReport);
        
        // Get expense items
        $expenseItems = $this->expenseReportService->getExpenseItemsByExpenseReport($expenseReport);
        
        return $this->render('expense_report/show.html.twig', [
            'expense_report' => $expenseReport,
            'expense_items' => $expenseItems
        ]);
    }

    #[Route('/{id}/edit', name: 'app_expense_report_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, ExpenseReport $expenseReport): Response
    {
        // Check if user can edit this expense report
        $this->denyAccessUnlessGranted('edit', $expenseReport);
        
        // Check if expense report can be edited
        if (!$expenseReport->canBeEdited()) {
            $this->addFlash('error', 'Cette note de frais ne peut pas être modifiée');
            return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
        }
        
        $form = $this->createForm(ExpenseReportForm::class, $expenseReport, [
            'mission_order' => $expenseReport->getMissionOrder()
        ]);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Update expense report
                $this->expenseReportService->updateExpenseReport($expenseReport);
                
                $this->addFlash('success', 'Note de frais mise à jour avec succès');
                return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour de la note de frais : ' . $e->getMessage());
            }
        }
        
        return $this->render('expense_report/edit.html.twig', [
            'expense_report' => $expenseReport,
            'form' => $form
        ]);
    }

    #[Route('/{id}/add-item', name: 'app_expense_report_add_item', methods: ['GET', 'POST'])]
    public function addItem(Request $request, ExpenseReport $expenseReport): Response
    {
        // Check if user can edit this expense report
        $this->denyAccessUnlessGranted('edit', $expenseReport);
        
        // Check if expense report can be edited
        if (!$expenseReport->canBeEdited()) {
            $this->addFlash('error', 'Cette note de frais ne peut pas être modifiée');
            return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
        }
        
        $expenseItem = new ExpenseItem();
        
        $form = $this->createForm(ExpenseItemForm::class, $expenseItem);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Add expense item to report
                $this->expenseReportService->addExpenseItem($expenseReport, $expenseItem);
                
                // Handle receipt file upload
                $receiptFile = $form->get('receiptFile')->getData();
                if ($receiptFile) {
                    $this->expenseReportService->uploadReceipt($expenseItem, $receiptFile);
                }
                
                $this->addFlash('success', 'Dépense ajoutée avec succès');
                return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'ajout de la dépense : ' . $e->getMessage());
            }
        }
        
        return $this->render('expense_report/add_item.html.twig', [
            'expense_report' => $expenseReport,
            'form' => $form
        ]);
    }

    #[Route('/item/{id}/remove', name: 'app_expense_item_remove', methods: ['GET', 'POST'])]
    public function removeItem(Request $request, ExpenseItem $expenseItem): Response
    {
        $expenseReport = $expenseItem->getExpenseReport();
        
        // Check if user can edit this expense report
        $this->denyAccessUnlessGranted('edit', $expenseReport);
        
        // Check if expense report can be edited
        if (!$expenseReport->canBeEdited()) {
            $this->addFlash('error', 'Cette note de frais ne peut pas être modifiée');
            return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
        }
        
        if ($request->isMethod('POST')) {
            try {
                // Remove expense item from report
                $this->expenseReportService->removeExpenseItem($expenseReport, $expenseItem);
                
                $this->addFlash('success', 'Dépense supprimée avec succès');
                return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression de la dépense : ' . $e->getMessage());
            }
        }
        
        return $this->render('expense_report/remove_item.html.twig', [
            'expense_report' => $expenseReport,
            'expense_item' => $expenseItem
        ]);
    }

    #[Route('/{id}/submit', name: 'app_expense_report_submit', methods: ['GET', 'POST'])]
    public function submit(Request $request, ExpenseReport $expenseReport): Response
    {
        // Check if user can submit this expense report
        $this->denyAccessUnlessGranted('submit', $expenseReport);
        
        // Check if expense report can be submitted
        if (!$expenseReport->canBeSubmitted()) {
            $this->addFlash('error', 'Cette note de frais ne peut pas être soumise');
            return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
        }
        
        if ($request->isMethod('POST')) {
            try {
                // Submit expense report
                $this->expenseReportService->submitExpenseReport($expenseReport);
                
                $this->addFlash('success', 'Note de frais soumise avec succès');
                return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la soumission de la note de frais : ' . $e->getMessage());
            }
        }
        
        return $this->render('expense_report/submit.html.twig', [
            'expense_report' => $expenseReport
        ]);
    }

    #[Route('/{id}/approve', name: 'app_expense_report_approve', methods: ['GET', 'POST'])]
    public function approve(Request $request, ExpenseReport $expenseReport): Response
    {
        $user = $this->getUser();
        $employee = $this->employeeRepository->findOneBy(['user' => $user]);
        
        // Check if user can approve this expense report
        if (!$employee) {
            $this->addFlash('error', 'Vous devez être un employé pour approuver une note de frais');
            return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
        }
        
        // Check if expense report can be approved
        if (!$expenseReport->canBeApproved()) {
            $this->addFlash('error', 'Cette note de frais ne peut pas être approuvée');
            return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
        }
        
        // Check if user is manager of the employee or has finance/admin role
        $isManager = $expenseReport->getMissionOrder()->getEmployee()->getManager() === $employee;
        $hasRole = $this->isGranted('ROLE_ADMIN') || $this->isGranted('ROLE_FINANCE');
        
        if (!$isManager && !$hasRole) {
            $this->addFlash('error', 'Vous n\'êtes pas autorisé à approuver cette note de frais');
            return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
        }
        
        $form = $this->createForm(ExpenseReportApprovalForm::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            
            try {
                if ($data['decision'] === 'approve') {
                    // Approve expense report
                    $this->expenseReportService->approveExpenseReport($expenseReport, $employee);
                    $this->addFlash('success', 'Note de frais approuvée avec succès');
                } else {
                    // Reject expense report
                    $this->expenseReportService->rejectExpenseReport($expenseReport, $data['rejectionReason'], $user);
                    $this->addFlash('success', 'Note de frais refusée');
                }
                
                return $this->redirectToRoute('app_expense_report_show', ['id' => $expenseReport->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'approbation de la note de frais : ' . $e->getMessage());
            }
        }
        
        return $this->render('expense_report/approve.html.twig', [
            'expense_report' => $expenseReport,
            'form' => $form
        ]);
    }
}
