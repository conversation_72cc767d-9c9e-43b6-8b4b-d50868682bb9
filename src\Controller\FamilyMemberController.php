<?php

namespace App\Controller;

use App\Entity\Employee;
use App\Entity\FamilyMember;
use App\Form\FamilyMemberForm;
use App\Repository\EmployeeRepository;
use App\Repository\FamilyMemberRepository;
use App\Service\FamilyMemberService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/family-member')]
#[IsGranted('ROLE_HR')]
class FamilyMemberController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private FamilyMemberService $familyMemberService;
    private EmployeeRepository $employeeRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        FamilyMemberService $familyMemberService,
        EmployeeRepository $employeeRepository
    ) {
        $this->entityManager = $entityManager;
        $this->familyMemberService = $familyMemberService;
        $this->employeeRepository = $employeeRepository;
    }

    #[Route('/', name: 'app_family_member_index')]
    public function index(FamilyMemberRepository $familyMemberRepository): Response
    {
        $familyMembers = $familyMemberRepository->findBy([], ['firstName' => 'ASC']);
        $dependents = $familyMemberRepository->findDependents();

        return $this->render('family_member/index.html.twig', [
            'family_members' => $familyMembers,
            'dependents' => $dependents
        ]);
    }

    #[Route('/new', name: 'app_family_member_new')]
    public function new(Request $request): Response
    {
        $familyMember = new FamilyMember();

        $form = $this->createForm(FamilyMemberForm::class, $familyMember);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->familyMemberService->createFamilyMember($familyMember);

                $this->addFlash('success', 'Membre de famille créé avec succès');
                return $this->redirectToRoute('app_family_member_show', ['id' => $familyMember->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du membre de famille : ' . $e->getMessage());
            }
        }

        return $this->render('family_member/new.html.twig', [
            'family_member' => $familyMember,
            'form' => $form
        ]);
    }

    #[Route('/employee/{id}/new', name: 'app_family_member_new_for_employee')]
    public function newForEmployee(Request $request, Employee $employee): Response
    {
        $familyMember = new FamilyMember();
        $familyMember->setEmployee($employee);

        $form = $this->createForm(FamilyMemberForm::class, $familyMember, [
            'employee' => $employee
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->familyMemberService->createFamilyMember($familyMember);

                $this->addFlash('success', 'Membre de famille créé avec succès');
                return $this->redirectToRoute('app_employee_show', ['id' => $employee->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du membre de famille : ' . $e->getMessage());
            }
        }

        return $this->render('family_member/new.html.twig', [
            'family_member' => $familyMember,
            'employee' => $employee,
            'form' => $form
        ]);
    }

    #[Route('/hr/employee/{id}/family-member/new', name: 'app_employee_family_member_new')]
    public function newFromEmployeeRoute(Request $request, Employee $employee): Response
    {
        // Redirect to the existing route
        return $this->redirectToRoute('app_family_member_new_for_employee', ['id' => $employee->getId()]);
    }

    #[Route('/dependents', name: 'app_family_member_dependents')]
    public function dependents(): Response
    {
        $dependents = $this->familyMemberService->getDependents();

        return $this->render('family_member/dependents.html.twig', [
            'dependents' => $dependents
        ]);
    }

    #[Route('/employee/{id}', name: 'app_family_member_by_employee')]
    public function byEmployee(Employee $employee): Response
    {
        $familyMembers = $this->familyMemberService->getFamilyMembersByEmployee($employee);

        return $this->render('family_member/by_employee.html.twig', [
            'family_members' => $familyMembers,
            'employee' => $employee
        ]);
    }

    #[Route('/relationship/{relationship}', name: 'app_family_member_by_relationship')]
    public function byRelationship(string $relationship): Response
    {
        $familyMembers = $this->familyMemberService->getFamilyMembersByRelationship($relationship);

        return $this->render('family_member/by_relationship.html.twig', [
            'family_members' => $familyMembers,
            'relationship' => $relationship
        ]);
    }

    #[Route('/{id}', name: 'app_family_member_show', methods: ['GET'])]
    public function show(FamilyMember $familyMember): Response
    {
        return $this->render('family_member/show.html.twig', [
            'family_member' => $familyMember
        ]);
    }

    #[Route('/{id}/edit', name: 'app_family_member_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, FamilyMember $familyMember): Response
    {
        $form = $this->createForm(FamilyMemberForm::class, $familyMember, [
            'employee' => $familyMember->getEmployee()
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->familyMemberService->updateFamilyMember($familyMember);

                $this->addFlash('success', 'Membre de famille mis à jour avec succès');
                return $this->redirectToRoute('app_family_member_show', ['id' => $familyMember->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour du membre de famille : ' . $e->getMessage());
            }
        }

        return $this->render('family_member/edit.html.twig', [
            'family_member' => $familyMember,
            'form' => $form
        ]);
    }

    #[Route('/{id}/delete', name: 'app_family_member_delete', methods: ['GET', 'POST'])]
    public function delete(Request $request, FamilyMember $familyMember): Response
    {
        if ($request->isMethod('POST')) {
            try {
                $employee = $familyMember->getEmployee();
                $this->familyMemberService->deleteFamilyMember($familyMember);

                $this->addFlash('success', 'Membre de famille supprimé avec succès');

                // Redirect to employee page if coming from there
                if ($request->query->has('from_employee')) {
                    return $this->redirectToRoute('app_employee_show', ['id' => $employee->getId()]);
                }

                return $this->redirectToRoute('app_family_member_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression du membre de famille : ' . $e->getMessage());
            }
        }

        return $this->render('family_member/delete.html.twig', [
            'family_member' => $familyMember
        ]);
    }
}
