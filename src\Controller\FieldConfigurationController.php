<?php

namespace App\Controller;

use App\Entity\FieldConfiguration;
use App\Form\FieldConfigurationForm;
use App\Repository\FieldConfigurationRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/field/configuration')]
final class FieldConfigurationController extends AbstractController
{
    #[Route(name: 'app_field_configuration_index', methods: ['GET'])]
    public function index(FieldConfigurationRepository $fieldConfigurationRepository): Response
    {
        return $this->render('field_configuration/index.html.twig', [
            'field_configurations' => $fieldConfigurationRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_field_configuration_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $fieldConfiguration = new FieldConfiguration();
        $form = $this->createForm(FieldConfigurationForm::class, $fieldConfiguration);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($fieldConfiguration);
            $entityManager->flush();

            return $this->redirectToRoute('app_field_configuration_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('field_configuration/new.html.twig', [
            'field_configuration' => $fieldConfiguration,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_field_configuration_show', methods: ['GET'])]
    public function show(FieldConfiguration $fieldConfiguration): Response
    {
        return $this->render('field_configuration/show.html.twig', [
            'field_configuration' => $fieldConfiguration,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_field_configuration_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, FieldConfiguration $fieldConfiguration, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(FieldConfigurationForm::class, $fieldConfiguration);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            return $this->redirectToRoute('app_field_configuration_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('field_configuration/edit.html.twig', [
            'field_configuration' => $fieldConfiguration,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_field_configuration_delete', methods: ['POST'])]
    public function delete(Request $request, FieldConfiguration $fieldConfiguration, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$fieldConfiguration->getId(), $request->getPayload()->getString('_token'))) {
            $entityManager->remove($fieldConfiguration);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_field_configuration_index', [], Response::HTTP_SEE_OTHER);
    }
}
