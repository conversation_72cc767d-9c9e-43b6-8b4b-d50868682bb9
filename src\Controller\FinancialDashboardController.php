<?php

namespace App\Controller;

use App\Entity\Invoice;
use App\Repository\InvoiceItemRepository;
use App\Repository\InvoicePaymentRepository;
use App\Repository\InvoiceRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/financial-dashboard')]
#[IsGranted('ROLE_FINANCE')]
class FinancialDashboardController extends AbstractController
{
    private InvoiceRepository $invoiceRepository;
    private InvoiceItemRepository $invoiceItemRepository;
    private InvoicePaymentRepository $invoicePaymentRepository;

    public function __construct(
        InvoiceRepository $invoiceRepository,
        InvoiceItemRepository $invoiceItemRepository,
        InvoicePaymentRepository $invoicePaymentRepository
    ) {
        $this->invoiceRepository = $invoiceRepository;
        $this->invoiceItemRepository = $invoiceItemRepository;
        $this->invoicePaymentRepository = $invoicePaymentRepository;
    }

    #[Route('/', name: 'app_financial_dashboard_index', methods: ['GET'])]
    public function index(): Response
    {
        // Get current year
        $currentYear = date('Y');
        $startDate = new \DateTime($currentYear . '-01-01');
        $endDate = new \DateTime($currentYear . '-12-31');
        
        // Get total amounts
        $totalInvoiced = $this->invoiceRepository->getTotalAmountByPeriod($startDate, $endDate);
        $totalPaid = $this->invoicePaymentRepository->getTotalPaymentsByPeriod($startDate, $endDate);
        $totalUnpaid = $this->invoiceRepository->getTotalUnpaidAmount();
        $totalOverdue = $this->invoiceRepository->getTotalOverdueAmount();
        
        // Get invoices by month
        $invoicesByMonth = $this->invoiceRepository->findInvoicesByMonth();
        
        // Get payments by month
        $paymentsByMonth = $this->invoicePaymentRepository->findPaymentsByMonth();
        
        // Get invoices by supplier
        $invoicesBySupplier = $this->invoiceRepository->findInvoicesBySupplier();
        
        // Get invoices by project
        $invoicesByProject = $this->invoiceRepository->findInvoicesByProject();
        
        // Get invoices by status
        $invoicesByStatus = $this->invoiceRepository->findInvoicesByStatus();
        
        // Get top products
        $topProducts = $this->invoiceItemRepository->getTopProductsByTotalAmount();
        
        // Get payments by payment method
        $paymentsByMethod = $this->invoicePaymentRepository->findPaymentsByPaymentMethod();
        
        // Get recent invoices
        $recentInvoices = $this->invoiceRepository->findBy([], ['invoiceDate' => 'DESC'], 5);
        
        // Get overdue invoices
        $overdueInvoices = $this->invoiceRepository->findOverdue();
        
        // Get invoices due soon
        $invoicesDueSoon = $this->invoiceRepository->findDueSoon();
        
        // Get pending approval invoices
        $pendingApprovalInvoices = $this->invoiceRepository->findPendingApproval();
        
        return $this->render('financial_dashboard/index.html.twig', [
            'total_invoiced' => $totalInvoiced,
            'total_paid' => $totalPaid,
            'total_unpaid' => $totalUnpaid,
            'total_overdue' => $totalOverdue,
            'invoices_by_month' => $invoicesByMonth,
            'payments_by_month' => $paymentsByMonth,
            'invoices_by_supplier' => $invoicesBySupplier,
            'invoices_by_project' => $invoicesByProject,
            'invoices_by_status' => $invoicesByStatus,
            'top_products' => $topProducts,
            'payments_by_method' => $paymentsByMethod,
            'recent_invoices' => $recentInvoices,
            'overdue_invoices' => $overdueInvoices,
            'invoices_due_soon' => $invoicesDueSoon,
            'pending_approval_invoices' => $pendingApprovalInvoices,
        ]);
    }

    #[Route('/invoices-by-period', name: 'app_financial_dashboard_invoices_by_period', methods: ['GET'])]
    public function invoicesByPeriod(Request $request): Response
    {
        $startDate = $request->query->get('start_date') ? new \DateTime($request->query->get('start_date')) : new \DateTime('first day of this month');
        $endDate = $request->query->get('end_date') ? new \DateTime($request->query->get('end_date')) : new \DateTime('last day of this month');
        
        // Get invoices for the period
        $invoices = $this->invoiceRepository->findByPeriod($startDate, $endDate);
        
        // Get total amount for the period
        $totalAmount = $this->invoiceRepository->getTotalAmountByPeriod($startDate, $endDate);
        
        // Get total paid amount for the period
        $totalPaidAmount = $this->invoicePaymentRepository->getTotalPaymentsByPeriod($startDate, $endDate);
        
        return $this->render('financial_dashboard/invoices_by_period.html.twig', [
            'invoices' => $invoices,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'total_amount' => $totalAmount,
            'total_paid_amount' => $totalPaidAmount,
        ]);
    }

    #[Route('/invoices-by-status/{status}', name: 'app_financial_dashboard_invoices_by_status', methods: ['GET'])]
    public function invoicesByStatus(string $status): Response
    {
        // Validate status
        if (!in_array($status, [
            Invoice::STATUS_DRAFT,
            Invoice::STATUS_RECEIVED,
            Invoice::STATUS_PENDING_APPROVAL,
            Invoice::STATUS_APPROVED,
            Invoice::STATUS_REJECTED,
            Invoice::STATUS_PAID,
            Invoice::STATUS_PARTIALLY_PAID,
            Invoice::STATUS_CANCELLED,
            Invoice::STATUS_DISPUTED,
        ])) {
            throw $this->createNotFoundException('Statut invalide.');
        }
        
        // Get invoices by status
        $invoices = $this->invoiceRepository->findByStatus($status);
        
        return $this->render('financial_dashboard/invoices_by_status.html.twig', [
            'invoices' => $invoices,
            'status' => $status,
        ]);
    }

    #[Route('/cash-flow', name: 'app_financial_dashboard_cash_flow', methods: ['GET'])]
    public function cashFlow(Request $request): Response
    {
        $year = $request->query->getInt('year', (int)date('Y'));
        
        // Get invoices by month for the year
        $startDate = new \DateTime($year . '-01-01');
        $endDate = new \DateTime($year . '-12-31');
        
        // Get invoices for the year
        $invoices = $this->invoiceRepository->findByPeriod($startDate, $endDate);
        
        // Get payments for the year
        $payments = $this->invoicePaymentRepository->findByPeriod($startDate, $endDate);
        
        // Prepare data for chart
        $months = [];
        $invoicedData = array_fill(0, 12, 0);
        $paidData = array_fill(0, 12, 0);
        
        foreach ($invoices as $invoice) {
            $month = (int)$invoice->getInvoiceDate()->format('n') - 1; // 0-based month
            $invoicedData[$month] += $invoice->getTotalAmount();
        }
        
        foreach ($payments as $payment) {
            $month = (int)$payment->getPaymentDate()->format('n') - 1; // 0-based month
            $paidData[$month] += $payment->getAmount();
        }
        
        for ($i = 0; $i < 12; $i++) {
            $months[] = date('F', mktime(0, 0, 0, $i + 1, 1, $year));
        }
        
        return $this->render('financial_dashboard/cash_flow.html.twig', [
            'year' => $year,
            'months' => $months,
            'invoiced_data' => $invoicedData,
            'paid_data' => $paidData,
        ]);
    }

    #[Route('/aging-report', name: 'app_financial_dashboard_aging_report', methods: ['GET'])]
    public function agingReport(): Response
    {
        $today = new \DateTime();
        
        // Get all unpaid invoices
        $unpaidInvoices = $this->invoiceRepository->findBy([
            'status' => [Invoice::STATUS_APPROVED, Invoice::STATUS_PARTIALLY_PAID]
        ]);
        
        // Prepare aging buckets
        $agingBuckets = [
            'current' => [], // Not yet due
            '1_30' => [], // 1-30 days overdue
            '31_60' => [], // 31-60 days overdue
            '61_90' => [], // 61-90 days overdue
            'over_90' => [], // Over 90 days overdue
        ];
        
        $totals = [
            'current' => 0,
            '1_30' => 0,
            '31_60' => 0,
            '61_90' => 0,
            'over_90' => 0,
        ];
        
        foreach ($unpaidInvoices as $invoice) {
            $dueDate = $invoice->getDueDate();
            $daysOverdue = $today->diff($dueDate)->days * ($today > $dueDate ? 1 : -1);
            $remainingAmount = $invoice->getRemainingAmount();
            
            if ($daysOverdue <= 0) {
                $agingBuckets['current'][] = $invoice;
                $totals['current'] += $remainingAmount;
            } elseif ($daysOverdue <= 30) {
                $agingBuckets['1_30'][] = $invoice;
                $totals['1_30'] += $remainingAmount;
            } elseif ($daysOverdue <= 60) {
                $agingBuckets['31_60'][] = $invoice;
                $totals['31_60'] += $remainingAmount;
            } elseif ($daysOverdue <= 90) {
                $agingBuckets['61_90'][] = $invoice;
                $totals['61_90'] += $remainingAmount;
            } else {
                $agingBuckets['over_90'][] = $invoice;
                $totals['over_90'] += $remainingAmount;
            }
        }
        
        return $this->render('financial_dashboard/aging_report.html.twig', [
            'aging_buckets' => $agingBuckets,
            'totals' => $totals,
        ]);
    }
}
