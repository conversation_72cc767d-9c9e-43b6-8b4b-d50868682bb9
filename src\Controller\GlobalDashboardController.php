<?php

namespace App\Controller;

use App\Repository\DepartmentRepository;
use App\Repository\EmployeeRepository;
use App\Repository\PartnerRepository;
use App\Repository\PositionRepository;
use App\Repository\ProductCategoryRepository;
use App\Repository\ProductRepository;
use App\Repository\ProjectRepository;
use App\Repository\PurchaseRequestRepository;
use App\Repository\TaskRepository;
use App\Repository\UserRepository;
use App\Service\ActivityLogService;
use App\Service\CacheService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/global-dashboard')]
#[IsGranted('ROLE_USER')]
class GlobalDashboardController extends AbstractController
{
    private CacheService $cacheService;
    
    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }
    
    #[Route('/', name: 'app_global_dashboard')]
    public function index(
        DepartmentRepository $departmentRepository,
        EmployeeRepository $employeeRepository,
        PartnerRepository $partnerRepository,
        PositionRepository $positionRepository,
        ProductRepository $productRepository,
        ProductCategoryRepository $productCategoryRepository,
        ProjectRepository $projectRepository,
        PurchaseRequestRepository $purchaseRequestRepository,
        TaskRepository $taskRepository,
        UserRepository $userRepository,
        ActivityLogService $activityLogService
    ): Response {
        // Journaliser l'action
        $activityLogService->logView('dashboard', null, [
            'action' => 'view_global_dashboard',
        ]);
        
        // Récupérer les statistiques globales avec mise en cache
        $stats = $this->cacheService->get('global_dashboard_stats', function() use (
            $departmentRepository,
            $employeeRepository,
            $partnerRepository,
            $positionRepository,
            $productRepository,
            $productCategoryRepository,
            $projectRepository,
            $purchaseRequestRepository,
            $taskRepository,
            $userRepository
        ) {
            return [
                'departments' => [
                    'total' => $departmentRepository->count([]),
                    'active' => $departmentRepository->count(['isActive' => true]),
                ],
                'employees' => [
                    'total' => $employeeRepository->count([]),
                    'active' => $employeeRepository->count(['isActive' => true]),
                ],
                'partners' => [
                    'total' => $partnerRepository->count([]),
                    'active' => $partnerRepository->count(['isActive' => true]),
                ],
                'positions' => [
                    'total' => $positionRepository->count([]),
                    'active' => $positionRepository->count(['isActive' => true]),
                ],
                'products' => [
                    'total' => $productRepository->count([]),
                    'active' => $productRepository->count(['isActive' => true]),
                ],
                'productCategories' => [
                    'total' => $productCategoryRepository->count([]),
                    'active' => $productCategoryRepository->count(['isActive' => true]),
                ],
                'projects' => [
                    'total' => $projectRepository->count([]),
                    'active' => $projectRepository->count(['status' => 'active']),
                    'completed' => $projectRepository->count(['status' => 'completed']),
                    'pending' => $projectRepository->count(['status' => 'pending']),
                ],
                'purchaseRequests' => [
                    'total' => $purchaseRequestRepository->count([]),
                    'pending' => $purchaseRequestRepository->count(['status' => 'pending']),
                    'approved' => $purchaseRequestRepository->count(['status' => 'approved']),
                    'rejected' => $purchaseRequestRepository->count(['status' => 'rejected']),
                ],
                'tasks' => [
                    'total' => $taskRepository->count([]),
                    'completed' => $taskRepository->count(['status' => 'completed']),
                    'inProgress' => $taskRepository->count(['status' => 'in_progress']),
                    'pending' => $taskRepository->count(['status' => 'pending']),
                ],
                'users' => [
                    'total' => $userRepository->count([]),
                    'active' => $userRepository->count(['isActive' => true]),
                ],
            ];
        }, 3600); // Cache pour 1 heure
        
        // Récupérer les dernières activités
        $recentActivities = $this->cacheService->get('global_dashboard_recent_activities', function() use ($activityLogService) {
            return $activityLogService->getRecentActivities(10);
        }, 300); // Cache pour 5 minutes
        
        // Récupérer les projets récents
        $recentProjects = $this->cacheService->get('global_dashboard_recent_projects', function() use ($projectRepository) {
            return $projectRepository->findBy([], ['createdAt' => 'DESC'], 5);
        }, 600); // Cache pour 10 minutes
        
        // Récupérer les tâches récentes
        $recentTasks = $this->cacheService->get('global_dashboard_recent_tasks', function() use ($taskRepository) {
            return $taskRepository->findBy([], ['createdAt' => 'DESC'], 5);
        }, 600); // Cache pour 10 minutes
        
        // Récupérer les partenaires récents
        $recentPartners = $this->cacheService->get('global_dashboard_recent_partners', function() use ($partnerRepository) {
            return $partnerRepository->findBy([], ['createdAt' => 'DESC'], 5);
        }, 600); // Cache pour 10 minutes
        
        return $this->render('global_dashboard/index.html.twig', [
            'stats' => $stats,
            'recentActivities' => $recentActivities,
            'recentProjects' => $recentProjects,
            'recentTasks' => $recentTasks,
            'recentPartners' => $recentPartners,
        ]);
    }
    
    #[Route('/refresh-stats', name: 'app_global_dashboard_refresh_stats')]
    public function refreshStats(): Response
    {
        // Supprimer les données en cache
        $this->cacheService->delete('global_dashboard_stats');
        $this->cacheService->delete('global_dashboard_recent_activities');
        $this->cacheService->delete('global_dashboard_recent_projects');
        $this->cacheService->delete('global_dashboard_recent_tasks');
        $this->cacheService->delete('global_dashboard_recent_partners');
        
        // Rediriger vers le tableau de bord
        $this->addFlash('success', 'Les statistiques ont été actualisées avec succès.');
        
        return $this->redirectToRoute('app_global_dashboard');
    }
}
