<?php

namespace App\Controller;

use App\Entity\GoodsReceipt;
use App\Entity\GoodsReceiptItem;
use App\Entity\PurchaseOrder;
use App\Form\GoodsReceiptForm;
use App\Repository\GoodsReceiptRepository;
use App\Service\GoodsReceiptService;
use App\Service\PurchaseOrderService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\String\Slugger\SluggerInterface;

#[Route('/goods/receipt')]
#[IsGranted('ROLE_USER')]
class GoodsReceiptController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private GoodsReceiptRepository $goodsReceiptRepository;
    private GoodsReceiptService $goodsReceiptService;
    private PurchaseOrderService $purchaseOrderService;
    private Security $security;
    private SluggerInterface $slugger;

    public function __construct(
        EntityManagerInterface $entityManager,
        GoodsReceiptRepository $goodsReceiptRepository,
        GoodsReceiptService $goodsReceiptService,
        PurchaseOrderService $purchaseOrderService,
        Security $security,
        SluggerInterface $slugger
    ) {
        $this->entityManager = $entityManager;
        $this->goodsReceiptRepository = $goodsReceiptRepository;
        $this->goodsReceiptService = $goodsReceiptService;
        $this->purchaseOrderService = $purchaseOrderService;
        $this->security = $security;
        $this->slugger = $slugger;
    }

    #[Route('/', name: 'app_goods_receipt_index', methods: ['GET'])]
    public function index(): Response
    {
        $goodsReceipts = $this->goodsReceiptRepository->findBy([], ['receiptDate' => 'DESC']);

        return $this->render('goods_receipt/index.html.twig', [
            'goods_receipts' => $goodsReceipts,
        ]);
    }

    #[Route('/new', name: 'app_goods_receipt_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $goodsReceipt = new GoodsReceipt();
        $goodsReceipt->setCreatedBy($this->security->getUser());
        
        $form = $this->createForm(GoodsReceiptForm::class, $goodsReceipt);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Handle delivery note file upload
                $deliveryNoteFile = $form->get('deliveryNoteFile')->getData();
                if ($deliveryNoteFile) {
                    $originalFilename = pathinfo($deliveryNoteFile->getClientOriginalName(), PATHINFO_FILENAME);
                    $safeFilename = $this->slugger->slug($originalFilename);
                    $newFilename = $safeFilename.'-'.uniqid().'.'.$deliveryNoteFile->guessExtension();
                    
                    try {
                        $deliveryNoteFile->move(
                            $this->getParameter('delivery_notes_directory'),
                            $newFilename
                        );
                        
                        $goodsReceipt->setDeliveryNoteFilename($newFilename);
                    } catch (FileException $e) {
                        $this->addFlash('error', 'Erreur lors du téléchargement du bon de livraison : ' . $e->getMessage());
                    }
                }
                
                $this->goodsReceiptService->createGoodsReceipt($goodsReceipt);
                
                // Update the purchase order status
                $this->purchaseOrderService->updateReceiptStatus($goodsReceipt->getPurchaseOrder());
                
                $this->addFlash('success', 'Bon de réception créé avec succès.');
                
                return $this->redirectToRoute('app_goods_receipt_show', ['id' => $goodsReceipt->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du bon de réception : ' . $e->getMessage());
            }
        }

        return $this->render('goods_receipt/new.html.twig', [
            'goods_receipt' => $goodsReceipt,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/new-from-order/{id}', name: 'app_goods_receipt_new_from_order', methods: ['GET', 'POST'])]
    public function newFromOrder(Request $request, PurchaseOrder $purchaseOrder): Response
    {
        // Check if the purchase order can receive goods
        if (!$purchaseOrder->canReceiveGoods()) {
            $this->addFlash('error', 'Ce bon de commande ne peut pas recevoir de marchandises dans son état actuel.');
            return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
        }
        
        try {
            $goodsReceipt = $this->goodsReceiptService->createFromPurchaseOrder($purchaseOrder);
            
            $form = $this->createForm(GoodsReceiptForm::class, $goodsReceipt);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                try {
                    // Handle delivery note file upload
                    $deliveryNoteFile = $form->get('deliveryNoteFile')->getData();
                    if ($deliveryNoteFile) {
                        $originalFilename = pathinfo($deliveryNoteFile->getClientOriginalName(), PATHINFO_FILENAME);
                        $safeFilename = $this->slugger->slug($originalFilename);
                        $newFilename = $safeFilename.'-'.uniqid().'.'.$deliveryNoteFile->guessExtension();
                        
                        try {
                            $deliveryNoteFile->move(
                                $this->getParameter('delivery_notes_directory'),
                                $newFilename
                            );
                            
                            $goodsReceipt->setDeliveryNoteFilename($newFilename);
                        } catch (FileException $e) {
                            $this->addFlash('error', 'Erreur lors du téléchargement du bon de livraison : ' . $e->getMessage());
                        }
                    }
                    
                    $this->goodsReceiptService->saveGoodsReceipt($goodsReceipt);
                    
                    // Update the purchase order status
                    $this->purchaseOrderService->updateReceiptStatus($purchaseOrder);
                    
                    $this->addFlash('success', 'Bon de réception créé avec succès.');
                    
                    return $this->redirectToRoute('app_goods_receipt_show', ['id' => $goodsReceipt->getId()]);
                } catch (\Exception $e) {
                    $this->addFlash('error', 'Erreur lors de la création du bon de réception : ' . $e->getMessage());
                }
            }

            return $this->render('goods_receipt/new_from_order.html.twig', [
                'goods_receipt' => $goodsReceipt,
                'purchase_order' => $purchaseOrder,
                'form' => $form->createView(),
            ]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Erreur lors de la création du bon de réception : ' . $e->getMessage());
            return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
        }
    }

    #[Route('/{id}', name: 'app_goods_receipt_show', methods: ['GET'])]
    public function show(GoodsReceipt $goodsReceipt): Response
    {
        return $this->render('goods_receipt/show.html.twig', [
            'goods_receipt' => $goodsReceipt,
        ]);
    }

    #[Route('/{id}/delete', name: 'app_goods_receipt_delete', methods: ['POST'])]
    #[IsGranted('ROLE_ADMIN')]
    public function delete(Request $request, GoodsReceipt $goodsReceipt): Response
    {
        if ($this->isCsrfTokenValid('delete'.$goodsReceipt->getId(), $request->request->get('_token'))) {
            try {
                $purchaseOrder = $goodsReceipt->getPurchaseOrder();
                
                $this->goodsReceiptService->deleteGoodsReceipt($goodsReceipt);
                
                // Update the purchase order status
                $this->purchaseOrderService->updateReceiptStatus($purchaseOrder);
                
                $this->addFlash('success', 'Bon de réception supprimé avec succès.');
                
                return $this->redirectToRoute('app_goods_receipt_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression du bon de réception : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_goods_receipt_show', ['id' => $goodsReceipt->getId()]);
    }

    #[Route('/by-order/{id}', name: 'app_goods_receipt_by_order', methods: ['GET'])]
    public function byOrder(PurchaseOrder $purchaseOrder): Response
    {
        $goodsReceipts = $this->goodsReceiptRepository->findByPurchaseOrder($purchaseOrder);

        return $this->render('goods_receipt/by_order.html.twig', [
            'goods_receipts' => $goodsReceipts,
            'purchase_order' => $purchaseOrder,
        ]);
    }

    #[Route('/print/{id}', name: 'app_goods_receipt_print', methods: ['GET'])]
    public function print(GoodsReceipt $goodsReceipt): Response
    {
        return $this->render('goods_receipt/print.html.twig', [
            'goods_receipt' => $goodsReceipt,
        ]);
    }
}
