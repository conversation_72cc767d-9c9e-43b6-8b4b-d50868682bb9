<?php

namespace App\Controller;

use App\Entity\EmployeeRequest;
use App\Service\EmployeeRequestWorkflowService;
use App\Repository\EmployeeRequestRepository;
use App\Repository\SalaryAdvanceRepository;
use App\Repository\DocumentRequestRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/requests')]
#[IsGranted('ROLE_USER')] // Pour l'instant, tout utilisateur connecté peut accéder
class HRRequestDashboardController extends AbstractController
{
    private EmployeeRequestWorkflowService $workflowService;
    private EmployeeRequestRepository $requestRepository;
    private SalaryAdvanceRepository $salaryAdvanceRepository;
    private DocumentRequestRepository $documentRequestRepository;

    public function __construct(
        EmployeeRequestWorkflowService $workflowService,
        EmployeeRequestRepository $requestRepository,
        SalaryAdvanceRepository $salaryAdvanceRepository,
        DocumentRequestRepository $documentRequestRepository
    ) {
        $this->workflowService = $workflowService;
        $this->requestRepository = $requestRepository;
        $this->salaryAdvanceRepository = $salaryAdvanceRepository;
        $this->documentRequestRepository = $documentRequestRepository;
    }

    #[Route('/', name: 'app_hr_requests_dashboard', methods: ['GET'])]
    public function dashboard(): Response
    {
        // Statistiques générales
        $stats = $this->requestRepository->getStatistics();
        
        // Demandes urgentes
        $urgentRequests = $this->requestRepository->findUrgentRequests();
        
        // Demandes en attente
        $pendingRequests = $this->requestRepository->findBy(['status' => 'pending'], ['createdAt' => 'ASC'], 10);
        
        // Demandes récentes
        $recentRequests = $this->requestRepository->findRecentRequests(7);
        
        // Statistiques avances
        $salaryAdvanceStats = $this->salaryAdvanceRepository->getStatistics();
        
        // Demandes de documents urgentes
        $urgentDocuments = $this->documentRequestRepository->findUrgentRequests();

        return $this->render('hr_requests/dashboard.html.twig', [
            'stats' => $stats,
            'urgent_requests' => $urgentRequests,
            'pending_requests' => $pendingRequests,
            'recent_requests' => $recentRequests,
            'salary_advance_stats' => $salaryAdvanceStats,
            'urgent_documents' => $urgentDocuments
        ]);
    }

    #[Route('/list', name: 'app_hr_requests_list', methods: ['GET'])]
    public function list(Request $request): Response
    {
        $status = $request->query->get('status');
        $type = $request->query->get('type');
        $employee = $request->query->get('employee');
        $priority = $request->query->get('priority');
        $search = $request->query->get('search');

        // Construction de la requête avec filtres
        $criteria = [];
        if ($status) {
            $criteria['status'] = $status;
        }
        if ($type) {
            $criteria['type'] = $type;
        }
        if ($priority) {
            $criteria['priority'] = $priority;
        }

        $requests = $this->requestRepository->findBy($criteria, ['createdAt' => 'DESC']);

        // Filtrage par recherche si nécessaire
        if ($search) {
            $requests = $this->requestRepository->searchRequests($search);
        }

        return $this->render('hr_requests/list.html.twig', [
            'requests' => $requests,
            'current_status' => $status,
            'current_type' => $type,
            'current_priority' => $priority,
            'current_search' => $search
        ]);
    }

    #[Route('/{id}', name: 'app_hr_requests_show', methods: ['GET'])]
    public function show(EmployeeRequest $request): Response
    {
        return $this->render('hr_requests/show.html.twig', [
            'request' => $request
        ]);
    }

    #[Route('/{id}/approve', name: 'app_hr_requests_approve', methods: ['POST'])]
    public function approve(EmployeeRequest $request, Request $httpRequest): Response
    {
        $user = $this->getUser();
        $comment = $httpRequest->request->get('comment');

        if ($this->workflowService->approveRequest($request, $user, $comment)) {
            $this->addFlash('success', 'Demande approuvée avec succès.');
        } else {
            $this->addFlash('error', 'Impossible d\'approuver cette demande.');
        }

        return $this->redirectToRoute('app_hr_requests_show', ['id' => $request->getId()]);
    }

    #[Route('/{id}/reject', name: 'app_hr_requests_reject', methods: ['POST'])]
    public function reject(EmployeeRequest $request, Request $httpRequest): Response
    {
        $user = $this->getUser();
        $reason = $httpRequest->request->get('reason');

        if (!$reason) {
            $this->addFlash('error', 'Une raison de rejet est obligatoire.');
            return $this->redirectToRoute('app_hr_requests_show', ['id' => $request->getId()]);
        }

        if ($this->workflowService->rejectRequest($request, $user, $reason)) {
            $this->addFlash('success', 'Demande rejetée avec succès.');
        } else {
            $this->addFlash('error', 'Impossible de rejeter cette demande.');
        }

        return $this->redirectToRoute('app_hr_requests_show', ['id' => $request->getId()]);
    }

    #[Route('/pending', name: 'app_hr_requests_pending', methods: ['GET'])]
    public function pending(): Response
    {
        $pendingRequests = $this->requestRepository->findBy(
            ['status' => ['pending', 'manager_approved', 'hr_review']], 
            ['priority' => 'DESC', 'createdAt' => 'ASC']
        );

        return $this->render('hr_requests/pending.html.twig', [
            'requests' => $pendingRequests
        ]);
    }

    #[Route('/salary-advances', name: 'app_hr_requests_salary_advances', methods: ['GET'])]
    public function salaryAdvances(): Response
    {
        $pendingAdvances = $this->salaryAdvanceRepository->findPendingApproval();
        $readyForPayment = $this->salaryAdvanceRepository->findReadyForPayment();
        $inRepayment = $this->salaryAdvanceRepository->findInRepayment();
        $stats = $this->salaryAdvanceRepository->getStatistics();

        return $this->render('hr_requests/salary_advances.html.twig', [
            'pending_advances' => $pendingAdvances,
            'ready_for_payment' => $readyForPayment,
            'in_repayment' => $inRepayment,
            'stats' => $stats
        ]);
    }

    #[Route('/documents', name: 'app_hr_requests_documents', methods: ['GET'])]
    public function documents(): Response
    {
        $urgentDocuments = $this->documentRequestRepository->findUrgentRequests();
        $pendingDocuments = $this->documentRequestRepository->findBy(['status' => 'pending'], ['createdAt' => 'ASC']);
        $readyDocuments = $this->documentRequestRepository->findReadyForDelivery();
        $stats = $this->documentRequestRepository->getStatistics();

        return $this->render('hr_requests/documents.html.twig', [
            'urgent_documents' => $urgentDocuments,
            'pending_documents' => $pendingDocuments,
            'ready_documents' => $readyDocuments,
            'stats' => $stats
        ]);
    }

    #[Route('/statistics', name: 'app_hr_requests_statistics', methods: ['GET'])]
    public function statistics(): Response
    {
        $requestStats = $this->requestRepository->getStatistics();
        $salaryAdvanceStats = $this->salaryAdvanceRepository->getStatistics();
        $documentStats = $this->documentRequestRepository->getStatistics();
        $workflowStats = $this->workflowService->getWorkflowStatistics();

        return $this->render('hr_requests/statistics.html.twig', [
            'request_stats' => $requestStats,
            'salary_advance_stats' => $salaryAdvanceStats,
            'document_stats' => $documentStats,
            'workflow_stats' => $workflowStats
        ]);
    }

    #[Route('/api/stats', name: 'app_hr_requests_api_stats', methods: ['GET'])]
    public function apiStats(): JsonResponse
    {
        $stats = [
            'requests' => $this->requestRepository->getStatistics(),
            'salary_advances' => $this->salaryAdvanceRepository->getStatistics(),
            'documents' => $this->documentRequestRepository->getStatistics(),
            'workflow' => $this->workflowService->getWorkflowStatistics()
        ];

        return $this->json($stats);
    }

    #[Route('/bulk-action', name: 'app_hr_requests_bulk_action', methods: ['POST'])]
    public function bulkAction(Request $request): Response
    {
        $action = $request->request->get('action');
        $requestIds = $request->request->all('request_ids');
        $user = $this->getUser();

        if (!$requestIds || !$action) {
            $this->addFlash('error', 'Aucune demande sélectionnée ou action non spécifiée.');
            return $this->redirectToRoute('app_hr_requests_list');
        }

        $successCount = 0;
        $errorCount = 0;

        foreach ($requestIds as $requestId) {
            $employeeRequest = $this->requestRepository->find($requestId);
            if (!$employeeRequest) {
                $errorCount++;
                continue;
            }

            $success = false;
            switch ($action) {
                case 'approve':
                    $success = $this->workflowService->approveRequest($employeeRequest, $user, 'Approbation en masse');
                    break;
                case 'reject':
                    $reason = $request->request->get('bulk_reason', 'Rejet en masse');
                    $success = $this->workflowService->rejectRequest($employeeRequest, $user, $reason);
                    break;
            }

            if ($success) {
                $successCount++;
            } else {
                $errorCount++;
            }
        }

        if ($successCount > 0) {
            $this->addFlash('success', "$successCount demande(s) traitée(s) avec succès.");
        }
        if ($errorCount > 0) {
            $this->addFlash('warning', "$errorCount demande(s) n'ont pas pu être traitées.");
        }

        return $this->redirectToRoute('app_hr_requests_list');
    }
}
