<?php

namespace App\Controller;

use App\Entity\Notification;
use App\Entity\Partner;
use App\Entity\ProjectTask;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class HomeController extends AbstractController
{
    #[Route('/', name: 'app_home')]
    public function index(): Response
    {
        // Redirect to dashboard if user is logged in
        if ($this->getUser()) {
            return $this->redirectToRoute('app_dashboard');
        }

        return $this->render('home/index.html.twig', [
            'controller_name' => 'HomeController',
        ]);
    }

    #[Route('/dashboard', name: 'app_dashboard')]
    #[IsGranted('ROLE_USER')]
    public function dashboard(ManagerRegistry $doctrine, SessionInterface $session): Response
    {
        // Get counts from repositories
        $partnerCount = $doctrine->getRepository(Partner::class)->count([]);
        $taskCount = $doctrine->getRepository(ProjectTask::class)->count(['status' => 'pending']);
        $notificationCount = $doctrine->getRepository(Notification::class)->count(['isRead' => false, 'userId' => $this->getUser()->getUserIdentifier()]);

        // Store in session for reuse
        $session->set('partner_count', $partnerCount);
        $session->set('task_count', $taskCount);
        $session->set('notification_count', $notificationCount);

        return $this->render('home/dashboard.html.twig', [
            'controller_name' => 'HomeController',
        ]);
    }
}
