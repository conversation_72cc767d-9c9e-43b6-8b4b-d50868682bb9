<?php

namespace App\Controller;

use App\Entity\Integration;
use App\Form\GoogleCalendarIntegrationType;
use App\Form\MicrosoftTeamsIntegrationType;
use App\Repository\IntegrationRepository;
use App\Service\GoogleCalendarService;
use App\Service\MicrosoftTeamsService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/integration')]
#[IsGranted('IS_AUTHENTICATED_FULLY')]
class IntegrationController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private IntegrationRepository $integrationRepository
    ) {
    }
    
    #[Route('/', name: 'app_integration_index')]
    public function index(): Response
    {
        $integrations = $this->integrationRepository->findByUser($this->getUser());
        
        return $this->render('integration/index.html.twig', [
            'integrations' => $integrations,
        ]);
    }
    
    #[Route('/google-calendar', name: 'app_integration_google_calendar')]
    public function googleCalendar(
        Request $request,
        GoogleCalendarService $googleCalendarService
    ): Response {
        $integration = $this->integrationRepository->findOneBy([
            'user' => $this->getUser(),
            'type' => 'google_calendar',
        ]) ?? new Integration();
        
        $integration->setUser($this->getUser());
        $integration->setType('google_calendar');
        
        $form = $this->createForm(GoogleCalendarIntegrationType::class, $integration);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            // Test connection
            $isConnected = $googleCalendarService->testConnection($integration);
            
            if ($isConnected) {
                $integration->setIsActive(true);
                $integration->setLastSyncAt(new \DateTimeImmutable());
                
                $this->entityManager->persist($integration);
                $this->entityManager->flush();
                
                $this->addFlash('success', 'Intégration avec Google Calendar configurée avec succès.');
                
                return $this->redirectToRoute('app_integration_index');
            } else {
                $this->addFlash('error', 'Impossible de se connecter à Google Calendar. Veuillez vérifier vos informations d\'identification.');
            }
        }
        
        return $this->render('integration/google_calendar.html.twig', [
            'form' => $form->createView(),
            'integration' => $integration,
        ]);
    }
    
    #[Route('/microsoft-teams', name: 'app_integration_microsoft_teams')]
    public function microsoftTeams(
        Request $request,
        MicrosoftTeamsService $microsoftTeamsService
    ): Response {
        $integration = $this->integrationRepository->findOneBy([
            'user' => $this->getUser(),
            'type' => 'microsoft_teams',
        ]) ?? new Integration();
        
        $integration->setUser($this->getUser());
        $integration->setType('microsoft_teams');
        
        $form = $this->createForm(MicrosoftTeamsIntegrationType::class, $integration);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            // Test connection
            $isConnected = $microsoftTeamsService->testConnection($integration);
            
            if ($isConnected) {
                $integration->setIsActive(true);
                $integration->setLastSyncAt(new \DateTimeImmutable());
                
                $this->entityManager->persist($integration);
                $this->entityManager->flush();
                
                $this->addFlash('success', 'Intégration avec Microsoft Teams configurée avec succès.');
                
                return $this->redirectToRoute('app_integration_index');
            } else {
                $this->addFlash('error', 'Impossible de se connecter à Microsoft Teams. Veuillez vérifier vos informations d\'identification.');
            }
        }
        
        return $this->render('integration/microsoft_teams.html.twig', [
            'form' => $form->createView(),
            'integration' => $integration,
        ]);
    }
    
    #[Route('/{id}/disable', name: 'app_integration_disable')]
    public function disable(Integration $integration): Response
    {
        // Check if the integration belongs to the current user
        if ($integration->getUser() !== $this->getUser()) {
            throw $this->createAccessDeniedException('Vous n\'êtes pas autorisé à désactiver cette intégration.');
        }
        
        $integration->setIsActive(false);
        
        $this->entityManager->persist($integration);
        $this->entityManager->flush();
        
        $this->addFlash('success', 'Intégration désactivée avec succès.');
        
        return $this->redirectToRoute('app_integration_index');
    }
    
    #[Route('/{id}/delete', name: 'app_integration_delete')]
    public function delete(Integration $integration): Response
    {
        // Check if the integration belongs to the current user
        if ($integration->getUser() !== $this->getUser()) {
            throw $this->createAccessDeniedException('Vous n\'êtes pas autorisé à supprimer cette intégration.');
        }
        
        $this->entityManager->remove($integration);
        $this->entityManager->flush();
        
        $this->addFlash('success', 'Intégration supprimée avec succès.');
        
        return $this->redirectToRoute('app_integration_index');
    }
}
