<?php

namespace App\Controller;

use App\Entity\Invoice;
use App\Entity\InvoiceApproval;
use App\Entity\InvoiceItem;
use App\Entity\InvoicePayment;
use App\Entity\PurchaseOrder;
use App\Form\InvoiceApprovalForm;
use App\Form\InvoiceForm;
use App\Form\InvoiceItemForm;
use App\Form\InvoicePaymentForm;
use App\Repository\InvoiceRepository;
use App\Repository\UserRepository;
use App\Service\InvoiceAccountingService;
use App\Service\InvoiceService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/invoice')]
#[IsGranted('ROLE_USER')]
class InvoiceController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private InvoiceRepository $invoiceRepository;
    private InvoiceService $invoiceService;
    private UserRepository $userRepository;
    private InvoiceAccountingService $invoiceAccountingService;

    public function __construct(
        EntityManagerInterface $entityManager,
        InvoiceRepository $invoiceRepository,
        InvoiceService $invoiceService,
        UserRepository $userRepository,
        InvoiceAccountingService $invoiceAccountingService
    ) {
        $this->entityManager = $entityManager;
        $this->invoiceRepository = $invoiceRepository;
        $this->invoiceService = $invoiceService;
        $this->userRepository = $userRepository;
        $this->invoiceAccountingService = $invoiceAccountingService;
    }

    #[Route('/', name: 'app_invoice_index', methods: ['GET'])]
    public function index(): Response
    {
        $invoices = $this->invoiceRepository->findBy([], ['invoiceDate' => 'DESC']);

        return $this->render('invoice/index.html.twig', [
            'invoices' => $invoices,
        ]);
    }



    #[Route('/new', name: 'app_invoice_new', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function new(Request $request): Response
    {
        $invoice = new Invoice();
        $invoice->setCreatedBy($this->getUser());

        // Add an empty item
        $item = new InvoiceItem();
        $invoice->addItem($item);

        $form = $this->createForm(InvoiceForm::class, $invoice);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->invoiceService->createInvoice($invoice);

                $this->addFlash('success', 'Facture créée avec succès.');

                return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création de la facture : ' . $e->getMessage());
            }
        }

        return $this->render('invoice/new.html.twig', [
            'invoice' => $invoice,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/from-purchase-order/{id}', name: 'app_invoice_new_from_purchase_order', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function newFromPurchaseOrder(Request $request, PurchaseOrder $purchaseOrder): Response
    {
        // Check if the purchase order can be invoiced
        if (!in_array($purchaseOrder->getStatus(), [PurchaseOrder::STATUS_APPROVED, PurchaseOrder::STATUS_PARTIALLY_RECEIVED, PurchaseOrder::STATUS_FULLY_RECEIVED])) {
            $this->addFlash('error', 'Le bon de commande doit être approuvé ou reçu pour pouvoir créer une facture.');
            return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
        }

        try {
            $invoice = $this->invoiceService->createInvoiceFromPurchaseOrder($purchaseOrder);

            $this->addFlash('success', 'Facture créée avec succès à partir du bon de commande.');

            return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Erreur lors de la création de la facture : ' . $e->getMessage());
            return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
        }
    }

    #[Route('/{id}', name: 'app_invoice_show', methods: ['GET'])]
    public function show(Invoice $invoice): Response
    {
        // Check if the invoice has accounting entries
        $hasAccountingEntries = $this->invoiceAccountingService->hasInvoiceEntries($invoice);
        $accountingEntries = $hasAccountingEntries ? $this->invoiceAccountingService->getInvoiceEntries($invoice) : [];

        // Get payment accounting entries
        $paymentAccountingEntries = [];
        foreach ($invoice->getPayments() as $payment) {
            if ($this->invoiceAccountingService->hasPaymentEntries($payment)) {
                $paymentAccountingEntries[$payment->getId()] = $this->invoiceAccountingService->getPaymentEntries($payment);
            }
        }

        return $this->render('invoice/show.html.twig', [
            'invoice' => $invoice,
            'hasAccountingEntries' => $hasAccountingEntries,
            'accountingEntries' => $accountingEntries,
            'paymentAccountingEntries' => $paymentAccountingEntries
        ]);
    }

    #[Route('/{id}/edit', name: 'app_invoice_edit', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function edit(Request $request, Invoice $invoice): Response
    {
        // Check if the invoice can be edited
        if (!in_array($invoice->getStatus(), [Invoice::STATUS_DRAFT, Invoice::STATUS_REJECTED])) {
            $this->addFlash('error', 'Seules les factures en brouillon ou rejetées peuvent être modifiées.');
            return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
        }

        $form = $this->createForm(InvoiceForm::class, $invoice);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->invoiceService->updateInvoice($invoice);

                $this->addFlash('success', 'Facture mise à jour avec succès.');

                return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour de la facture : ' . $e->getMessage());
            }
        }

        return $this->render('invoice/edit.html.twig', [
            'invoice' => $invoice,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/add-item', name: 'app_invoice_add_item', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function addItem(Request $request, Invoice $invoice): Response
    {
        // Check if the invoice can be edited
        if (!in_array($invoice->getStatus(), [Invoice::STATUS_DRAFT, Invoice::STATUS_REJECTED])) {
            $this->addFlash('error', 'Seules les factures en brouillon ou rejetées peuvent être modifiées.');
            return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
        }

        $item = new InvoiceItem();
        $item->setInvoice($invoice);

        $form = $this->createForm(InvoiceItemForm::class, $item);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $invoice->addItem($item);
                $this->invoiceService->updateInvoice($invoice);

                $this->addFlash('success', 'Ligne ajoutée avec succès.');

                return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'ajout de la ligne : ' . $e->getMessage());
            }
        }

        return $this->render('invoice/add_item.html.twig', [
            'invoice' => $invoice,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/remove-item/{itemId}', name: 'app_invoice_remove_item', methods: ['POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function removeItem(Request $request, Invoice $invoice, int $itemId): Response
    {
        // Check if the invoice can be edited
        if (!in_array($invoice->getStatus(), [Invoice::STATUS_DRAFT, Invoice::STATUS_REJECTED])) {
            $this->addFlash('error', 'Seules les factures en brouillon ou rejetées peuvent être modifiées.');
            return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
        }

        if ($this->isCsrfTokenValid('delete-item'.$itemId, $request->request->get('_token'))) {
            try {
                $item = $this->entityManager->getRepository(InvoiceItem::class)->find($itemId);

                if (!$item || $item->getInvoice() !== $invoice) {
                    throw new \Exception('Ligne non trouvée.');
                }

                $invoice->removeItem($item);
                $this->entityManager->remove($item);
                $this->invoiceService->updateInvoice($invoice);

                $this->addFlash('success', 'Ligne supprimée avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression de la ligne : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
    }

    #[Route('/{id}/submit', name: 'app_invoice_submit', methods: ['POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function submit(Request $request, Invoice $invoice): Response
    {
        // Check if the invoice can be submitted
        if (!in_array($invoice->getStatus(), [Invoice::STATUS_DRAFT, Invoice::STATUS_REJECTED])) {
            $this->addFlash('error', 'Seules les factures en brouillon ou rejetées peuvent être soumises pour approbation.');
            return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
        }

        if ($this->isCsrfTokenValid('submit'.$invoice->getId(), $request->request->get('_token'))) {
            try {
                $this->invoiceService->submitInvoiceForApproval($invoice);

                $this->addFlash('success', 'Facture soumise pour approbation avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la soumission de la facture : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
    }

    #[Route('/{id}/approve', name: 'app_invoice_approve', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_INVOICE_APPROVER')]
    public function approve(Request $request, Invoice $invoice): Response
    {
        // Check if the invoice can be approved
        if ($invoice->getStatus() !== Invoice::STATUS_PENDING_APPROVAL) {
            $this->addFlash('error', 'Seules les factures en attente d\'approbation peuvent être approuvées.');
            return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
        }

        $approval = new InvoiceApproval();
        $approval->setInvoice($invoice);
        $approval->setUser($this->getUser());
        $approval->setAction(InvoiceApproval::ACTION_APPROVE);

        $form = $this->createForm(InvoiceApprovalForm::class, $approval, [
            'action_choices' => [
                'Approuver' => InvoiceApproval::ACTION_APPROVE,
                'Rejeter' => InvoiceApproval::ACTION_REJECT,
                'Demander des modifications' => InvoiceApproval::ACTION_REQUEST_CHANGES,
                'Déléguer' => InvoiceApproval::ACTION_DELEGATE,
            ],
        ]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $action = $approval->getAction();

                switch ($action) {
                    case InvoiceApproval::ACTION_APPROVE:
                        $this->invoiceService->approveInvoice($invoice, $approval->getComments());

                        // Create accounting entries for the approved invoice
                        try {
                            $this->invoiceAccountingService->createInvoiceEntries($invoice, $this->getUser());
                            $this->addFlash('success', 'Facture approuvée et écritures comptables créées avec succès.');
                        } catch (\Exception $e) {
                            $this->addFlash('warning', 'Facture approuvée mais une erreur est survenue lors de la création des écritures comptables : ' . $e->getMessage());
                        }
                        break;

                    case InvoiceApproval::ACTION_REJECT:
                        if (!$approval->getComments()) {
                            throw new \Exception('Veuillez fournir une raison pour le rejet.');
                        }
                        $this->invoiceService->rejectInvoice($invoice, $approval->getComments());
                        $this->addFlash('success', 'Facture rejetée avec succès.');
                        break;

                    case InvoiceApproval::ACTION_REQUEST_CHANGES:
                        if (!$approval->getComments()) {
                            throw new \Exception('Veuillez fournir des commentaires sur les modifications demandées.');
                        }
                        $this->invoiceService->requestChangesToInvoice($invoice, $approval->getComments());
                        $this->addFlash('success', 'Demande de modifications envoyée avec succès.');
                        break;

                    case InvoiceApproval::ACTION_DELEGATE:
                        if (!$approval->getDelegatedTo()) {
                            throw new \Exception('Veuillez sélectionner un utilisateur à qui déléguer l\'approbation.');
                        }
                        $this->invoiceService->delegateInvoiceApproval($invoice, $approval->getDelegatedTo(), $approval->getComments());
                        $this->addFlash('success', 'Approbation déléguée avec succès.');
                        break;
                }

                return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur : ' . $e->getMessage());
            }
        }

        return $this->render('invoice/approve.html.twig', [
            'invoice' => $invoice,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/record-payment', name: 'app_invoice_record_payment', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function recordPayment(Request $request, Invoice $invoice): Response
    {
        // Check if the invoice can be paid
        if (!in_array($invoice->getStatus(), [Invoice::STATUS_APPROVED, Invoice::STATUS_PARTIALLY_PAID])) {
            $this->addFlash('error', 'Seules les factures approuvées ou partiellement payées peuvent recevoir un paiement.');
            return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
        }

        $payment = new InvoicePayment();
        $payment->setInvoice($invoice);

        // Set default amount to remaining amount
        $payment->setAmount($invoice->getRemainingAmount());

        $form = $this->createForm(InvoicePaymentForm::class, $payment);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->invoiceService->recordPayment($invoice, $payment);

                // Create accounting entries for the payment
                try {
                    $this->invoiceAccountingService->createPaymentEntries($payment, $this->getUser());
                    $this->addFlash('success', 'Paiement enregistré et écritures comptables créées avec succès.');
                } catch (\Exception $e) {
                    $this->addFlash('warning', 'Paiement enregistré mais une erreur est survenue lors de la création des écritures comptables : ' . $e->getMessage());
                }

                return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'enregistrement du paiement : ' . $e->getMessage());
            }
        }

        return $this->render('invoice/record_payment.html.twig', [
            'invoice' => $invoice,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/cancel', name: 'app_invoice_cancel', methods: ['POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function cancel(Request $request, Invoice $invoice): Response
    {
        // Check if the invoice can be cancelled
        if (in_array($invoice->getStatus(), [Invoice::STATUS_PAID, Invoice::STATUS_CANCELLED])) {
            $this->addFlash('error', 'Les factures payées ou déjà annulées ne peuvent pas être annulées.');
            return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
        }

        if ($this->isCsrfTokenValid('cancel'.$invoice->getId(), $request->request->get('_token'))) {
            try {
                $reason = $request->request->get('reason');
                $this->invoiceService->cancelInvoice($invoice, $reason);

                // Create accounting entries for the cancelled invoice if it had accounting entries
                if ($this->invoiceAccountingService->hasInvoiceEntries($invoice)) {
                    try {
                        $this->invoiceAccountingService->createCancelledInvoiceEntries($invoice, $this->getUser());
                        $this->addFlash('success', 'Facture annulée et écritures comptables d\'annulation créées avec succès.');
                    } catch (\Exception $e) {
                        $this->addFlash('warning', 'Facture annulée mais une erreur est survenue lors de la création des écritures comptables d\'annulation : ' . $e->getMessage());
                    }
                } else {
                    $this->addFlash('success', 'Facture annulée avec succès.');
                }
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'annulation de la facture : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
    }

    #[Route('/{id}/dispute', name: 'app_invoice_dispute', methods: ['POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function dispute(Request $request, Invoice $invoice): Response
    {
        // Check if the invoice can be disputed
        if (in_array($invoice->getStatus(), [Invoice::STATUS_DRAFT, Invoice::STATUS_CANCELLED, Invoice::STATUS_DISPUTED])) {
            $this->addFlash('error', 'Les factures en brouillon, annulées ou déjà contestées ne peuvent pas être contestées.');
            return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
        }

        if ($this->isCsrfTokenValid('dispute'.$invoice->getId(), $request->request->get('_token'))) {
            try {
                $reason = $request->request->get('reason');

                if (!$reason) {
                    throw new \Exception('Veuillez fournir une raison pour la contestation.');
                }

                $this->invoiceService->disputeInvoice($invoice, $reason);

                $this->addFlash('success', 'Facture contestée avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la contestation de la facture : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
    }

    #[Route('/{id}/upload-document', name: 'app_invoice_upload_document', methods: ['POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function uploadDocument(Request $request, Invoice $invoice): Response
    {
        if ($this->isCsrfTokenValid('upload'.$invoice->getId(), $request->request->get('_token'))) {
            /** @var UploadedFile $file */
            $file = $request->files->get('document');

            if (!$file) {
                $this->addFlash('error', 'Aucun fichier sélectionné.');
                return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
            }

            try {
                $this->invoiceService->uploadInvoiceDocument($invoice, $file);

                $this->addFlash('success', 'Document téléchargé avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors du téléchargement du document : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
    }

    #[Route('/{id}/sign', name: 'app_invoice_sign', methods: ['POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function sign(Request $request, Invoice $invoice): Response
    {
        if ($this->isCsrfTokenValid('sign'.$invoice->getId(), $request->request->get('_token'))) {
            try {
                $this->invoiceService->signInvoiceElectronically($invoice);

                $this->addFlash('success', 'Facture signée électroniquement avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la signature électronique : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
    }

    #[Route('/{id}/record-supplier-signature', name: 'app_invoice_record_supplier_signature', methods: ['POST'])]
    #[IsGranted('ROLE_FINANCE')]
    public function recordSupplierSignature(Request $request, Invoice $invoice): Response
    {
        if ($this->isCsrfTokenValid('supplier-sign'.$invoice->getId(), $request->request->get('_token'))) {
            try {
                $this->invoiceService->recordSupplierElectronicSignature($invoice);

                $this->addFlash('success', 'Signature électronique du fournisseur enregistrée avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'enregistrement de la signature : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_invoice_show', ['id' => $invoice->getId()]);
    }


}
