<?php

namespace App\Controller;

use App\Repository\InvoiceRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/invoice-status')]
class InvoiceStatusController extends AbstractController
{
    public function __construct(
        private InvoiceRepository $invoiceRepository
    ) {
    }

    #[Route('/pending-approval', name: 'app_invoice_status_pending_approval', methods: ['GET'])]
    #[IsGranted('ROLE_USER')]
    public function pendingApproval(): Response
    {
        $invoices = $this->invoiceRepository->findPendingApprovalForUser($this->getUser());

        return $this->render('invoice/pending_approval.html.twig', [
            'invoices' => $invoices,
        ]);
    }

    #[Route('/overdue', name: 'app_invoice_status_overdue', methods: ['GET'])]
    #[IsGranted('ROLE_FINANCE')]
    public function overdue(): Response
    {
        $invoices = $this->invoiceRepository->findOverdue();

        return $this->render('invoice/overdue.html.twig', [
            'invoices' => $invoices,
        ]);
    }

    #[Route('/due-soon', name: 'app_invoice_status_due_soon', methods: ['GET'])]
    #[IsGranted('ROLE_FINANCE')]
    public function dueSoon(Request $request): Response
    {
        $days = $request->query->getInt('days', 7);
        $invoices = $this->invoiceRepository->findDueSoon($days);

        return $this->render('invoice/due_soon.html.twig', [
            'invoices' => $invoices,
            'days' => $days,
        ]);
    }
}
