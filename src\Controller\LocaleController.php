<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class LocaleController extends AbstractController
{
    #[Route('/change-locale/{locale}', name: 'app_change_locale')]
    public function changeLocale(Request $request, string $locale): Response
    {
        // Vérifier si la locale est valide
        $validLocales = ['fr', 'en', 'es', 'ar'];
        
        if (!in_array($locale, $validLocales)) {
            $locale = 'fr'; // Locale par défaut
        }
        
        // Stocker la locale dans la session
        $request->getSession()->set('_locale', $locale);
        
        // Rediriger vers la page précédente
        $referer = $request->headers->get('referer');
        
        return $this->redirect($referer ?: $this->generateUrl('app_home'));
    }
}
