<?php

namespace App\Controller;

use App\Repository\MedicalRecordRepository;
use App\Service\MedicalExaminationService;
use App\Service\MedicalRecordService;
use App\Service\MedicalStatisticsService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr')]
#[IsGranted('ROLE_HR')]
class MedicalDashboardController extends AbstractController
{
    private MedicalRecordService $medicalRecordService;
    private MedicalExaminationService $medicalExaminationService;
    private MedicalStatisticsService $medicalStatisticsService;

    public function __construct(
        MedicalRecordService $medicalRecordService,
        MedicalExaminationService $medicalExaminationService,
        MedicalStatisticsService $medicalStatisticsService
    ) {
        $this->medicalRecordService = $medicalRecordService;
        $this->medicalExaminationService = $medicalExaminationService;
        $this->medicalStatisticsService = $medicalStatisticsService;
    }

    #[Route('/medical-dashboard', name: 'app_medical_dashboard')]
    public function index(MedicalRecordRepository $medicalRecordRepository): Response
    {
        // Get statistics
        $totalMedicalRecords = count($medicalRecordRepository->findAll());
        $upcomingExaminations = $this->medicalExaminationService->getUpcomingExaminations();
        $overdueExaminations = $this->medicalExaminationService->getOverdueExaminations();
        
        // Get charts data
        $examinationsByMonth = $this->medicalStatisticsService->getMedicalExaminationsByMonth();
        $examinationsByType = $this->medicalStatisticsService->getMedicalExaminationsByType();
        $examinationsByStatus = $this->medicalStatisticsService->getMedicalExaminationsByResult();
        
        return $this->render('medical_dashboard/index.html.twig', [
            'total_medical_records' => $totalMedicalRecords,
            'upcoming_examinations' => $upcomingExaminations,
            'overdue_examinations' => $overdueExaminations,
            'examinations_by_month' => $examinationsByMonth,
            'examinations_by_type' => $examinationsByType,
            'examinations_by_status' => $examinationsByStatus,
        ]);
    }
}
