<?php

namespace App\Controller;

use App\Entity\MedicalDocument;
use App\Entity\MedicalRecord;
use App\Form\MedicalDocumentForm;
use App\Repository\MedicalDocumentRepository;
use App\Service\MedicalDocumentService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/medical-document')]
#[IsGranted('ROLE_HR')]
class MedicalDocumentController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private MedicalDocumentService $medicalDocumentService;
    private string $uploadDirectory;

    public function __construct(
        EntityManagerInterface $entityManager,
        MedicalDocumentService $medicalDocumentService,
        string $uploadDirectory
    ) {
        $this->entityManager = $entityManager;
        $this->medicalDocumentService = $medicalDocumentService;
        $this->uploadDirectory = $uploadDirectory;
    }

    #[Route('/', name: 'app_medical_document_index')]
    public function index(MedicalDocumentRepository $medicalDocumentRepository): Response
    {
        $medicalDocuments = $medicalDocumentRepository->findBy([], ['documentDate' => 'DESC']);
        $confidentialDocuments = $this->medicalDocumentService->getConfidentialDocuments();
        
        return $this->render('medical_document/index.html.twig', [
            'medical_documents' => $medicalDocuments,
            'confidential_documents' => $confidentialDocuments,
            'can_view_confidential' => $this->medicalDocumentService->canViewConfidentialMedicalDocuments()
        ]);
    }

    #[Route('/new', name: 'app_medical_document_new')]
    public function new(Request $request): Response
    {
        $medicalDocument = new MedicalDocument();
        
        $form = $this->createForm(MedicalDocumentForm::class, $medicalDocument);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $file = $form->get('documentFile')->getData();
                
                if (!$file) {
                    throw new \Exception('Veuillez télécharger un fichier');
                }
                
                $this->medicalDocumentService->createMedicalDocument($medicalDocument, $file);
                
                $this->addFlash('success', 'Document médical créé avec succès');
                return $this->redirectToRoute('app_medical_document_show', ['id' => $medicalDocument->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du document médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_document/new.html.twig', [
            'medical_document' => $medicalDocument,
            'form' => $form
        ]);
    }

    #[Route('/medical-record/{id}/new', name: 'app_medical_document_new_for_record')]
    public function newForRecord(Request $request, MedicalRecord $medicalRecord): Response
    {
        $medicalDocument = new MedicalDocument();
        $medicalDocument->setMedicalRecord($medicalRecord);
        
        $form = $this->createForm(MedicalDocumentForm::class, $medicalDocument, [
            'medical_record' => $medicalRecord
        ]);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $file = $form->get('documentFile')->getData();
                
                if (!$file) {
                    throw new \Exception('Veuillez télécharger un fichier');
                }
                
                $this->medicalDocumentService->createMedicalDocument($medicalDocument, $file);
                
                $this->addFlash('success', 'Document médical créé avec succès');
                return $this->redirectToRoute('app_medical_record_show', ['id' => $medicalRecord->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du document médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_document/new.html.twig', [
            'medical_document' => $medicalDocument,
            'medical_record' => $medicalRecord,
            'form' => $form
        ]);
    }

    #[Route('/medical-record/{id}', name: 'app_medical_document_by_record')]
    public function byRecord(MedicalRecord $medicalRecord): Response
    {
        $medicalDocuments = $this->medicalDocumentService->getMedicalDocumentsByRecord($medicalRecord);
        
        // Check if user can view confidential documents
        $canViewConfidential = $this->isGranted('ROLE_ADMIN') || $this->isGranted('ROLE_MEDICAL_ADMIN');
        
        return $this->render('medical_document/by_record.html.twig', [
            'medical_documents' => $medicalDocuments,
            'medical_record' => $medicalRecord,
            'can_view_confidential' => $canViewConfidential
        ]);
    }

    #[Route('/type/{documentType}', name: 'app_medical_document_by_type')]
    public function byType(string $documentType): Response
    {
        $medicalDocuments = $this->medicalDocumentService->getMedicalDocumentsByType($documentType);
        
        // Check if user can view confidential documents
        $canViewConfidential = $this->isGranted('ROLE_ADMIN') || $this->isGranted('ROLE_MEDICAL_ADMIN');
        
        return $this->render('medical_document/by_type.html.twig', [
            'medical_documents' => $medicalDocuments,
            'document_type' => $documentType,
            'can_view_confidential' => $canViewConfidential
        ]);
    }

    #[Route('/search', name: 'app_medical_document_search', methods: ['GET'])]
    public function search(Request $request): Response
    {
        $keyword = $request->query->get('keyword');
        $medicalDocuments = [];
        
        if ($keyword) {
            $medicalDocuments = $this->medicalDocumentService->searchMedicalDocumentsByKeyword($keyword);
        }
        
        // Check if user can view confidential documents
        $canViewConfidential = $this->isGranted('ROLE_ADMIN') || $this->isGranted('ROLE_MEDICAL_ADMIN');
        
        return $this->render('medical_document/search.html.twig', [
            'medical_documents' => $medicalDocuments,
            'keyword' => $keyword,
            'can_view_confidential' => $canViewConfidential
        ]);
    }

    #[Route('/{id}', name: 'app_medical_document_show', methods: ['GET'], requirements: ['id' => '\d+'])]
    public function show(MedicalDocument $medicalDocument): Response
    {
        // Check if user can view confidential documents
        $canViewConfidential = $this->isGranted('ROLE_ADMIN') || $this->isGranted('ROLE_MEDICAL_ADMIN');
        
        if ($medicalDocument->isIsConfidential() && !$canViewConfidential) {
            throw $this->createAccessDeniedException('Vous n\'êtes pas autorisé à voir ce document confidentiel.');
        }
        
        return $this->render('medical_document/show.html.twig', [
            'medical_document' => $medicalDocument
        ]);
    }

    #[Route('/{id}/edit', name: 'app_medical_document_edit', methods: ['GET', 'POST'], requirements: ['id' => '\d+'])]
    public function edit(Request $request, MedicalDocument $medicalDocument): Response
    {
        // Check if user can edit confidential documents
        $canEditConfidential = $this->isGranted('ROLE_ADMIN') || $this->isGranted('ROLE_MEDICAL_ADMIN');
        
        if ($medicalDocument->isIsConfidential() && !$canEditConfidential) {
            throw $this->createAccessDeniedException('Vous n\'êtes pas autorisé à modifier ce document confidentiel.');
        }
        
        $form = $this->createForm(MedicalDocumentForm::class, $medicalDocument);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $file = $form->has('documentFile') ? $form->get('documentFile')->getData() : null;
                
                $this->medicalDocumentService->updateMedicalDocument($medicalDocument, $file);
                
                $this->addFlash('success', 'Document médical mis à jour avec succès');
                return $this->redirectToRoute('app_medical_document_show', ['id' => $medicalDocument->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour du document médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_document/edit.html.twig', [
            'medical_document' => $medicalDocument,
            'form' => $form
        ]);
    }

    #[Route('/{id}/delete', name: 'app_medical_document_delete', methods: ['GET', 'POST'], requirements: ['id' => '\d+'])]
    public function delete(Request $request, MedicalDocument $medicalDocument): Response
    {
        // Check if user can delete confidential documents
        $canDeleteConfidential = $this->isGranted('ROLE_ADMIN') || $this->isGranted('ROLE_MEDICAL_ADMIN');
        
        if ($medicalDocument->isIsConfidential() && !$canDeleteConfidential) {
            throw $this->createAccessDeniedException('Vous n\'êtes pas autorisé à supprimer ce document confidentiel.');
        }
        
        if ($request->isMethod('POST')) {
            try {
                $medicalRecordId = $medicalDocument->getMedicalRecord()->getId();
                $this->medicalDocumentService->deleteMedicalDocument($medicalDocument);
                
                $this->addFlash('success', 'Document médical supprimé avec succès');
                
                // Redirect to medical record if coming from there
                if ($request->query->has('from_record')) {
                    return $this->redirectToRoute('app_medical_record_show', ['id' => $medicalRecordId]);
                }
                
                return $this->redirectToRoute('app_medical_document_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression du document médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_document/delete.html.twig', [
            'medical_document' => $medicalDocument
        ]);
    }

    #[Route('/{id}/download', name: 'app_medical_document_download', requirements: ['id' => '\d+'])]
    public function download(MedicalDocument $medicalDocument): Response
    {
        // Check if user can download confidential documents
        $canDownloadConfidential = $this->isGranted('ROLE_ADMIN') || $this->isGranted('ROLE_MEDICAL_ADMIN');
        
        if ($medicalDocument->isIsConfidential() && !$canDownloadConfidential) {
            throw $this->createAccessDeniedException('Vous n\'êtes pas autorisé à télécharger ce document confidentiel.');
        }
        
        $filePath = $this->getParameter('medical_documents_directory') . '/' . $medicalDocument->getFilename();
        
        if (!file_exists($filePath)) {
            throw $this->createNotFoundException('Le fichier demandé n\'existe pas.');
        }
        
        $response = new BinaryFileResponse($filePath);
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $medicalDocument->getTitle() . '.' . pathinfo($medicalDocument->getFilename(), PATHINFO_EXTENSION)
        );
        
        return $response;
    }
}
