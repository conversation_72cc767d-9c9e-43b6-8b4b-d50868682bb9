<?php

namespace App\Controller;

use App\Entity\MedicalExamination;
use App\Entity\MedicalRecord;
use App\Form\MedicalExaminationCancelForm;
use App\Form\MedicalExaminationCompleteForm;
use App\Form\MedicalExaminationForm;
use App\Form\MedicalExaminationRescheduleForm;
use App\Service\MedicalExaminationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/medical-examination')]
#[IsGranted('ROLE_HR')]
class MedicalExaminationController extends AbstractController
{
    private MedicalExaminationService $medicalExaminationService;

    public function __construct(MedicalExaminationService $medicalExaminationService)
    {
        $this->medicalExaminationService = $medicalExaminationService;
    }

    #[Route('/', name: 'app_medical_examination_index')]
    public function index(): Response
    {
        $medicalExaminations = $this->medicalExaminationService->getAllMedicalExaminations();
        
        return $this->render('medical_examination/index.html.twig', [
            'medical_examinations' => $medicalExaminations
        ]);
    }

    #[Route('/new', name: 'app_medical_examination_new')]
    public function new(Request $request): Response
    {
        $medicalExamination = new MedicalExamination();
        
        $form = $this->createForm(MedicalExaminationForm::class, $medicalExamination);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Create medical examination
                $this->medicalExaminationService->createMedicalExamination($medicalExamination);
                
                $this->addFlash('success', 'Examen médical créé avec succès');
                return $this->redirectToRoute('app_medical_examination_show', ['id' => $medicalExamination->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création de l\'examen médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_examination/new.html.twig', [
            'medical_examination' => $medicalExamination,
            'form' => $form
        ]);
    }

    #[Route('/medical-record/{id}/new', name: 'app_medical_examination_new_for_record')]
    public function newForMedicalRecord(Request $request, MedicalRecord $medicalRecord): Response
    {
        $medicalExamination = new MedicalExamination();
        $medicalExamination->setMedicalRecord($medicalRecord);
        
        $form = $this->createForm(MedicalExaminationForm::class, $medicalExamination, [
            'medical_record' => $medicalRecord
        ]);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Create medical examination
                $this->medicalExaminationService->createMedicalExamination($medicalExamination);
                
                $this->addFlash('success', 'Examen médical créé avec succès');
                return $this->redirectToRoute('app_medical_record_show', ['id' => $medicalRecord->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création de l\'examen médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_examination/new.html.twig', [
            'medical_examination' => $medicalExamination,
            'form' => $form,
            'medical_record' => $medicalRecord
        ]);
    }

    #[Route('/{id}', name: 'app_medical_examination_show', methods: ['GET'])]
    public function show(MedicalExamination $medicalExamination): Response
    {
        return $this->render('medical_examination/show.html.twig', [
            'medical_examination' => $medicalExamination
        ]);
    }

    #[Route('/{id}/edit', name: 'app_medical_examination_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, MedicalExamination $medicalExamination): Response
    {
        $form = $this->createForm(MedicalExaminationForm::class, $medicalExamination, [
            'medical_record' => $medicalExamination->getMedicalRecord()
        ]);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Update medical examination
                $this->medicalExaminationService->updateMedicalExamination($medicalExamination);
                
                $this->addFlash('success', 'Examen médical mis à jour avec succès');
                return $this->redirectToRoute('app_medical_examination_show', ['id' => $medicalExamination->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour de l\'examen médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_examination/edit.html.twig', [
            'medical_examination' => $medicalExamination,
            'form' => $form
        ]);
    }
    
    #[Route('/{id}/complete', name: 'app_medical_examination_complete', methods: ['GET', 'POST'])]
    public function complete(Request $request, MedicalExamination $medicalExamination): Response
    {
        $form = $this->createForm(MedicalExaminationCompleteForm::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Complete medical examination
                $this->medicalExaminationService->completeMedicalExamination($medicalExamination, $form->get('results')->getData());
                
                $this->addFlash('success', 'Examen médical terminé avec succès');
                return $this->redirectToRoute('app_medical_examination_show', ['id' => $medicalExamination->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la complétion de l\'examen médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_examination/complete.html.twig', [
            'medical_examination' => $medicalExamination,
            'form' => $form
        ]);
    }
    
    #[Route('/{id}/cancel', name: 'app_medical_examination_cancel', methods: ['GET', 'POST'])]
    public function cancel(Request $request, MedicalExamination $medicalExamination): Response
    {
        $form = $this->createForm(MedicalExaminationCancelForm::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Cancel medical examination
                $this->medicalExaminationService->cancelMedicalExamination($medicalExamination, $form->get('reason')->getData());
                
                $this->addFlash('success', 'Examen médical annulé avec succès');
                return $this->redirectToRoute('app_medical_examination_show', ['id' => $medicalExamination->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'annulation de l\'examen médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_examination/cancel.html.twig', [
            'medical_examination' => $medicalExamination,
            'form' => $form
        ]);
    }
    
    #[Route('/{id}/reschedule', name: 'app_medical_examination_reschedule', methods: ['GET', 'POST'])]
    public function reschedule(Request $request, MedicalExamination $medicalExamination): Response
    {
        $form = $this->createForm(MedicalExaminationRescheduleForm::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Reschedule medical examination
                $this->medicalExaminationService->rescheduleMedicalExamination($medicalExamination, $form->get('scheduledDate')->getData());
                
                $this->addFlash('success', 'Examen médical reprogrammé avec succès');
                return $this->redirectToRoute('app_medical_examination_show', ['id' => $medicalExamination->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la reprogrammation de l\'examen médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_examination/reschedule.html.twig', [
            'medical_examination' => $medicalExamination,
            'form' => $form
        ]);
    }
    
    #[Route('/{id}/delete', name: 'app_medical_examination_delete', methods: ['GET', 'POST'])]
    public function delete(Request $request, MedicalExamination $medicalExamination): Response
    {
        if ($this->isCsrfTokenValid('delete'.$medicalExamination->getId(), $request->request->get('_token'))) {
            try {
                $medicalRecord = $medicalExamination->getMedicalRecord();
                
                // Delete medical examination
                $this->medicalExaminationService->deleteMedicalExamination($medicalExamination);
                
                $this->addFlash('success', 'Examen médical supprimé avec succès');
                
                if ($medicalRecord) {
                    return $this->redirectToRoute('app_medical_record_show', ['id' => $medicalRecord->getId()]);
                }
                
                return $this->redirectToRoute('app_medical_examination_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression de l\'examen médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_examination/delete.html.twig', [
            'medical_examination' => $medicalExamination
        ]);
    }
    
    #[Route('/medical-record/{id}', name: 'app_medical_examination_by_record')]
    public function byMedicalRecord(MedicalRecord $medicalRecord): Response
    {
        $medicalExaminations = $this->medicalExaminationService->getMedicalExaminationsByMedicalRecord($medicalRecord);
        
        return $this->render('medical_examination/by_record.html.twig', [
            'medical_record' => $medicalRecord,
            'medical_examinations' => $medicalExaminations
        ]);
    }
    
    #[Route('/upcoming', name: 'app_medical_examination_upcoming')]
    public function upcoming(Request $request): Response
    {
        $days = $request->query->getInt('days', 30);
        
        if ($days > 0) {
            $upcomingExaminations = $this->medicalExaminationService->getUpcomingExaminationsForNextDays($days);
        } else {
            $upcomingExaminations = $this->medicalExaminationService->getUpcomingExaminations();
        }
        
        return $this->render('medical_examination/upcoming.html.twig', [
            'upcoming_examinations' => $upcomingExaminations,
            'days' => $days
        ]);
    }
    
    #[Route('/overdue', name: 'app_medical_examination_overdue')]
    public function overdue(): Response
    {
        $overdueExaminations = $this->medicalExaminationService->getOverdueExaminations();
        
        return $this->render('medical_examination/overdue.html.twig', [
            'overdue_examinations' => $overdueExaminations
        ]);
    }
    
    #[Route('/send-reminders', name: 'app_medical_examination_send_reminders')]
    #[IsGranted('ROLE_ADMIN')]
    public function sendReminders(Request $request): Response
    {
        $count = 0;
        $success = false;
        $error = null;
        
        if ($request->isMethod('POST')) {
            try {
                // Get examinations that need reminders
                $examinations = $this->medicalExaminationService->getExaminationsNeedingReminders();
                $count = count($examinations);
                
                // Send reminders
                foreach ($examinations as $examination) {
                    // Mark as reminder sent
                    $examination->setReminderSent(true);
                    $this->medicalExaminationService->updateMedicalExamination($examination);
                }
                
                $success = true;
                $this->addFlash('success', "Rappels envoyés avec succès pour $count examens médicaux.");
            } catch (\Exception $e) {
                $error = $e->getMessage();
                $this->addFlash('error', 'Erreur lors de l\'envoi des rappels : ' . $error);
            }
        }
        
        // Get examinations that need reminders for display
        $examinations = $this->medicalExaminationService->getExaminationsNeedingReminders();
        
        return $this->render('medical_examination/send_reminders.html.twig', [
            'examinations' => $examinations,
            'count' => $count,
            'success' => $success,
            'error' => $error
        ]);
    }
}
