<?php

namespace App\Controller;

use App\Form\MedicalRecordSearchForm;
use App\Repository\MedicalRecordRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/medical-record')]
#[IsGranted('ROLE_HR')]
class MedicalRecordAdvancedSearchController extends AbstractController
{
    #[Route('/advanced-search', name: 'app_medical_record_advanced_search')]
    public function advancedSearch(Request $request, MedicalRecordRepository $medicalRecordRepository): Response
    {
        $form = $this->createForm(MedicalRecordSearchForm::class);
        $form->handleRequest($request);
        
        $medicalRecords = [];
        $filtersApplied = false;
        $activeFilters = [];
        
        if ($form->isSubmitted() && $form->isValid() || count($request->query->all()) > 0) {
            // Get search criteria from form or query parameters
            $criteria = $form->getData() ?: $request->query->all();
            
            // Check if any filters are applied
            foreach ($criteria as $key => $value) {
                if (!empty($value) && $key !== 'sortBy') {
                    $filtersApplied = true;
                    
                    // Format the filter label and value for display
                    $filterLabel = $this->formatFilterLabel($key);
                    $filterValue = $this->formatFilterValue($key, $value);
                    
                    if ($filterLabel && $filterValue) {
                        $activeFilters[$filterLabel] = $filterValue;
                    }
                }
            }
            
            // Perform the search
            $medicalRecords = $medicalRecordRepository->advancedSearch($criteria);
        }
        
        return $this->render('medical_record/advanced_search.html.twig', [
            'form' => $form,
            'medical_records' => $medicalRecords,
            'filters_applied' => $filtersApplied,
            'active_filters' => $activeFilters
        ]);
    }
    
    #[Route('/export', name: 'app_medical_record_export')]
    public function export(Request $request, MedicalRecordRepository $medicalRecordRepository): Response
    {
        // Get search criteria from query parameters
        $criteria = $request->query->all();
        
        // Perform the search
        $medicalRecords = $medicalRecordRepository->advancedSearch($criteria);
        
        // Generate CSV content
        $csvContent = $this->generateCsvContent($medicalRecords);
        
        // Create response
        $response = new Response($csvContent);
        $response->headers->set('Content-Type', 'text/csv; charset=utf-8');
        $response->headers->set('Content-Disposition', 'attachment; filename="medical_records_export.csv"');
        
        return $response;
    }
    
    /**
     * Format filter label for display
     */
    private function formatFilterLabel(string $key): ?string
    {
        $labels = [
            'keyword' => 'Mot-clé',
            'recordType' => 'Type de dossier',
            'status' => 'Statut',
            'bloodGroup' => 'Groupe sanguin',
            'employee' => 'Employé',
            'personType' => 'Type de personne',
            'startDate' => 'Date de début',
            'endDate' => 'Date de fin',
            'hasExaminations' => 'Examens médicaux',
            'hasDocuments' => 'Documents médicaux'
        ];
        
        return $labels[$key] ?? null;
    }
    
    /**
     * Format filter value for display
     */
    private function formatFilterValue(string $key, $value): ?string
    {
        if ($key === 'employee' && is_object($value)) {
            return $value->getUser()->getFullName();
        }
        
        if ($key === 'startDate' || $key === 'endDate') {
            return $value->format('d/m/Y');
        }
        
        if ($key === 'personType') {
            return $value === 'employee' ? 'Employé' : 'Membre de famille';
        }
        
        if ($key === 'recordType') {
            $types = [
                'general' => 'Général',
                'chronic' => 'Chronique',
                'emergency' => 'Urgence',
                'preventive' => 'Préventif',
                'occupational' => 'Professionnel',
                'specialist' => 'Spécialiste',
                'other' => 'Autre'
            ];
            
            return $types[$value] ?? $value;
        }
        
        if ($key === 'status') {
            $statuses = [
                'active' => 'Actif',
                'archived' => 'Archivé',
                'pending' => 'En attente'
            ];
            
            return $statuses[$value] ?? $value;
        }
        
        if ($key === 'hasExaminations' || $key === 'hasDocuments') {
            return $value ? 'Oui' : 'Non';
        }
        
        return (string)$value;
    }
    
    /**
     * Generate CSV content from medical records
     */
    private function generateCsvContent(array $medicalRecords): string
    {
        $headers = [
            'ID',
            'Titre',
            'Personne',
            'Type',
            'Date',
            'Statut',
            'Groupe sanguin',
            'Description',
            'Examens',
            'Documents'
        ];
        
        $rows = [];
        $rows[] = implode(',', $headers);
        
        foreach ($medicalRecords as $record) {
            $row = [
                $record->getId(),
                '"' . str_replace('"', '""', $record->getTitle()) . '"',
                '"' . str_replace('"', '""', $record->getPersonName()) . '"',
                $record->getRecordType(),
                $record->getRecordDate()->format('Y-m-d'),
                $record->getStatus(),
                $record->getBloodGroup() ?: 'N/A',
                '"' . str_replace('"', '""', $record->getDescription()) . '"',
                count($record->getMedicalExaminations()),
                count($record->getMedicalDocuments())
            ];
            
            $rows[] = implode(',', $row);
        }
        
        return implode("\n", $rows);
    }
}
