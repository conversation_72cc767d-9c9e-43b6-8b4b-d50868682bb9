<?php

namespace App\Controller;

use App\Entity\MedicalRecord;
use App\Form\MedicalRecordForm;
use App\Repository\EmployeeRepository;
use App\Repository\FamilyMemberRepository;
use App\Repository\MedicalRecordRepository;
use App\Service\MedicalRecordService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/medical-record')]
#[IsGranted('ROLE_HR')]
class MedicalRecordController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private MedicalRecordService $medicalRecordService;
    private EmployeeRepository $employeeRepository;
    private FamilyMemberRepository $familyMemberRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        MedicalRecordService $medicalRecordService,
        EmployeeRepository $employeeRepository,
        FamilyMemberRepository $familyMemberRepository
    ) {
        $this->entityManager = $entityManager;
        $this->medicalRecordService = $medicalRecordService;
        $this->employeeRepository = $employeeRepository;
        $this->familyMemberRepository = $familyMemberRepository;
    }

    #[Route('/', name: 'app_medical_record_index')]
    public function index(MedicalRecordRepository $medicalRecordRepository): Response
    {
        $medicalRecords = $medicalRecordRepository->findAll();
        
        return $this->render('medical_record/index.html.twig', [
            'medical_records' => $medicalRecords
        ]);
    }

    #[Route('/new', name: 'app_medical_record_new')]
    public function new(Request $request): Response
    {
        $medicalRecord = new MedicalRecord();
        
        $form = $this->createForm(MedicalRecordForm::class, $medicalRecord);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Create medical record
                $this->medicalRecordService->createMedicalRecord($medicalRecord);
                
                $this->addFlash('success', 'Dossier médical créé avec succès');
                return $this->redirectToRoute('app_medical_record_show', ['id' => $medicalRecord->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du dossier médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_record/new.html.twig', [
            'medical_record' => $medicalRecord,
            'form' => $form
        ]);
    }

    #[Route('/employee/{id}', name: 'app_medical_record_new_for_employee')]
    public function newForEmployee(Request $request, int $id): Response
    {
        $employee = $this->employeeRepository->find($id);
        
        if (!$employee) {
            throw $this->createNotFoundException('Employé non trouvé');
        }
        
        $medicalRecord = new MedicalRecord();
        $medicalRecord->setEmployee($employee);
        $medicalRecord->setTitle('Dossier médical - ' . $employee->getFullName());
        
        $form = $this->createForm(MedicalRecordForm::class, $medicalRecord, [
            'employee' => $employee
        ]);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Create medical record
                $this->medicalRecordService->createMedicalRecord($medicalRecord);
                
                $this->addFlash('success', 'Dossier médical créé avec succès');
                return $this->redirectToRoute('app_medical_record_show', ['id' => $medicalRecord->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du dossier médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_record/new.html.twig', [
            'medical_record' => $medicalRecord,
            'form' => $form,
            'employee' => $employee
        ]);
    }

    #[Route('/family-member/{id}', name: 'app_medical_record_new_for_family_member')]
    public function newForFamilyMember(Request $request, int $id): Response
    {
        $familyMember = $this->familyMemberRepository->find($id);
        
        if (!$familyMember) {
            throw $this->createNotFoundException('Membre de famille non trouvé');
        }
        
        $medicalRecord = new MedicalRecord();
        $medicalRecord->setFamilyMember($familyMember);
        $medicalRecord->setTitle('Dossier médical - ' . $familyMember->getFullName());
        
        $form = $this->createForm(MedicalRecordForm::class, $medicalRecord, [
            'family_member' => $familyMember
        ]);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Create medical record
                $this->medicalRecordService->createMedicalRecord($medicalRecord);
                
                $this->addFlash('success', 'Dossier médical créé avec succès');
                return $this->redirectToRoute('app_medical_record_show', ['id' => $medicalRecord->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du dossier médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_record/new.html.twig', [
            'medical_record' => $medicalRecord,
            'form' => $form,
            'family_member' => $familyMember
        ]);
    }

    #[Route('/{id}', name: 'app_medical_record_show', methods: ['GET'])]
    public function show(MedicalRecord $medicalRecord): Response
    {
        // Get medical examinations
        $medicalExaminations = $this->medicalRecordService->getMedicalExaminationsByMedicalRecord($medicalRecord);
        
        // Get medical documents
        $medicalDocuments = $this->medicalRecordService->getMedicalDocumentsByMedicalRecord($medicalRecord);
        
        return $this->render('medical_record/show.html.twig', [
            'medical_record' => $medicalRecord,
            'medical_examinations' => $medicalExaminations,
            'medical_documents' => $medicalDocuments
        ]);
    }

    #[Route('/{id}/edit', name: 'app_medical_record_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, MedicalRecord $medicalRecord): Response
    {
        $form = $this->createForm(MedicalRecordForm::class, $medicalRecord, [
            'employee' => $medicalRecord->getEmployee(),
            'family_member' => $medicalRecord->getFamilyMember()
        ]);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Update medical record
                $this->medicalRecordService->updateMedicalRecord($medicalRecord);
                
                $this->addFlash('success', 'Dossier médical mis à jour avec succès');
                return $this->redirectToRoute('app_medical_record_show', ['id' => $medicalRecord->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour du dossier médical : ' . $e->getMessage());
            }
        }
        
        return $this->render('medical_record/edit.html.twig', [
            'medical_record' => $medicalRecord,
            'form' => $form
        ]);
    }

    #[Route('/{id}/delete', name: 'app_medical_record_delete', methods: ['POST'])]
    public function delete(Request $request, MedicalRecord $medicalRecord): Response
    {
        if ($this->isCsrfTokenValid('delete'.$medicalRecord->getId(), $request->request->get('_token'))) {
            try {
                // Delete medical record
                $this->medicalRecordService->deleteMedicalRecord($medicalRecord);
                
                $this->addFlash('success', 'Dossier médical supprimé avec succès');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression du dossier médical : ' . $e->getMessage());
            }
        }
        
        return $this->redirectToRoute('app_medical_record_index');
    }

    #[Route('/examinations/upcoming', name: 'app_medical_record_upcoming_examinations')]
    public function upcomingExaminations(): Response
    {
        $upcomingExaminations = $this->medicalRecordService->getUpcomingExaminations();
        
        return $this->render('medical_record/upcoming_examinations.html.twig', [
            'upcoming_examinations' => $upcomingExaminations
        ]);
    }

    #[Route('/search', name: 'app_medical_record_search')]
    public function search(Request $request, MedicalRecordRepository $medicalRecordRepository, MedicalRecordService $medicalRecordService): Response
    {
        $keyword = $request->query->get('keyword');
        $recordType = $request->query->get('recordType');
        $status = $request->query->get('status');
        $bloodGroup = $request->query->get('bloodGroup');
        
        $medicalRecords = [];
        
        if ($keyword || $recordType || $status || $bloodGroup) {
            // Perform search
            $medicalRecords = $medicalRecordService->searchMedicalRecords($keyword, $recordType, $status, $bloodGroup);
        }
        
        return $this->render('medical_record/search.html.twig', [
            'medical_records' => $medicalRecords,
            'keyword' => $keyword,
            'recordType' => $recordType,
            'status' => $status,
            'bloodGroup' => $bloodGroup
        ]);
    }
}
