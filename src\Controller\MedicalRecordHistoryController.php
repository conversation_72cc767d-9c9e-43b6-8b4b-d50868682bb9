<?php

namespace App\Controller;

use App\Entity\MedicalRecord;
use App\Entity\User;
use App\Repository\MedicalRecordHistoryRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/medical-record')]
#[IsGranted('ROLE_HR')]
class MedicalRecordHistoryController extends AbstractController
{
    #[Route('/{id}/history', name: 'app_medical_record_history')]
    public function history(MedicalRecord $medicalRecord, MedicalRecordHistoryRepository $historyRepository): Response
    {
        $historyEntries = $historyRepository->findByMedicalRecord($medicalRecord);
        
        return $this->render('medical_record/history.html.twig', [
            'medical_record' => $medicalRecord,
            'history_entries' => $historyEntries
        ]);
    }
    
    #[Route('/history/recent', name: 'app_medical_record_history_recent')]
    #[IsGranted('ROLE_ADMIN')]
    public function recentHistory(MedicalRecordHistoryRepository $historyRepository): Response
    {
        $historyEntries = $historyRepository->findRecent(50);
        
        return $this->render('medical_record/recent_history.html.twig', [
            'history_entries' => $historyEntries
        ]);
    }
    
    #[Route('/history/by-user/{id}', name: 'app_medical_record_history_by_user')]
    #[IsGranted('ROLE_ADMIN')]
    public function historyByUser(User $user, MedicalRecordHistoryRepository $historyRepository): Response
    {
        $historyEntries = $historyRepository->findByUser($user);
        
        return $this->render('medical_record/user_history.html.twig', [
            'user' => $user,
            'history_entries' => $historyEntries
        ]);
    }
}
