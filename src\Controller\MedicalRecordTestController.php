<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/medical-record-test')]
#[IsGranted('ROLE_HR')]
class MedicalRecordTestController extends AbstractController
{
    #[Route('/upcoming-examinations-test', name: 'app_medical_record_upcoming_examinations_test')]
    public function upcomingExaminationsTest(): Response
    {
        return $this->render('medical_record/upcoming_examinations_test.html.twig');
    }
}
