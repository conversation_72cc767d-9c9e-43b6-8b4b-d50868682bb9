<?php

namespace App\Controller;

use App\Service\MedicalRecordService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/medical-record-upcoming')]
#[IsGranted('ROLE_HR')]
class MedicalRecordUpcomingController extends AbstractController
{
    private MedicalRecordService $medicalRecordService;

    public function __construct(MedicalRecordService $medicalRecordService)
    {
        $this->medicalRecordService = $medicalRecordService;
    }

    #[Route('/', name: 'app_medical_record_upcoming_examinations_new')]
    public function upcomingExaminations(): Response
    {
        $medicalRecords = $this->medicalRecordService->getMedicalRecordsWithUpcomingExaminations();
        
        return $this->render('medical_record/upcoming_examinations.html.twig', [
            'medical_records' => $medicalRecords
        ]);
    }
}
