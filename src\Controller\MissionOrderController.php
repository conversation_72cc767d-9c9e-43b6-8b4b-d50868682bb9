<?php

namespace App\Controller;

use App\Entity\MissionOrder;
use App\Form\MissionOrderApprovalForm;
use App\Form\MissionOrderForm;
use App\Repository\EmployeeRepository;
use App\Repository\MissionOrderRepository;
use App\Service\MissionOrderService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/mission/order')]
#[IsGranted('ROLE_USER')]
class MissionOrderController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private MissionOrderService $missionOrderService;
    private EmployeeRepository $employeeRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        MissionOrderService $missionOrderService,
        EmployeeRepository $employeeRepository
    ) {
        $this->entityManager = $entityManager;
        $this->missionOrderService = $missionOrderService;
        $this->employeeRepository = $employeeRepository;
    }

    #[Route('/', name: 'app_mission_order_index')]
    public function index(MissionOrderRepository $missionOrderRepository): Response
    {
        $user = $this->getUser();
        
        // Get employee for current user
        $employee = $this->employeeRepository->findOneBy(['user' => $user]);
        
        // Get mission orders
        $missionOrders = [];
        $pendingApproval = [];
        $pendingFinance = [];
        
        if ($employee) {
            // Get mission orders for employee
            $missionOrders = $missionOrderRepository->findByEmployee($employee);
            
            // If manager, get pending approval
            if ($this->isGranted('ROLE_MANAGER')) {
                $pendingApproval = $this->missionOrderService->getMissionOrdersPendingApprovalByManager($employee);
            }
        } else {
            // For users without employee record, show mission orders they created
            $missionOrders = $missionOrderRepository->findByCreatedBy($user);
        }
        
        // If finance role, get pending finance approval
        if ($this->isGranted('ROLE_FINANCE')) {
            $pendingFinance = $this->missionOrderService->getMissionOrdersPendingFinanceApproval();
        }
        
        // If admin, get all mission orders
        if ($this->isGranted('ROLE_ADMIN')) {
            $missionOrders = $missionOrderRepository->findBy([], ['createdAt' => 'DESC']);
        }
        
        // Get statistics
        $statistics = $this->missionOrderService->getMissionOrderStatistics();
        
        return $this->render('mission_order/index.html.twig', [
            'mission_orders' => $missionOrders,
            'pending_approval' => $pendingApproval,
            'pending_finance' => $pendingFinance,
            'statistics' => $statistics,
            'can_create' => $employee !== null
        ]);
    }

    #[Route('/new', name: 'app_mission_order_new')]
    public function new(Request $request): Response
    {
        $user = $this->getUser();
        
        // Get employee for current user
        $employee = $this->employeeRepository->findOneBy(['user' => $user]);
        
        // Check if user has an employee record
        if (!$employee && !$this->isGranted('ROLE_ADMIN') && !$this->isGranted('ROLE_HR')) {
            $this->addFlash('error', 'Vous devez être un employé pour créer un ordre de mission');
            return $this->redirectToRoute('app_mission_order_index');
        }
        
        $missionOrder = new MissionOrder();
        
        // If not admin or HR, set employee to current user's employee
        if (!$this->isGranted('ROLE_ADMIN') && !$this->isGranted('ROLE_HR')) {
            $missionOrder->setEmployee($employee);
        }
        
        $form = $this->createForm(MissionOrderForm::class, $missionOrder);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Create mission order
                $this->missionOrderService->createMissionOrder($missionOrder);
                
                $this->addFlash('success', 'Ordre de mission créé avec succès');
                return $this->redirectToRoute('app_mission_order_show', ['id' => $missionOrder->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création de l\'ordre de mission : ' . $e->getMessage());
            }
        }
        
        return $this->render('mission_order/new.html.twig', [
            'mission_order' => $missionOrder,
            'form' => $form
        ]);
    }

    #[Route('/dashboard', name: 'app_mission_order_dashboard')]
    #[IsGranted('ROLE_MANAGER')]
    public function dashboard(): Response
    {
        $statistics = $this->missionOrderService->getMissionOrderStatistics();
        $pendingFinance = $this->missionOrderService->getMissionOrdersPendingFinanceApproval();
        
        return $this->render('mission_order/dashboard.html.twig', [
            'statistics' => $statistics,
            'pending_finance' => $pendingFinance
        ]);
    }

    #[Route('/{id}', name: 'app_mission_order_show', methods: ['GET'])]
    public function show(MissionOrder $missionOrder): Response
    {
        // Check if user can view this mission order
        $this->denyAccessUnlessGranted('view', $missionOrder);
        
        return $this->render('mission_order/show.html.twig', [
            'mission_order' => $missionOrder
        ]);
    }

    #[Route('/{id}/edit', name: 'app_mission_order_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, MissionOrder $missionOrder): Response
    {
        // Check if user can edit this mission order
        $this->denyAccessUnlessGranted('edit', $missionOrder);
        
        // Check if mission order can be edited
        if (!$missionOrder->canBeEdited()) {
            $this->addFlash('error', 'Cet ordre de mission ne peut pas être modifié');
            return $this->redirectToRoute('app_mission_order_show', ['id' => $missionOrder->getId()]);
        }
        
        $form = $this->createForm(MissionOrderForm::class, $missionOrder);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                // Update mission order
                $this->missionOrderService->updateMissionOrder($missionOrder);
                
                $this->addFlash('success', 'Ordre de mission mis à jour avec succès');
                return $this->redirectToRoute('app_mission_order_show', ['id' => $missionOrder->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour de l\'ordre de mission : ' . $e->getMessage());
            }
        }
        
        return $this->render('mission_order/edit.html.twig', [
            'mission_order' => $missionOrder,
            'form' => $form
        ]);
    }

    #[Route('/{id}/submit', name: 'app_mission_order_submit', methods: ['GET', 'POST'])]
    public function submit(Request $request, MissionOrder $missionOrder): Response
    {
        // Check if user can submit this mission order
        $this->denyAccessUnlessGranted('submit', $missionOrder);
        
        // Check if mission order can be submitted
        if (!$missionOrder->canBeSubmitted()) {
            $this->addFlash('error', 'Cet ordre de mission ne peut pas être soumis');
            return $this->redirectToRoute('app_mission_order_show', ['id' => $missionOrder->getId()]);
        }
        
        if ($request->isMethod('POST')) {
            try {
                // Submit mission order
                $this->missionOrderService->submitMissionOrder($missionOrder);
                
                $this->addFlash('success', 'Ordre de mission soumis avec succès');
                return $this->redirectToRoute('app_mission_order_show', ['id' => $missionOrder->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la soumission de l\'ordre de mission : ' . $e->getMessage());
            }
        }
        
        return $this->render('mission_order/submit.html.twig', [
            'mission_order' => $missionOrder
        ]);
    }

    #[Route('/{id}/approve', name: 'app_mission_order_approve', methods: ['GET', 'POST'])]
    public function approve(Request $request, MissionOrder $missionOrder): Response
    {
        $user = $this->getUser();
        $employee = $this->employeeRepository->findOneBy(['user' => $user]);
        
        // Check if user can approve this mission order
        if (!$employee) {
            $this->addFlash('error', 'Vous devez être un employé pour approuver un ordre de mission');
            return $this->redirectToRoute('app_mission_order_show', ['id' => $missionOrder->getId()]);
        }
        
        // Check if mission order can be approved
        $canApproveManager = $missionOrder->canBeApprovedByManager() && 
                            $missionOrder->getEmployee()->getManager() === $employee;
        
        $canApproveFinance = $missionOrder->canBeApprovedByFinance() && 
                            $this->isGranted('ROLE_FINANCE');
        
        if (!$canApproveManager && !$canApproveFinance) {
            $this->addFlash('error', 'Vous ne pouvez pas approuver cet ordre de mission');
            return $this->redirectToRoute('app_mission_order_show', ['id' => $missionOrder->getId()]);
        }
        
        $form = $this->createForm(MissionOrderApprovalForm::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            
            try {
                if ($data['decision'] === 'approve') {
                    if ($canApproveManager) {
                        // Approve by manager
                        $this->missionOrderService->approveByManager($missionOrder, $employee);
                        $this->addFlash('success', 'Ordre de mission approuvé avec succès');
                    } elseif ($canApproveFinance) {
                        // Approve by finance
                        $this->missionOrderService->approveByFinance($missionOrder, $employee);
                        $this->addFlash('success', 'Avance approuvée avec succès');
                    }
                } else {
                    // Reject
                    $this->missionOrderService->rejectMissionOrder($missionOrder, $data['rejectionReason'], $user);
                    $this->addFlash('success', 'Ordre de mission refusé');
                }
                
                return $this->redirectToRoute('app_mission_order_show', ['id' => $missionOrder->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'approbation de l\'ordre de mission : ' . $e->getMessage());
            }
        }
        
        return $this->render('mission_order/approve.html.twig', [
            'mission_order' => $missionOrder,
            'form' => $form,
            'is_manager_approval' => $canApproveManager,
            'is_finance_approval' => $canApproveFinance
        ]);
    }

    #[Route('/{id}/cancel', name: 'app_mission_order_cancel', methods: ['GET', 'POST'])]
    public function cancel(Request $request, MissionOrder $missionOrder): Response
    {
        // Check if user can cancel this mission order
        $this->denyAccessUnlessGranted('cancel', $missionOrder);
        
        // Check if mission order can be cancelled
        if (!$missionOrder->canBeCancelled()) {
            $this->addFlash('error', 'Cet ordre de mission ne peut pas être annulé');
            return $this->redirectToRoute('app_mission_order_show', ['id' => $missionOrder->getId()]);
        }
        
        if ($request->isMethod('POST')) {
            try {
                // Cancel mission order
                $this->missionOrderService->cancelMissionOrder($missionOrder);
                
                $this->addFlash('success', 'Ordre de mission annulé avec succès');
                return $this->redirectToRoute('app_mission_order_show', ['id' => $missionOrder->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'annulation de l\'ordre de mission : ' . $e->getMessage());
            }
        }
        
        return $this->render('mission_order/cancel.html.twig', [
            'mission_order' => $missionOrder
        ]);
    }

    #[Route('/project/{projectId}', name: 'app_mission_order_by_project')]
    public function byProject(int $projectId, MissionOrderRepository $missionOrderRepository): Response
    {
        $missionOrders = $missionOrderRepository->findByProject($projectId);
        
        return $this->render('mission_order/by_project.html.twig', [
            'mission_orders' => $missionOrders,
            'project_id' => $projectId
        ]);
    }

    #[Route('/employee/{employeeId}', name: 'app_mission_order_by_employee')]
    public function byEmployee(int $employeeId): Response
    {
        $employee = $this->employeeRepository->find($employeeId);
        
        if (!$employee) {
            throw $this->createNotFoundException('Employé non trouvé');
        }
        
        $missionOrders = $this->missionOrderService->getMissionOrdersByEmployee($employee);
        
        return $this->render('mission_order/by_employee.html.twig', [
            'mission_orders' => $missionOrders,
            'employee' => $employee
        ]);
    }
}
