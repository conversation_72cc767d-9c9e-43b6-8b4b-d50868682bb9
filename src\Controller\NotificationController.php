<?php

namespace App\Controller;

use App\Entity\Notification;
use App\Repository\NotificationRepository;
use App\Service\NotificationService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/notification')]
final class NotificationController extends AbstractController
{
    private NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    #[Route(name: 'app_notification_index', methods: ['GET'])]
    public function index(NotificationRepository $notificationRepository): Response
    {
        // Get all notifications for the current user
        $notifications = $this->notificationService->getAllNotifications();
        
        return $this->render('notification/index.html.twig', [
            'notifications' => $notifications,
        ]);
    }

    #[Route('/unread', name: 'app_notification_unread', methods: ['GET'])]
    public function unread(): Response
    {
        // Get unread notifications for the current user
        $notifications = $this->notificationService->getUnreadNotifications();
        
        return $this->render('notification/index.html.twig', [
            'notifications' => $notifications,
            'unread_only' => true,
        ]);
    }

    #[Route('/{id}/mark-read', name: 'app_notification_mark_read', methods: ['POST'])]
    public function markAsRead(Request $request, Notification $notification): Response
    {
        if ($this->isCsrfTokenValid('mark_read'.$notification->getId(), $request->getPayload()->getString('_token'))) {
            $this->notificationService->markAsRead($notification);
            $this->addFlash('success', 'Notification marked as read.');
        }

        return $this->redirectToRoute('app_notification_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/mark-all-read', name: 'app_notification_mark_all_read', methods: ['POST'])]
    public function markAllAsRead(Request $request): Response
    {
        if ($this->isCsrfTokenValid('mark_all_read', $request->getPayload()->getString('_token'))) {
            $this->notificationService->markAllAsRead();
            $this->addFlash('success', 'All notifications marked as read.');
        }

        return $this->redirectToRoute('app_notification_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/{id}', name: 'app_notification_show', methods: ['GET'])]
    public function show(Notification $notification): Response
    {
        // Mark notification as read when viewed
        if (!$notification->isIsRead()) {
            $this->notificationService->markAsRead($notification);
        }
        
        return $this->render('notification/show.html.twig', [
            'notification' => $notification,
        ]);
    }

    #[Route('/{id}', name: 'app_notification_delete', methods: ['POST'])]
    public function delete(Request $request, Notification $notification, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$notification->getId(), $request->getPayload()->getString('_token'))) {
            $entityManager->remove($notification);
            $entityManager->flush();
            $this->addFlash('success', 'Notification deleted successfully.');
        }

        return $this->redirectToRoute('app_notification_index', [], Response::HTTP_SEE_OTHER);
    }
}
