<?php

namespace App\Controller;

use App\Service\NotificationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/notifications')]
#[IsGranted('ROLE_USER')]
class NotificationController extends AbstractController
{
    private NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    #[Route('/', name: 'app_notifications_index')]
    public function index(): Response
    {
        $user = $this->getUser();
        $notifications = $this->notificationService->getUnreadNotifications($user, 20);
        $unreadCount = $this->notificationService->getUnreadCount($user);

        return $this->render('notifications/index.html.twig', [
            'notifications' => $notifications,
            'unread_count' => $unreadCount,
        ]);
    }

    #[Route('/api/unread', name: 'app_notifications_api_unread', methods: ['GET'])]
    public function getUnread(Request $request): JsonResponse
    {
        $user = $this->getUser();
        $limit = $request->query->getInt('limit', 10);

        $notifications = $this->notificationService->getUnreadNotifications($user, $limit);
        $unreadCount = $this->notificationService->getUnreadCount($user);

        $data = [];
        foreach ($notifications as $notification) {
            $data[] = [
                'id' => $notification->getId(),
                'type' => $notification->getType(),
                'title' => $notification->getTitle(),
                'content' => $notification->getContent(),
                'priority' => $notification->getPriority(),
                'icon' => $notification->getIcon(),
                'color' => $notification->getColor(),
                'actionUrl' => $notification->getActionUrl(),
                'actionLabel' => $notification->getActionLabel(),
                'createdAt' => $notification->getCreatedAt()->format('c'),
                'data' => $notification->getData(),
                'link' => $notification->getLink(),
            ];
        }

        return $this->json([
            'notifications' => $data,
            'unreadCount' => $unreadCount,
            'success' => true
        ]);
    }

    #[Route('/api/count', name: 'app_notifications_api_count', methods: ['GET'])]
    public function getUnreadCount(): JsonResponse
    {
        $user = $this->getUser();
        $count = $this->notificationService->getUnreadCount($user);

        return $this->json([
            'count' => $count,
            'success' => true
        ]);
    }

    #[Route('/api/{id}/read', name: 'app_notifications_api_mark_read', methods: ['POST'])]
    public function markAsRead(int $id): JsonResponse
    {
        $user = $this->getUser();
        $success = $this->notificationService->markAsReadSimple($id, $user);

        if (!$success) {
            return $this->json([
                'success' => false,
                'message' => 'Notification non trouvée'
            ], 404);
        }

        $unreadCount = $this->notificationService->getUnreadCount($user);

        return $this->json([
            'success' => true,
            'unreadCount' => $unreadCount
        ]);
    }

    #[Route('/api/mark-all-read', name: 'app_notifications_api_mark_all_read', methods: ['POST'])]
    public function markAllAsRead(): JsonResponse
    {
        $user = $this->getUser();
        $readCount = $this->notificationService->markAllAsReadSimple($user);

        return $this->json([
            'success' => true,
            'readCount' => $readCount,
            'unreadCount' => 0
        ]);
    }

    #[Route('/test', name: 'app_notifications_test')]
    public function test(): Response
    {
        $user = $this->getUser();

        // Créer quelques notifications de test
        $this->notificationService->createSimpleNotification(
            $user,
            'project',
            'Mise à jour du projet Test',
            'Ceci est une notification de test pour un projet.'
        );

        $this->notificationService->createSimpleNotification(
            $user,
            'security',
            'Alerte de sécurité',
            'Tentative de connexion suspecte détectée depuis une nouvelle adresse IP.'
        );

        $this->notificationService->createSimpleNotification(
            $user,
            'financial',
            'Facture en retard',
            'La facture #12345 est en retard de paiement.'
        );

        $this->addFlash('success', 'Notifications de test créées avec succès !');

        return $this->redirectToRoute('app_notifications_index');
    }

    #[Route('/widget', name: 'app_notifications_widget')]
    public function widget(): Response
    {
        $user = $this->getUser();
        $notifications = $this->notificationService->getUnreadNotifications($user, 5);
        $unreadCount = $this->notificationService->getUnreadCount($user);

        return $this->render('notifications/widget.html.twig', [
            'notifications' => $notifications,
            'unread_count' => $unreadCount,
        ]);
    }
}
