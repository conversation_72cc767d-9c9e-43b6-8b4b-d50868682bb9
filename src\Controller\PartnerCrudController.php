<?php

namespace App\Controller;

use App\Entity\Partner;
use App\Form\PartnerForm;
use App\Repository\PartnerRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/partner/crud')]
final class PartnerCrudController extends AbstractController
{
    #[Route(name: 'app_partner_crud_index', methods: ['GET'])]
    public function index(PartnerRepository $partnerRepository): Response
    {
        return $this->render('partner_crud/index.html.twig', [
            'partners' => $partnerRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_partner_crud_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $partner = new Partner();
        // Définir la date de création
        $partner->setCreatedAt(new \DateTimeImmutable());

        $form = $this->createForm(PartnerForm::class, $partner);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Définir les attributs personnalisés comme un tableau vide s'ils ne sont pas définis
            if ($partner->getCustomAttributes() === null) {
                $partner->setCustomAttributes([]);
            }

            $entityManager->persist($partner);
            $entityManager->flush();

            $this->addFlash('success', 'Le partenaire a été créé avec succès.');
            return $this->redirectToRoute('app_partner_crud_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('partner_crud/new.html.twig', [
            'partner' => $partner,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partner_crud_show', methods: ['GET'])]
    public function show(Partner $partner): Response
    {
        return $this->render('partner_crud/show.html.twig', [
            'partner' => $partner,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_partner_crud_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Partner $partner, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PartnerForm::class, $partner);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Mettre à jour la date de modification
            $partner->setUpdatedAt(new \DateTimeImmutable());

            // Définir les attributs personnalisés comme un tableau vide s'ils ne sont pas définis
            if ($partner->getCustomAttributes() === null) {
                $partner->setCustomAttributes([]);
            }

            $entityManager->flush();

            $this->addFlash('success', 'Le partenaire a été modifié avec succès.');
            return $this->redirectToRoute('app_partner_crud_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('partner_crud/edit.html.twig', [
            'partner' => $partner,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partner_crud_delete', methods: ['POST'])]
    public function delete(Request $request, Partner $partner, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$partner->getId(), $request->getPayload()->getString('_token'))) {
            $entityManager->remove($partner);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_partner_crud_index', [], Response::HTTP_SEE_OTHER);
    }
}
