<?php

namespace App\Controller;

use App\Form\PartnerSearchForm;
use App\Service\PartnerSearchService;
use App\Service\PartnerService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/partner/dashboard')]
class PartnerDashboardController extends AbstractController
{
    private PartnerSearchService $partnerSearchService;
    private PartnerService $partnerService;

    public function __construct(
        PartnerSearchService $partnerSearchService,
        PartnerService $partnerService
    ) {
        $this->partnerSearchService = $partnerSearchService;
        $this->partnerService = $partnerService;
    }

    #[Route('/', name: 'app_partner_dashboard')]
    public function index(): Response
    {
        // Get partner statistics
        $statistics = $this->partnerSearchService->getPartnerStatistics();
        
        return $this->render('partner_dashboard/index.html.twig', [
            'statistics' => $statistics
        ]);
    }

    #[Route('/search', name: 'app_partner_search')]
    public function search(Request $request): Response
    {
        $form = $this->createForm(PartnerSearchForm::class);
        $form->handleRequest($request);
        
        $page = $request->query->getInt('page', 1);
        $limit = $request->query->getInt('limit', 10);
        
        $criteria = $form->getData() ?: [];
        
        $results = $this->partnerSearchService->searchPartners($criteria, $page, $limit);
        
        return $this->render('partner_dashboard/search.html.twig', [
            'form' => $form,
            'results' => $results,
            'criteria' => $criteria
        ]);
    }

    #[Route('/export', name: 'app_partner_export')]
    public function export(Request $request): Response
    {
        $criteria = $request->query->all();
        
        // Remove pagination and sorting parameters
        unset($criteria['page'], $criteria['limit']);
        
        // Get all partners matching the criteria
        $results = $this->partnerSearchService->searchPartners($criteria, 1, 1000);
        $partners = $results['partners'];
        
        // Generate CSV content
        $csvContent = $this->generateCsvContent($partners);
        
        // Create response with CSV content
        $response = new Response($csvContent);
        $response->headers->set('Content-Type', 'text/csv; charset=utf-8');
        $response->headers->set('Content-Disposition', 'attachment; filename="partners_export_' . date('Y-m-d') . '.csv"');
        
        return $response;
    }
    
    /**
     * Generate CSV content from partners
     */
    private function generateCsvContent(array $partners): string
    {
        $headers = [
            'ID',
            'Nom',
            'Email',
            'Téléphone',
            'Statut',
            'Type',
            'Nature',
            'Structure',
            'Créé le',
            'Mis à jour le'
        ];
        
        $rows = [];
        $rows[] = implode(',', $headers);
        
        foreach ($partners as $partner) {
            $row = [
                $partner->getId(),
                $this->escapeCsvField($partner->getName()),
                $this->escapeCsvField($partner->getEmail()),
                $this->escapeCsvField($partner->getPhone()),
                $partner->getStatus() ? $this->escapeCsvField($partner->getStatus()->getName()) : '',
                $partner->getType() ? $this->escapeCsvField($partner->getType()->getName()) : '',
                $partner->getNature() ? $this->escapeCsvField($partner->getNature()->getName()) : '',
                $partner->getScope() ? $this->escapeCsvField($partner->getScope()->getName()) : '',
                $partner->getCreatedAt() ? $partner->getCreatedAt()->format('Y-m-d H:i:s') : '',
                $partner->getUpdatedAt() ? $partner->getUpdatedAt()->format('Y-m-d H:i:s') : ''
            ];
            
            $rows[] = implode(',', $row);
        }
        
        return implode("\n", $rows);
    }
    
    /**
     * Escape CSV field
     */
    private function escapeCsvField(string $field): string
    {
        if (strpos($field, ',') !== false || strpos($field, '"') !== false || strpos($field, "\n") !== false) {
            return '"' . str_replace('"', '""', $field) . '"';
        }
        
        return $field;
    }
}
