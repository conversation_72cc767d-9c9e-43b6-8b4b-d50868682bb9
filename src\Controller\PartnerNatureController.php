<?php

namespace App\Controller;

use App\Entity\PartnerNature;
use App\Form\PartnerNatureForm;
use App\Repository\PartnerNatureRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/partner/nature')]
class PartnerNatureController extends AbstractController
{
    #[Route('/', name: 'app_partner_nature_index', methods: ['GET'])]
    public function index(PartnerNatureRepository $partnerNatureRepository): Response
    {
        return $this->render('partner_nature/index.html.twig', [
            'partner_natures' => $partnerNatureRepository->findBy([], ['displayOrder' => 'ASC']),
        ]);
    }

    #[Route('/new', name: 'app_partner_nature_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $partnerNature = new PartnerNature();
        $form = $this->createForm(PartnerNatureForm::class, $partnerNature);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // If this is set as default, unset any other default
            if ($partnerNature->isIsDefault()) {
                $this->unsetOtherDefaults($entityManager, $partnerNature);
            }
            
            $entityManager->persist($partnerNature);
            $entityManager->flush();

            $this->addFlash('success', 'Partner nature created successfully.');
            return $this->redirectToRoute('app_partner_nature_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('partner_nature/new.html.twig', [
            'partner_nature' => $partnerNature,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partner_nature_show', methods: ['GET'])]
    public function show(PartnerNature $partnerNature): Response
    {
        return $this->render('partner_nature/show.html.twig', [
            'partner_nature' => $partnerNature,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_partner_nature_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, PartnerNature $partnerNature, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PartnerNatureForm::class, $partnerNature);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // If this is set as default, unset any other default
            if ($partnerNature->isIsDefault()) {
                $this->unsetOtherDefaults($entityManager, $partnerNature);
            }
            
            $partnerNature->setUpdatedAt(new \DateTimeImmutable());
            $entityManager->flush();

            $this->addFlash('success', 'Partner nature updated successfully.');
            return $this->redirectToRoute('app_partner_nature_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('partner_nature/edit.html.twig', [
            'partner_nature' => $partnerNature,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partner_nature_delete', methods: ['POST'])]
    public function delete(Request $request, PartnerNature $partnerNature, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$partnerNature->getId(), $request->getPayload()->getString('_token'))) {
            // Check if this nature is used by any partner
            if (!$partnerNature->getPartners()->isEmpty()) {
                $this->addFlash('error', 'Cannot delete this nature because it is used by one or more partners.');
                return $this->redirectToRoute('app_partner_nature_index', [], Response::HTTP_SEE_OTHER);
            }
            
            $entityManager->remove($partnerNature);
            $entityManager->flush();
            
            $this->addFlash('success', 'Partner nature deleted successfully.');
        }

        return $this->redirectToRoute('app_partner_nature_index', [], Response::HTTP_SEE_OTHER);
    }
    
    /**
     * Unset default flag for all other natures
     */
    private function unsetOtherDefaults(EntityManagerInterface $entityManager, PartnerNature $currentNature): void
    {
        $defaultNatures = $entityManager->getRepository(PartnerNature::class)->findBy(['isDefault' => true]);
        
        foreach ($defaultNatures as $nature) {
            if ($nature->getId() !== $currentNature->getId()) {
                $nature->setIsDefault(false);
            }
        }
    }
}
