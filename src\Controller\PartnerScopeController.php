<?php

namespace App\Controller;

use App\Entity\PartnerScope;
use App\Form\PartnerScopeForm;
use App\Repository\PartnerScopeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/partner/scope')]
class PartnerScopeController extends AbstractController
{
    #[Route('/', name: 'app_partner_scope_index', methods: ['GET'])]
    public function index(PartnerScopeRepository $partnerScopeRepository): Response
    {
        return $this->render('partner_scope/index.html.twig', [
            'partner_scopes' => $partnerScopeRepository->findBy([], ['displayOrder' => 'ASC']),
        ]);
    }

    #[Route('/new', name: 'app_partner_scope_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $partnerScope = new PartnerScope();
        $form = $this->createForm(PartnerScopeForm::class, $partnerScope);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // If this is set as default, unset any other default
            if ($partnerScope->isIsDefault()) {
                $this->unsetOtherDefaults($entityManager, $partnerScope);
            }
            
            $entityManager->persist($partnerScope);
            $entityManager->flush();

            $this->addFlash('success', 'Structure de partenaire créée avec succès.');
            return $this->redirectToRoute('app_partner_scope_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('partner_scope/new.html.twig', [
            'partner_scope' => $partnerScope,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partner_scope_show', methods: ['GET'])]
    public function show(PartnerScope $partnerScope): Response
    {
        return $this->render('partner_scope/show.html.twig', [
            'partner_scope' => $partnerScope,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_partner_scope_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, PartnerScope $partnerScope, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PartnerScopeForm::class, $partnerScope);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // If this is set as default, unset any other default
            if ($partnerScope->isIsDefault()) {
                $this->unsetOtherDefaults($entityManager, $partnerScope);
            }
            
            $partnerScope->setUpdatedAt(new \DateTimeImmutable());
            $entityManager->flush();

            $this->addFlash('success', 'Structure de partenaire mise à jour avec succès.');
            return $this->redirectToRoute('app_partner_scope_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('partner_scope/edit.html.twig', [
            'partner_scope' => $partnerScope,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partner_scope_delete', methods: ['POST'])]
    public function delete(Request $request, PartnerScope $partnerScope, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$partnerScope->getId(), $request->getPayload()->getString('_token'))) {
            // Check if this scope is used by any partner
            if (!$partnerScope->getPartners()->isEmpty()) {
                $this->addFlash('error', 'Impossible de supprimer cette structure car elle est utilisée par un ou plusieurs partenaires.');
                return $this->redirectToRoute('app_partner_scope_index', [], Response::HTTP_SEE_OTHER);
            }
            
            $entityManager->remove($partnerScope);
            $entityManager->flush();
            
            $this->addFlash('success', 'Structure de partenaire supprimée avec succès.');
        }

        return $this->redirectToRoute('app_partner_scope_index', [], Response::HTTP_SEE_OTHER);
    }
    
    /**
     * Unset default flag for all other scopes
     */
    private function unsetOtherDefaults(EntityManagerInterface $entityManager, PartnerScope $currentScope): void
    {
        $defaultScopes = $entityManager->getRepository(PartnerScope::class)->findBy(['isDefault' => true]);
        
        foreach ($defaultScopes as $scope) {
            if ($scope->getId() !== $currentScope->getId()) {
                $scope->setIsDefault(false);
            }
        }
    }
}
