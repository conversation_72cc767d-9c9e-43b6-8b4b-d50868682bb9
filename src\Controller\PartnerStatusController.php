<?php

namespace App\Controller;

use App\Entity\PartnerStatus;
use App\Form\PartnerStatusType;
use App\Repository\PartnerStatusRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/partner/status')]
class PartnerStatusController extends AbstractController
{
    #[Route('/', name: 'app_partner_status_index', methods: ['GET'])]
    public function index(PartnerStatusRepository $partnerStatusRepository): Response
    {
        return $this->render('partner_status/index.html.twig', [
            'partner_statuses' => $partnerStatusRepository->findBy([], ['displayOrder' => 'ASC']),
        ]);
    }

    #[Route('/new', name: 'app_partner_status_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $partnerStatus = new PartnerStatus();
        $form = $this->createForm(PartnerStatusType::class, $partnerStatus);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // If this is set as default, unset any other default
            if ($partnerStatus->isIsDefault()) {
                $this->unsetOtherDefaults($entityManager, $partnerStatus);
            }
            
            $entityManager->persist($partnerStatus);
            $entityManager->flush();

            $this->addFlash('success', 'Partner status created successfully.');
            return $this->redirectToRoute('app_partner_status_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('partner_status/new.html.twig', [
            'partner_status' => $partnerStatus,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partner_status_show', methods: ['GET'])]
    public function show(PartnerStatus $partnerStatus): Response
    {
        return $this->render('partner_status/show.html.twig', [
            'partner_status' => $partnerStatus,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_partner_status_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, PartnerStatus $partnerStatus, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PartnerStatusType::class, $partnerStatus);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // If this is set as default, unset any other default
            if ($partnerStatus->isIsDefault()) {
                $this->unsetOtherDefaults($entityManager, $partnerStatus);
            }
            
            $partnerStatus->setUpdatedAt(new \DateTimeImmutable());
            $entityManager->flush();

            $this->addFlash('success', 'Partner status updated successfully.');
            return $this->redirectToRoute('app_partner_status_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('partner_status/edit.html.twig', [
            'partner_status' => $partnerStatus,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partner_status_delete', methods: ['POST'])]
    public function delete(Request $request, PartnerStatus $partnerStatus, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$partnerStatus->getId(), $request->getPayload()->getString('_token'))) {
            // Check if this status is used by any partner
            if (!$partnerStatus->getPartners()->isEmpty()) {
                $this->addFlash('error', 'Cannot delete this status because it is used by one or more partners.');
                return $this->redirectToRoute('app_partner_status_index', [], Response::HTTP_SEE_OTHER);
            }
            
            $entityManager->remove($partnerStatus);
            $entityManager->flush();
            
            $this->addFlash('success', 'Partner status deleted successfully.');
        }

        return $this->redirectToRoute('app_partner_status_index', [], Response::HTTP_SEE_OTHER);
    }
    
    /**
     * Unset default flag for all other statuses
     */
    private function unsetOtherDefaults(EntityManagerInterface $entityManager, PartnerStatus $currentStatus): void
    {
        $defaultStatuses = $entityManager->getRepository(PartnerStatus::class)->findBy(['isDefault' => true]);
        
        foreach ($defaultStatuses as $status) {
            if ($status->getId() !== $currentStatus->getId()) {
                $status->setIsDefault(false);
            }
        }
    }
}
