<?php

namespace App\Controller;

use App\Entity\Partner;
use App\Form\PartnerStatusChangeForm;
use App\Service\PartnerService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/partner/{id}/status')]
class PartnerStatusHistoryController extends AbstractController
{
    private PartnerService $partnerService;

    public function __construct(PartnerService $partnerService)
    {
        $this->partnerService = $partnerService;
    }

    #[Route('/history', name: 'app_partner_status_history')]
    public function history(Partner $partner): Response
    {
        $statusHistory = $this->partnerService->getPartnerStatusHistory($partner);
        
        return $this->render('partner_status_history/history.html.twig', [
            'partner' => $partner,
            'status_history' => $statusHistory
        ]);
    }

    #[Route('/change', name: 'app_partner_status_change')]
    public function change(Request $request, Partner $partner): Response
    {
        $form = $this->createForm(PartnerStatusChangeForm::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $newStatus = $data['status'];
            $comment = $data['comment'];
            
            $this->partnerService->updatePartnerStatus($partner, $newStatus, $comment);
            
            $this->addFlash('success', 'Le statut du partenaire a été mis à jour avec succès.');
            
            return $this->redirectToRoute('app_partner_crud_show', [
                'id' => $partner->getId()
            ]);
        }
        
        return $this->render('partner_status_history/change.html.twig', [
            'partner' => $partner,
            'form' => $form
        ]);
    }
}
