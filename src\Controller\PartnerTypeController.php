<?php

namespace App\Controller;

use App\Entity\PartnerType;
use App\Form\PartnerTypeEntityForm;
use App\Repository\PartnerTypeRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/admin/partner/type')]
class PartnerTypeController extends AbstractController
{
    #[Route('/', name: 'app_partner_type_index', methods: ['GET'])]
    public function index(PartnerTypeRepository $partnerTypeRepository): Response
    {
        return $this->render('partner_type/index.html.twig', [
            'partner_types' => $partnerTypeRepository->findBy([], ['displayOrder' => 'ASC']),
        ]);
    }

    #[Route('/new', name: 'app_partner_type_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $partnerType = new PartnerType();
        $form = $this->createForm(PartnerTypeEntityForm::class, $partnerType);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // If this is set as default, unset any other default
            if ($partnerType->isIsDefault()) {
                $this->unsetOtherDefaults($entityManager, $partnerType);
            }

            $entityManager->persist($partnerType);
            $entityManager->flush();

            $this->addFlash('success', 'Partner type created successfully.');
            return $this->redirectToRoute('app_partner_type_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('partner_type/new.html.twig', [
            'partner_type' => $partnerType,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partner_type_show', methods: ['GET'])]
    public function show(PartnerType $partnerType): Response
    {
        return $this->render('partner_type/show.html.twig', [
            'partner_type' => $partnerType,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_partner_type_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, PartnerType $partnerType, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PartnerTypeEntityForm::class, $partnerType);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // If this is set as default, unset any other default
            if ($partnerType->isIsDefault()) {
                $this->unsetOtherDefaults($entityManager, $partnerType);
            }

            $partnerType->setUpdatedAt(new \DateTimeImmutable());
            $entityManager->flush();

            $this->addFlash('success', 'Partner type updated successfully.');
            return $this->redirectToRoute('app_partner_type_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('partner_type/edit.html.twig', [
            'partner_type' => $partnerType,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_partner_type_delete', methods: ['POST'])]
    public function delete(Request $request, PartnerType $partnerType, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$partnerType->getId(), $request->getPayload()->getString('_token'))) {
            // Check if this type is used by any partner
            if (!$partnerType->getPartners()->isEmpty()) {
                $this->addFlash('error', 'Cannot delete this type because it is used by one or more partners.');
                return $this->redirectToRoute('app_partner_type_index', [], Response::HTTP_SEE_OTHER);
            }

            $entityManager->remove($partnerType);
            $entityManager->flush();

            $this->addFlash('success', 'Partner type deleted successfully.');
        }

        return $this->redirectToRoute('app_partner_type_index', [], Response::HTTP_SEE_OTHER);
    }

    /**
     * Unset default flag for all other types
     */
    private function unsetOtherDefaults(EntityManagerInterface $entityManager, PartnerType $currentType): void
    {
        $defaultTypes = $entityManager->getRepository(PartnerType::class)->findBy(['isDefault' => true]);

        foreach ($defaultTypes as $type) {
            if ($type->getId() !== $currentType->getId()) {
                $type->setIsDefault(false);
            }
        }
    }
}
