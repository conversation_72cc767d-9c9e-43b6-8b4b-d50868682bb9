<?php

namespace App\Controller;

use App\Entity\Position;
use App\Form\PositionTypeForm;
use App\Repository\PositionRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/hr/position')]
#[IsGranted('ROLE_HR')]
class PositionController extends AbstractController
{
    #[Route('/', name: 'app_position_index', methods: ['GET'])]
    public function index(PositionRepository $positionRepository): Response
    {
        $positions = $positionRepository->findAll();
        
        return $this->render('position/index.html.twig', [
            'positions' => $positions,
        ]);
    }

    #[Route('/new', name: 'app_position_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $position = new Position();
        $position->setIsActive(true);
        $form = $this->createForm(PositionTypeForm::class, $position);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($position);
            $entityManager->flush();

            $this->addFlash('success', 'Le poste a été créé avec succès.');
            return $this->redirectToRoute('app_position_index');
        }

        return $this->render('position/new.html.twig', [
            'position' => $position,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_position_show', methods: ['GET'])]
    public function show(Position $position): Response
    {
        return $this->render('position/show.html.twig', [
            'position' => $position,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_position_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Position $position, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(PositionTypeForm::class, $position);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            $this->addFlash('success', 'Le poste a été modifié avec succès.');
            return $this->redirectToRoute('app_position_index');
        }

        return $this->render('position/edit.html.twig', [
            'position' => $position,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/delete', name: 'app_position_delete', methods: ['GET', 'POST'])]
    public function delete(Request $request, Position $position, EntityManagerInterface $entityManager): Response
    {
        // Vérifier si le poste a des employés
        // Cette vérification nécessite une requête personnalisée dans le repository
        
        if ($request->isMethod('POST')) {
            $entityManager->remove($position);
            $entityManager->flush();

            $this->addFlash('success', 'Le poste a été supprimé avec succès.');
            return $this->redirectToRoute('app_position_index');
        }

        return $this->render('position/delete.html.twig', [
            'position' => $position,
        ]);
    }

    #[Route('/{id}/toggle-status', name: 'app_position_toggle_status', methods: ['GET'])]
    public function toggleStatus(Position $position, EntityManagerInterface $entityManager): Response
    {
        $position->setIsActive(!$position->isIsActive());
        $entityManager->flush();

        $status = $position->isIsActive() ? 'activé' : 'désactivé';
        $this->addFlash('success', "Le poste a été $status avec succès.");
        
        return $this->redirectToRoute('app_position_show', ['id' => $position->getId()]);
    }

    #[Route('/by-department/{id}', name: 'app_position_by_department', methods: ['GET'])]
    public function byDepartment(int $id, PositionRepository $positionRepository): Response
    {
        $positions = $positionRepository->findBy(['department' => $id, 'isActive' => true]);
        
        return $this->json([
            'positions' => array_map(function($position) {
                return [
                    'id' => $position->getId(),
                    'title' => $position->getTitle(),
                    'code' => $position->getCode()
                ];
            }, $positions)
        ]);
    }
}
