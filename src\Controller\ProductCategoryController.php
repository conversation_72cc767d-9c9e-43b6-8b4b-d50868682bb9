<?php

namespace App\Controller;

use App\Entity\ProductCategory;
use App\Form\ProductCategoryForm;
use App\Repository\ProductCategoryRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/product-category')]
#[IsGranted('ROLE_USER')]
class ProductCategoryController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private ProductCategoryRepository $categoryRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        ProductCategoryRepository $categoryRepository
    ) {
        $this->entityManager = $entityManager;
        $this->categoryRepository = $categoryRepository;
    }

    #[Route('/', name: 'app_product_category_index', methods: ['GET'])]
    public function index(): Response
    {
        $categories = $this->categoryRepository->findBy([], ['displayOrder' => 'ASC', 'name' => 'ASC']);

        return $this->render('product_category/index.html.twig', [
            'categories' => $categories,
        ]);
    }

    #[Route('/new', name: 'app_product_category_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $category = new ProductCategory();
        
        $form = $this->createForm(ProductCategoryForm::class, $category);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->entityManager->persist($category);
                $this->entityManager->flush();

                $this->addFlash('success', 'Catégorie créée avec succès.');

                return $this->redirectToRoute('app_product_category_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création de la catégorie : ' . $e->getMessage());
            }
        }

        return $this->render('product_category/new.html.twig', [
            'category' => $category,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_product_category_show', methods: ['GET'])]
    public function show(ProductCategory $category): Response
    {
        return $this->render('product_category/show.html.twig', [
            'category' => $category,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_product_category_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, ProductCategory $category): Response
    {
        $form = $this->createForm(ProductCategoryForm::class, $category);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->entityManager->flush();

                $this->addFlash('success', 'Catégorie mise à jour avec succès.');

                return $this->redirectToRoute('app_product_category_show', ['id' => $category->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour de la catégorie : ' . $e->getMessage());
            }
        }

        return $this->render('product_category/edit.html.twig', [
            'category' => $category,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/delete', name: 'app_product_category_delete', methods: ['POST'])]
    public function delete(Request $request, ProductCategory $category): Response
    {
        if ($this->isCsrfTokenValid('delete'.$category->getId(), $request->request->get('_token'))) {
            try {
                // Check if the category has products
                if ($category->getProducts()->count() > 0) {
                    throw new \Exception('Cette catégorie contient des produits et ne peut pas être supprimée.');
                }
                
                $this->entityManager->remove($category);
                $this->entityManager->flush();

                $this->addFlash('success', 'Catégorie supprimée avec succès.');

                return $this->redirectToRoute('app_product_category_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression de la catégorie : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_product_category_show', ['id' => $category->getId()]);
    }
}
