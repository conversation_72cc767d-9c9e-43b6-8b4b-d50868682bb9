<?php

namespace App\Controller;

use App\Entity\PriceHistory;
use App\Entity\Product;
use App\Form\PriceHistoryForm;
use App\Form\ProductForm;
use App\Repository\ProductRepository;
use App\Service\PriceHistoryService;
use App\Service\ProductService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Bundle\SecurityBundle\Security;

#[Route('/product')]
#[IsGranted('ROLE_USER')]
class ProductController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private ProductRepository $productRepository;
    private ProductService $productService;
    private PriceHistoryService $priceHistoryService;
    private Security $security;

    public function __construct(
        EntityManagerInterface $entityManager,
        ProductRepository $productRepository,
        ProductService $productService,
        PriceHistoryService $priceHistoryService,
        Security $security
    ) {
        $this->entityManager = $entityManager;
        $this->productRepository = $productRepository;
        $this->productService = $productService;
        $this->priceHistoryService = $priceHistoryService;
        $this->security = $security;
    }

    #[Route('/', name: 'app_product_index', methods: ['GET'])]
    public function index(): Response
    {
        $products = $this->productRepository->findBy([], ['name' => 'ASC']);

        return $this->render('product/index.html.twig', [
            'products' => $products,
        ]);
    }

    #[Route('/low-stock', name: 'app_product_low_stock', methods: ['GET'])]
    public function lowStock(): Response
    {
        $products = $this->productService->getLowStockProducts();

        return $this->render('product/low_stock.html.twig', [
            'products' => $products,
        ]);
    }

    #[Route('/search', name: 'app_product_search', methods: ['GET', 'POST'])]
    public function search(Request $request): Response
    {
        $keyword = $request->query->get('keyword');
        $products = [];
        
        if ($keyword) {
            $products = $this->productService->searchProducts($keyword);
        }

        return $this->render('product/search.html.twig', [
            'products' => $products,
            'keyword' => $keyword,
        ]);
    }

    #[Route('/new', name: 'app_product_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $product = new Product();
        
        $form = $this->createForm(ProductForm::class, $product);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->productService->createProduct($product);

                $this->addFlash('success', 'Produit créé avec succès.');

                return $this->redirectToRoute('app_product_show', ['id' => $product->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du produit : ' . $e->getMessage());
            }
        }

        return $this->render('product/new.html.twig', [
            'product' => $product,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_product_show', methods: ['GET'])]
    public function show(Product $product): Response
    {
        // Get price history
        $priceHistory = $this->priceHistoryService->getPriceHistoryForProduct($product);
        
        // Get price evolution data for chart
        $priceEvolutionData = $this->priceHistoryService->getPriceEvolutionData($product);
        
        // Get price comparison data
        $priceComparisonData = $this->priceHistoryService->getPriceComparisonData($product);

        return $this->render('product/show.html.twig', [
            'product' => $product,
            'priceHistory' => $priceHistory,
            'priceEvolutionData' => $priceEvolutionData,
            'priceComparisonData' => $priceComparisonData,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_product_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Product $product): Response
    {
        $form = $this->createForm(ProductForm::class, $product);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->productService->updateProduct($product);

                $this->addFlash('success', 'Produit mis à jour avec succès.');

                return $this->redirectToRoute('app_product_show', ['id' => $product->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour du produit : ' . $e->getMessage());
            }
        }

        return $this->render('product/edit.html.twig', [
            'product' => $product,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/delete', name: 'app_product_delete', methods: ['POST'])]
    public function delete(Request $request, Product $product): Response
    {
        if ($this->isCsrfTokenValid('delete'.$product->getId(), $request->request->get('_token'))) {
            try {
                $this->productService->deleteProduct($product);

                $this->addFlash('success', 'Produit supprimé avec succès.');

                return $this->redirectToRoute('app_product_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression du produit : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_product_show', ['id' => $product->getId()]);
    }

    #[Route('/{id}/add-price', name: 'app_product_add_price', methods: ['GET', 'POST'])]
    public function addPrice(Request $request, Product $product): Response
    {
        $priceHistory = new PriceHistory();
        $priceHistory->setProduct($product);
        $priceHistory->setSourceType('manual');
        
        $form = $this->createForm(PriceHistoryForm::class, $priceHistory);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->priceHistoryService->createPriceHistory($priceHistory);

                $this->addFlash('success', 'Prix ajouté avec succès.');

                return $this->redirectToRoute('app_product_show', ['id' => $product->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'ajout du prix : ' . $e->getMessage());
            }
        }

        return $this->render('product/add_price.html.twig', [
            'product' => $product,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/update-stock', name: 'app_product_update_stock', methods: ['POST'])]
    public function updateStock(Request $request, Product $product): Response
    {
        $quantity = (int) $request->request->get('quantity', 0);
        $operation = $request->request->get('operation', 'add');
        
        if ($this->isCsrfTokenValid('update_stock'.$product->getId(), $request->request->get('_token'))) {
            try {
                $this->productService->updateStock($product, $quantity, $operation);

                $this->addFlash('success', 'Stock mis à jour avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour du stock : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_product_show', ['id' => $product->getId()]);
    }
}
