<?php

namespace App\Controller;

use App\Entity\User;
use App\Form\ChangePasswordType;
use App\Form\UserProfileType;
use App\Repository\ActivityLogRepository;
use App\Service\ActivityLogService;
use App\Service\PasswordPolicyService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/profile')]
#[IsGranted('ROLE_USER')]
class ProfileController extends AbstractController
{
    #[Route('/', name: 'app_profile')]
    public function index(ActivityLogRepository $activityLogRepository, ActivityLogService $activityLogService): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        // Journaliser l'action
        $activityLogService->log('view', 'user', $user->getId(), json_encode([
            'action' => 'view_profile',
        ]));

        // Récupérer les activités récentes de l'utilisateur
        $recentActivities = $activityLogRepository->findByUserId($user->getUserIdentifier(), 10);

        return $this->render('profile/index.html.twig', [
            'user' => $user,
            'recentActivities' => $recentActivities,
        ]);
    }

    #[Route('/edit', name: 'app_profile_edit')]
    public function edit(Request $request, EntityManagerInterface $entityManager, ActivityLogService $activityLogService): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        $form = $this->createForm(UserProfileType::class, $user);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            // Journaliser l'action
            $activityLogService->log('update', 'user', $user->getId(), json_encode([
                'action' => 'update_profile',
            ]));

            $this->addFlash('success', 'Votre profil a été mis à jour avec succès.');

            return $this->redirectToRoute('app_profile');
        }

        return $this->render('profile/edit.html.twig', [
            'user' => $user,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/change-password', name: 'app_profile_change_password')]
    public function changePassword(
        Request $request,
        UserPasswordHasherInterface $passwordHasher,
        EntityManagerInterface $entityManager,
        PasswordPolicyService $passwordPolicyService,
        ActivityLogService $activityLogService
    ): Response {
        /** @var User $user */
        $user = $this->getUser();

        $form = $this->createForm(ChangePasswordType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();

            // Vérifier le mot de passe actuel
            if (!$passwordHasher->isPasswordValid($user, $data['currentPassword'])) {
                $this->addFlash('error', 'Le mot de passe actuel est incorrect.');
                return $this->redirectToRoute('app_profile_change_password');
            }

            // Vérifier la politique de mot de passe
            $passwordValidation = $passwordPolicyService->validatePassword($data['newPassword']);
            if (!$passwordValidation['valid']) {
                foreach ($passwordValidation['errors'] as $error) {
                    $this->addFlash('error', $error);
                }
                return $this->redirectToRoute('app_profile_change_password');
            }

            // Vérifier l'historique des mots de passe
            if ($user->getPasswordHistory() && $passwordPolicyService->isPasswordInHistory($data['newPassword'], $user->getPasswordHistory())) {
                $this->addFlash('error', 'Ce mot de passe a déjà été utilisé récemment. Veuillez en choisir un nouveau.');
                return $this->redirectToRoute('app_profile_change_password');
            }

            // Mettre à jour l'historique des mots de passe
            $passwordHistory = $user->getPasswordHistory() ?? [];
            array_unshift($passwordHistory, $user->getPassword()); // Ajouter le mot de passe actuel à l'historique
            $passwordHistory = array_slice($passwordHistory, 0, 5); // Garder les 5 derniers mots de passe
            $user->setPasswordHistory($passwordHistory);

            // Mettre à jour le mot de passe
            $hashedPassword = $passwordHasher->hashPassword($user, $data['newPassword']);
            $user->setPassword($hashedPassword);
            $user->setPasswordChangedAt(new \DateTime());

            $entityManager->flush();

            // Journaliser l'action
            $activityLogService->log('update', 'user', $user->getId(), json_encode([
                'action' => 'change_password',
            ]));

            $this->addFlash('success', 'Votre mot de passe a été modifié avec succès.');

            return $this->redirectToRoute('app_profile');
        }

        return $this->render('profile/change_password.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/security', name: 'app_profile_security')]
    public function security(ActivityLogRepository $activityLogRepository): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        // Récupérer les connexions récentes
        $loginActivities = $activityLogRepository->createQueryBuilder('a')
            ->andWhere('a.userId = :userId')
            ->andWhere('a.action = :action')
            ->setParameter('userId', $user->getUserIdentifier())
            ->setParameter('action', 'login')
            ->orderBy('a.createdAt', 'DESC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult();

        return $this->render('profile/security.html.twig', [
            'user' => $user,
            'loginActivities' => $loginActivities,
        ]);
    }

    #[Route('/settings', name: 'app_profile_settings')]
    public function settings(): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        return $this->render('profile/settings.html.twig', [
            'user' => $user,
        ]);
    }
}
