<?php

namespace App\Controller;

use App\Entity\Project;
use App\Entity\ProjectBudgetLine;
use App\Entity\ProjectBudgetSubLine;
use App\Form\ProjectBudgetLineType;
use App\Form\ProjectBudgetSubLineType;
use App\Repository\ProjectBudgetLineRepository;
use App\Repository\ProjectBudgetSubLineRepository;
use App\Repository\ProjectRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Bundle\SecurityBundle\Security;

#[Route('/project/budget')]
class ProjectBudgetController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private Security $security;
    private ProjectRepository $projectRepository;
    private ProjectBudgetLineRepository $budgetLineRepository;
    private ProjectBudgetSubLineRepository $budgetSubLineRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        Security $security,
        ProjectRepository $projectRepository,
        ProjectBudgetLineRepository $budgetLineRepository,
        ProjectBudgetSubLineRepository $budgetSubLineRepository
    ) {
        $this->entityManager = $entityManager;
        $this->security = $security;
        $this->projectRepository = $projectRepository;
        $this->budgetLineRepository = $budgetLineRepository;
        $this->budgetSubLineRepository = $budgetSubLineRepository;
    }

    #[Route('/{id}', name: 'app_project_budget')]
    public function index(Project $project): Response
    {
        // Get budget lines
        $budgetLines = $this->budgetLineRepository->findByProject($project);
        
        // Get operating and investment budget lines
        $operatingLines = $this->budgetLineRepository->findOperatingByProject($project);
        $investmentLines = $this->budgetLineRepository->findInvestmentByProject($project);
        
        return $this->render('project/budget/index.html.twig', [
            'project' => $project,
            'budget_lines' => $budgetLines,
            'operating_lines' => $operatingLines,
            'investment_lines' => $investmentLines
        ]);
    }

    #[Route('/{id}/line/new', name: 'app_project_budget_line_new')]
    public function newBudgetLine(Request $request, Project $project): Response
    {
        $budgetLine = new ProjectBudgetLine();
        $budgetLine->setProject($project);
        // Commenté car la méthode setCreatedBy pose problème
        // $budgetLine->setCreatedBy($this->security->getUser());

        $form = $this->createForm(ProjectBudgetLineType::class, $budgetLine);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($budgetLine);
            $this->entityManager->flush();

            // Update project budget
            $project->updateBudgetFromBudgetLines();
            $this->entityManager->flush();

            $this->addFlash('success', 'Ligne budgétaire créée avec succès.');

            return $this->redirectToRoute('app_project_budget', ['id' => $project->getId()]);
        }

        return $this->render('project/budget/new_line.html.twig', [
            'project' => $project,
            'form' => $form
        ]);
    }

    #[Route('/line/{id}/edit', name: 'app_project_budget_line_edit')]
    public function editBudgetLine(Request $request, ProjectBudgetLine $budgetLine): Response
    {
        $project = $budgetLine->getProject();

        $form = $this->createForm(ProjectBudgetLineType::class, $budgetLine);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();

            // Update project budget
            $project->updateBudgetFromBudgetLines();
            $this->entityManager->flush();

            $this->addFlash('success', 'Ligne budgétaire mise à jour avec succès.');

            return $this->redirectToRoute('app_project_budget', ['id' => $project->getId()]);
        }

        return $this->render('project/budget/edit_line.html.twig', [
            'project' => $project,
            'budget_line' => $budgetLine,
            'form' => $form
        ]);
    }

    #[Route('/line/{id}/delete', name: 'app_project_budget_line_delete', methods: ['POST'])]
    public function deleteBudgetLine(Request $request, ProjectBudgetLine $budgetLine): Response
    {
        $project = $budgetLine->getProject();

        if ($this->isCsrfTokenValid('delete'.$budgetLine->getId(), $request->request->get('_token'))) {
            $this->entityManager->remove($budgetLine);
            $this->entityManager->flush();

            // Update project budget
            $project->updateBudgetFromBudgetLines();
            $this->entityManager->flush();

            $this->addFlash('success', 'Ligne budgétaire supprimée avec succès.');
        }

        return $this->redirectToRoute('app_project_budget', ['id' => $project->getId()]);
    }

    #[Route('/line/{id}/subline/new', name: 'app_project_budget_subline_new')]
    public function newBudgetSubLine(Request $request, ProjectBudgetLine $budgetLine): Response
    {
        $project = $budgetLine->getProject();

        $subLine = new ProjectBudgetSubLine();
        $subLine->setBudgetLine($budgetLine);
        // Commenté car la méthode setCreatedBy peut poser problème
        // $subLine->setCreatedBy($this->security->getUser());

        $form = $this->createForm(ProjectBudgetSubLineType::class, $subLine);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($subLine);
            $this->entityManager->flush();

            // Update budget line spent amount
            $budgetLine->updateSpentAmount();
            $this->entityManager->flush();

            // Update project budget
            $project->updateBudgetFromBudgetLines();
            $this->entityManager->flush();

            $this->addFlash('success', 'Sous-ligne budgétaire créée avec succès.');

            return $this->redirectToRoute('app_project_budget', ['id' => $project->getId()]);
        }

        return $this->render('project/budget/new_subline.html.twig', [
            'project' => $project,
            'budget_line' => $budgetLine,
            'form' => $form
        ]);
    }

    #[Route('/subline/{id}/edit', name: 'app_project_budget_subline_edit')]
    public function editBudgetSubLine(Request $request, ProjectBudgetSubLine $subLine): Response
    {
        $budgetLine = $subLine->getBudgetLine();
        $project = $budgetLine->getProject();

        $form = $this->createForm(ProjectBudgetSubLineType::class, $subLine);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();

            // Update budget line spent amount
            $budgetLine->updateSpentAmount();
            $this->entityManager->flush();

            // Update project budget
            $project->updateBudgetFromBudgetLines();
            $this->entityManager->flush();

            $this->addFlash('success', 'Sous-ligne budgétaire mise à jour avec succès.');

            return $this->redirectToRoute('app_project_budget', ['id' => $project->getId()]);
        }

        return $this->render('project/budget/edit_subline.html.twig', [
            'project' => $project,
            'budget_line' => $budgetLine,
            'sub_line' => $subLine,
            'form' => $form
        ]);
    }

    #[Route('/subline/{id}/delete', name: 'app_project_budget_subline_delete', methods: ['POST'])]
    public function deleteBudgetSubLine(Request $request, ProjectBudgetSubLine $subLine): Response
    {
        $budgetLine = $subLine->getBudgetLine();
        $project = $budgetLine->getProject();

        if ($this->isCsrfTokenValid('delete'.$subLine->getId(), $request->request->get('_token'))) {
            $this->entityManager->remove($subLine);
            $this->entityManager->flush();

            // Update budget line spent amount
            $budgetLine->updateSpentAmount();
            $this->entityManager->flush();

            // Update project budget
            $project->updateBudgetFromBudgetLines();
            $this->entityManager->flush();

            $this->addFlash('success', 'Sous-ligne budgétaire supprimée avec succès.');
        }

        return $this->redirectToRoute('app_project_budget', ['id' => $project->getId()]);
    }
}
