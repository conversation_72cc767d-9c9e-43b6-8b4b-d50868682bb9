<?php

namespace App\Controller;

use App\Entity\Project;
use App\Entity\ProjectTask;
use App\Service\ProjectCalendarService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/project/calendar')]
#[IsGranted('ROLE_USER')]
class ProjectCalendarController extends AbstractController
{
    private ProjectCalendarService $calendarService;

    public function __construct(ProjectCalendarService $calendarService)
    {
        $this->calendarService = $calendarService;
    }

    #[Route('/', name: 'app_project_calendar_index', methods: ['GET'])]
    public function index(): Response
    {
        return $this->render('project_calendar/index.html.twig');
    }

    #[Route('/events', name: 'app_project_calendar_events', methods: ['GET'])]
    public function events(Request $request): JsonResponse
    {
        // Get parameters
        $start = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
        $end = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;
        
        // Get events
        $projectEvents = $this->calendarService->getProjectEvents(null, $start, $end);
        $taskEvents = $this->calendarService->getTaskEvents(null, $start, $end);
        $milestoneEvents = $this->calendarService->getMilestoneEvents(null, $start, $end);
        $deadlineEvents = $this->calendarService->getDeadlineEvents(null, $start, $end);
        
        // Combine events
        $events = array_merge($projectEvents, $taskEvents, $milestoneEvents, $deadlineEvents);
        
        return new JsonResponse($events);
    }

    #[Route('/my-events', name: 'app_project_calendar_my_events', methods: ['GET'])]
    public function myEvents(Request $request): JsonResponse
    {
        // Get parameters
        $start = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
        $end = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;
        
        // Get events for current user
        $events = $this->calendarService->getUserEvents(null, $start, $end);
        
        return new JsonResponse($events);
    }

    #[Route('/project/{id}', name: 'app_project_calendar_project', methods: ['GET'])]
    public function projectCalendar(Project $project): Response
    {
        return $this->render('project_calendar/project.html.twig', [
            'project' => $project,
        ]);
    }

    #[Route('/project/{id}/events', name: 'app_project_calendar_project_events', methods: ['GET'])]
    public function projectEvents(Request $request, Project $project): JsonResponse
    {
        // Get parameters
        $start = $request->query->get('start') ? new \DateTime($request->query->get('start')) : null;
        $end = $request->query->get('end') ? new \DateTime($request->query->get('end')) : null;
        
        // Get events
        $projectEvents = $this->calendarService->getProjectEvents([$project], $start, $end);
        $taskEvents = $this->calendarService->getTaskEvents($project->getTasks()->toArray(), $start, $end);
        $milestoneEvents = $this->calendarService->getMilestoneEvents([$project], $start, $end);
        $deadlineEvents = $this->calendarService->getDeadlineEvents([$project], $start, $end);
        
        // Combine events
        $events = array_merge($projectEvents, $taskEvents, $milestoneEvents, $deadlineEvents);
        
        return new JsonResponse($events);
    }

    #[Route('/update-project-dates/{id}', name: 'app_project_calendar_update_project_dates', methods: ['POST'])]
    #[IsGranted('ROLE_MANAGER')]
    public function updateProjectDates(Request $request, Project $project): JsonResponse
    {
        // Get parameters
        $startDate = $request->request->get('start') ? new \DateTime($request->request->get('start')) : null;
        $endDate = $request->request->get('end') ? new \DateTime($request->request->get('end')) : null;
        
        if (!$startDate || !$endDate) {
            return new JsonResponse(['success' => false, 'message' => 'Dates invalides.'], 400);
        }
        
        // Update project dates
        $this->calendarService->updateProjectDates($project, $startDate, $endDate);
        
        return new JsonResponse(['success' => true]);
    }

    #[Route('/update-task-dates/{id}', name: 'app_project_calendar_update_task_dates', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function updateTaskDates(Request $request, ProjectTask $task): JsonResponse
    {
        // Get parameters
        $startDate = $request->request->get('start') ? new \DateTime($request->request->get('start')) : null;
        $endDate = $request->request->get('end') ? new \DateTime($request->request->get('end')) : null;
        
        if (!$startDate || !$endDate) {
            return new JsonResponse(['success' => false, 'message' => 'Dates invalides.'], 400);
        }
        
        // Check if user is allowed to update this task
        if (!$this->isGranted('ROLE_MANAGER') && $task->getAssignedTo() !== $this->getUser()) {
            return new JsonResponse(['success' => false, 'message' => 'Vous n\'êtes pas autorisé à modifier cette tâche.'], 403);
        }
        
        // Update task dates
        $this->calendarService->updateTaskDates($task, $startDate, $endDate);
        
        return new JsonResponse(['success' => true]);
    }

    #[Route('/export/ical', name: 'app_project_calendar_export_ical', methods: ['GET'])]
    public function exportIcal(Request $request): Response
    {
        // Get parameters
        $start = $request->query->get('start') ? new \DateTime($request->query->get('start')) : new \DateTime();
        $end = $request->query->get('end') ? new \DateTime($request->query->get('end')) : (clone $start)->modify('+30 days');
        
        // Get events for current user
        $events = $this->calendarService->getUserEvents(null, $start, $end);
        
        // Convert to iCalendar format
        $ical = $this->calendarService->exportToICalendar($events);
        
        // Create response
        $response = new Response($ical);
        $response->headers->set('Content-Type', 'text/calendar; charset=utf-8');
        $response->headers->set('Content-Disposition', 'attachment; filename="project_calendar.ics"');
        
        return $response;
    }

    #[Route('/export/project/{id}/ical', name: 'app_project_calendar_export_project_ical', methods: ['GET'])]
    public function exportProjectIcal(Project $project): Response
    {
        // Get events
        $projectEvents = $this->calendarService->getProjectEvents([$project]);
        $taskEvents = $this->calendarService->getTaskEvents($project->getTasks()->toArray());
        $milestoneEvents = $this->calendarService->getMilestoneEvents([$project]);
        $deadlineEvents = $this->calendarService->getDeadlineEvents([$project]);
        
        // Combine events
        $events = array_merge($projectEvents, $taskEvents, $milestoneEvents, $deadlineEvents);
        
        // Convert to iCalendar format
        $ical = $this->calendarService->exportToICalendar($events);
        
        // Create response
        $response = new Response($ical);
        $response->headers->set('Content-Type', 'text/calendar; charset=utf-8');
        $response->headers->set('Content-Disposition', 'attachment; filename="project_' . $project->getCode() . '.ics"');
        
        return $response;
    }
}
