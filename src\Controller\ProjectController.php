<?php

namespace App\Controller;

use App\Entity\Project;
use App\Entity\ProjectMember;
use App\Entity\ProjectTask;
use App\Form\ProjectDocumentForm;
use App\Form\ProjectForm;
use App\Form\ProjectMemberForm;
use App\Form\ProjectTaskForm;
use App\Repository\ProjectRepository;
use App\Service\ProjectDocumentService;
use App\Service\ProjectService;
use App\Service\ProjectTaskService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/project')]
#[IsGranted('ROLE_USER')]
class ProjectController extends AbstractController
{
    private ProjectService $projectService;
    private ProjectTaskService $taskService;
    private ProjectDocumentService $documentService;
    private EntityManagerInterface $entityManager;

    public function __construct(
        ProjectService $projectService,
        ProjectTaskService $taskService,
        ProjectDocumentService $documentService,
        EntityManagerInterface $entityManager
    ) {
        $this->projectService = $projectService;
        $this->taskService = $taskService;
        $this->documentService = $documentService;
        $this->entityManager = $entityManager;
    }

    #[Route('/', name: 'app_project_index', methods: ['GET'])]
    public function index(ProjectRepository $projectRepository): Response
    {
        $dashboardData = $this->projectService->getProjectDashboardData();

        return $this->render('project/index.html.twig', [
            'dashboard_data' => $dashboardData,
        ]);
    }

    #[Route('/list', name: 'app_project_list', methods: ['GET'])]
    public function list(ProjectRepository $projectRepository): Response
    {
        $projects = $projectRepository->findBy([], ['createdAt' => 'DESC']);

        return $this->render('project/list.html.twig', [
            'projects' => $projects,
        ]);
    }

    #[Route('/new', name: 'app_project_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $project = new Project();
        $form = $this->createForm(ProjectForm::class, $project);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->projectService->createProject($project);

            $this->addFlash('success', 'Projet créé avec succès.');
            return $this->redirectToRoute('app_project_show', ['id' => $project->getId()], Response::HTTP_SEE_OTHER);
        }

        return $this->render('project/new.html.twig', [
            'project' => $project,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_project_show', methods: ['GET'])]
    public function show(Project $project): Response
    {
        $projectDetails = $this->projectService->getProjectDetails($project);

        return $this->render('project/show.html.twig', [
            'project_details' => $projectDetails,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_project_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Project $project): Response
    {
        $form = $this->createForm(ProjectForm::class, $project);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->projectService->updateProject($project);

            $this->addFlash('success', 'Projet mis à jour avec succès.');
            return $this->redirectToRoute('app_project_show', ['id' => $project->getId()], Response::HTTP_SEE_OTHER);
        }

        return $this->render('project/edit.html.twig', [
            'project' => $project,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/delete', name: 'app_project_delete', methods: ['POST'])]
    public function delete(Request $request, Project $project): Response
    {
        if ($this->isCsrfTokenValid('delete'.$project->getId(), $request->getPayload()->getString('_token'))) {
            $this->projectService->deleteProject($project);
            $this->addFlash('success', 'Projet supprimé avec succès.');
        }

        return $this->redirectToRoute('app_project_list', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/{id}/status/{status}', name: 'app_project_change_status', methods: ['GET', 'POST'])]
    public function changeStatus(Request $request, Project $project, string $status): Response
    {
        $allowedStatuses = ['draft', 'planned', 'in_progress', 'on_hold', 'completed', 'cancelled'];

        if (!in_array($status, $allowedStatuses)) {
            $this->addFlash('error', 'Statut invalide.');
            return $this->redirectToRoute('app_project_show', ['id' => $project->getId()]);
        }

        $this->projectService->changeStatus($project, $status);
        $this->addFlash('success', 'Statut du projet mis à jour avec succès.');

        return $this->redirectToRoute('app_project_show', ['id' => $project->getId()]);
    }

    #[Route('/{id}/tasks', name: 'app_project_tasks', methods: ['GET'])]
    public function tasks(Project $project): Response
    {
        return $this->render('project/tasks.html.twig', [
            'project' => $project,
            'tasks' => $project->getTasks(),
        ]);
    }

    #[Route('/{id}/task/new', name: 'app_project_task_new', methods: ['GET', 'POST'])]
    public function newTask(Request $request, Project $project): Response
    {
        $task = new ProjectTask();
        $task->setProject($project);

        $form = $this->createForm(ProjectTaskForm::class, $task);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->taskService->createTask($task);

            $this->addFlash('success', 'Tâche créée avec succès.');
            return $this->redirectToRoute('app_project_tasks', ['id' => $project->getId()], Response::HTTP_SEE_OTHER);
        }

        return $this->render('project/task_new.html.twig', [
            'project' => $project,
            'task' => $task,
            'form' => $form,
        ]);
    }

    #[Route('/{projectId}/task/{id}/edit', name: 'app_project_task_edit', methods: ['GET', 'POST'])]
    public function editTask(Request $request, int $projectId, ProjectTask $task): Response
    {
        // Security check
        if ($task->getProject()->getId() != $projectId) {
            throw $this->createAccessDeniedException('Cette tâche n\'appartient pas à ce projet.');
        }

        $form = $this->createForm(ProjectTaskForm::class, $task);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->taskService->updateTask($task);

            $this->addFlash('success', 'Tâche mise à jour avec succès.');
            return $this->redirectToRoute('app_project_tasks', ['id' => $projectId], Response::HTTP_SEE_OTHER);
        }

        return $this->render('project/task_edit.html.twig', [
            'project' => $task->getProject(),
            'task' => $task,
            'form' => $form,
        ]);
    }

    #[Route('/{projectId}/task/{id}/status/{status}', name: 'app_project_task_change_status', methods: ['GET', 'POST'])]
    public function changeTaskStatus(Request $request, int $projectId, ProjectTask $task, string $status): Response
    {
        // Security check
        if ($task->getProject()->getId() != $projectId) {
            throw $this->createAccessDeniedException('Cette tâche n\'appartient pas à ce projet.');
        }

        $allowedStatuses = ['pending', 'in_progress', 'on_hold', 'completed', 'cancelled'];

        if (!in_array($status, $allowedStatuses)) {
            $this->addFlash('error', 'Statut invalide.');
            return $this->redirectToRoute('app_project_tasks', ['id' => $projectId]);
        }

        $this->taskService->changeStatus($task, $status);
        $this->addFlash('success', 'Statut de la tâche mis à jour avec succès.');

        return $this->redirectToRoute('app_project_tasks', ['id' => $projectId]);
    }

    #[Route('/{id}/members', name: 'app_project_members', methods: ['GET'])]
    public function members(Project $project): Response
    {
        return $this->render('project/members.html.twig', [
            'project' => $project,
            'members' => $project->getMembers(),
        ]);
    }

    #[Route('/{id}/member/new', name: 'app_project_member_new', methods: ['GET', 'POST'])]
    public function newMember(Request $request, Project $project): Response
    {
        $member = new ProjectMember();
        $member->setProject($project);

        $form = $this->createForm(ProjectMemberForm::class, $member);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->projectService->addMember(
                $project,
                $member->getUser(),
                $member->getRole(),
                $member->getHourlyRate()
            );

            $this->addFlash('success', 'Membre ajouté avec succès.');
            return $this->redirectToRoute('app_project_members', ['id' => $project->getId()], Response::HTTP_SEE_OTHER);
        }

        return $this->render('project/member_new.html.twig', [
            'project' => $project,
            'member' => $member,
            'form' => $form,
        ]);
    }

    #[Route('/{projectId}/member/{id}/remove', name: 'app_project_member_remove', methods: ['POST'])]
    public function removeMember(Request $request, int $projectId, ProjectMember $member): Response
    {
        // Security check
        if ($member->getProject()->getId() != $projectId) {
            throw $this->createAccessDeniedException('Ce membre n\'appartient pas à ce projet.');
        }

        if ($this->isCsrfTokenValid('remove'.$member->getId(), $request->getPayload()->getString('_token'))) {
            $this->projectService->removeMember($member);
            $this->addFlash('success', 'Membre retiré avec succès.');
        }

        return $this->redirectToRoute('app_project_members', ['id' => $projectId], Response::HTTP_SEE_OTHER);
    }

    #[Route('/{id}/documents', name: 'app_project_documents', methods: ['GET'])]
    public function documents(Project $project): Response
    {
        $documents = $this->documentService->getDocumentsByProject($project);
        $statistics = $this->documentService->getDocumentStatistics($project);

        return $this->render('project/documents.html.twig', [
            'project' => $project,
            'documents' => $documents,
            'statistics' => $statistics,
        ]);
    }

    #[Route('/{id}/document/new', name: 'app_project_document_new', methods: ['GET', 'POST'])]
    public function newDocument(Request $request, Project $project): Response
    {
        $form = $this->createForm(ProjectDocumentForm::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $file = $form->get('file')->getData();

            if ($file) {
                $document = $this->documentService->uploadDocument(
                    $project,
                    $file,
                    $form->get('title')->getData(),
                    $form->get('description')->getData(),
                    $form->get('category')->getData(),
                    $form->get('version')->getData()
                );

                $this->addFlash('success', 'Document ajouté avec succès.');
                return $this->redirectToRoute('app_project_documents', ['id' => $project->getId()], Response::HTTP_SEE_OTHER);
            }
        }

        return $this->render('project/document_new.html.twig', [
            'project' => $project,
            'form' => $form,
        ]);
    }

    #[Route('/{projectId}/document/{id}/delete', name: 'app_project_document_delete', methods: ['POST'])]
    public function deleteDocument(Request $request, int $projectId, $id): Response
    {
        $document = $this->entityManager->getRepository('App\Entity\ProjectDocument')->find($id);

        if (!$document) {
            throw $this->createNotFoundException('Document non trouvé.');
        }

        // Security check
        if ($document->getProject()->getId() != $projectId) {
            throw $this->createAccessDeniedException('Ce document n\'appartient pas à ce projet.');
        }

        if ($this->isCsrfTokenValid('delete'.$document->getId(), $request->getPayload()->getString('_token'))) {
            $this->documentService->deleteDocument($document);
            $this->addFlash('success', 'Document supprimé avec succès.');
        }

        return $this->redirectToRoute('app_project_documents', ['id' => $projectId], Response::HTTP_SEE_OTHER);
    }

    #[Route('/my-projects', name: 'app_project_my_projects', methods: ['GET'])]
    public function myProjects(): Response
    {
        $projects = $this->projectService->getProjectsForCurrentUser();

        return $this->render('project/my_projects.html.twig', [
            'projects' => $projects,
        ]);
    }

    #[Route('/my-tasks', name: 'app_project_my_tasks', methods: ['GET'])]
    public function myTasks(): Response
    {
        $tasks = $this->taskService->getTasksForCurrentUser();
        $overdueTasks = $this->taskService->getOverdueTasks();
        $tasksDueSoon = $this->taskService->getTasksDueSoon();

        return $this->render('project/my_tasks.html.twig', [
            'tasks' => $tasks,
            'overdue_tasks' => $overdueTasks,
            'tasks_due_soon' => $tasksDueSoon,
        ]);
    }
}
