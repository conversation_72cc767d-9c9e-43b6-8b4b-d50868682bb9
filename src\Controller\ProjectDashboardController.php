<?php

namespace App\Controller;

use App\Entity\Project;
use App\Form\ProjectSearchForm;
use App\Service\ProjectDashboardService;
use App\Service\ProjectSearchService;
use App\Service\ProjectService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/project-dashboard')]
#[IsGranted('ROLE_USER')]
class ProjectDashboardController extends AbstractController
{
    private ProjectSearchService $projectSearchService;
    private ProjectService $projectService;
    private ProjectDashboardService $dashboardService;

    public function __construct(
        ProjectSearchService $projectSearchService,
        ProjectService $projectService,
        ProjectDashboardService $dashboardService
    ) {
        $this->projectSearchService = $projectSearchService;
        $this->projectService = $projectService;
        $this->dashboardService = $dashboardService;
    }

    #[Route('/', name: 'app_project_dashboard')]
    public function index(): Response
    {
        // Get project statistics
        $statistics = $this->projectSearchService->getProjectStatistics();

        return $this->render('project_dashboard/index.html.twig', [
            'statistics' => $statistics
        ]);
    }



    #[Route('/search', name: 'app_project_search')]
    public function search(Request $request): Response
    {
        $form = $this->createForm(ProjectSearchForm::class);
        $form->handleRequest($request);

        $page = $request->query->getInt('page', 1);
        $limit = $request->query->getInt('limit', 10);

        $criteria = $form->getData() ?: [];

        $results = $this->projectSearchService->searchProjects($criteria, $page, $limit);

        return $this->render('project_dashboard/search.html.twig', [
            'form' => $form,
            'results' => $results,
            'criteria' => $criteria
        ]);
    }

    #[Route('/export', name: 'app_project_export')]
    public function export(Request $request): Response
    {
        // This is a placeholder for an export feature
        // You can implement CSV or Excel export here

        $form = $this->createForm(ProjectSearchForm::class);
        $form->handleRequest($request);

        $criteria = $form->getData() ?: [];

        // Get all results without pagination
        $results = $this->projectSearchService->searchProjects($criteria, 1, 1000);

        // Return a simple CSV for now
        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="projects.csv"');

        $output = fopen('php://output', 'w');

        // Add headers
        fputcsv($output, ['ID', 'Code', 'Nom', 'Statut', 'Priorité', 'Partenaire', 'Chef de projet', 'Date de début', 'Date de fin', 'Budget', 'Progression']);

        // Add data
        foreach ($results['projects'] as $project) {
            fputcsv($output, [
                $project->getId(),
                $project->getCode(),
                $project->getName(),
                $project->getStatus(),
                $project->getPriority(),
                $project->getPartner() ? $project->getPartner()->getName() : '',
                $project->getManager() ? $project->getManager()->getFullName() : '',
                $project->getStartDate() ? $project->getStartDate()->format('Y-m-d') : '',
                $project->getEndDate() ? $project->getEndDate()->format('Y-m-d') : '',
                $project->getBudget(),
                $project->getProgress() . '%'
            ]);
        }

        $response->setContent(ob_get_clean());

        return $response;
    }

    #[Route('/project/{id}', name: 'app_project_detail_dashboard')]
    public function projectDashboard(Project $project): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('view', $project);

        // Get dashboard data
        $dashboardData = $this->dashboardService->getProjectDashboardData($project);

        // Get chart data
        $chartData = $this->dashboardService->getProjectMetricsForCharts($project);

        return $this->render('project_dashboard/project_detail.html.twig', [
            'project' => $project,
            'dashboard' => $dashboardData,
            'charts' => $chartData,
        ]);
    }
}
