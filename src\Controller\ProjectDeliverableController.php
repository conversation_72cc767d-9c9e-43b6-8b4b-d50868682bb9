<?php

namespace App\Controller;

use App\Entity\Project;
use App\Entity\ProjectDeliverable;
use App\Form\ProjectDeliverableForm;
use App\Service\ProjectDeliverableService;
use App\Service\ProjectDocumentService;
use App\Service\ProjectService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/project')]
#[IsGranted('ROLE_USER')]
class ProjectDeliverableController extends AbstractController
{
    private ProjectDeliverableService $deliverableService;
    private ProjectService $projectService;
    private ProjectDocumentService $documentService;

    public function __construct(
        ProjectDeliverableService $deliverableService,
        ProjectService $projectService,
        ProjectDocumentService $documentService
    ) {
        $this->deliverableService = $deliverableService;
        $this->projectService = $projectService;
        $this->documentService = $documentService;
    }

    #[Route('/{id}/deliverables', name: 'app_project_deliverables')]
    public function index(Project $project): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('view', $project);

        // Get all deliverables for the project
        $deliverables = $this->deliverableService->getDeliverablesByProject($project);

        return $this->render('project_deliverable/index.html.twig', [
            'project' => $project,
            'deliverables' => $deliverables,
        ]);
    }

    #[Route('/{id}/deliverable/new', name: 'app_project_deliverable_new')]
    public function new(Request $request, Project $project): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $project);

        $deliverable = new ProjectDeliverable();
        $deliverable->setProject($project);

        // Get project members for the assignedTo field
        $projectMembers = $this->projectService->getProjectMembers($project);
        
        // Get project documents for the document field
        $projectDocuments = $this->documentService->getDocumentsByProject($project);

        $form = $this->createForm(ProjectDeliverableForm::class, $deliverable, [
            'project_member_only' => true,
            'project_members' => $projectMembers,
            'project_documents_only' => true,
            'project_documents' => $projectDocuments,
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->deliverableService->createDeliverable($deliverable);

            $this->addFlash('success', 'Livrable créé avec succès.');
            return $this->redirectToRoute('app_project_deliverables', ['id' => $project->getId()]);
        }

        return $this->render('project_deliverable/new.html.twig', [
            'project' => $project,
            'form' => $form,
        ]);
    }

    #[Route('/deliverable/{id}', name: 'app_project_deliverable_show')]
    public function show(ProjectDeliverable $deliverable): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('view', $deliverable->getProject());

        return $this->render('project_deliverable/show.html.twig', [
            'deliverable' => $deliverable,
        ]);
    }

    #[Route('/deliverable/{id}/edit', name: 'app_project_deliverable_edit')]
    public function edit(Request $request, ProjectDeliverable $deliverable): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $deliverable->getProject());

        // Get project members for the assignedTo field
        $projectMembers = $this->projectService->getProjectMembers($deliverable->getProject());
        
        // Get project documents for the document field
        $projectDocuments = $this->documentService->getDocumentsByProject($deliverable->getProject());

        $form = $this->createForm(ProjectDeliverableForm::class, $deliverable, [
            'project_member_only' => true,
            'project_members' => $projectMembers,
            'project_documents_only' => true,
            'project_documents' => $projectDocuments,
            'include_feedback' => true,
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->deliverableService->updateDeliverable($deliverable);

            $this->addFlash('success', 'Livrable mis à jour avec succès.');
            return $this->redirectToRoute('app_project_deliverable_show', ['id' => $deliverable->getId()]);
        }

        return $this->render('project_deliverable/edit.html.twig', [
            'deliverable' => $deliverable,
            'form' => $form,
        ]);
    }

    #[Route('/deliverable/{id}/delete', name: 'app_project_deliverable_delete', methods: ['POST'])]
    public function delete(Request $request, ProjectDeliverable $deliverable): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $deliverable->getProject());

        if ($this->isCsrfTokenValid('delete'.$deliverable->getId(), $request->request->get('_token'))) {
            $projectId = $deliverable->getProject()->getId();
            $this->deliverableService->deleteDeliverable($deliverable);

            $this->addFlash('success', 'Livrable supprimé avec succès.');
            return $this->redirectToRoute('app_project_deliverables', ['id' => $projectId]);
        }

        return $this->redirectToRoute('app_project_deliverable_show', ['id' => $deliverable->getId()]);
    }

    #[Route('/deliverable/{id}/approve', name: 'app_project_deliverable_approve', methods: ['POST'])]
    public function approve(Request $request, ProjectDeliverable $deliverable): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $deliverable->getProject());

        if ($this->isCsrfTokenValid('approve'.$deliverable->getId(), $request->request->get('_token'))) {
            $this->deliverableService->approveDeliverable($deliverable, $this->getUser());

            $this->addFlash('success', 'Livrable approuvé avec succès.');
        }

        return $this->redirectToRoute('app_project_deliverable_show', ['id' => $deliverable->getId()]);
    }

    #[Route('/deliverable/{id}/reject', name: 'app_project_deliverable_reject', methods: ['POST'])]
    public function reject(Request $request, ProjectDeliverable $deliverable): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $deliverable->getProject());

        if ($this->isCsrfTokenValid('reject'.$deliverable->getId(), $request->request->get('_token'))) {
            $feedback = $request->request->get('feedback');
            $this->deliverableService->rejectDeliverable($deliverable, $feedback);

            $this->addFlash('success', 'Livrable rejeté avec succès.');
        }

        return $this->redirectToRoute('app_project_deliverable_show', ['id' => $deliverable->getId()]);
    }

    #[Route('/deliverable/{id}/deliver', name: 'app_project_deliverable_deliver', methods: ['POST'])]
    public function deliver(Request $request, ProjectDeliverable $deliverable): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $deliverable->getProject());

        if ($this->isCsrfTokenValid('deliver'.$deliverable->getId(), $request->request->get('_token'))) {
            $this->deliverableService->markAsDelivered($deliverable);

            $this->addFlash('success', 'Livrable marqué comme livré avec succès.');
        }

        return $this->redirectToRoute('app_project_deliverable_show', ['id' => $deliverable->getId()]);
    }
}
