<?php

namespace App\Controller;

use App\Entity\Notification;
use App\Service\ProjectNotificationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/project/notification')]
#[IsGranted('ROLE_USER')]
class ProjectNotificationController extends AbstractController
{
    private ProjectNotificationService $notificationService;

    public function __construct(ProjectNotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    #[Route('/', name: 'app_project_notification_index', methods: ['GET'])]
    public function index(): Response
    {
        // Get notifications for current user
        $notificationRepository = $this->getDoctrine()->getRepository(Notification::class);
        $notifications = $notificationRepository->findBy(
            ['userId' => $this->getUser()->getUserIdentifier()],
            ['createdAt' => 'DESC'],
            50
        );

        return $this->render('project_notification/index.html.twig', [
            'notifications' => $notifications,
        ]);
    }

    #[Route('/unread', name: 'app_project_notification_unread', methods: ['GET'])]
    public function unread(): Response
    {
        // Get unread notifications for current user
        $notificationRepository = $this->getDoctrine()->getRepository(Notification::class);
        $notifications = $notificationRepository->findBy(
            ['userId' => $this->getUser()->getUserIdentifier(), 'isRead' => false],
            ['createdAt' => 'DESC']
        );

        return $this->render('project_notification/unread.html.twig', [
            'notifications' => $notifications,
        ]);
    }

    #[Route('/count', name: 'app_project_notification_count', methods: ['GET'])]
    public function count(): JsonResponse
    {
        // Get unread notifications count for current user
        $notificationRepository = $this->getDoctrine()->getRepository(Notification::class);
        $count = $notificationRepository->count([
            'userId' => $this->getUser()->getUserIdentifier(),
            'isRead' => false,
        ]);

        return new JsonResponse(['count' => $count]);
    }

    #[Route('/{id}/mark-read', name: 'app_project_notification_mark_read', methods: ['POST'])]
    public function markRead(Notification $notification): JsonResponse
    {
        // Check if notification belongs to current user
        if ($notification->getUserId() !== $this->getUser()->getUserIdentifier()) {
            return new JsonResponse(['success' => false, 'message' => 'Notification non trouvée.'], 404);
        }

        // Mark notification as read
        $notification->setIsRead(true);
        $this->getDoctrine()->getManager()->flush();

        return new JsonResponse(['success' => true]);
    }

    #[Route('/mark-all-read', name: 'app_project_notification_mark_all_read', methods: ['POST'])]
    public function markAllRead(): Response
    {
        // Get unread notifications for current user
        $notificationRepository = $this->getDoctrine()->getRepository(Notification::class);
        $notifications = $notificationRepository->findBy([
            'userId' => $this->getUser()->getUserIdentifier(),
            'isRead' => false,
        ]);

        // Mark all as read
        foreach ($notifications as $notification) {
            $notification->setIsRead(true);
        }

        $this->getDoctrine()->getManager()->flush();

        $this->addFlash('success', 'Toutes les notifications ont été marquées comme lues.');

        return $this->redirectToRoute('app_project_notification_index');
    }

    #[Route('/{id}/delete', name: 'app_project_notification_delete', methods: ['POST'])]
    public function delete(Request $request, Notification $notification): Response
    {
        // Check if notification belongs to current user
        if ($notification->getUserId() !== $this->getUser()->getUserIdentifier()) {
            throw $this->createNotFoundException('Notification non trouvée.');
        }

        if ($this->isCsrfTokenValid('delete'.$notification->getId(), $request->request->get('_token'))) {
            $entityManager = $this->getDoctrine()->getManager();
            $entityManager->remove($notification);
            $entityManager->flush();

            $this->addFlash('success', 'Notification supprimée.');
        }

        return $this->redirectToRoute('app_project_notification_index');
    }

    #[Route('/delete-all', name: 'app_project_notification_delete_all', methods: ['POST'])]
    public function deleteAll(Request $request): Response
    {
        if ($this->isCsrfTokenValid('delete_all', $request->request->get('_token'))) {
            // Get all notifications for current user
            $notificationRepository = $this->getDoctrine()->getRepository(Notification::class);
            $notifications = $notificationRepository->findBy([
                'userId' => $this->getUser()->getUserIdentifier(),
            ]);

            // Delete all
            $entityManager = $this->getDoctrine()->getManager();
            foreach ($notifications as $notification) {
                $entityManager->remove($notification);
            }

            $entityManager->flush();

            $this->addFlash('success', 'Toutes les notifications ont été supprimées.');
        }

        return $this->redirectToRoute('app_project_notification_index');
    }

    #[Route('/settings', name: 'app_project_notification_settings', methods: ['GET', 'POST'])]
    public function settings(Request $request): Response
    {
        // Get current user
        $user = $this->getUser();

        // Handle form submission
        if ($request->isMethod('POST')) {
            // Update notification settings
            $user->setNotificationSettings([
                'email_notifications' => $request->request->getBoolean('email_notifications'),
                'task_assignment' => $request->request->getBoolean('task_assignment'),
                'task_status_change' => $request->request->getBoolean('task_status_change'),
                'task_due_date' => $request->request->getBoolean('task_due_date'),
                'project_status_change' => $request->request->getBoolean('project_status_change'),
                'project_deadline' => $request->request->getBoolean('project_deadline'),
            ]);

            $this->getDoctrine()->getManager()->flush();

            $this->addFlash('success', 'Paramètres de notification mis à jour.');

            return $this->redirectToRoute('app_project_notification_settings');
        }

        return $this->render('project_notification/settings.html.twig', [
            'notification_settings' => $user->getNotificationSettings(),
        ]);
    }

    #[Route('/check-due-dates', name: 'app_project_notification_check_due_dates', methods: ['GET'])]
    #[IsGranted('ROLE_ADMIN')]
    public function checkDueDates(): Response
    {
        // Check for tasks with approaching due dates
        $this->notificationService->checkTaskDueDates();

        // Check for overdue tasks
        $this->notificationService->checkOverdueTasks();

        // Check for projects with approaching deadlines
        $this->notificationService->checkProjectDeadlines();

        // Check for overdue projects
        $this->notificationService->checkOverdueProjects();

        $this->addFlash('success', 'Vérification des échéances effectuée avec succès.');

        return $this->redirectToRoute('app_project_notification_index');
    }
}
