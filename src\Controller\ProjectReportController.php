<?php

namespace App\Controller;

use App\Entity\Project;
use App\Entity\ProjectTask;
use App\Service\ProjectReportService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/project/report')]
#[IsGranted('ROLE_USER')]
class ProjectReportController extends AbstractController
{
    private ProjectReportService $reportService;

    public function __construct(ProjectReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    #[Route('/', name: 'app_project_report_index', methods: ['GET'])]
    public function index(): Response
    {
        // Get project statistics
        $statistics = $this->reportService->getProjectStatistics();

        // Get charts
        $statusChart = $this->reportService->getProjectStatusChart();
        $priorityChart = $this->reportService->getProjectPriorityChart();
        $progressChart = $this->reportService->getProjectProgressChart();
        $budgetChart = $this->reportService->getProjectBudgetChart();

        return $this->render('project_report/index.html.twig', [
            'statistics' => $statistics,
            'status_chart' => $statusChart,
            'priority_chart' => $priorityChart,
            'progress_chart' => $progressChart,
            'budget_chart' => $budgetChart,
        ]);
    }

    #[Route('/project/{id}', name: 'app_project_report_detail', methods: ['GET'])]
    public function projectDetail(Project $project): Response
    {
        // Get task status chart
        $taskStatusChart = $this->reportService->getProjectTaskStatusChart($project);

        // Get project performance metrics
        $performanceMetrics = $this->reportService->getProjectPerformanceMetrics($project);

        return $this->render('project_report/project_detail.html.twig', [
            'project' => $project,
            'task_status_chart' => $taskStatusChart,
            'performance_metrics' => $performanceMetrics,
        ]);
    }

    #[Route('/status/{id}', name: 'app_project_report_status', methods: ['GET'])]
    public function projectStatus(Project $project): Response
    {
        try {
            // Add debug message
            $this->addFlash('info', 'Accessing project status report for project ' . $project->getId());

            // Enable permission check
            $this->denyAccessUnlessGranted('view', $project);
            // Get project statistics
            $overallProgress = $project->getProgress();

            // Get task statistics
            $taskStats = $this->reportService->getProjectTaskStats($project);
            $taskProgress = ($taskStats['total'] > 0 && $taskStats['completed'] > 0) ?
                ($taskStats['completed'] / $taskStats['total'] * 100) : 0;

            // Add task priority statistics
            $taskStats['priority_low'] = 0;
            $taskStats['priority_medium'] = 0;
            $taskStats['priority_high'] = 0;
            $taskStats['priority_urgent'] = 0;

            foreach ($project->getTasks() as $task) {
                $priority = $task->getPriority();
                if (isset($taskStats['priority_' . $priority])) {
                    $taskStats['priority_' . $priority]++;
                }
            }

            // Get timeline progress
            $timelineProgress = 0;
            if ($project->getStartDate() && $project->getEndDate()) {
                $totalDays = $project->getStartDate()->diff($project->getEndDate())->days;
                if ($totalDays > 0) {
                    $elapsedDays = $project->getStartDate()->diff(new \DateTime())->days;
                    $timelineProgress = min(100, max(0, ($elapsedDays / $totalDays) * 100));
                }
            }

            // Get deliverable statistics
            $deliverableStats = $this->reportService->getProjectDeliverableStats($project);
            $deliverableProgress = $deliverableStats['total'] > 0 ?
                (($deliverableStats['approved'] + $deliverableStats['delivered']) / $deliverableStats['total'] * 100) : 0;

            // Get risk statistics
            $riskStats = $this->reportService->getProjectRiskStats($project);

            // Get resource statistics
            $resourceStats = $this->reportService->getProjectResourceStats($project);

            // Get budget statistics
            $budgetStats = [
                'budget' => $project->getBudget(),
                'cost_to_date' => $project->getCostToDate(),
                'remaining_budget' => $project->getRemainingBudget(),
                'utilization_percentage' => $project->getBudgetUtilizationPercentage(),
            ];

            // Get budget by category
            $budgetByCategory = [];
            foreach ($project->getBudgetLines() as $budgetLine) {
                $category = $budgetLine->getCategory() ?: 'Non catégorisé';
                if (!isset($budgetByCategory[$category])) {
                    $budgetByCategory[$category] = [
                        'amount' => 0,
                        'spent' => 0,
                    ];
                }
                $budgetByCategory[$category]['amount'] += $budgetLine->getAllocatedAmount();
                $budgetByCategory[$category]['spent'] += $budgetLine->getSpentAmount();
            }

            // Si aucune catégorie n'est définie, ajouter une catégorie par défaut
            if (empty($budgetByCategory)) {
                $budgetByCategory['Non catégorisé'] = [
                    'amount' => 0,
                    'spent' => 0,
                ];
            }

            return $this->render('project_report/status.html.twig', [
                'project' => $project,
                'overall_progress' => $overallProgress,
                'task_stats' => $taskStats,
                'task_progress' => $taskProgress,
                'timeline_progress' => $timelineProgress,
                'deliverable_stats' => $deliverableStats,
                'deliverable_progress' => $deliverableProgress,
                'risk_stats' => $riskStats,
                'resource_stats' => $resourceStats,
                'budget_stats' => $budgetStats,
                'budget_by_category' => $budgetByCategory,
            ]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Erreur lors de l\'accès au rapport de statut du projet: ' . $e->getMessage());
            return $this->redirectToRoute('app_project_list');
        }
    }

    #[Route('/timeline', name: 'app_project_report_timeline', methods: ['GET'])]
    public function timeline(Request $request): Response
    {
        // Get active projects
        $projects = $this->reportService->getProjectRepository()->findActiveProjects();

        // Get timeline data
        $timelineData = $this->reportService->getProjectTimelineData($projects);

        return $this->render('project_report/timeline.html.twig', [
            'projects' => $projects,
            'timeline_data' => json_encode($timelineData),
        ]);
    }

    #[Route('/resource-utilization', name: 'app_project_report_resource_utilization', methods: ['GET'])]
    #[IsGranted('ROLE_MANAGER')]
    public function resourceUtilization(): Response
    {
        try {
            // Get resource utilization statistics
            $resourceStats = $this->reportService->getResourceUtilization();

            return $this->render('project_report/resource_utilization.html.twig', [
                'resource_stats' => $resourceStats,
            ]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Erreur lors de la récupération des statistiques d\'utilisation des ressources: ' . $e->getMessage());
            return $this->redirectToRoute('app_project_report_index');
        }
    }

    #[Route('/export/pdf/{id}', name: 'app_project_report_export_pdf', methods: ['GET'])]
    public function exportPdf(Project $project): Response
    {
        // TODO: Implémenter la génération de PDF avec une bibliothèque comme Dompdf ou TCPDF

        $this->addFlash('info', 'La fonctionnalité d\'export PDF sera disponible prochainement.');

        return $this->redirectToRoute('app_project_report_detail', ['id' => $project->getId()]);
    }

    #[Route('/status/pdf/{id}', name: 'app_project_report_status_pdf', methods: ['GET'])]
    public function exportStatusPdf(Project $project): Response
    {
        // TODO: Implémenter la génération de PDF avec une bibliothèque comme Dompdf ou TCPDF

        $this->addFlash('info', 'La fonctionnalité d\'export PDF du rapport de statut sera disponible prochainement.');

        return $this->redirectToRoute('app_project_report_status', ['id' => $project->getId()]);
    }

    #[Route('/export/csv/{id}', name: 'app_project_report_export_csv', methods: ['GET'])]
    public function exportCsv(Project $project): Response
    {
        // Get project data
        $projectData = [
            'name' => $project->getName(),
            'code' => $project->getCode(),
            'status' => $project->getStatus(),
            'priority' => $project->getPriority(),
            'progress' => $project->getProgress(),
            'start_date' => $project->getStartDate() ? $project->getStartDate()->format('Y-m-d') : '',
            'end_date' => $project->getEndDate() ? $project->getEndDate()->format('Y-m-d') : '',
            'budget' => $project->getBudget(),
            'cost_to_date' => $project->getCostToDate(),
        ];

        // Get task data
        $taskData = [];
        foreach ($project->getTasks() as $task) {
            $taskData[] = [
                'title' => $task->getTitle(),
                'status' => $task->getStatus(),
                'priority' => $task->getPriority(),
                'progress' => $task->getProgress(),
                'start_date' => $task->getStartDate() ? $task->getStartDate()->format('Y-m-d') : '',
                'due_date' => $task->getDueDate() ? $task->getDueDate()->format('Y-m-d') : '',
                'assigned_to' => $task->getAssignedTo() ? $task->getAssignedTo()->getFullName() : '',
                'estimated_hours' => $task->getEstimatedHours(),
                'actual_hours' => $task->getActualHours(),
            ];
        }

        // Create CSV content
        $csv = "Project Report: " . $project->getName() . "\n\n";

        // Project details
        $csv .= "Détails du Projet\n";
        foreach ($projectData as $key => $value) {
            $csv .= ucfirst(str_replace('_', ' ', $key)) . "," . $this->escapeCsvValue($value) . "\n";
        }

        $csv .= "\nTâches\n";
        // Task headers
        $csv .= "Titre,Statut,Priorité,Progression,Date de début,Date d'échéance,Assigné à,Heures estimées,Heures réelles\n";

        // Task data
        foreach ($taskData as $task) {
            $csv .= implode(',', array_map([$this, 'escapeCsvValue'], $task)) . "\n";
        }

        // Create response
        $response = new Response($csv);
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="rapport_projet_' . $project->getCode() . '.csv"');

        return $response;
    }

    /**
     * Échappe les valeurs CSV
     */
    private function escapeCsvValue($value): string
    {
        if ($value === null) {
            return '';
        }

        if (is_numeric($value)) {
            return (string)$value;
        }

        $value = (string)$value;
        if (strpos($value, ',') !== false || strpos($value, '"') !== false || strpos($value, "\n") !== false) {
            $value = str_replace('"', '""', $value);
            return '"' . $value . '"';
        }

        return $value;
    }
}
