<?php

namespace App\Controller;

use App\Entity\Project;
use App\Entity\ProjectResource;
use App\Form\ProjectResourceForm;
use App\Service\ProjectResourceAllocationService;
use App\Service\ProjectService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/project')]
#[IsGranted('ROLE_USER')]
class ProjectResourceAllocationController extends AbstractController
{
    private ProjectResourceAllocationService $resourceAllocationService;
    private ProjectService $projectService;

    public function __construct(
        ProjectResourceAllocationService $resourceAllocationService,
        ProjectService $projectService
    ) {
        $this->resourceAllocationService = $resourceAllocationService;
        $this->projectService = $projectService;
    }

    #[Route('/{id}/resource-allocations', name: 'app_project_resource_allocations')]
    public function index(Project $project): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('view', $project);

        // Get all resources for the project
        $resources = $this->resourceAllocationService->getResourceAllocationsByProject($project);
        
        // Get resource statistics
        $statistics = $this->resourceAllocationService->getResourceStatisticsByProject($project);
        
        // Get resource allocation by role
        $allocationByRole = $this->resourceAllocationService->getResourceAllocationByRole($project);
        
        // Get overallocated resources
        $overallocatedResources = $this->resourceAllocationService->getOverallocatedResources($project);

        return $this->render('project_resource_allocation/index.html.twig', [
            'project' => $project,
            'resources' => $resources,
            'statistics' => $statistics,
            'allocation_by_role' => $allocationByRole,
            'overallocated_resources' => $overallocatedResources,
        ]);
    }

    #[Route('/{id}/resource-allocation/new', name: 'app_project_resource_allocation_new')]
    public function new(Request $request, Project $project): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $project);

        $resource = new ProjectResource();
        $resource->setProject($project);

        $form = $this->createForm(ProjectResourceForm::class, $resource);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->resourceAllocationService->createResourceAllocation($resource);

            $this->addFlash('success', 'Ressource ajoutée avec succès.');
            return $this->redirectToRoute('app_project_resource_allocations', ['id' => $project->getId()]);
        }

        return $this->render('project_resource_allocation/new.html.twig', [
            'project' => $project,
            'form' => $form,
        ]);
    }

    #[Route('/resource-allocation/{id}', name: 'app_project_resource_allocation_show')]
    public function show(ProjectResource $resource): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('view', $resource->getProject());

        return $this->render('project_resource_allocation/show.html.twig', [
            'resource' => $resource,
        ]);
    }

    #[Route('/resource-allocation/{id}/edit', name: 'app_project_resource_allocation_edit')]
    public function edit(Request $request, ProjectResource $resource): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $resource->getProject());

        $form = $this->createForm(ProjectResourceForm::class, $resource);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->resourceAllocationService->updateResourceAllocation($resource);

            $this->addFlash('success', 'Ressource mise à jour avec succès.');
            return $this->redirectToRoute('app_project_resource_allocation_show', ['id' => $resource->getId()]);
        }

        return $this->render('project_resource_allocation/edit.html.twig', [
            'resource' => $resource,
            'form' => $form,
        ]);
    }

    #[Route('/resource-allocation/{id}/delete', name: 'app_project_resource_allocation_delete', methods: ['POST'])]
    public function delete(Request $request, ProjectResource $resource): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $resource->getProject());

        if ($this->isCsrfTokenValid('delete'.$resource->getId(), $request->request->get('_token'))) {
            $projectId = $resource->getProject()->getId();
            $this->resourceAllocationService->deleteResourceAllocation($resource);

            $this->addFlash('success', 'Ressource supprimée avec succès.');
            return $this->redirectToRoute('app_project_resource_allocations', ['id' => $projectId]);
        }

        return $this->redirectToRoute('app_project_resource_allocation_show', ['id' => $resource->getId()]);
    }

    #[Route('/resource-allocation/{id}/update-usage', name: 'app_project_resource_allocation_update_usage', methods: ['POST'])]
    public function updateUsage(Request $request, ProjectResource $resource): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $resource->getProject());

        if ($this->isCsrfTokenValid('update_usage'.$resource->getId(), $request->request->get('_token'))) {
            $usedDays = (float)$request->request->get('used_days', 0);
            $this->resourceAllocationService->updateResourceUsage($resource, $usedDays);

            $this->addFlash('success', 'Utilisation de la ressource mise à jour avec succès.');
        }

        return $this->redirectToRoute('app_project_resource_allocation_show', ['id' => $resource->getId()]);
    }
}
