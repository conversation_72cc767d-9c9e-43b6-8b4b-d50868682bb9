<?php

namespace App\Controller;

use App\Entity\Project;
use App\Entity\ProjectResource;
use App\Entity\User;
use App\Entity\ProjectTask;
use App\Form\ProjectResourceType;
use App\Repository\ProjectResourceRepository;
use App\Service\ProjectResourceService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/project/resource')]
#[IsGranted('ROLE_USER')]
class ProjectResourceController extends AbstractController
{
    private ProjectResourceService $resourceService;
    private EntityManagerInterface $entityManager;

    public function __construct(
        ProjectResourceService $resourceService,
        EntityManagerInterface $entityManager
    ) {
        $this->resourceService = $resourceService;
        $this->entityManager = $entityManager;
    }

    #[Route('/{id}/allocation', name: 'app_project_resource_allocation', methods: ['GET'])]
    public function allocation(Project $project): Response
    {
        // Get resource allocation for the project
        $allocation = $this->resourceService->getProjectResourceAllocation($project);
        
        return $this->render('project_resource/allocation.html.twig', [
            'project' => $project,
            'allocation' => $allocation,
        ]);
    }

    #[Route('/workload/{id}', name: 'app_project_resource_workload', methods: ['GET'])]
    public function userWorkload(User $user): Response
    {
        // Get workload for the user
        $workload = $this->resourceService->getUserWorkload($user);
        
        return $this->render('project_resource/workload.html.twig', [
            'user' => $user,
            'workload' => $workload,
        ]);
    }

    #[Route('/my-workload', name: 'app_project_resource_my_workload', methods: ['GET'])]
    public function myWorkload(): Response
    {
        // Get current user
        $user = $this->getUser();
        
        // Get workload for the current user
        $workload = $this->resourceService->getUserWorkload($user);
        
        return $this->render('project_resource/my_workload.html.twig', [
            'workload' => $workload,
        ]);
    }

    #[Route('/availability', name: 'app_project_resource_availability', methods: ['GET'])]
    #[IsGranted('ROLE_MANAGER')]
    public function availability(Request $request): Response
    {
        // Get parameters
        $startDate = $request->query->get('start_date') ? new \DateTime($request->query->get('start_date')) : new \DateTime();
        $endDate = $request->query->get('end_date') ? new \DateTime($request->query->get('end_date')) : (clone $startDate)->modify('+14 days');
        
        // Get users
        $userRepository = $this->entityManager->getRepository(User::class);
        $users = $userRepository->findBy(['isActive' => true]);
        
        // Get availability
        $availability = $this->resourceService->getResourceAvailability($users, $startDate, $endDate);
        
        return $this->render('project_resource/availability.html.twig', [
            'availability' => $availability,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);
    }

    #[Route('/task/{id}/assign', name: 'app_project_resource_assign_task', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_MANAGER')]
    public function assignTask(Request $request, ProjectTask $task): Response
    {
        // Handle form submission
        if ($request->isMethod('POST')) {
            $userId = $request->request->get('user_id');
            
            if ($userId) {
                $userRepository = $this->entityManager->getRepository(User::class);
                $user = $userRepository->find($userId);
                
                if ($user) {
                    $this->resourceService->assignTaskToUser($task, $user);
                    
                    $this->addFlash('success', 'Tâche assignée avec succès à ' . $user->getFullName() . '.');
                    
                    return $this->redirectToRoute('app_project_task_show', [
                        'projectId' => $task->getProject()->getId(),
                        'id' => $task->getId(),
                    ]);
                }
            }
            
            $this->addFlash('error', 'Utilisateur non trouvé.');
        }
        
        // Get recommended resources
        $recommendations = $this->resourceService->getRecommendedResourcesForTask($task);
        
        // Get all users
        $userRepository = $this->entityManager->getRepository(User::class);
        $users = $userRepository->findBy(['isActive' => true]);
        
        return $this->render('project_resource/assign_task.html.twig', [
            'task' => $task,
            'recommendations' => $recommendations,
            'users' => $users,
        ]);
    }

    #[Route('/task/{id}/recommendations', name: 'app_project_resource_task_recommendations', methods: ['GET'])]
    #[IsGranted('ROLE_MANAGER')]
    public function taskRecommendations(ProjectTask $task): Response
    {
        // Get recommended resources
        $recommendations = $this->resourceService->getRecommendedResourcesForTask($task);
        
        return new JsonResponse($recommendations);
    }

    #[Route('/bulk-assign/{id}', name: 'app_project_resource_bulk_assign', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_MANAGER')]
    public function bulkAssign(Request $request, Project $project): Response
    {
        // Handle form submission
        if ($request->isMethod('POST')) {
            $taskIds = $request->request->get('task_ids', []);
            $userId = $request->request->get('user_id');
            
            if ($userId && !empty($taskIds)) {
                $userRepository = $this->entityManager->getRepository(User::class);
                $user = $userRepository->find($userId);
                
                $taskRepository = $this->entityManager->getRepository(ProjectTask::class);
                $tasks = $taskRepository->findBy(['id' => $taskIds]);
                
                if ($user && !empty($tasks)) {
                    $this->resourceService->assignTasksToUser($tasks, $user);
                    
                    $this->addFlash('success', count($tasks) . ' tâches assignées avec succès à ' . $user->getFullName() . '.');
                    
                    return $this->redirectToRoute('app_project_show', ['id' => $project->getId()]);
                }
            }
            
            $this->addFlash('error', 'Erreur lors de l\'assignation des tâches.');
        }
        
        // Get unassigned tasks
        $taskRepository = $this->entityManager->getRepository(ProjectTask::class);
        $unassignedTasks = $taskRepository->createQueryBuilder('t')
            ->andWhere('t.project = :project')
            ->andWhere('t.assignedTo IS NULL')
            ->andWhere('t.status != :status')
            ->setParameter('project', $project)
            ->setParameter('status', 'completed')
            ->getQuery()
            ->getResult();
        
        // Get all users
        $userRepository = $this->entityManager->getRepository(User::class);
        $users = $userRepository->findBy(['isActive' => true]);
        
        return $this->render('project_resource/bulk_assign.html.twig', [
            'project' => $project,
            'unassigned_tasks' => $unassignedTasks,
            'users' => $users,
        ]);
    }

    #[Route('/utilization', name: 'app_project_resource_utilization', methods: ['GET'])]
    #[IsGranted('ROLE_MANAGER')]
    public function utilization(): Response
    {
        // Get resource utilization statistics
        $utilizationStats = $this->resourceService->getResourceUtilizationStatistics();
        
        return $this->render('project_resource/utilization.html.twig', [
            'utilization_stats' => $utilizationStats,
        ]);
    }
}
