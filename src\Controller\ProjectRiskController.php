<?php

namespace App\Controller;

use App\Entity\Project;
use App\Entity\ProjectRisk;
use App\Form\ProjectRiskForm;
use App\Service\ProjectRiskService;
use App\Service\ProjectService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/project')]
#[IsGranted('ROLE_USER')]
class ProjectRiskController extends AbstractController
{
    private ProjectRiskService $riskService;
    private ProjectService $projectService;

    public function __construct(
        ProjectRiskService $riskService,
        ProjectService $projectService
    ) {
        $this->riskService = $riskService;
        $this->projectService = $projectService;
    }

    #[Route('/{id}/risks', name: 'app_project_risks')]
    public function index(Project $project): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('view', $project);

        // Get all risks for the project
        $risks = $this->riskService->getRisksByProject($project);

        return $this->render('project_risk/index.html.twig', [
            'project' => $project,
            'risks' => $risks,
        ]);
    }

    #[Route('/{id}/risk/new', name: 'app_project_risk_new')]
    public function new(Request $request, Project $project): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $project);

        $risk = new ProjectRisk();
        $risk->setProject($project);

        // Get project members for the owner field
        $projectMembers = $this->projectService->getProjectMembers($project);

        $form = $this->createForm(ProjectRiskForm::class, $risk, [
            'project_member_only' => true,
            'project_members' => $projectMembers,
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->riskService->createRisk($risk);

            $this->addFlash('success', 'Risque créé avec succès.');
            return $this->redirectToRoute('app_project_risks', ['id' => $project->getId()]);
        }

        return $this->render('project_risk/new.html.twig', [
            'project' => $project,
            'form' => $form,
        ]);
    }

    #[Route('/risk/{id}', name: 'app_project_risk_show')]
    public function show(ProjectRisk $risk): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('view', $risk->getProject());

        return $this->render('project_risk/show.html.twig', [
            'risk' => $risk,
        ]);
    }

    #[Route('/risk/{id}/edit', name: 'app_project_risk_edit')]
    public function edit(Request $request, ProjectRisk $risk): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $risk->getProject());

        // Get project members for the owner field
        $projectMembers = $this->projectService->getProjectMembers($risk->getProject());

        $form = $this->createForm(ProjectRiskForm::class, $risk, [
            'project_member_only' => true,
            'project_members' => $projectMembers,
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->riskService->updateRisk($risk);

            $this->addFlash('success', 'Risque mis à jour avec succès.');
            return $this->redirectToRoute('app_project_risk_show', ['id' => $risk->getId()]);
        }

        return $this->render('project_risk/edit.html.twig', [
            'risk' => $risk,
            'form' => $form,
        ]);
    }

    #[Route('/risk/{id}/delete', name: 'app_project_risk_delete', methods: ['POST'])]
    public function delete(Request $request, ProjectRisk $risk): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $risk->getProject());

        if ($this->isCsrfTokenValid('delete'.$risk->getId(), $request->request->get('_token'))) {
            $projectId = $risk->getProject()->getId();
            $this->riskService->deleteRisk($risk);

            $this->addFlash('success', 'Risque supprimé avec succès.');
            return $this->redirectToRoute('app_project_risks', ['id' => $projectId]);
        }

        return $this->redirectToRoute('app_project_risk_show', ['id' => $risk->getId()]);
    }

    #[Route('/risk/{id}/close', name: 'app_project_risk_close', methods: ['POST'])]
    public function close(Request $request, ProjectRisk $risk): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $risk->getProject());

        if ($this->isCsrfTokenValid('close'.$risk->getId(), $request->request->get('_token'))) {
            $this->riskService->closeRisk($risk);

            $this->addFlash('success', 'Risque clôturé avec succès.');
        }

        return $this->redirectToRoute('app_project_risk_show', ['id' => $risk->getId()]);
    }

    #[Route('/risk/{id}/mitigate', name: 'app_project_risk_mitigate', methods: ['POST'])]
    public function mitigate(Request $request, ProjectRisk $risk): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $risk->getProject());

        if ($this->isCsrfTokenValid('mitigate'.$risk->getId(), $request->request->get('_token'))) {
            $this->riskService->mitigateRisk($risk);

            $this->addFlash('success', 'Risque atténué avec succès.');
        }

        return $this->redirectToRoute('app_project_risk_show', ['id' => $risk->getId()]);
    }

    #[Route('/risk/{id}/occurred', name: 'app_project_risk_occurred', methods: ['POST'])]
    public function occurred(Request $request, ProjectRisk $risk): Response
    {
        // Check if user has access to the project
        $this->denyAccessUnlessGranted('edit', $risk->getProject());

        if ($this->isCsrfTokenValid('occurred'.$risk->getId(), $request->request->get('_token'))) {
            $this->riskService->markRiskAsOccurred($risk);

            $this->addFlash('success', 'Risque marqué comme survenu.');
        }

        return $this->redirectToRoute('app_project_risk_show', ['id' => $risk->getId()]);
    }
}
