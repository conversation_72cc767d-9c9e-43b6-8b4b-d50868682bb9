<?php

namespace App\Controller;

use App\Entity\Project;
use App\Form\ProjectTemplateForm;
use App\Form\ProjectTemplateSelectForm;
use App\Service\ProjectTemplateService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/project/template')]
#[IsGranted('ROLE_MANAGER')]
class ProjectTemplateController extends AbstractController
{
    private ProjectTemplateService $templateService;

    public function __construct(ProjectTemplateService $templateService)
    {
        $this->templateService = $templateService;
    }

    #[Route('/', name: 'app_project_template_index', methods: ['GET'])]
    public function index(): Response
    {
        // Get all templates
        $templates = $this->templateService->getAllTemplates();
        
        return $this->render('project_template/index.html.twig', [
            'templates' => $templates,
        ]);
    }

    #[Route('/new-from-project/{id}', name: 'app_project_template_new_from_project', methods: ['GET', 'POST'])]
    public function newFromProject(Request $request, Project $project): Response
    {
        // Check if project is already a template
        if ($project->isIsTemplate()) {
            $this->addFlash('error', 'Ce projet est déjà un modèle.');
            return $this->redirectToRoute('app_project_show', ['id' => $project->getId()]);
        }
        
        // Handle form submission
        if ($request->isMethod('POST')) {
            $templateName = $request->request->get('template_name');
            
            if ($templateName) {
                $template = $this->templateService->createTemplateFromProject($project, $templateName);
                
                $this->addFlash('success', 'Modèle créé avec succès.');
                
                return $this->redirectToRoute('app_project_template_show', ['id' => $template->getId()]);
            }
            
            $this->addFlash('error', 'Veuillez entrer un nom pour le modèle.');
        }
        
        return $this->render('project_template/new_from_project.html.twig', [
            'project' => $project,
        ]);
    }

    #[Route('/new-standard', name: 'app_project_template_new_standard', methods: ['GET', 'POST'])]
    public function newStandard(Request $request): Response
    {
        // Handle form submission
        if ($request->isMethod('POST')) {
            $templateName = $request->request->get('template_name');
            $templateType = $request->request->get('template_type');
            
            if ($templateName && $templateType) {
                $template = $this->templateService->createStandardTemplate($templateName, $templateType);
                
                $this->addFlash('success', 'Modèle standard créé avec succès.');
                
                return $this->redirectToRoute('app_project_template_show', ['id' => $template->getId()]);
            }
            
            $this->addFlash('error', 'Veuillez remplir tous les champs.');
        }
        
        return $this->render('project_template/new_standard.html.twig', [
            'template_types' => [
                'web_development' => 'Développement Web',
                'marketing_campaign' => 'Campagne Marketing',
                'software_implementation' => 'Implémentation Logicielle',
                'event_planning' => 'Organisation d\'Événement',
            ],
        ]);
    }

    #[Route('/{id}', name: 'app_project_template_show', methods: ['GET'])]
    public function show(Project $template): Response
    {
        // Check if project is a template
        if (!$template->isIsTemplate()) {
            $this->addFlash('error', 'Ce projet n\'est pas un modèle.');
            return $this->redirectToRoute('app_project_show', ['id' => $template->getId()]);
        }
        
        return $this->render('project_template/show.html.twig', [
            'template' => $template,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_project_template_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Project $template): Response
    {
        // Check if project is a template
        if (!$template->isIsTemplate()) {
            $this->addFlash('error', 'Ce projet n\'est pas un modèle.');
            return $this->redirectToRoute('app_project_show', ['id' => $template->getId()]);
        }
        
        $form = $this->createForm(ProjectTemplateForm::class, $template);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $this->templateService->updateTemplate($template);
            
            $this->addFlash('success', 'Modèle mis à jour avec succès.');
            
            return $this->redirectToRoute('app_project_template_show', ['id' => $template->getId()]);
        }
        
        return $this->render('project_template/edit.html.twig', [
            'template' => $template,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/delete', name: 'app_project_template_delete', methods: ['POST'])]
    public function delete(Request $request, Project $template): Response
    {
        // Check if project is a template
        if (!$template->isIsTemplate()) {
            $this->addFlash('error', 'Ce projet n\'est pas un modèle.');
            return $this->redirectToRoute('app_project_show', ['id' => $template->getId()]);
        }
        
        if ($this->isCsrfTokenValid('delete'.$template->getId(), $request->request->get('_token'))) {
            $this->templateService->deleteTemplate($template);
            
            $this->addFlash('success', 'Modèle supprimé avec succès.');
        }
        
        return $this->redirectToRoute('app_project_template_index');
    }

    #[Route('/use/{id}', name: 'app_project_template_use', methods: ['GET', 'POST'])]
    public function useTemplate(Request $request, Project $template): Response
    {
        // Check if project is a template
        if (!$template->isIsTemplate()) {
            $this->addFlash('error', 'Ce projet n\'est pas un modèle.');
            return $this->redirectToRoute('app_project_show', ['id' => $template->getId()]);
        }
        
        // Handle form submission
        if ($request->isMethod('POST')) {
            $projectName = $request->request->get('project_name');
            $projectCode = $request->request->get('project_code');
            $managerId = $request->request->get('manager_id');
            
            if ($projectName && $projectCode && $managerId) {
                $userRepository = $this->getDoctrine()->getRepository('App\Entity\User');
                $manager = $userRepository->find($managerId);
                
                if ($manager) {
                    $project = $this->templateService->createProjectFromTemplate($template, $projectName, $projectCode, $manager);
                    
                    $this->addFlash('success', 'Projet créé avec succès à partir du modèle.');
                    
                    return $this->redirectToRoute('app_project_show', ['id' => $project->getId()]);
                }
            }
            
            $this->addFlash('error', 'Veuillez remplir tous les champs.');
        }
        
        // Get all users
        $userRepository = $this->getDoctrine()->getRepository('App\Entity\User');
        $users = $userRepository->findBy(['isActive' => true]);
        
        return $this->render('project_template/use_template.html.twig', [
            'template' => $template,
            'users' => $users,
        ]);
    }

    #[Route('/select', name: 'app_project_template_select', methods: ['GET', 'POST'])]
    public function selectTemplate(Request $request): Response
    {
        $form = $this->createForm(ProjectTemplateSelectForm::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $template = $form->get('template')->getData();
            
            return $this->redirectToRoute('app_project_template_use', ['id' => $template->getId()]);
        }
        
        return $this->render('project_template/select.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}
