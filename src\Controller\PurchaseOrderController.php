<?php

namespace App\Controller;

use App\Entity\Product;
use App\Entity\PurchaseOrder;
use App\Entity\PurchaseOrderItem;
use App\Entity\PurchaseRequest;
use App\Form\PurchaseOrderForm;
use App\Repository\PurchaseOrderRepository;
use App\Service\PurchaseOrderService;
use App\Service\PurchaseRequestService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Bundle\SecurityBundle\Security;

#[Route('/purchase/order')]
#[IsGranted('ROLE_USER')]
class PurchaseOrderController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private PurchaseOrderRepository $purchaseOrderRepository;
    private PurchaseOrderService $purchaseOrderService;
    private PurchaseRequestService $purchaseRequestService;
    private Security $security;

    public function __construct(
        EntityManagerInterface $entityManager,
        PurchaseOrderRepository $purchaseOrderRepository,
        PurchaseOrderService $purchaseOrderService,
        PurchaseRequestService $purchaseRequestService,
        Security $security
    ) {
        $this->entityManager = $entityManager;
        $this->purchaseOrderRepository = $purchaseOrderRepository;
        $this->purchaseOrderService = $purchaseOrderService;
        $this->purchaseRequestService = $purchaseRequestService;
        $this->security = $security;
    }

    #[Route('/', name: 'app_purchase_order_index', methods: ['GET'])]
    public function index(): Response
    {
        $purchaseOrders = $this->purchaseOrderRepository->findBy([], ['orderDate' => 'DESC']);

        return $this->render('purchase_order/index.html.twig', [
            'purchase_orders' => $purchaseOrders,
        ]);
    }

    #[Route('/ready-for-receipt', name: 'app_purchase_order_ready_for_receipt', methods: ['GET'])]
    public function readyForReceipt(): Response
    {
        $purchaseOrders = $this->purchaseOrderRepository->findReadyForGoodsReceipt();

        return $this->render('purchase_order/ready_for_receipt.html.twig', [
            'purchase_orders' => $purchaseOrders,
        ]);
    }

    #[Route('/print/{id}', name: 'app_purchase_order_print', methods: ['GET'])]
    public function print(PurchaseOrder $purchaseOrder): Response
    {
        return $this->render('purchase_order/print.html.twig', [
            'purchase_order' => $purchaseOrder,
        ]);
    }

    #[Route('/product-info/{id}', name: 'app_purchase_order_product_info', methods: ['GET'])]
    public function getProductInfo(Product $product): JsonResponse
    {
        $data = [
            'id' => $product->getId(),
            'code' => $product->getCode(),
            'name' => $product->getName(),
            'description' => $product->getDescription(),
            'unit' => $product->getUnit(),
            'price' => $product->getReferencePrice() ?: $product->getLastPurchasePrice() ?: 0,
            'category' => $product->getCategory() ? $product->getCategory()->getName() : null,
        ];

        return new JsonResponse(['success' => true, 'product' => $data]);
    }

    #[Route('/new', name: 'app_purchase_order_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $purchaseOrder = new PurchaseOrder();
        $purchaseOrder->setCreatedBy($this->security->getUser());

        // Add an initial empty item
        $item = new PurchaseOrderItem();
        $purchaseOrder->addItem($item);

        $form = $this->createForm(PurchaseOrderForm::class, $purchaseOrder);

        // Gérer les requêtes AJAX pour la sélection de produits
        if ($request->isXmlHttpRequest() && $request->request->get('ajax_submit')) {
            $form->handleRequest($request);

            // Récupérer les informations du produit sélectionné
            $items = $purchaseOrder->getItems();
            $lastItem = $items->last();

            if ($lastItem && $lastItem->getProduct()) {
                $product = $lastItem->getProduct();

                $data = [
                    'id' => $product->getId(),
                    'code' => $product->getCode(),
                    'name' => $product->getName(),
                    'description' => $product->getDescription(),
                    'unit' => $product->getUnit(),
                    'price' => $product->getReferencePrice() ?: $product->getLastPurchasePrice() ?: 0,
                    'category' => $product->getCategory() ? $product->getCategory()->getName() : null,
                ];

                return new JsonResponse(['success' => true, 'product' => $data]);
            }

            return new JsonResponse(['success' => false, 'message' => 'Produit non trouvé']);
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->purchaseOrderService->createPurchaseOrder($purchaseOrder);

                $this->addFlash('success', 'Bon de commande créé avec succès.');

                return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du bon de commande : ' . $e->getMessage());
            }
        }

        return $this->render('purchase_order/new.html.twig', [
            'purchase_order' => $purchaseOrder,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/new-from-request/{id}', name: 'app_purchase_order_new_from_request', methods: ['GET', 'POST'])]
    public function newFromRequest(Request $request, PurchaseRequest $purchaseRequest): Response
    {
        // Check if the purchase request can be converted
        if (!$purchaseRequest->canBeConverted()) {
            $this->addFlash('error', 'Cette demande d\'achat ne peut pas être convertie en bon de commande dans son état actuel.');
            return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
        }

        try {
            $purchaseOrder = $this->purchaseOrderService->createFromPurchaseRequest($purchaseRequest);

            $form = $this->createForm(PurchaseOrderForm::class, $purchaseOrder);
            $form->handleRequest($request);

            if ($form->isSubmitted() && $form->isValid()) {
                try {
                    $this->purchaseOrderService->savePurchaseOrder($purchaseOrder);

                    // Mark the purchase request as converted
                    $this->purchaseRequestService->markAsConverted($purchaseRequest, 'po', $purchaseOrder->getId());

                    $this->addFlash('success', 'Bon de commande créé avec succès à partir de la demande d\'achat.');

                    return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
                } catch (\Exception $e) {
                    $this->addFlash('error', 'Erreur lors de la création du bon de commande : ' . $e->getMessage());
                }
            }

            return $this->render('purchase_order/new_from_request.html.twig', [
                'purchase_order' => $purchaseOrder,
                'purchase_request' => $purchaseRequest,
                'form' => $form->createView(),
            ]);
        } catch (\Exception $e) {
            $this->addFlash('error', 'Erreur lors de la création du bon de commande : ' . $e->getMessage());
            return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
        }
    }

    #[Route('/{id}', name: 'app_purchase_order_show', methods: ['GET'])]
    public function show(PurchaseOrder $purchaseOrder): Response
    {
        return $this->render('purchase_order/show.html.twig', [
            'purchase_order' => $purchaseOrder,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_purchase_order_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, PurchaseOrder $purchaseOrder): Response
    {
        // Check if the purchase order can be edited
        if (!$purchaseOrder->canBeEdited()) {
            $this->addFlash('error', 'Ce bon de commande ne peut pas être modifié dans son état actuel.');
            return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
        }

        $form = $this->createForm(PurchaseOrderForm::class, $purchaseOrder);

        // Gérer les requêtes AJAX pour la sélection de produits
        if ($request->isXmlHttpRequest() && $request->request->get('ajax_submit')) {
            $form->handleRequest($request);

            // Récupérer les informations du produit sélectionné
            $items = $purchaseOrder->getItems();
            $lastItem = $items->last();

            if ($lastItem && $lastItem->getProduct()) {
                $product = $lastItem->getProduct();

                $data = [
                    'id' => $product->getId(),
                    'code' => $product->getCode(),
                    'name' => $product->getName(),
                    'description' => $product->getDescription(),
                    'unit' => $product->getUnit(),
                    'price' => $product->getReferencePrice() ?: $product->getLastPurchasePrice() ?: 0,
                    'category' => $product->getCategory() ? $product->getCategory()->getName() : null,
                ];

                return new JsonResponse(['success' => true, 'product' => $data]);
            }

            return new JsonResponse(['success' => false, 'message' => 'Produit non trouvé']);
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->purchaseOrderService->updatePurchaseOrder($purchaseOrder);

                $this->addFlash('success', 'Bon de commande mis à jour avec succès.');

                return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour du bon de commande : ' . $e->getMessage());
            }
        }

        return $this->render('purchase_order/edit.html.twig', [
            'purchase_order' => $purchaseOrder,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/send', name: 'app_purchase_order_send', methods: ['POST'])]
    public function send(Request $request, PurchaseOrder $purchaseOrder): Response
    {
        // Check if the purchase order can be sent
        if (!$purchaseOrder->canBeSent()) {
            $this->addFlash('error', 'Ce bon de commande ne peut pas être envoyé dans son état actuel.');
            return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
        }

        if ($this->isCsrfTokenValid('send'.$purchaseOrder->getId(), $request->request->get('_token'))) {
            try {
                $this->purchaseOrderService->sendPurchaseOrder($purchaseOrder);

                $this->addFlash('success', 'Bon de commande envoyé avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'envoi du bon de commande : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
    }

    #[Route('/{id}/cancel', name: 'app_purchase_order_cancel', methods: ['POST'])]
    public function cancel(Request $request, PurchaseOrder $purchaseOrder): Response
    {
        // Check if the purchase order can be cancelled
        if (!$purchaseOrder->canBeCancelled()) {
            $this->addFlash('error', 'Ce bon de commande ne peut pas être annulé dans son état actuel.');
            return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
        }

        if ($this->isCsrfTokenValid('cancel'.$purchaseOrder->getId(), $request->request->get('_token'))) {
            try {
                $this->purchaseOrderService->cancelPurchaseOrder($purchaseOrder);

                $this->addFlash('success', 'Bon de commande annulé avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'annulation du bon de commande : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
    }

    #[Route('/{id}/delete', name: 'app_purchase_order_delete', methods: ['POST'])]
    public function delete(Request $request, PurchaseOrder $purchaseOrder): Response
    {
        // Only draft purchase orders can be deleted
        if ($purchaseOrder->getStatus() !== PurchaseOrder::STATUS_DRAFT) {
            $this->addFlash('error', 'Seuls les bons de commande en brouillon peuvent être supprimés.');
            return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
        }

        if ($this->isCsrfTokenValid('delete'.$purchaseOrder->getId(), $request->request->get('_token'))) {
            try {
                $this->purchaseOrderService->deletePurchaseOrder($purchaseOrder);

                $this->addFlash('success', 'Bon de commande supprimé avec succès.');

                return $this->redirectToRoute('app_purchase_order_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression du bon de commande : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
    }


}
