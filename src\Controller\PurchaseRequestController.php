<?php

namespace App\Controller;

use App\Entity\PurchaseRequest;
use App\Entity\PurchaseRequestItem;
use App\Entity\Product;
use App\Form\PurchaseRequestApprovalForm;
use App\Form\PurchaseRequestForm;
use App\Form\PurchaseRequestItemForm;
use App\Repository\PurchaseRequestRepository;
use App\Repository\QuoteRepository;
use App\Service\PurchaseRequestService;
use App\Service\QuoteService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Bundle\SecurityBundle\Security;

#[Route('/purchase/request')]
#[IsGranted('ROLE_USER')]
class PurchaseRequestController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private PurchaseRequestRepository $purchaseRequestRepository;
    private PurchaseRequestService $purchaseRequestService;
    private QuoteRepository $quoteRepository;
    private QuoteService $quoteService;
    private Security $security;

    public function __construct(
        EntityManagerInterface $entityManager,
        PurchaseRequestRepository $purchaseRequestRepository,
        PurchaseRequestService $purchaseRequestService,
        QuoteRepository $quoteRepository,
        QuoteService $quoteService,
        Security $security
    ) {
        $this->entityManager = $entityManager;
        $this->purchaseRequestRepository = $purchaseRequestRepository;
        $this->purchaseRequestService = $purchaseRequestService;
        $this->quoteRepository = $quoteRepository;
        $this->quoteService = $quoteService;
        $this->security = $security;
    }

    #[Route('/', name: 'app_purchase_request_index', methods: ['GET'])]
    public function index(): Response
    {
        $purchaseRequests = $this->purchaseRequestRepository->findBy([], ['requestDate' => 'DESC']);

        return $this->render('purchase_request/index.html.twig', [
            'purchase_requests' => $purchaseRequests,
        ]);
    }

    #[Route('/pending-approval', name: 'app_purchase_request_pending_approval', methods: ['GET'])]
    #[IsGranted('ROLE_MANAGER')]
    public function pendingApproval(): Response
    {
        $purchaseRequests = $this->purchaseRequestRepository->findPendingApproval();

        return $this->render('purchase_request/pending_approval.html.twig', [
            'purchase_requests' => $purchaseRequests,
        ]);
    }

    #[Route('/ready-for-conversion', name: 'app_purchase_request_ready_for_conversion', methods: ['GET'])]
    public function readyForConversion(): Response
    {
        $purchaseRequests = $this->purchaseRequestRepository->findReadyForConversion();

        return $this->render('purchase_request/ready_for_conversion.html.twig', [
            'purchase_requests' => $purchaseRequests,
        ]);
    }

    #[Route('/product-info/{id}', name: 'app_purchase_request_product_info', methods: ['GET'])]
    public function getProductInfo(Product $product): JsonResponse
    {
        $data = [
            'id' => $product->getId(),
            'code' => $product->getCode(),
            'name' => $product->getName(),
            'description' => $product->getDescription(),
            'unit' => $product->getUnit(),
            'price' => $product->getReferencePrice() ?: $product->getLastPurchasePrice() ?: 0,
            'category' => $product->getCategory() ? $product->getCategory()->getName() : null,
        ];

        return new JsonResponse(['success' => true, 'product' => $data]);
    }

    #[Route('/new', name: 'app_purchase_request_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $purchaseRequest = new PurchaseRequest();
        $purchaseRequest->setCreatedBy($this->security->getUser());

        // Add an initial empty item
        $item = new PurchaseRequestItem();
        $purchaseRequest->addItem($item);

        $form = $this->createForm(PurchaseRequestForm::class, $purchaseRequest);

        // Gérer les requêtes AJAX pour la sélection de produits
        if ($request->isXmlHttpRequest() && $request->request->get('ajax_submit')) {
            $form->handleRequest($request);

            // Récupérer les informations du produit sélectionné
            $items = $purchaseRequest->getItems();
            $lastItem = $items->last();

            if ($lastItem && $lastItem->getProduct()) {
                $product = $lastItem->getProduct();

                $data = [
                    'id' => $product->getId(),
                    'code' => $product->getCode(),
                    'name' => $product->getName(),
                    'description' => $product->getDescription(),
                    'unit' => $product->getUnit(),
                    'price' => $product->getReferencePrice() ?: $product->getLastPurchasePrice() ?: 0,
                    'category' => $product->getCategory() ? $product->getCategory()->getName() : null,
                ];

                return new JsonResponse(['success' => true, 'product' => $data]);
            }

            return new JsonResponse(['success' => false, 'message' => 'Produit non trouvé']);
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->purchaseRequestService->createPurchaseRequest($purchaseRequest);

                $this->addFlash('success', 'Demande d\'achat créée avec succès.');

                return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création de la demande d\'achat : ' . $e->getMessage());
            }
        }

        return $this->render('purchase_request/new.html.twig', [
            'purchase_request' => $purchaseRequest,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_purchase_request_show', methods: ['GET'])]
    public function show(PurchaseRequest $purchaseRequest): Response
    {
        // Get quotes for this purchase request
        $quotes = $this->quoteRepository->findByPurchaseRequest($purchaseRequest);

        // Check if there are quotes to compare
        $hasQuotesToCompare = count($quotes) > 1;

        return $this->render('purchase_request/show.html.twig', [
            'purchase_request' => $purchaseRequest,
            'quotes' => $quotes,
            'has_quotes_to_compare' => $hasQuotesToCompare,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_purchase_request_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, PurchaseRequest $purchaseRequest): Response
    {
        // Check if the purchase request can be edited
        if (!$purchaseRequest->canBeEdited()) {
            $this->addFlash('error', 'Cette demande d\'achat ne peut pas être modifiée dans son état actuel.');
            return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
        }

        $form = $this->createForm(PurchaseRequestForm::class, $purchaseRequest);

        // Gérer les requêtes AJAX pour la sélection de produits
        if ($request->isXmlHttpRequest() && $request->request->get('ajax_submit')) {
            $form->handleRequest($request);

            // Récupérer les informations du produit sélectionné
            $items = $purchaseRequest->getItems();
            $lastItem = $items->last();

            if ($lastItem && $lastItem->getProduct()) {
                $product = $lastItem->getProduct();

                $data = [
                    'id' => $product->getId(),
                    'code' => $product->getCode(),
                    'name' => $product->getName(),
                    'description' => $product->getDescription(),
                    'unit' => $product->getUnit(),
                    'price' => $product->getReferencePrice() ?: $product->getLastPurchasePrice() ?: 0,
                    'category' => $product->getCategory() ? $product->getCategory()->getName() : null,
                ];

                return new JsonResponse(['success' => true, 'product' => $data]);
            }

            return new JsonResponse(['success' => false, 'message' => 'Produit non trouvé']);
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->purchaseRequestService->updatePurchaseRequest($purchaseRequest);

                $this->addFlash('success', 'Demande d\'achat mise à jour avec succès.');

                return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour de la demande d\'achat : ' . $e->getMessage());
            }
        }

        return $this->render('purchase_request/edit.html.twig', [
            'purchase_request' => $purchaseRequest,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/submit', name: 'app_purchase_request_submit', methods: ['POST'])]
    public function submit(Request $request, PurchaseRequest $purchaseRequest): Response
    {
        // Check if the purchase request can be submitted
        if (!$purchaseRequest->canBeSubmitted()) {
            $this->addFlash('error', 'Cette demande d\'achat ne peut pas être soumise dans son état actuel.');
            return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
        }

        if ($this->isCsrfTokenValid('submit'.$purchaseRequest->getId(), $request->request->get('_token'))) {
            try {
                $this->purchaseRequestService->submitPurchaseRequest($purchaseRequest);

                $this->addFlash('success', 'Demande d\'achat soumise avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la soumission de la demande d\'achat : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
    }

    #[Route('/{id}/approve', name: 'app_purchase_request_approve', methods: ['GET', 'POST'])]
    #[IsGranted('ROLE_MANAGER')]
    public function approve(Request $request, PurchaseRequest $purchaseRequest): Response
    {
        // Check if the purchase request can be approved
        if (!$purchaseRequest->canBeApproved() && !$purchaseRequest->canBeRejected()) {
            $this->addFlash('error', 'Cette demande d\'achat ne peut pas être approuvée ou rejetée dans son état actuel.');
            return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
        }

        $form = $this->createForm(PurchaseRequestApprovalForm::class, $purchaseRequest);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $decision = $form->get('decision')->getData();

            try {
                if ($decision === 'approve') {
                    $this->purchaseRequestService->approvePurchaseRequest($purchaseRequest);
                    $this->addFlash('success', 'Demande d\'achat approuvée avec succès.');
                } else {
                    $this->purchaseRequestService->rejectPurchaseRequest($purchaseRequest);
                    $this->addFlash('success', 'Demande d\'achat rejetée avec succès.');
                }

                return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors du traitement de la demande d\'achat : ' . $e->getMessage());
            }
        }

        return $this->render('purchase_request/approve.html.twig', [
            'purchase_request' => $purchaseRequest,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/delete', name: 'app_purchase_request_delete', methods: ['POST'])]
    public function delete(Request $request, PurchaseRequest $purchaseRequest): Response
    {
        // Only draft purchase requests can be deleted
        if ($purchaseRequest->getStatus() !== PurchaseRequest::STATUS_DRAFT) {
            $this->addFlash('error', 'Seules les demandes d\'achat en brouillon peuvent être supprimées.');
            return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
        }

        if ($this->isCsrfTokenValid('delete'.$purchaseRequest->getId(), $request->request->get('_token'))) {
            try {
                $this->purchaseRequestService->deletePurchaseRequest($purchaseRequest);

                $this->addFlash('success', 'Demande d\'achat supprimée avec succès.');

                return $this->redirectToRoute('app_purchase_request_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression de la demande d\'achat : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
    }

    #[Route('/{id}/convert', name: 'app_purchase_request_convert', methods: ['POST'])]
    public function convert(Request $request, PurchaseRequest $purchaseRequest): Response
    {
        // Check if the purchase request can be converted
        if (!$purchaseRequest->canBeConverted()) {
            $this->addFlash('error', 'Cette demande d\'achat ne peut pas être convertie dans son état actuel.');
            return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
        }

        if ($this->isCsrfTokenValid('convert'.$purchaseRequest->getId(), $request->request->get('_token'))) {
            try {
                // Check if there are quotes to compare
                $quotes = $this->quoteRepository->findByPurchaseRequest($purchaseRequest);
                if (count($quotes) > 1) {
                    // Redirect to quote comparison page
                    return $this->redirectToRoute('app_quote_compare', ['requestId' => $purchaseRequest->getId()]);
                }

                // Determine if we should create a purchase order or a contract based on the amount
                if ($this->purchaseRequestService->shouldCreateContract($purchaseRequest)) {
                    // Redirect to contract creation
                    return $this->redirectToRoute('app_contract_new_from_request', ['id' => $purchaseRequest->getId()]);
                } else {
                    // Redirect to purchase order creation
                    return $this->redirectToRoute('app_purchase_order_new_from_request', ['id' => $purchaseRequest->getId()]);
                }
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la conversion de la demande d\'achat : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
    }

    #[Route('/{id}/request-quotes', name: 'app_purchase_request_request_quotes', methods: ['GET'])]
    public function requestQuotes(PurchaseRequest $purchaseRequest): Response
    {
        // Check if the purchase request is in a state where quotes can be requested
        if (!$purchaseRequest->isApproved()) {
            $this->addFlash('error', 'Les devis ne peuvent être demandés que pour des demandes d\'achat approuvées.');
            return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
        }

        return $this->redirectToRoute('app_quote_request', ['requestId' => $purchaseRequest->getId()]);
    }
}
