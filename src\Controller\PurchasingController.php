<?php

namespace App\Controller;

use App\Repository\ContractRepository;
use App\Repository\GoodsReceiptRepository;
use App\Repository\PurchaseOrderRepository;
use App\Repository\PurchaseRequestRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/purchasing')]
#[IsGranted('ROLE_USER')]
class PurchasingController extends AbstractController
{
    private PurchaseRequestRepository $purchaseRequestRepository;
    private PurchaseOrderRepository $purchaseOrderRepository;
    private ContractRepository $contractRepository;
    private GoodsReceiptRepository $goodsReceiptRepository;

    public function __construct(
        PurchaseRequestRepository $purchaseRequestRepository,
        PurchaseOrderRepository $purchaseOrderRepository,
        ContractRepository $contractRepository,
        GoodsReceiptRepository $goodsReceiptRepository
    ) {
        $this->purchaseRequestRepository = $purchaseRequestRepository;
        $this->purchaseOrderRepository = $purchaseOrderRepository;
        $this->contractRepository = $contractRepository;
        $this->goodsReceiptRepository = $goodsReceiptRepository;
    }

    #[Route('/', name: 'app_purchasing_dashboard', methods: ['GET'])]
    public function dashboard(): Response
    {
        // Get counts for dashboard
        $pendingRequestsCount = count($this->purchaseRequestRepository->findPendingApproval());
        $approvedRequestsCount = count($this->purchaseRequestRepository->findReadyForConversion());
        $draftOrdersCount = count($this->purchaseOrderRepository->findByStatus('draft'));
        $sentOrdersCount = count($this->purchaseOrderRepository->findByStatus('sent'));
        $partiallyReceivedOrdersCount = count($this->purchaseOrderRepository->findByStatus('partially_received'));
        $readyForReceiptCount = count($this->purchaseOrderRepository->findReadyForGoodsReceipt());
        $activeContractsCount = count($this->contractRepository->findActiveContracts());
        $expiringContractsCount = count($this->contractRepository->findContractsExpiringSoon());
        
        // Get recent items
        $recentRequests = $this->purchaseRequestRepository->findBy([], ['requestDate' => 'DESC'], 5);
        $recentOrders = $this->purchaseOrderRepository->findBy([], ['orderDate' => 'DESC'], 5);
        $recentReceipts = $this->goodsReceiptRepository->findBy([], ['receiptDate' => 'DESC'], 5);
        $recentContracts = $this->contractRepository->findBy([], ['startDate' => 'DESC'], 5);

        return $this->render('purchasing/dashboard.html.twig', [
            'pending_requests_count' => $pendingRequestsCount,
            'approved_requests_count' => $approvedRequestsCount,
            'draft_orders_count' => $draftOrdersCount,
            'sent_orders_count' => $sentOrdersCount,
            'partially_received_orders_count' => $partiallyReceivedOrdersCount,
            'ready_for_receipt_count' => $readyForReceiptCount,
            'active_contracts_count' => $activeContractsCount,
            'expiring_contracts_count' => $expiringContractsCount,
            'recent_requests' => $recentRequests,
            'recent_orders' => $recentOrders,
            'recent_receipts' => $recentReceipts,
            'recent_contracts' => $recentContracts,
        ]);
    }
}
