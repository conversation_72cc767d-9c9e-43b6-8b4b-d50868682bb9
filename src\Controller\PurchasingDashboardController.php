<?php

namespace App\Controller;

use App\Repository\ContractRepository;
use App\Repository\PartnerRepository;
use App\Repository\PriceHistoryRepository;
use App\Repository\ProductRepository;
use App\Repository\PurchaseOrderRepository;
use App\Repository\PurchaseRequestRepository;
use App\Repository\QuoteRepository;
use App\Service\DashboardService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/purchasing/dashboard')]
#[IsGranted('ROLE_USER')]
class PurchasingDashboardController extends AbstractController
{
    private PurchaseRequestRepository $purchaseRequestRepository;
    private PurchaseOrderRepository $purchaseOrderRepository;
    private QuoteRepository $quoteRepository;
    private ContractRepository $contractRepository;
    private PartnerRepository $partnerRepository;
    private ProductRepository $productRepository;
    private PriceHistoryRepository $priceHistoryRepository;
    private DashboardService $dashboardService;

    public function __construct(
        PurchaseRequestRepository $purchaseRequestRepository,
        PurchaseOrderRepository $purchaseOrderRepository,
        QuoteRepository $quoteRepository,
        ContractRepository $contractRepository,
        PartnerRepository $partnerRepository,
        ProductRepository $productRepository,
        PriceHistoryRepository $priceHistoryRepository,
        DashboardService $dashboardService
    ) {
        $this->purchaseRequestRepository = $purchaseRequestRepository;
        $this->purchaseOrderRepository = $purchaseOrderRepository;
        $this->quoteRepository = $quoteRepository;
        $this->contractRepository = $contractRepository;
        $this->partnerRepository = $partnerRepository;
        $this->productRepository = $productRepository;
        $this->priceHistoryRepository = $priceHistoryRepository;
        $this->dashboardService = $dashboardService;
    }

    #[Route('/', name: 'app_purchasing_dashboard', methods: ['GET'])]
    public function index(): Response
    {
        // Get summary data
        $totalRequests = $this->purchaseRequestRepository->count([]);
        $pendingRequests = $this->purchaseRequestRepository->countPendingRequests();
        $totalOrders = $this->purchaseOrderRepository->count([]);
        $totalContracts = $this->contractRepository->count([]);
        $totalSuppliers = $this->partnerRepository->count(['isSupplier' => true]);
        $lowStockProducts = $this->productRepository->findLowStock();
        $expiringContracts = $this->contractRepository->findContractsExpiringSoon();
        
        // Get chart data
        $purchasesByMonth = $this->dashboardService->getPurchasesByMonth();
        $purchasesBySupplier = $this->dashboardService->getPurchasesBySupplier();
        $purchasesByCategory = $this->dashboardService->getPurchasesByCategory();
        $priceEvolutionData = $this->dashboardService->getPriceEvolutionData();
        
        // Get top suppliers
        $topSuppliers = $this->dashboardService->getTopSuppliers();
        
        // Get recent activity
        $recentRequests = $this->purchaseRequestRepository->findBy([], ['requestDate' => 'DESC'], 5);
        $recentOrders = $this->purchaseOrderRepository->findBy([], ['orderDate' => 'DESC'], 5);
        $recentQuotes = $this->quoteRepository->findBy([], ['quoteDate' => 'DESC'], 5);
        
        return $this->render('purchasing_dashboard/index.html.twig', [
            'total_requests' => $totalRequests,
            'pending_requests' => $pendingRequests,
            'total_orders' => $totalOrders,
            'total_contracts' => $totalContracts,
            'total_suppliers' => $totalSuppliers,
            'low_stock_products' => $lowStockProducts,
            'expiring_contracts' => $expiringContracts,
            'purchases_by_month' => $purchasesByMonth,
            'purchases_by_supplier' => $purchasesBySupplier,
            'purchases_by_category' => $purchasesByCategory,
            'price_evolution_data' => $priceEvolutionData,
            'top_suppliers' => $topSuppliers,
            'recent_requests' => $recentRequests,
            'recent_orders' => $recentOrders,
            'recent_quotes' => $recentQuotes,
        ]);
    }

    #[Route('/supplier-performance', name: 'app_purchasing_dashboard_supplier_performance', methods: ['GET'])]
    public function supplierPerformance(Request $request): Response
    {
        $supplierId = $request->query->get('supplier');
        $supplier = null;
        
        if ($supplierId) {
            $supplier = $this->partnerRepository->find($supplierId);
        }
        
        // Get all suppliers
        $suppliers = $this->partnerRepository->findBy(['isSupplier' => true], ['name' => 'ASC']);
        
        // Get performance data
        $performanceData = $this->dashboardService->getSupplierPerformanceData($supplier);
        
        // Get price comparison data
        $priceComparisonData = $this->dashboardService->getSupplierPriceComparisonData($supplier);
        
        return $this->render('purchasing_dashboard/supplier_performance.html.twig', [
            'suppliers' => $suppliers,
            'selected_supplier' => $supplier,
            'performance_data' => $performanceData,
            'price_comparison_data' => $priceComparisonData,
        ]);
    }

    #[Route('/price-analysis', name: 'app_purchasing_dashboard_price_analysis', methods: ['GET'])]
    public function priceAnalysis(Request $request): Response
    {
        $productId = $request->query->get('product');
        $product = null;
        
        if ($productId) {
            $product = $this->productRepository->find($productId);
        }
        
        // Get all products with price history
        $products = $this->productRepository->findWithPriceHistory();
        
        // Get price analysis data
        $priceAnalysisData = $this->dashboardService->getProductPriceAnalysisData($product);
        
        // Get supplier comparison data
        $supplierComparisonData = $this->dashboardService->getProductSupplierComparisonData($product);
        
        return $this->render('purchasing_dashboard/price_analysis.html.twig', [
            'products' => $products,
            'selected_product' => $product,
            'price_analysis_data' => $priceAnalysisData,
            'supplier_comparison_data' => $supplierComparisonData,
        ]);
    }

    #[Route('/purchase-trends', name: 'app_purchasing_dashboard_purchase_trends', methods: ['GET'])]
    public function purchaseTrends(): Response
    {
        // Get trend data
        $monthlyTrends = $this->dashboardService->getMonthlyPurchaseTrends();
        $categoryTrends = $this->dashboardService->getCategoryPurchaseTrends();
        $supplierTrends = $this->dashboardService->getSupplierPurchaseTrends();
        $projectTrends = $this->dashboardService->getProjectPurchaseTrends();
        
        return $this->render('purchasing_dashboard/purchase_trends.html.twig', [
            'monthly_trends' => $monthlyTrends,
            'category_trends' => $categoryTrends,
            'supplier_trends' => $supplierTrends,
            'project_trends' => $projectTrends,
        ]);
    }
}
