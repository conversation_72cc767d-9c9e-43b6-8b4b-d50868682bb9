<?php

namespace App\Controller;

use App\Entity\Partner;
use App\Entity\PurchaseRequest;
use App\Entity\Quote;
use App\Entity\QuoteItem;
use App\Form\QuoteForm;
use App\Form\QuoteItemForm;
use App\Form\QuoteRequestForm;
use App\Repository\PartnerRepository;
use App\Repository\QuoteRepository;
use App\Service\EmailService;
use App\Service\QuoteService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\Bundle\SecurityBundle\Security;

#[Route('/quote')]
#[IsGranted('ROLE_USER')]
class QuoteController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private QuoteRepository $quoteRepository;
    private QuoteService $quoteService;
    private Security $security;
    private EmailService $emailService;
    private PartnerRepository $partnerRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        QuoteRepository $quoteRepository,
        QuoteService $quoteService,
        Security $security,
        EmailService $emailService,
        PartnerRepository $partnerRepository
    ) {
        $this->entityManager = $entityManager;
        $this->quoteRepository = $quoteRepository;
        $this->quoteService = $quoteService;
        $this->security = $security;
        $this->emailService = $emailService;
        $this->partnerRepository = $partnerRepository;
    }

    #[Route('/', name: 'app_quote_index', methods: ['GET'])]
    public function index(): Response
    {
        $quotes = $this->quoteRepository->findBy([], ['quoteDate' => 'DESC']);

        return $this->render('quote/index.html.twig', [
            'quotes' => $quotes,
        ]);
    }

    #[Route('/new', name: 'app_quote_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $quote = new Quote();

        $form = $this->createForm(QuoteForm::class, $quote);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->quoteService->createQuote($quote);

                $this->addFlash('success', 'Devis créé avec succès.');

                return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du devis : ' . $e->getMessage());
            }
        }

        return $this->render('quote/new.html.twig', [
            'quote' => $quote,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/new-from-request/{requestId}', name: 'app_quote_new_from_request', methods: ['GET', 'POST'])]
    public function newFromRequest(Request $request, int $requestId): Response
    {
        $purchaseRequest = $this->entityManager->getRepository(PurchaseRequest::class)->find($requestId);

        if (!$purchaseRequest) {
            throw $this->createNotFoundException('Demande d\'achat non trouvée.');
        }

        $quote = $this->quoteService->createFromPurchaseRequest($purchaseRequest);

        $form = $this->createForm(QuoteForm::class, $quote);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->quoteService->createQuote($quote);

                $this->addFlash('success', 'Devis créé avec succès.');

                return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création du devis : ' . $e->getMessage());
            }
        }

        return $this->render('quote/new_from_request.html.twig', [
            'quote' => $quote,
            'purchaseRequest' => $purchaseRequest,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_quote_show', methods: ['GET'])]
    public function show(Quote $quote): Response
    {
        // Get historical price comparison data
        $historicalPriceComparison = $this->quoteService->getHistoricalPriceComparisonData($quote);

        return $this->render('quote/show.html.twig', [
            'quote' => $quote,
            'historicalPriceComparison' => $historicalPriceComparison,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_quote_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Quote $quote): Response
    {
        // Check if the quote can be edited
        if (!$quote->canBeEdited()) {
            $this->addFlash('error', 'Ce devis ne peut pas être modifié dans son état actuel.');
            return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
        }

        $form = $this->createForm(QuoteForm::class, $quote);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->quoteService->updateQuote($quote);

                $this->addFlash('success', 'Devis mis à jour avec succès.');

                return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la mise à jour du devis : ' . $e->getMessage());
            }
        }

        return $this->render('quote/edit.html.twig', [
            'quote' => $quote,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/delete', name: 'app_quote_delete', methods: ['POST'])]
    public function delete(Request $request, Quote $quote): Response
    {
        if ($this->isCsrfTokenValid('delete'.$quote->getId(), $request->request->get('_token'))) {
            try {
                $this->quoteService->deleteQuote($quote);

                $this->addFlash('success', 'Devis supprimé avec succès.');

                return $this->redirectToRoute('app_quote_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression du devis : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
    }

    #[Route('/{id}/add-item', name: 'app_quote_add_item', methods: ['GET', 'POST'])]
    public function addItem(Request $request, Quote $quote): Response
    {
        // Check if the quote can be edited
        if (!$quote->canBeEdited()) {
            $this->addFlash('error', 'Ce devis ne peut pas être modifié dans son état actuel.');
            return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
        }

        $quoteItem = new QuoteItem();
        $quoteItem->setQuote($quote);

        $form = $this->createForm(QuoteItemForm::class, $quoteItem);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $quote->addItem($quoteItem);
                $this->quoteService->updateQuote($quote);

                $this->addFlash('success', 'Article ajouté avec succès.');

                return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'ajout de l\'article : ' . $e->getMessage());
            }
        }

        return $this->render('quote/add_item.html.twig', [
            'quote' => $quote,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/send', name: 'app_quote_send', methods: ['POST'])]
    public function send(Request $request, Quote $quote): Response
    {
        if ($this->isCsrfTokenValid('send'.$quote->getId(), $request->request->get('_token'))) {
            try {
                $this->quoteService->sendQuote($quote);

                $this->addFlash('success', 'Devis envoyé avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'envoi du devis : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
    }

    #[Route('/{id}/receive', name: 'app_quote_receive', methods: ['POST'])]
    public function receive(Request $request, Quote $quote): Response
    {
        if ($this->isCsrfTokenValid('receive'.$quote->getId(), $request->request->get('_token'))) {
            try {
                $this->quoteService->receiveQuote($quote);

                $this->addFlash('success', 'Devis reçu avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la réception du devis : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
    }

    #[Route('/{id}/accept', name: 'app_quote_accept', methods: ['POST'])]
    public function accept(Request $request, Quote $quote): Response
    {
        if ($this->isCsrfTokenValid('accept'.$quote->getId(), $request->request->get('_token'))) {
            try {
                $this->quoteService->acceptQuote($quote);

                $this->addFlash('success', 'Devis accepté avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'acceptation du devis : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
    }

    #[Route('/{id}/reject', name: 'app_quote_reject', methods: ['POST'])]
    public function reject(Request $request, Quote $quote): Response
    {
        if ($this->isCsrfTokenValid('reject'.$quote->getId(), $request->request->get('_token'))) {
            try {
                $this->quoteService->rejectQuote($quote);

                $this->addFlash('success', 'Devis rejeté avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors du rejet du devis : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
    }

    #[Route('/compare/{requestId}', name: 'app_quote_compare', methods: ['GET'])]
    public function compare(int $requestId): Response
    {
        $purchaseRequest = $this->entityManager->getRepository(PurchaseRequest::class)->find($requestId);

        if (!$purchaseRequest) {
            throw $this->createNotFoundException('Demande d\'achat non trouvée.');
        }

        // Get quotes for the purchase request
        $quotes = $this->quoteService->getQuotesForPurchaseRequest($purchaseRequest);

        // Get comparison data
        $comparisonData = $this->quoteService->getQuoteComparisonData($quotes);

        return $this->render('quote/compare.html.twig', [
            'purchaseRequest' => $purchaseRequest,
            'quotes' => $quotes,
            'comparisonData' => $comparisonData,
        ]);
    }

    #[Route('/request-quote/{requestId}', name: 'app_quote_request', methods: ['GET', 'POST'])]
    public function requestQuote(Request $request, int $requestId): Response
    {
        $purchaseRequest = $this->entityManager->getRepository(PurchaseRequest::class)->find($requestId);

        if (!$purchaseRequest) {
            throw $this->createNotFoundException('Demande d\'achat non trouvée.');
        }

        // Get suppliers (partners with isSupplier = true)
        $suppliers = $this->partnerRepository->findBy(['isSupplier' => true], ['name' => 'ASC']);

        // Create form
        $form = $this->createFormBuilder()
            ->add('suppliers', null, [
                'label' => 'Fournisseurs',
                'class' => Partner::class,
                'choice_label' => 'name',
                'multiple' => true,
                'expanded' => true,
                'choices' => $suppliers,
                'required' => true,
            ])
            ->add('message', null, [
                'label' => 'Message supplémentaire',
                'required' => false,
                'attr' => [
                    'rows' => 5,
                ],
            ])
            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $selectedSuppliers = $data['suppliers'];
            $message = $data['message'] ?? null;

            try {
                // Send email to each selected supplier
                $this->emailService->sendQuoteRequestToMultipleSuppliers($purchaseRequest, $selectedSuppliers, $message);

                $this->addFlash('success', 'Demandes de devis envoyées avec succès à ' . count($selectedSuppliers) . ' fournisseur(s).');

                return $this->redirectToRoute('app_purchase_request_show', ['id' => $purchaseRequest->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'envoi des demandes de devis : ' . $e->getMessage());
            }
        }

        return $this->render('quote/request_quote.html.twig', [
            'purchase_request' => $purchaseRequest,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}/notify-received', name: 'app_quote_notify_received', methods: ['POST'])]
    public function notifyReceived(Request $request, Quote $quote): Response
    {
        if ($this->isCsrfTokenValid('notify_received'.$quote->getId(), $request->request->get('_token'))) {
            try {
                // Send notification email
                $this->emailService->sendQuoteReceivedNotification($quote);

                $this->addFlash('success', 'Notification envoyée avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de l\'envoi de la notification : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_quote_show', ['id' => $quote->getId()]);
    }
}
