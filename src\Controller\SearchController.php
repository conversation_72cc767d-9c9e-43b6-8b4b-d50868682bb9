<?php

namespace App\Controller;

use App\Service\GlobalSearchService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/search')]
#[IsGranted('ROLE_USER')]
class SearchController extends AbstractController
{
    private GlobalSearchService $searchService;

    public function __construct(GlobalSearchService $searchService)
    {
        $this->searchService = $searchService;
    }

    #[Route('/', name: 'app_search_index')]
    public function index(Request $request): Response
    {
        $query = $request->query->get('q', '');
        $results = [];
        
        if ($query && strlen($query) >= 2) {
            $searchResults = $this->searchService->searchWithSuggestions($query, [
                'limit' => 100
            ]);
            
            if ($searchResults['success']) {
                $results = $searchResults;
            }
        }

        return $this->render('search/index.html.twig', [
            'query' => $query,
            'results' => $results,
        ]);
    }

    #[Route('/api', name: 'app_search_api', methods: ['GET'])]
    public function api(Request $request): JsonResponse
    {
        $query = $request->query->get('q', '');
        $modules = $request->query->get('modules', 'all');
        $limit = $request->query->getInt('limit', 20);

        if (!$query || strlen($query) < 2) {
            return $this->json([
                'success' => false,
                'message' => 'La recherche doit contenir au moins 2 caractères',
                'results' => []
            ]);
        }

        $moduleArray = $modules === 'all' ? ['all'] : explode(',', $modules);
        
        $results = $this->searchService->globalSearch($query, [
            'modules' => $moduleArray,
            'limit' => $limit
        ]);

        return $this->json($results);
    }

    #[Route('/suggestions', name: 'app_search_suggestions', methods: ['GET'])]
    public function suggestions(Request $request): JsonResponse
    {
        $query = $request->query->get('q', '');
        
        if (!$query || strlen($query) < 2) {
            return $this->json([
                'success' => false,
                'suggestions' => []
            ]);
        }

        // Recherche rapide pour l'auto-complétion
        $results = $this->searchService->globalSearch($query, [
            'limit' => 10
        ]);

        $suggestions = [];
        
        if ($results['success']) {
            foreach ($results['results'] as $moduleResults) {
                foreach ($moduleResults as $result) {
                    $suggestions[] = [
                        'title' => $result['title'],
                        'subtitle' => $result['subtitle'] ?? '',
                        'type' => $result['type'],
                        'url' => $result['url'],
                        'icon' => $result['icon'],
                        'color' => $result['color']
                    ];
                }
            }
        }

        // Limiter à 10 suggestions
        $suggestions = array_slice($suggestions, 0, 10);

        return $this->json([
            'success' => true,
            'suggestions' => $suggestions
        ]);
    }

    #[Route('/quick', name: 'app_search_quick', methods: ['GET'])]
    public function quickSearch(Request $request): JsonResponse
    {
        $query = $request->query->get('q', '');
        
        if (!$query || strlen($query) < 2) {
            return $this->json([
                'success' => false,
                'results' => []
            ]);
        }

        $results = $this->searchService->globalSearch($query, [
            'limit' => 5 // Limite réduite pour la recherche rapide
        ]);

        // Formater les résultats pour l'affichage rapide
        $quickResults = [];
        
        if ($results['success']) {
            foreach ($results['results'] as $module => $moduleResults) {
                foreach ($moduleResults as $result) {
                    $quickResults[] = [
                        'id' => $result['id'],
                        'type' => $result['type'],
                        'module' => $module,
                        'title' => $result['title'],
                        'subtitle' => $result['subtitle'] ?? '',
                        'url' => $result['url'],
                        'icon' => $result['icon'],
                        'color' => $result['color'],
                        'relevance' => $result['relevance']
                    ];
                }
            }
            
            // Trier par pertinence
            usort($quickResults, function($a, $b) {
                return $b['relevance'] <=> $a['relevance'];
            });
            
            // Limiter à 8 résultats
            $quickResults = array_slice($quickResults, 0, 8);
        }

        return $this->json([
            'success' => true,
            'query' => $query,
            'results' => $quickResults,
            'total' => count($quickResults)
        ]);
    }

    #[Route('/advanced', name: 'app_search_advanced')]
    public function advanced(Request $request): Response
    {
        $filters = [
            'modules' => $request->query->get('modules', []),
            'date_from' => $request->query->get('date_from'),
            'date_to' => $request->query->get('date_to'),
            'status' => $request->query->get('status'),
            'sort' => $request->query->get('sort', 'relevance')
        ];

        $query = $request->query->get('q', '');
        $results = [];
        
        if ($query && strlen($query) >= 2) {
            $searchOptions = [
                'modules' => $filters['modules'] ?: ['all'],
                'limit' => 200
            ];
            
            $searchResults = $this->searchService->globalSearch($query, $searchOptions);
            
            if ($searchResults['success']) {
                $results = $this->applyAdvancedFilters($searchResults, $filters);
            }
        }

        return $this->render('search/advanced.html.twig', [
            'query' => $query,
            'results' => $results,
            'filters' => $filters,
            'available_modules' => [
                'projects' => 'Projets',
                'employees' => 'Employés',
                'invoices' => 'Factures',
                'partners' => 'Partenaires',
                'purchases' => 'Achats',
                'stock' => 'Stock',
                'departments' => 'Départements'
            ]
        ]);
    }

    #[Route('/export', name: 'app_search_export', methods: ['POST'])]
    public function export(Request $request): Response
    {
        $query = $request->request->get('query');
        $format = $request->request->get('format', 'csv');
        
        if (!$query) {
            $this->addFlash('error', 'Requête de recherche requise pour l\'export');
            return $this->redirectToRoute('app_search_index');
        }

        $results = $this->searchService->globalSearch($query, [
            'limit' => 1000
        ]);

        if (!$results['success']) {
            $this->addFlash('error', 'Erreur lors de la recherche');
            return $this->redirectToRoute('app_search_index');
        }

        switch ($format) {
            case 'csv':
                return $this->exportToCsv($results, $query);
            case 'json':
                return $this->exportToJson($results, $query);
            default:
                $this->addFlash('error', 'Format d\'export non supporté');
                return $this->redirectToRoute('app_search_index');
        }
    }

    private function applyAdvancedFilters(array $results, array $filters): array
    {
        $filteredResults = $results;
        
        // Filtrer par date
        if ($filters['date_from'] || $filters['date_to']) {
            $dateFrom = $filters['date_from'] ? new \DateTime($filters['date_from']) : null;
            $dateTo = $filters['date_to'] ? new \DateTime($filters['date_to']) : null;
            
            foreach ($filteredResults['results'] as $module => &$moduleResults) {
                $moduleResults = array_filter($moduleResults, function($result) use ($dateFrom, $dateTo) {
                    if (!isset($result['created_at'])) return true;
                    
                    $createdAt = new \DateTime($result['created_at']);
                    
                    if ($dateFrom && $createdAt < $dateFrom) return false;
                    if ($dateTo && $createdAt > $dateTo) return false;
                    
                    return true;
                });
            }
        }
        
        // Filtrer par statut
        if ($filters['status']) {
            foreach ($filteredResults['results'] as $module => &$moduleResults) {
                $moduleResults = array_filter($moduleResults, function($result) use ($filters) {
                    return !isset($result['status']) || $result['status'] === $filters['status'];
                });
            }
        }
        
        // Trier les résultats
        foreach ($filteredResults['results'] as $module => &$moduleResults) {
            switch ($filters['sort']) {
                case 'date':
                    usort($moduleResults, function($a, $b) {
                        return strtotime($b['created_at'] ?? '0') <=> strtotime($a['created_at'] ?? '0');
                    });
                    break;
                case 'title':
                    usort($moduleResults, function($a, $b) {
                        return strcasecmp($a['title'], $b['title']);
                    });
                    break;
                case 'relevance':
                default:
                    usort($moduleResults, function($a, $b) {
                        return ($b['relevance'] ?? 0) <=> ($a['relevance'] ?? 0);
                    });
                    break;
            }
        }
        
        return $filteredResults;
    }

    private function exportToCsv(array $results, string $query): Response
    {
        $csvContent = "Type,Titre,Sous-titre,Statut,Date de création,URL\n";
        
        foreach ($results['results'] as $module => $moduleResults) {
            foreach ($moduleResults as $result) {
                $csvContent .= sprintf(
                    "%s,%s,%s,%s,%s,%s\n",
                    $this->escapeCsv($result['type']),
                    $this->escapeCsv($result['title']),
                    $this->escapeCsv($result['subtitle'] ?? ''),
                    $this->escapeCsv($result['status'] ?? ''),
                    $this->escapeCsv($result['created_at'] ?? ''),
                    $this->escapeCsv($result['url'])
                );
            }
        }

        $response = new Response($csvContent);
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="search_results_' . date('Y-m-d_H-i-s') . '.csv"');

        return $response;
    }

    private function exportToJson(array $results, string $query): Response
    {
        $response = new Response(json_encode($results, JSON_PRETTY_PRINT));
        $response->headers->set('Content-Type', 'application/json');
        $response->headers->set('Content-Disposition', 'attachment; filename="search_results_' . date('Y-m-d_H-i-s') . '.json"');

        return $response;
    }

    private function escapeCsv(string $value): string
    {
        return '"' . str_replace('"', '""', $value) . '"';
    }
}
