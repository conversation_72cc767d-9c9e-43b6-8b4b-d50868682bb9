<?php

namespace App\Controller;

use App\Entity\Product;
use App\Entity\Stock\Inventory;
use App\Entity\Stock\InventoryLine;
use App\Entity\Stock\StockAlert;
use App\Entity\Stock\StockItem;
use App\Entity\Stock\StockLocation;
use App\Entity\Stock\StockMovement;
use App\Form\Stock\InventoryLineCountType;
use App\Form\Stock\InventoryType;
use App\Form\Stock\StockAdjustmentType;
use App\Form\Stock\StockAlertResolveType;
use App\Form\Stock\StockLocationFilterType;
use App\Form\Stock\StockLocationType;
use App\Form\Stock\StockMovementFilterType;
use App\Form\Stock\StockMovementType;
use App\Form\Stock\StockTransferType;
use App\Repository\ProductRepository;
use App\Repository\Stock\InventoryLineRepository;
use App\Repository\Stock\InventoryRepository;
use App\Repository\Stock\StockAlertRepository;
use App\Repository\Stock\StockItemRepository;
use App\Repository\Stock\StockLocationRepository;
use App\Repository\Stock\StockMovementRepository;
use App\Service\StockService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/stock')]
#[IsGranted('IS_AUTHENTICATED_FULLY')]
class StockController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private StockService $stockService;

    public function __construct(EntityManagerInterface $entityManager, StockService $stockService)
    {
        $this->entityManager = $entityManager;
        $this->stockService = $stockService;
    }

    #[Route('/', name: 'app_stock_dashboard')]
    public function dashboard(
        StockItemRepository $stockItemRepository,
        StockLocationRepository $stockLocationRepository,
        StockAlertRepository $stockAlertRepository
    ): Response {
        $dashboardData = $this->stockService->getDashboardData();
        $activeAlerts = $stockAlertRepository->findActive();
        
        return $this->render('stock/dashboard.html.twig', [
            'dashboardData' => $dashboardData,
            'activeAlerts' => $activeAlerts
        ]);
    }

    #[Route('/locations', name: 'app_stock_location_index')]
    public function locationIndex(StockLocationRepository $stockLocationRepository): Response
    {
        $locations = $stockLocationRepository->findAll();
        
        return $this->render('stock/location/index.html.twig', [
            'locations' => $locations
        ]);
    }

    #[Route('/locations/new', name: 'app_stock_location_new')]
    public function locationNew(Request $request, StockLocationRepository $stockLocationRepository): Response
    {
        $location = new StockLocation();
        $form = $this->createForm(StockLocationType::class, $location);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($location);
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Emplacement créé avec succès.');
            
            return $this->redirectToRoute('app_stock_location_index');
        }
        
        return $this->render('stock/location/new.html.twig', [
            'form' => $form->createView()
        ]);
    }

    #[Route('/locations/{id}', name: 'app_stock_location_show')]
    public function locationShow(
        StockLocation $location,
        StockItemRepository $stockItemRepository,
        StockMovementRepository $stockMovementRepository
    ): Response {
        $stockItems = $stockItemRepository->findByLocation($location->getId());
        $recentMovements = $stockMovementRepository->findByLocation($location->getId(), null, null);
        
        return $this->render('stock/location/show.html.twig', [
            'location' => $location,
            'stockItems' => $stockItems,
            'recentMovements' => $recentMovements
        ]);
    }

    #[Route('/locations/{id}/edit', name: 'app_stock_location_edit')]
    public function locationEdit(Request $request, StockLocation $location): Response
    {
        $form = $this->createForm(StockLocationType::class, $location);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $location->setUpdatedAt(new \DateTime());
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Emplacement modifié avec succès.');
            
            return $this->redirectToRoute('app_stock_location_show', ['id' => $location->getId()]);
        }
        
        return $this->render('stock/location/edit.html.twig', [
            'location' => $location,
            'form' => $form->createView()
        ]);
    }

    #[Route('/locations/{id}/delete', name: 'app_stock_location_delete', methods: ['POST'])]
    public function locationDelete(Request $request, StockLocation $location, StockItemRepository $stockItemRepository): Response
    {
        if ($this->isCsrfTokenValid('delete'.$location->getId(), $request->request->get('_token'))) {
            // Check if location has stock items
            $stockItems = $stockItemRepository->findByLocation($location->getId());
            
            if (!empty($stockItems)) {
                $this->addFlash('error', 'Impossible de supprimer cet emplacement car il contient des articles en stock.');
                return $this->redirectToRoute('app_stock_location_show', ['id' => $location->getId()]);
            }
            
            $this->entityManager->remove($location);
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Emplacement supprimé avec succès.');
        }
        
        return $this->redirectToRoute('app_stock_location_index');
    }

    #[Route('/items', name: 'app_stock_item_index')]
    public function itemIndex(Request $request, StockItemRepository $stockItemRepository, StockLocationRepository $stockLocationRepository): Response
    {
        $filterForm = $this->createForm(StockLocationFilterType::class);
        $filterForm->handleRequest($request);
        
        $locationId = null;
        if ($filterForm->isSubmitted() && $filterForm->isValid()) {
            $data = $filterForm->getData();
            if (isset($data['location']) && $data['location']) {
                $locationId = $data['location']->getId();
            }
        }
        
        $stockItems = $locationId 
            ? $stockItemRepository->findByLocation($locationId)
            : $stockItemRepository->findWithStock();
        
        return $this->render('stock/item/index.html.twig', [
            'stockItems' => $stockItems,
            'filterForm' => $filterForm->createView()
        ]);
    }

    #[Route('/items/{id}', name: 'app_stock_item_show')]
    public function itemShow(StockItem $stockItem, StockMovementRepository $stockMovementRepository): Response
    {
        $movements = $stockMovementRepository->findBy(['stockItem' => $stockItem], ['movementDate' => 'DESC']);
        
        return $this->render('stock/item/show.html.twig', [
            'stockItem' => $stockItem,
            'movements' => $movements
        ]);
    }

    #[Route('/products/{id}/stock', name: 'app_stock_product_show')]
    public function productStock(Product $product): Response
    {
        $stockStatus = $this->stockService->getProductStockStatus($product);
        $movements = $this->stockService->getProductMovements($product);
        
        return $this->render('stock/product/show.html.twig', [
            'stockStatus' => $stockStatus,
            'movements' => $movements
        ]);
    }

    #[Route('/movements', name: 'app_stock_movement_index')]
    public function movementIndex(Request $request, StockMovementRepository $stockMovementRepository): Response
    {
        $filterForm = $this->createForm(StockMovementFilterType::class);
        $filterForm->handleRequest($request);
        
        $startDate = null;
        $endDate = null;
        $type = null;
        $source = null;
        
        if ($filterForm->isSubmitted() && $filterForm->isValid()) {
            $data = $filterForm->getData();
            $startDate = $data['startDate'] ?? null;
            $endDate = $data['endDate'] ?? null;
            $type = $data['type'] ?? null;
            $source = $data['source'] ?? null;
        }
        
        if ($type) {
            $movements = $stockMovementRepository->findByType($type, $startDate, $endDate);
        } elseif ($source) {
            $movements = $stockMovementRepository->findBySource($source, $startDate, $endDate);
        } elseif ($startDate && $endDate) {
            $movements = $stockMovementRepository->findByDateRange($startDate, $endDate);
        } else {
            $movements = $stockMovementRepository->findRecent(50);
        }
        
        return $this->render('stock/movement/index.html.twig', [
            'movements' => $movements,
            'filterForm' => $filterForm->createView()
        ]);
    }

    #[Route('/movements/new', name: 'app_stock_movement_new')]
    public function movementNew(Request $request, ProductRepository $productRepository, StockLocationRepository $stockLocationRepository): Response
    {
        $form = $this->createForm(StockMovementType::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            
            try {
                if ($data['type'] === StockMovement::TYPE_IN) {
                    $this->stockService->addStock(
                        $data['product'],
                        $data['location'],
                        $data['quantity'],
                        $this->getUser(),
                        $data['source'],
                        $data['referenceType'],
                        $data['referenceNumber'],
                        $data['reason'],
                        $data['lotNumber'],
                        $data['expiryDate']
                    );
                    
                    $this->addFlash('success', 'Entrée de stock enregistrée avec succès.');
                } else {
                    $this->stockService->removeStock(
                        $data['product'],
                        $data['location'],
                        $data['quantity'],
                        $this->getUser(),
                        $data['source'],
                        $data['referenceType'],
                        $data['referenceNumber'],
                        $data['reason'],
                        $data['lotNumber']
                    );
                    
                    $this->addFlash('success', 'Sortie de stock enregistrée avec succès.');
                }
                
                return $this->redirectToRoute('app_stock_movement_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur: ' . $e->getMessage());
            }
        }
        
        return $this->render('stock/movement/new.html.twig', [
            'form' => $form->createView()
        ]);
    }

    #[Route('/transfers/new', name: 'app_stock_transfer_new')]
    public function transferNew(Request $request): Response
    {
        $form = $this->createForm(StockTransferType::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            
            try {
                $this->stockService->transferStock(
                    $data['product'],
                    $data['sourceLocation'],
                    $data['destinationLocation'],
                    $data['quantity'],
                    $this->getUser(),
                    $data['referenceType'],
                    $data['referenceNumber'],
                    $data['reason'],
                    $data['lotNumber'],
                    $data['expiryDate']
                );
                
                $this->addFlash('success', 'Transfert de stock effectué avec succès.');
                
                return $this->redirectToRoute('app_stock_movement_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur: ' . $e->getMessage());
            }
        }
        
        return $this->render('stock/transfer/new.html.twig', [
            'form' => $form->createView()
        ]);
    }

    #[Route('/adjustments/new', name: 'app_stock_adjustment_new')]
    public function adjustmentNew(Request $request): Response
    {
        $form = $this->createForm(StockAdjustmentType::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            
            try {
                $this->stockService->adjustStock(
                    $data['product'],
                    $data['location'],
                    $data['newQuantity'],
                    $this->getUser(),
                    $data['reason'],
                    $data['lotNumber'],
                    $data['expiryDate']
                );
                
                $this->addFlash('success', 'Ajustement de stock effectué avec succès.');
                
                return $this->redirectToRoute('app_stock_movement_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur: ' . $e->getMessage());
            }
        }
        
        return $this->render('stock/adjustment/new.html.twig', [
            'form' => $form->createView()
        ]);
    }

    #[Route('/inventories', name: 'app_stock_inventory_index')]
    public function inventoryIndex(InventoryRepository $inventoryRepository): Response
    {
        $inventories = $inventoryRepository->findBy([], ['startDate' => 'DESC']);
        
        return $this->render('stock/inventory/index.html.twig', [
            'inventories' => $inventories
        ]);
    }

    #[Route('/inventories/new', name: 'app_stock_inventory_new')]
    public function inventoryNew(Request $request, StockLocationRepository $stockLocationRepository): Response
    {
        $inventory = new Inventory();
        $form = $this->createForm(InventoryType::class, $inventory);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $inventory->setCreatedBy($this->getUser());
            $inventory->setStatus(Inventory::STATUS_DRAFT);
            
            $this->entityManager->persist($inventory);
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Inventaire créé avec succès.');
            
            return $this->redirectToRoute('app_stock_inventory_show', ['id' => $inventory->getId()]);
        }
        
        return $this->render('stock/inventory/new.html.twig', [
            'form' => $form->createView()
        ]);
    }

    #[Route('/inventories/{id}', name: 'app_stock_inventory_show')]
    public function inventoryShow(Inventory $inventory, InventoryLineRepository $inventoryLineRepository): Response
    {
        $lines = $inventoryLineRepository->findByInventory($inventory->getId());
        $stats = $inventoryLineRepository->getCompletionStats($inventory->getId());
        
        return $this->render('stock/inventory/show.html.twig', [
            'inventory' => $inventory,
            'lines' => $lines,
            'stats' => $stats
        ]);
    }

    #[Route('/inventories/{id}/generate-lines', name: 'app_stock_inventory_generate_lines', methods: ['POST'])]
    public function inventoryGenerateLines(Request $request, Inventory $inventory): Response
    {
        if ($this->isCsrfTokenValid('generate-lines'.$inventory->getId(), $request->request->get('_token'))) {
            try {
                $this->stockService->generateInventoryLines($inventory);
                $this->addFlash('success', 'Lignes d\'inventaire générées avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur: ' . $e->getMessage());
            }
        }
        
        return $this->redirectToRoute('app_stock_inventory_show', ['id' => $inventory->getId()]);
    }

    #[Route('/inventories/{id}/start', name: 'app_stock_inventory_start', methods: ['POST'])]
    public function inventoryStart(Request $request, Inventory $inventory): Response
    {
        if ($this->isCsrfTokenValid('start'.$inventory->getId(), $request->request->get('_token'))) {
            try {
                $this->stockService->startInventory($inventory, $this->getUser());
                $this->addFlash('success', 'Inventaire démarré avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur: ' . $e->getMessage());
            }
        }
        
        return $this->redirectToRoute('app_stock_inventory_show', ['id' => $inventory->getId()]);
    }

    #[Route('/inventories/{id}/complete', name: 'app_stock_inventory_complete', methods: ['POST'])]
    public function inventoryComplete(Request $request, Inventory $inventory): Response
    {
        if ($this->isCsrfTokenValid('complete'.$inventory->getId(), $request->request->get('_token'))) {
            try {
                $applyAdjustments = $request->request->get('apply_adjustments') === 'yes';
                $this->stockService->completeInventory($inventory, $this->getUser(), $applyAdjustments);
                $this->addFlash('success', 'Inventaire terminé avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur: ' . $e->getMessage());
            }
        }
        
        return $this->redirectToRoute('app_stock_inventory_show', ['id' => $inventory->getId()]);
    }

    #[Route('/inventories/{id}/cancel', name: 'app_stock_inventory_cancel', methods: ['POST'])]
    public function inventoryCancel(Request $request, Inventory $inventory): Response
    {
        if ($this->isCsrfTokenValid('cancel'.$inventory->getId(), $request->request->get('_token'))) {
            try {
                $this->stockService->cancelInventory($inventory);
                $this->addFlash('success', 'Inventaire annulé avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur: ' . $e->getMessage());
            }
        }
        
        return $this->redirectToRoute('app_stock_inventory_show', ['id' => $inventory->getId()]);
    }

    #[Route('/inventory-lines/{id}/count', name: 'app_stock_inventory_line_count')]
    public function inventoryLineCount(Request $request, InventoryLine $line): Response
    {
        $form = $this->createForm(InventoryLineCountType::class, $line);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->stockService->updateInventoryLineCount($line, $line->getCountedQuantity(), $this->getUser());
                $this->addFlash('success', 'Comptage enregistré avec succès.');
                
                return $this->redirectToRoute('app_stock_inventory_show', ['id' => $line->getInventory()->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur: ' . $e->getMessage());
            }
        }
        
        return $this->render('stock/inventory/count.html.twig', [
            'line' => $line,
            'form' => $form->createView()
        ]);
    }

    #[Route('/alerts', name: 'app_stock_alert_index')]
    public function alertIndex(StockAlertRepository $stockAlertRepository): Response
    {
        $alerts = $stockAlertRepository->findBy([], ['createdAt' => 'DESC']);
        
        return $this->render('stock/alert/index.html.twig', [
            'alerts' => $alerts
        ]);
    }

    #[Route('/alerts/{id}', name: 'app_stock_alert_show')]
    public function alertShow(StockAlert $alert): Response
    {
        return $this->render('stock/alert/show.html.twig', [
            'alert' => $alert
        ]);
    }

    #[Route('/alerts/{id}/acknowledge', name: 'app_stock_alert_acknowledge', methods: ['POST'])]
    public function alertAcknowledge(Request $request, StockAlert $alert): Response
    {
        if ($this->isCsrfTokenValid('acknowledge'.$alert->getId(), $request->request->get('_token'))) {
            $alert->acknowledge();
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Alerte reconnue avec succès.');
        }
        
        return $this->redirectToRoute('app_stock_alert_show', ['id' => $alert->getId()]);
    }

    #[Route('/alerts/{id}/resolve', name: 'app_stock_alert_resolve')]
    public function alertResolve(Request $request, StockAlert $alert): Response
    {
        $form = $this->createForm(StockAlertResolveType::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $alert->resolve($data['notes']);
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Alerte résolue avec succès.');
            
            return $this->redirectToRoute('app_stock_alert_index');
        }
        
        return $this->render('stock/alert/resolve.html.twig', [
            'alert' => $alert,
            'form' => $form->createView()
        ]);
    }

    #[Route('/alerts/{id}/ignore', name: 'app_stock_alert_ignore')]
    public function alertIgnore(Request $request, StockAlert $alert): Response
    {
        $form = $this->createForm(StockAlertResolveType::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $alert->ignore($data['notes']);
            $this->entityManager->flush();
            
            $this->addFlash('success', 'Alerte ignorée avec succès.');
            
            return $this->redirectToRoute('app_stock_alert_index');
        }
        
        return $this->render('stock/alert/ignore.html.twig', [
            'alert' => $alert,
            'form' => $form->createView()
        ]);
    }

    #[Route('/check-expiring', name: 'app_stock_check_expiring')]
    public function checkExpiring(): Response
    {
        $count = $this->stockService->checkForExpiringProducts();
        
        if ($count > 0) {
            $this->addFlash('success', $count . ' alerte(s) créée(s) pour des produits proches de la date d\'expiration.');
        } else {
            $this->addFlash('info', 'Aucun produit proche de la date d\'expiration trouvé.');
        }
        
        return $this->redirectToRoute('app_stock_dashboard');
    }
}
