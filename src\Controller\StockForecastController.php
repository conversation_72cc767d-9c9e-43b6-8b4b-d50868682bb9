<?php

namespace App\Controller;

use App\Entity\Product;
use App\Entity\ProductCategory;
use App\Repository\ProductCategoryRepository;
use App\Repository\ProductRepository;
use App\Service\StockForecastService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/stock-forecast')]
#[IsGranted('ROLE_USER')]
class StockForecastController extends AbstractController
{
    private ProductRepository $productRepository;
    private ProductCategoryRepository $categoryRepository;
    private StockForecastService $forecastService;

    public function __construct(
        ProductRepository $productRepository,
        ProductCategoryRepository $categoryRepository,
        StockForecastService $forecastService
    ) {
        $this->productRepository = $productRepository;
        $this->categoryRepository = $categoryRepository;
        $this->forecastService = $forecastService;
    }

    #[Route('/', name: 'app_stock_forecast_index', methods: ['GET'])]
    public function index(): Response
    {
        $products = $this->productRepository->findBy([], ['name' => 'ASC']);
        $categories = $this->categoryRepository->findBy([], ['name' => 'ASC']);
        $productsNeedingReorder = $this->forecastService->getProductsNeedingReorder();
        
        return $this->render('stock_forecast/index.html.twig', [
            'products' => $products,
            'categories' => $categories,
            'products_needing_reorder' => $productsNeedingReorder,
        ]);
    }

    #[Route('/product/{id}', name: 'app_stock_forecast_product', methods: ['GET'])]
    public function forecastForProduct(Request $request, Product $product): Response
    {
        $months = $request->query->getInt('months', 6);
        $forecast = $this->forecastService->getStockForecast($product, $months);
        
        return $this->render('stock_forecast/product.html.twig', [
            'forecast' => $forecast,
            'months' => $months,
        ]);
    }

    #[Route('/category/{id}', name: 'app_stock_forecast_category', methods: ['GET'])]
    public function forecastForCategory(Request $request, ProductCategory $category): Response
    {
        $months = $request->query->getInt('months', 6);
        $forecast = $this->forecastService->getStockForecastForCategory($category, $months);
        
        return $this->render('stock_forecast/category.html.twig', [
            'forecast' => $forecast,
            'months' => $months,
        ]);
    }

    #[Route('/reorder-list', name: 'app_stock_forecast_reorder_list', methods: ['GET'])]
    public function reorderList(): Response
    {
        $productsNeedingReorder = $this->forecastService->getProductsNeedingReorder();
        
        return $this->render('stock_forecast/reorder_list.html.twig', [
            'products_needing_reorder' => $productsNeedingReorder,
        ]);
    }
}
