<?php

namespace App\Controller;

use App\Entity\Contract;
use App\Entity\Partner;
use App\Entity\PurchaseOrder;
use App\Entity\SupplierRating;
use App\Form\SupplierRatingForm;
use App\Form\SupplierRatingMultipleForm;
use App\Repository\SupplierRatingRepository;
use App\Service\SupplierRatingService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/supplier-rating')]
#[IsGranted('ROLE_USER')]
class SupplierRatingController extends AbstractController
{
    private EntityManagerInterface $entityManager;
    private SupplierRatingRepository $ratingRepository;
    private SupplierRatingService $ratingService;

    public function __construct(
        EntityManagerInterface $entityManager,
        SupplierRatingRepository $ratingRepository,
        SupplierRatingService $ratingService
    ) {
        $this->entityManager = $entityManager;
        $this->ratingRepository = $ratingRepository;
        $this->ratingService = $ratingService;
    }

    #[Route('/', name: 'app_supplier_rating_index', methods: ['GET'])]
    public function index(): Response
    {
        $ratings = $this->ratingRepository->findBy([], ['createdAt' => 'DESC']);

        return $this->render('supplier_rating/index.html.twig', [
            'ratings' => $ratings,
        ]);
    }

    #[Route('/new', name: 'app_supplier_rating_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        $rating = new SupplierRating();
        
        $form = $this->createForm(SupplierRatingForm::class, $rating);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->ratingService->createRating($rating);

                $this->addFlash('success', 'Évaluation créée avec succès.');

                return $this->redirectToRoute('app_supplier_rating_index');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création de l\'évaluation : ' . $e->getMessage());
            }
        }

        return $this->render('supplier_rating/new.html.twig', [
            'rating' => $rating,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/supplier/{id}', name: 'app_supplier_rating_by_supplier', methods: ['GET'])]
    public function bySupplier(Partner $supplier): Response
    {
        $ratings = $this->ratingRepository->findBySupplier($supplier);
        $averageRatingsByCategory = $this->ratingService->getAverageRatingsByCategory($supplier);
        $ratingDistribution = $this->ratingService->getRatingDistribution($supplier);
        $ratingTrends = $this->ratingService->getRatingTrends($supplier);
        
        return $this->render('supplier_rating/by_supplier.html.twig', [
            'supplier' => $supplier,
            'ratings' => $ratings,
            'averageRatingsByCategory' => $averageRatingsByCategory,
            'ratingDistribution' => $ratingDistribution,
            'ratingTrends' => $ratingTrends,
            'ratingCategories' => $this->ratingService->getRatingCategories(),
        ]);
    }

    #[Route('/purchase-order/{id}/rate', name: 'app_supplier_rating_purchase_order', methods: ['GET', 'POST'])]
    public function ratePurchaseOrder(Request $request, PurchaseOrder $purchaseOrder): Response
    {
        // Check if the purchase order can be rated
        if (!in_array($purchaseOrder->getStatus(), [PurchaseOrder::STATUS_FULLY_RECEIVED, PurchaseOrder::STATUS_CLOSED])) {
            $this->addFlash('error', 'Seules les commandes entièrement reçues ou clôturées peuvent être évaluées.');
            return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
        }
        
        $form = $this->createForm(SupplierRatingMultipleForm::class, null, [
            'supplier' => $purchaseOrder->getSupplier(),
            'categories' => $this->ratingService->getRatingCategories(),
        ]);
        
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $ratings = $form->getData()['ratings'];
                $this->ratingService->createRatingsForPurchaseOrder($purchaseOrder, $ratings);

                $this->addFlash('success', 'Évaluations créées avec succès.');

                return $this->redirectToRoute('app_purchase_order_show', ['id' => $purchaseOrder->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création des évaluations : ' . $e->getMessage());
            }
        }

        return $this->render('supplier_rating/rate_purchase_order.html.twig', [
            'purchase_order' => $purchaseOrder,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/contract/{id}/rate', name: 'app_supplier_rating_contract', methods: ['GET', 'POST'])]
    public function rateContract(Request $request, Contract $contract): Response
    {
        // Check if the contract can be rated
        if ($contract->getEndDate() > new \DateTime()) {
            $this->addFlash('error', 'Seuls les contrats terminés peuvent être évalués.');
            return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
        }
        
        $form = $this->createForm(SupplierRatingMultipleForm::class, null, [
            'supplier' => $contract->getSupplier(),
            'categories' => $this->ratingService->getRatingCategories(),
        ]);
        
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $ratings = $form->getData()['ratings'];
                $this->ratingService->createRatingsForContract($contract, $ratings);

                $this->addFlash('success', 'Évaluations créées avec succès.');

                return $this->redirectToRoute('app_contract_show', ['id' => $contract->getId()]);
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la création des évaluations : ' . $e->getMessage());
            }
        }

        return $this->render('supplier_rating/rate_contract.html.twig', [
            'contract' => $contract,
            'form' => $form->createView(),
        ]);
    }

    #[Route('/{id}', name: 'app_supplier_rating_show', methods: ['GET'])]
    public function show(SupplierRating $rating): Response
    {
        return $this->render('supplier_rating/show.html.twig', [
            'rating' => $rating,
        ]);
    }

    #[Route('/{id}/delete', name: 'app_supplier_rating_delete', methods: ['POST'])]
    public function delete(Request $request, SupplierRating $rating): Response
    {
        if ($this->isCsrfTokenValid('delete'.$rating->getId(), $request->request->get('_token'))) {
            try {
                $supplier = $rating->getSupplier();
                
                $this->entityManager->remove($rating);
                $this->entityManager->flush();
                
                // Update the supplier's overall rating
                $this->ratingService->updateSupplierRating($supplier);

                $this->addFlash('success', 'Évaluation supprimée avec succès.');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Erreur lors de la suppression de l\'évaluation : ' . $e->getMessage());
            }
        }

        return $this->redirectToRoute('app_supplier_rating_index');
    }
}
