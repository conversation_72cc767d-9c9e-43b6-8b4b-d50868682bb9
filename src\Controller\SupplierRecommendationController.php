<?php

namespace App\Controller;

use App\Entity\Product;
use App\Entity\ProductCategory;
use App\Repository\ProductCategoryRepository;
use App\Repository\ProductRepository;
use App\Service\SupplierRecommendationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/supplier-recommendation')]
#[IsGranted('ROLE_USER')]
class SupplierRecommendationController extends AbstractController
{
    private ProductRepository $productRepository;
    private ProductCategoryRepository $categoryRepository;
    private SupplierRecommendationService $recommendationService;

    public function __construct(
        ProductRepository $productRepository,
        ProductCategoryRepository $categoryRepository,
        SupplierRecommendationService $recommendationService
    ) {
        $this->productRepository = $productRepository;
        $this->categoryRepository = $categoryRepository;
        $this->recommendationService = $recommendationService;
    }

    #[Route('/', name: 'app_supplier_recommendation_index', methods: ['GET'])]
    public function index(): Response
    {
        $products = $this->productRepository->findBy([], ['name' => 'ASC']);
        $categories = $this->categoryRepository->findBy([], ['name' => 'ASC']);
        
        return $this->render('supplier_recommendation/index.html.twig', [
            'products' => $products,
            'categories' => $categories,
        ]);
    }

    #[Route('/product/{id}', name: 'app_supplier_recommendation_product', methods: ['GET'])]
    public function recommendForProduct(Product $product): Response
    {
        $recommendations = $this->recommendationService->getRecommendedSuppliersForProduct($product);
        
        return $this->render('supplier_recommendation/product.html.twig', [
            'product' => $product,
            'recommendations' => $recommendations,
        ]);
    }

    #[Route('/category/{id}', name: 'app_supplier_recommendation_category', methods: ['GET'])]
    public function recommendForCategory(ProductCategory $category): Response
    {
        $recommendations = $this->recommendationService->getRecommendedSuppliersForCategory($category);
        
        return $this->render('supplier_recommendation/category.html.twig', [
            'category' => $category,
            'recommendations' => $recommendations,
        ]);
    }
}
