<?php

namespace App\Controller;

use App\Entity\Task;
use App\Form\TaskForm;
use App\Repository\TaskRepository;
use App\Service\TaskService;
use App\Service\ActivityLogService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/task')]
final class TaskController extends AbstractController
{
    private TaskService $taskService;
    private ActivityLogService $activityLogService;

    public function __construct(TaskService $taskService, ActivityLogService $activityLogService)
    {
        $this->taskService = $taskService;
        $this->activityLogService = $activityLogService;
    }

    #[Route(name: 'app_task_index', methods: ['GET'])]
    public function index(TaskRepository $taskRepository): Response
    {
        $pendingTasks = $taskRepository->findBy(['status' => 'pending'], ['dueDate' => 'ASC']);
        $completedTasks = $taskRepository->findBy(['status' => 'completed'], ['completedAt' => 'DESC'], 10);
        $overdueTasks = $this->taskService->getOverdueTasks();
        $tasksDueSoon = $this->taskService->getTasksDueSoon();

        return $this->render('task/index.html.twig', [
            'pending_tasks' => $pendingTasks,
            'completed_tasks' => $completedTasks,
            'overdue_tasks' => $overdueTasks,
            'tasks_due_soon' => $tasksDueSoon,
        ]);
    }

    #[Route('/new', name: 'app_task_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $task = new Task();
        $form = $this->createForm(TaskForm::class, $task);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($task);
            $entityManager->flush();

            // Log activity
            $this->activityLogService->log(
                'created',
                'task',
                $task->getId(),
                'Task "' . $task->getTitle() . '" created'
            );

            $this->addFlash('success', 'Task created successfully.');
            return $this->redirectToRoute('app_task_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('task/new.html.twig', [
            'task' => $task,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_task_show', methods: ['GET'])]
    public function show(Task $task): Response
    {
        // Get activity logs for this task
        $activityLogs = $this->activityLogService->getLogsForEntity('task', $task->getId());

        return $this->render('task/show.html.twig', [
            'task' => $task,
            'activity_logs' => $activityLogs,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_task_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, Task $task, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(TaskForm::class, $task);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $task->setUpdatedAt(new \DateTimeImmutable());
            $entityManager->flush();

            // Log activity
            $this->activityLogService->log(
                'updated',
                'task',
                $task->getId(),
                'Task "' . $task->getTitle() . '" updated'
            );

            $this->addFlash('success', 'Task updated successfully.');
            return $this->redirectToRoute('app_task_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('task/edit.html.twig', [
            'task' => $task,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/complete', name: 'app_task_complete', methods: ['POST'])]
    public function complete(Request $request, Task $task): Response
    {
        if ($this->isCsrfTokenValid('complete'.$task->getId(), $request->getPayload()->getString('_token'))) {
            $this->taskService->completeTask($task);

            // Log activity
            $this->activityLogService->log(
                'completed',
                'task',
                $task->getId(),
                'Task "' . $task->getTitle() . '" marked as completed'
            );

            $this->addFlash('success', 'Task marked as completed.');
        }

        return $this->redirectToRoute('app_task_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/{id}', name: 'app_task_delete', methods: ['POST'])]
    public function delete(Request $request, Task $task, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$task->getId(), $request->getPayload()->getString('_token'))) {
            $taskTitle = $task->getTitle();
            $taskId = $task->getId();

            $entityManager->remove($task);
            $entityManager->flush();

            // Log activity
            $this->activityLogService->log(
                'deleted',
                'task',
                $taskId,
                'Task "' . $taskTitle . '" deleted'
            );

            $this->addFlash('success', 'Task deleted successfully.');
        }

        return $this->redirectToRoute('app_task_index', [], Response::HTTP_SEE_OTHER);
    }
}
