<?php

namespace App\Controller;

use App\Entity\Project;
use App\Repository\ProjectRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

class TestController extends AbstractController
{
    #[Route('/test', name: 'app_test')]
    #[IsGranted('ROLE_USER')]
    public function index(ProjectRepository $projectRepository): Response
    {
        $projects = $projectRepository->findAll();
        
        return $this->render('test/index.html.twig', [
            'projects' => $projects,
        ]);
    }
    
    #[Route('/test/project/{id}', name: 'app_test_project')]
    #[IsGranted('ROLE_USER')]
    public function project(Project $project): Response
    {
        return $this->render('test/project.html.twig', [
            'project' => $project,
        ]);
    }
}
