<?php

namespace App\Controller;

use App\Entity\User;
use App\Form\TwoFactorVerifyType;
use App\Form\TwoFactorEnableType;
use App\Service\TwoFactorAuthService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/2fa')]
class TwoFactorAuthController extends AbstractController
{
    public function __construct(
        private TwoFactorAuthService $twoFactorAuthService,
        private EntityManagerInterface $entityManager,
    ) {
    }
    
    #[Route('/verify', name: 'app_2fa_verify')]
    public function verify(Request $request, SessionInterface $session): Response
    {
        // Check if user is already verified
        if ($this->twoFactorAuthService->isVerified()) {
            return $this->redirectToRoute('app_home');
        }
        
        // Get the user ID from session
        $userId = $session->get('2fa_user_id');
        if (!$userId) {
            return $this->redirectToRoute('app_login');
        }
        
        // Get the user
        $user = $this->entityManager->getRepository(User::class)->find($userId);
        if (!$user) {
            return $this->redirectToRoute('app_login');
        }
        
        $form = $this->createForm(TwoFactorVerifyType::class);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $code = $form->get('code')->getData();
            
            if ($this->twoFactorAuthService->verifyCode($code)) {
                // Code is valid, redirect to home
                return $this->redirectToRoute('app_home');
            }
            
            // Code is invalid
            $this->addFlash('error', 'Le code est invalide ou a expiré.');
        }
        
        return $this->render('security/two_factor_verify.html.twig', [
            'form' => $form->createView(),
            'user' => $user,
        ]);
    }
    
    #[Route('/setup', name: 'app_2fa_setup')]
    #[IsGranted('IS_AUTHENTICATED_FULLY')]
    public function setup(Request $request): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        
        $form = $this->createForm(TwoFactorEnableType::class, null, [
            'is_enabled' => $user->isTwoFactorEnabled(),
        ]);
        $form->handleRequest($request);
        
        if ($form->isSubmitted() && $form->isValid()) {
            $enable = $form->get('enable')->getData();
            $method = $form->get('method')->getData();
            
            if ($enable) {
                // Enable 2FA
                $this->twoFactorAuthService->enableTwoFactor($user, $method);
                $this->addFlash('success', 'L\'authentification à deux facteurs a été activée.');
                
                // Generate backup codes
                $backupCodes = $this->twoFactorAuthService->generateBackupCodes($user);
                
                return $this->render('security/two_factor_backup_codes.html.twig', [
                    'backupCodes' => $backupCodes,
                ]);
            } else {
                // Disable 2FA
                $this->twoFactorAuthService->disableTwoFactor($user);
                $this->addFlash('success', 'L\'authentification à deux facteurs a été désactivée.');
            }
            
            return $this->redirectToRoute('app_profile_security');
        }
        
        return $this->render('security/two_factor_setup.html.twig', [
            'form' => $form->createView(),
            'user' => $user,
        ]);
    }
    
    #[Route('/resend', name: 'app_2fa_resend')]
    public function resend(SessionInterface $session): Response
    {
        // Get the user ID from session
        $userId = $session->get('2fa_user_id');
        if (!$userId) {
            return $this->redirectToRoute('app_login');
        }
        
        // Get the user
        $user = $this->entityManager->getRepository(User::class)->find($userId);
        if (!$user) {
            return $this->redirectToRoute('app_login');
        }
        
        // Send a new code
        $method = $user->getPreferredTwoFactorMethod() ?? 'email';
        
        if ($method === 'email') {
            $this->twoFactorAuthService->sendCodeByEmail($user);
        } elseif ($method === 'sms') {
            $this->twoFactorAuthService->sendCodeBySms($user);
        }
        
        $this->addFlash('success', 'Un nouveau code a été envoyé.');
        
        return $this->redirectToRoute('app_2fa_verify');
    }
}
