<?php

namespace App\Controller;

use App\Entity\ViewConfiguration;
use App\Form\ViewConfigurationForm;
use App\Repository\ViewConfigurationRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/view/configuration')]
final class ViewConfigurationController extends AbstractController
{
    #[Route(name: 'app_view_configuration_index', methods: ['GET'])]
    public function index(ViewConfigurationRepository $viewConfigurationRepository): Response
    {
        return $this->render('view_configuration/index.html.twig', [
            'view_configurations' => $viewConfigurationRepository->findAll(),
        ]);
    }

    #[Route('/new', name: 'app_view_configuration_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $viewConfiguration = new ViewConfiguration();
        $form = $this->createForm(ViewConfigurationForm::class, $viewConfiguration);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->persist($viewConfiguration);
            $entityManager->flush();

            return $this->redirectToRoute('app_view_configuration_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('view_configuration/new.html.twig', [
            'view_configuration' => $viewConfiguration,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_view_configuration_show', methods: ['GET'])]
    public function show(ViewConfiguration $viewConfiguration): Response
    {
        return $this->render('view_configuration/show.html.twig', [
            'view_configuration' => $viewConfiguration,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_view_configuration_edit', methods: ['GET', 'POST'])]
    public function edit(Request $request, ViewConfiguration $viewConfiguration, EntityManagerInterface $entityManager): Response
    {
        $form = $this->createForm(ViewConfigurationForm::class, $viewConfiguration);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $entityManager->flush();

            return $this->redirectToRoute('app_view_configuration_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('view_configuration/edit.html.twig', [
            'view_configuration' => $viewConfiguration,
            'form' => $form,
        ]);
    }

    #[Route('/{id}', name: 'app_view_configuration_delete', methods: ['POST'])]
    public function delete(Request $request, ViewConfiguration $viewConfiguration, EntityManagerInterface $entityManager): Response
    {
        if ($this->isCsrfTokenValid('delete'.$viewConfiguration->getId(), $request->getPayload()->getString('_token'))) {
            $entityManager->remove($viewConfiguration);
            $entityManager->flush();
        }

        return $this->redirectToRoute('app_view_configuration_index', [], Response::HTTP_SEE_OTHER);
    }
}
