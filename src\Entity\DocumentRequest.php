<?php

namespace App\Entity;

use App\Repository\DocumentRequestRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: DocumentRequestRepository::class)]
#[ORM\Table(name: 'document_request')]
#[ORM\HasLifecycleCallbacks]
class DocumentRequest
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\OneToOne(inversedBy: 'documentRequest')]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'La demande est obligatoire')]
    private ?EmployeeRequest $request = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'L\'employé est obligatoire')]
    private ?Employee $employee = null;

    #[ORM\Column(length: 100)]
    #[Assert\NotBlank(message: 'Le type de document est obligatoire')]
    #[Assert\Choice(choices: [
        'work_certificate', 'employment_attestation', 'payslip', 'contract', 
        'salary_attestation', 'career_summary', 'tax_certificate', 'other'
    ], message: 'Type de document invalide')]
    private ?string $documentType = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank(message: 'L\'objectif du document est obligatoire')]
    #[Assert\Length(min: 5, max: 500, minMessage: 'L\'objectif doit faire au moins {{ limit }} caractères', maxMessage: 'L\'objectif ne peut pas dépasser {{ limit }} caractères')]
    private ?string $purpose = null;

    #[ORM\Column]
    #[Assert\NotBlank(message: 'La quantité est obligatoire')]
    #[Assert\Range(min: 1, max: 10, notInRangeMessage: 'La quantité doit être entre {{ min }} et {{ max }}')]
    private ?int $quantity = 1;

    #[ORM\Column(length: 10)]
    #[Assert\Choice(choices: ['fr', 'en', 'ar'], message: 'Langue invalide')]
    private ?string $language = 'fr';

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $neededBy = null;

    #[ORM\Column(length: 50)]
    #[Assert\Choice(choices: ['email', 'pickup', 'mail'], message: 'Méthode de livraison invalide')]
    private ?string $deliveryMethod = 'email';

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $deliveryAddress = null;

    #[ORM\Column(length: 30)]
    #[Assert\Choice(choices: ['pending', 'processing', 'ready', 'delivered', 'cancelled'], message: 'Statut invalide')]
    private ?string $status = 'pending';

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $processedAt = null;

    #[ORM\ManyToOne]
    private ?User $processedBy = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $deliveredAt = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $notes = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->status = 'pending';
        $this->quantity = 1;
        $this->language = 'fr';
        $this->deliveryMethod = 'email';
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRequest(): ?EmployeeRequest
    {
        return $this->request;
    }

    public function setRequest(?EmployeeRequest $request): static
    {
        $this->request = $request;
        return $this;
    }

    public function getEmployee(): ?Employee
    {
        return $this->employee;
    }

    public function setEmployee(?Employee $employee): static
    {
        $this->employee = $employee;
        return $this;
    }

    public function getDocumentType(): ?string
    {
        return $this->documentType;
    }

    public function setDocumentType(string $documentType): static
    {
        $this->documentType = $documentType;
        return $this;
    }

    public function getPurpose(): ?string
    {
        return $this->purpose;
    }

    public function setPurpose(string $purpose): static
    {
        $this->purpose = $purpose;
        return $this;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): static
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getLanguage(): ?string
    {
        return $this->language;
    }

    public function setLanguage(string $language): static
    {
        $this->language = $language;
        return $this;
    }

    public function getNeededBy(): ?\DateTimeInterface
    {
        return $this->neededBy;
    }

    public function setNeededBy(?\DateTimeInterface $neededBy): static
    {
        $this->neededBy = $neededBy;
        return $this;
    }

    public function getDeliveryMethod(): ?string
    {
        return $this->deliveryMethod;
    }

    public function setDeliveryMethod(string $deliveryMethod): static
    {
        $this->deliveryMethod = $deliveryMethod;
        return $this;
    }

    public function getDeliveryAddress(): ?string
    {
        return $this->deliveryAddress;
    }

    public function setDeliveryAddress(?string $deliveryAddress): static
    {
        $this->deliveryAddress = $deliveryAddress;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;
        return $this;
    }

    public function getProcessedAt(): ?\DateTimeInterface
    {
        return $this->processedAt;
    }

    public function setProcessedAt(?\DateTimeInterface $processedAt): static
    {
        $this->processedAt = $processedAt;
        return $this;
    }

    public function getProcessedBy(): ?User
    {
        return $this->processedBy;
    }

    public function setProcessedBy(?User $processedBy): static
    {
        $this->processedBy = $processedBy;
        return $this;
    }

    public function getDeliveredAt(): ?\DateTimeInterface
    {
        return $this->deliveredAt;
    }

    public function setDeliveredAt(?\DateTimeInterface $deliveredAt): static
    {
        $this->deliveredAt = $deliveredAt;
        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): static
    {
        $this->notes = $notes;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    /**
     * Get the document type label in French
     */
    public function getDocumentTypeLabel(): string
    {
        return match($this->documentType) {
            'work_certificate' => 'Certificat de travail',
            'employment_attestation' => 'Attestation d\'emploi',
            'payslip' => 'Bulletin de paie',
            'contract' => 'Contrat de travail',
            'salary_attestation' => 'Attestation de salaire',
            'career_summary' => 'Relevé de carrière',
            'tax_certificate' => 'Certificat fiscal',
            'other' => 'Autre document',
            default => ucfirst($this->documentType),
        };
    }

    /**
     * Get the status label in French
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'pending' => 'En attente',
            'processing' => 'En cours de traitement',
            'ready' => 'Prêt',
            'delivered' => 'Livré',
            'cancelled' => 'Annulé',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get the delivery method label in French
     */
    public function getDeliveryMethodLabel(): string
    {
        return match($this->deliveryMethod) {
            'email' => 'Email',
            'pickup' => 'Retrait sur place',
            'mail' => 'Courrier postal',
            default => ucfirst($this->deliveryMethod),
        };
    }

    /**
     * Get the language label in French
     */
    public function getLanguageLabel(): string
    {
        return match($this->language) {
            'fr' => 'Français',
            'en' => 'Anglais',
            'ar' => 'Arabe',
            default => ucfirst($this->language),
        };
    }

    /**
     * Check if the document is urgent (needed within 3 days)
     */
    public function isUrgent(): bool
    {
        if (!$this->neededBy) {
            return false;
        }

        $now = new \DateTime();
        $urgentDate = $now->modify('+3 days');
        
        return $this->neededBy <= $urgentDate;
    }

    /**
     * Get the number of days until needed
     */
    public function getDaysUntilNeeded(): ?int
    {
        if (!$this->neededBy) {
            return null;
        }

        $now = new \DateTime();
        $interval = $now->diff($this->neededBy);
        
        return $interval->invert ? -$interval->days : $interval->days;
    }

    /**
     * Mark the document as ready
     */
    public function markAsReady(User $processedBy, ?string $notes = null): static
    {
        $this->status = 'ready';
        $this->processedBy = $processedBy;
        $this->processedAt = new \DateTime();
        if ($notes) {
            $this->notes = $notes;
        }
        return $this;
    }

    /**
     * Mark the document as delivered
     */
    public function markAsDelivered(?string $notes = null): static
    {
        $this->status = 'delivered';
        $this->deliveredAt = new \DateTime();
        if ($notes) {
            $this->notes = $notes;
        }
        return $this;
    }

    public function __toString(): string
    {
        return $this->getDocumentTypeLabel() . ' pour ' . $this->employee?->getUser()?->getFirstName() . ' ' . $this->employee?->getUser()?->getLastName();
    }
}
