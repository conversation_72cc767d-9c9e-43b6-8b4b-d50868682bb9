<?php

namespace App\Entity;

use App\Repository\EmployeeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: EmployeeRepository::class)]
#[ORM\Table(name: 'employee')]
#[ORM\HasLifecycleCallbacks]
class Employee
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\OneToOne(inversedBy: 'employee')]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $user = null;

    #[ORM\Column(length: 20)]
    #[Assert\NotBlank(message: 'Le numéro d\'employé est obligatoire')]
    #[Assert\Length(max: 20, maxMessage: 'Le numéro d\'employé ne peut pas dépasser {{ limit }} caractères')]
    private ?string $employeeNumber = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Department $department = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Position $position = null;

    #[ORM\ManyToOne]
    private ?Employee $manager = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $hireDate = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $terminationDate = null;

    #[ORM\Column(length: 20)]
    private ?string $employmentStatus = null;

    #[ORM\Column(length: 20)]
    private ?string $employmentType = null;

    #[ORM\Column]
    private ?float $salary = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $salaryFrequency = null;

    #[ORM\Column(nullable: true)]
    private ?float $hourlyRate = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $bankName = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $bankAccountNumber = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $bankRoutingNumber = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $taxId = null;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $emergencyContactName = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $emergencyContactPhone = null;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $emergencyContactRelationship = null;

    #[ORM\OneToMany(mappedBy: 'employee', targetEntity: EmployeeSkill::class, orphanRemoval: true)]
    private Collection $skills;

    #[ORM\OneToMany(mappedBy: 'employee', targetEntity: EmployeeEducation::class, orphanRemoval: true)]
    private Collection $education;

    #[ORM\OneToMany(mappedBy: 'employee', targetEntity: EmployeeDocument::class, orphanRemoval: true)]
    private Collection $documents;

    #[ORM\OneToMany(mappedBy: 'employee', targetEntity: EmployeeLeave::class, orphanRemoval: true)]
    private Collection $leaves;

    #[ORM\OneToMany(mappedBy: 'employee', targetEntity: EmployeePerformance::class, orphanRemoval: true)]
    private Collection $performances;

    #[ORM\OneToMany(mappedBy: 'employee', targetEntity: FamilyMember::class, orphanRemoval: true)]
    private Collection $familyMembers;

    #[ORM\OneToMany(mappedBy: 'employee', targetEntity: EmployeeRequest::class, orphanRemoval: true)]
    private Collection $requests;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $updatedAt = null;

    public function __construct()
    {
        $this->skills = new ArrayCollection();
        $this->education = new ArrayCollection();
        $this->documents = new ArrayCollection();
        $this->leaves = new ArrayCollection();
        $this->performances = new ArrayCollection();
        $this->familyMembers = new ArrayCollection();
        $this->requests = new ArrayCollection();
        $this->createdAt = new \DateTimeImmutable();
        $this->hireDate = new \DateTime();
        $this->employmentStatus = 'active';
        $this->employmentType = 'full_time';
        $this->salary = 0;
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getEmployeeNumber(): ?string
    {
        return $this->employeeNumber;
    }

    public function setEmployeeNumber(string $employeeNumber): static
    {
        $this->employeeNumber = $employeeNumber;

        return $this;
    }

    public function getDepartment(): ?Department
    {
        return $this->department;
    }

    public function setDepartment(?Department $department): static
    {
        $this->department = $department;

        return $this;
    }

    public function getPosition(): ?Position
    {
        return $this->position;
    }

    public function setPosition(?Position $position): static
    {
        $this->position = $position;

        return $this;
    }

    public function getManager(): ?self
    {
        return $this->manager;
    }

    public function setManager(?self $manager): static
    {
        $this->manager = $manager;

        return $this;
    }

    public function getHireDate(): ?\DateTimeInterface
    {
        return $this->hireDate;
    }

    public function setHireDate(\DateTimeInterface $hireDate): static
    {
        $this->hireDate = $hireDate;

        return $this;
    }

    public function getTerminationDate(): ?\DateTimeInterface
    {
        return $this->terminationDate;
    }

    public function setTerminationDate(?\DateTimeInterface $terminationDate): static
    {
        $this->terminationDate = $terminationDate;

        return $this;
    }

    public function getEmploymentStatus(): ?string
    {
        return $this->employmentStatus;
    }

    public function setEmploymentStatus(string $employmentStatus): static
    {
        $this->employmentStatus = $employmentStatus;

        return $this;
    }

    public function getEmploymentType(): ?string
    {
        return $this->employmentType;
    }

    public function setEmploymentType(string $employmentType): static
    {
        $this->employmentType = $employmentType;

        return $this;
    }

    public function getSalary(): ?float
    {
        return $this->salary;
    }

    public function setSalary(float $salary): static
    {
        $this->salary = $salary;

        return $this;
    }

    public function getSalaryFrequency(): ?string
    {
        return $this->salaryFrequency;
    }

    public function setSalaryFrequency(?string $salaryFrequency): static
    {
        $this->salaryFrequency = $salaryFrequency;

        return $this;
    }

    public function getHourlyRate(): ?float
    {
        return $this->hourlyRate;
    }

    public function setHourlyRate(?float $hourlyRate): static
    {
        $this->hourlyRate = $hourlyRate;

        return $this;
    }

    public function getBankName(): ?string
    {
        return $this->bankName;
    }

    public function setBankName(?string $bankName): static
    {
        $this->bankName = $bankName;

        return $this;
    }

    public function getBankAccountNumber(): ?string
    {
        return $this->bankAccountNumber;
    }

    public function setBankAccountNumber(?string $bankAccountNumber): static
    {
        $this->bankAccountNumber = $bankAccountNumber;

        return $this;
    }

    public function getBankRoutingNumber(): ?string
    {
        return $this->bankRoutingNumber;
    }

    public function setBankRoutingNumber(?string $bankRoutingNumber): static
    {
        $this->bankRoutingNumber = $bankRoutingNumber;

        return $this;
    }

    public function getTaxId(): ?string
    {
        return $this->taxId;
    }

    public function setTaxId(?string $taxId): static
    {
        $this->taxId = $taxId;

        return $this;
    }

    public function getEmergencyContactName(): ?string
    {
        return $this->emergencyContactName;
    }

    public function setEmergencyContactName(?string $emergencyContactName): static
    {
        $this->emergencyContactName = $emergencyContactName;

        return $this;
    }

    public function getEmergencyContactPhone(): ?string
    {
        return $this->emergencyContactPhone;
    }

    public function setEmergencyContactPhone(?string $emergencyContactPhone): static
    {
        $this->emergencyContactPhone = $emergencyContactPhone;

        return $this;
    }

    public function getEmergencyContactRelationship(): ?string
    {
        return $this->emergencyContactRelationship;
    }

    public function setEmergencyContactRelationship(?string $emergencyContactRelationship): static
    {
        $this->emergencyContactRelationship = $emergencyContactRelationship;

        return $this;
    }

    /**
     * @return Collection<int, EmployeeSkill>
     */
    public function getSkills(): Collection
    {
        return $this->skills;
    }

    public function addSkill(EmployeeSkill $skill): static
    {
        if (!$this->skills->contains($skill)) {
            $this->skills->add($skill);
            $skill->setEmployee($this);
        }

        return $this;
    }

    public function removeSkill(EmployeeSkill $skill): static
    {
        if ($this->skills->removeElement($skill)) {
            // set the owning side to null (unless already changed)
            if ($skill->getEmployee() === $this) {
                $skill->setEmployee(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, EmployeeEducation>
     */
    public function getEducation(): Collection
    {
        return $this->education;
    }

    public function addEducation(EmployeeEducation $education): static
    {
        if (!$this->education->contains($education)) {
            $this->education->add($education);
            $education->setEmployee($this);
        }

        return $this;
    }

    public function removeEducation(EmployeeEducation $education): static
    {
        if ($this->education->removeElement($education)) {
            // set the owning side to null (unless already changed)
            if ($education->getEmployee() === $this) {
                $education->setEmployee(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, EmployeeDocument>
     */
    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    public function addDocument(EmployeeDocument $document): static
    {
        if (!$this->documents->contains($document)) {
            $this->documents->add($document);
            $document->setEmployee($this);
        }

        return $this;
    }

    public function removeDocument(EmployeeDocument $document): static
    {
        if ($this->documents->removeElement($document)) {
            // set the owning side to null (unless already changed)
            if ($document->getEmployee() === $this) {
                $document->setEmployee(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, EmployeeLeave>
     */
    public function getLeaves(): Collection
    {
        return $this->leaves;
    }

    public function addLeaf(EmployeeLeave $leaf): static
    {
        if (!$this->leaves->contains($leaf)) {
            $this->leaves->add($leaf);
            $leaf->setEmployee($this);
        }

        return $this;
    }

    public function removeLeaf(EmployeeLeave $leaf): static
    {
        if ($this->leaves->removeElement($leaf)) {
            // set the owning side to null (unless already changed)
            if ($leaf->getEmployee() === $this) {
                $leaf->setEmployee(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, EmployeePerformance>
     */
    public function getPerformances(): Collection
    {
        return $this->performances;
    }

    public function addPerformance(EmployeePerformance $performance): static
    {
        if (!$this->performances->contains($performance)) {
            $this->performances->add($performance);
            $performance->setEmployee($this);
        }

        return $this;
    }

    public function removePerformance(EmployeePerformance $performance): static
    {
        if ($this->performances->removeElement($performance)) {
            // set the owning side to null (unless already changed)
            if ($performance->getEmployee() === $this) {
                $performance->setEmployee(null);
            }
        }

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get the full name of the employee
     */
    public function getFullName(): string
    {
        return $this->user ? $this->user->getFullName() : '';
    }

    /**
     * Get the employment status label
     */
    public function getEmploymentStatusLabel(): string
    {
        return match($this->employmentStatus) {
            'active' => 'Actif',
            'on_leave' => 'En congé',
            'terminated' => 'Terminé',
            'suspended' => 'Suspendu',
            default => ucfirst($this->employmentStatus),
        };
    }

    /**
     * Get the employment type label
     */
    public function getEmploymentTypeLabel(): string
    {
        return match($this->employmentType) {
            'full_time' => 'Temps plein',
            'part_time' => 'Temps partiel',
            'contract' => 'Contractuel',
            'intern' => 'Stagiaire',
            'temporary' => 'Temporaire',
            default => ucfirst($this->employmentType),
        };
    }

    /**
     * Get the salary frequency label
     */
    public function getSalaryFrequencyLabel(): string
    {
        return match($this->salaryFrequency) {
            'hourly' => 'Horaire',
            'weekly' => 'Hebdomadaire',
            'biweekly' => 'Bimensuel',
            'monthly' => 'Mensuel',
            'annual' => 'Annuel',
            default => ucfirst($this->salaryFrequency ?? ''),
        };
    }

    /**
     * Get the tenure in years
     */
    public function getTenureInYears(): float
    {
        $endDate = $this->terminationDate ?? new \DateTime();
        $interval = $this->hireDate->diff($endDate);

        return $interval->y + ($interval->m / 12) + ($interval->d / 365);
    }

    /**
     * Check if the employee is active
     */
    public function isActive(): bool
    {
        return $this->employmentStatus === 'active';
    }

    /**
     * Check if the employee is on leave
     */
    public function isOnLeave(): bool
    {
        return $this->employmentStatus === 'on_leave';
    }

    /**
     * Check if the employee is terminated
     */
    public function isTerminated(): bool
    {
        return $this->employmentStatus === 'terminated';
    }

    /**
     * Get the formatted salary
     */
    public function getFormattedSalary(): string
    {
        $amount = number_format($this->salary, 2, ',', ' ');

        if ($this->salaryFrequency === 'hourly') {
            return $amount . ' €/h';
        } elseif ($this->salaryFrequency === 'annual') {
            return $amount . ' €/an';
        } else {
            return $amount . ' €';
        }
    }

    /**
     * String representation of the employee
     */
    public function __toString(): string
    {
        return (string) $this->id;
    }

    /**
     * @return Collection<int, FamilyMember>
     */
    public function getFamilyMembers(): Collection
    {
        return $this->familyMembers;
    }

    public function addFamilyMember(FamilyMember $familyMember): static
    {
        if (!$this->familyMembers->contains($familyMember)) {
            $this->familyMembers->add($familyMember);
            $familyMember->setEmployee($this);
        }

        return $this;
    }

    public function removeFamilyMember(FamilyMember $familyMember): static
    {
        if ($this->familyMembers->removeElement($familyMember)) {
            // set the owning side to null (unless already changed)
            if ($familyMember->getEmployee() === $this) {
                $familyMember->setEmployee(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, EmployeeRequest>
     */
    public function getRequests(): Collection
    {
        return $this->requests;
    }

    public function addRequest(EmployeeRequest $request): static
    {
        if (!$this->requests->contains($request)) {
            $this->requests->add($request);
            $request->setEmployee($this);
        }

        return $this;
    }

    public function removeRequest(EmployeeRequest $request): static
    {
        if ($this->requests->removeElement($request)) {
            // set the owning side to null (unless already changed)
            if ($request->getEmployee() === $this) {
                $request->setEmployee(null);
            }
        }

        return $this;
    }

    /**
     * Get pending requests count
     */
    public function getPendingRequestsCount(): int
    {
        return $this->requests->filter(function(EmployeeRequest $request) {
            return in_array($request->getStatus(), ['pending', 'manager_approved', 'hr_review']);
        })->count();
    }

    /**
     * Get recent requests (last 30 days)
     */
    public function getRecentRequests(): Collection
    {
        $thirtyDaysAgo = new \DateTime('-30 days');

        return $this->requests->filter(function(EmployeeRequest $request) use ($thirtyDaysAgo) {
            return $request->getCreatedAt() >= $thirtyDaysAgo;
        });
    }
}
