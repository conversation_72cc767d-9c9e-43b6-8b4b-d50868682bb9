<?php

namespace App\Entity;

use App\Repository\EmployeeRequestRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: EmployeeRequestRepository::class)]
#[ORM\Table(name: 'employee_request')]
#[ORM\HasLifecycleCallbacks]
class EmployeeRequest
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'requests')]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'L\'employé est obligatoire')]
    private ?Employee $employee = null;

    #[ORM\Column(length: 50)]
    #[Assert\NotBlank(message: 'Le type de demande est obligatoire')]
    #[Assert\Choice(choices: ['leave', 'document', 'salary_advance', 'other'], message: 'Type de demande invalide')]
    private ?string $type = null;

    #[ORM\Column(length: 200)]
    #[Assert\NotBlank(message: 'Le titre est obligatoire')]
    #[Assert\Length(max: 200, maxMessage: 'Le titre ne peut pas dépasser {{ limit }} caractères')]
    private ?string $title = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank(message: 'La description est obligatoire')]
    private ?string $description = null;

    #[ORM\Column(length: 30)]
    #[Assert\Choice(choices: ['pending', 'manager_approved', 'hr_review', 'approved', 'rejected', 'cancelled', 'processing', 'completed'], message: 'Statut invalide')]
    private ?string $status = 'pending';

    #[ORM\ManyToOne]
    private ?User $approvedBy = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $approvedAt = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $rejectionReason = null;

    #[ORM\Column(length: 20)]
    #[Assert\Choice(choices: ['low', 'medium', 'high', 'urgent'], message: 'Priorité invalide')]
    private ?string $priority = 'medium';

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $requestedDate = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    #[ORM\OneToMany(mappedBy: 'request', targetEntity: RequestComment::class, orphanRemoval: true)]
    private Collection $comments;

    #[ORM\OneToMany(mappedBy: 'request', targetEntity: RequestAttachment::class, orphanRemoval: true)]
    private Collection $attachments;

    #[ORM\OneToOne(mappedBy: 'request', targetEntity: SalaryAdvance::class)]
    private ?SalaryAdvance $salaryAdvance = null;

    #[ORM\OneToOne(mappedBy: 'request', targetEntity: DocumentRequest::class)]
    private ?DocumentRequest $documentRequest = null;

    public function __construct()
    {
        $this->comments = new ArrayCollection();
        $this->attachments = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->status = 'pending';
        $this->priority = 'medium';
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmployee(): ?Employee
    {
        return $this->employee;
    }

    public function setEmployee(?Employee $employee): static
    {
        $this->employee = $employee;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;
        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(string $description): static
    {
        $this->description = $description;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;
        return $this;
    }

    public function getApprovedBy(): ?User
    {
        return $this->approvedBy;
    }

    public function setApprovedBy(?User $approvedBy): static
    {
        $this->approvedBy = $approvedBy;
        return $this;
    }

    public function getApprovedAt(): ?\DateTimeInterface
    {
        return $this->approvedAt;
    }

    public function setApprovedAt(?\DateTimeInterface $approvedAt): static
    {
        $this->approvedAt = $approvedAt;
        return $this;
    }

    public function getRejectionReason(): ?string
    {
        return $this->rejectionReason;
    }

    public function setRejectionReason(?string $rejectionReason): static
    {
        $this->rejectionReason = $rejectionReason;
        return $this;
    }

    public function getPriority(): ?string
    {
        return $this->priority;
    }

    public function setPriority(string $priority): static
    {
        $this->priority = $priority;
        return $this;
    }

    public function getRequestedDate(): ?\DateTimeInterface
    {
        return $this->requestedDate;
    }

    public function setRequestedDate(?\DateTimeInterface $requestedDate): static
    {
        $this->requestedDate = $requestedDate;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    /**
     * @return Collection<int, RequestComment>
     */
    public function getComments(): Collection
    {
        return $this->comments;
    }

    public function addComment(RequestComment $comment): static
    {
        if (!$this->comments->contains($comment)) {
            $this->comments->add($comment);
            $comment->setRequest($this);
        }
        return $this;
    }

    public function removeComment(RequestComment $comment): static
    {
        if ($this->comments->removeElement($comment)) {
            if ($comment->getRequest() === $this) {
                $comment->setRequest(null);
            }
        }
        return $this;
    }

    /**
     * @return Collection<int, RequestAttachment>
     */
    public function getAttachments(): Collection
    {
        return $this->attachments;
    }

    public function addAttachment(RequestAttachment $attachment): static
    {
        if (!$this->attachments->contains($attachment)) {
            $this->attachments->add($attachment);
            $attachment->setRequest($this);
        }
        return $this;
    }

    public function removeAttachment(RequestAttachment $attachment): static
    {
        if ($this->attachments->removeElement($attachment)) {
            if ($attachment->getRequest() === $this) {
                $attachment->setRequest(null);
            }
        }
        return $this;
    }

    public function getSalaryAdvance(): ?SalaryAdvance
    {
        return $this->salaryAdvance;
    }

    public function setSalaryAdvance(?SalaryAdvance $salaryAdvance): static
    {
        if ($salaryAdvance === null && $this->salaryAdvance !== null) {
            $this->salaryAdvance->setRequest(null);
        }

        if ($salaryAdvance !== null && $salaryAdvance->getRequest() !== $this) {
            $salaryAdvance->setRequest($this);
        }

        $this->salaryAdvance = $salaryAdvance;
        return $this;
    }

    public function getDocumentRequest(): ?DocumentRequest
    {
        return $this->documentRequest;
    }

    public function setDocumentRequest(?DocumentRequest $documentRequest): static
    {
        if ($documentRequest === null && $this->documentRequest !== null) {
            $this->documentRequest->setRequest(null);
        }

        if ($documentRequest !== null && $documentRequest->getRequest() !== $this) {
            $documentRequest->setRequest($this);
        }

        $this->documentRequest = $documentRequest;
        return $this;
    }

    /**
     * Get the status label in French
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'pending' => 'En attente',
            'manager_approved' => 'Approuvé par le manager',
            'hr_review' => 'En cours d\'examen RH',
            'approved' => 'Approuvé',
            'rejected' => 'Rejeté',
            'cancelled' => 'Annulé',
            'processing' => 'En cours de traitement',
            'completed' => 'Terminé',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get the type label in French
     */
    public function getTypeLabel(): string
    {
        return match($this->type) {
            'leave' => 'Demande de congé',
            'document' => 'Demande de document',
            'salary_advance' => 'Avance sur salaire',
            'other' => 'Autre demande',
            default => ucfirst($this->type),
        };
    }

    /**
     * Get the priority label in French
     */
    public function getPriorityLabel(): string
    {
        return match($this->priority) {
            'low' => 'Basse',
            'medium' => 'Moyenne',
            'high' => 'Haute',
            'urgent' => 'Urgente',
            default => ucfirst($this->priority),
        };
    }

    /**
     * Check if the request can be approved
     */
    public function canBeApproved(): bool
    {
        return in_array($this->status, ['pending', 'manager_approved', 'hr_review']);
    }

    /**
     * Check if the request can be rejected
     */
    public function canBeRejected(): bool
    {
        return in_array($this->status, ['pending', 'manager_approved', 'hr_review']);
    }

    /**
     * Check if the request can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'manager_approved', 'hr_review']);
    }

    public function __toString(): string
    {
        return $this->title ?? '';
    }
}
