<?php

namespace App\Entity;

use App\Repository\PartnerRepository;
use Doctrine\ORM\Mapping as ORM;
use App\Entity\PartnerStatus;
use App\Entity\PartnerType;
use App\Entity\PartnerNature;
use App\Entity\PartnerScope;
use App\Entity\PartnerStatusHistory;
use App\Entity\Project;
use App\Entity\Employee;
use App\Entity\Department;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

#[ORM\Entity(repositoryClass: PartnerRepository::class)]
class Partner
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 255)]
    private ?string $email = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $phone = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $website = null;

    #[ORM\Column(nullable: true)]
    private ?array $customAttributes = null;

    #[ORM\ManyToOne(inversedBy: 'partners')]
    private ?PartnerStatus $status = null;

    #[ORM\ManyToOne(inversedBy: 'partners')]
    private ?PartnerType $type = null;

    #[ORM\ManyToOne(inversedBy: 'partners')]
    private ?PartnerNature $nature = null;

    #[ORM\ManyToOne(inversedBy: 'partners')]
    private ?PartnerScope $scope = null;

    #[ORM\OneToMany(mappedBy: 'partner', targetEntity: PartnerStatusHistory::class, orphanRemoval: true)]
    private Collection $statusHistories;

    // Supplier specific attributes
    #[ORM\Column(options: ["default" => false])]
    private bool $isSupplier = false;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $contactPerson = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $contactEmail = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $contactPhone = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $taxId = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $paymentTerms = null;

    #[ORM\Column(nullable: true)]
    private ?int $deliveryLeadTime = null;

    #[ORM\Column(nullable: true)]
    private ?float $rating = null;

    #[ORM\Column(nullable: true)]
    private ?bool $isApproved = null;

    #[ORM\OneToMany(mappedBy: 'supplier', targetEntity: Quote::class, orphanRemoval: true)]
    private Collection $quotes;

    #[ORM\OneToMany(mappedBy: 'partner', targetEntity: Project::class)]
    private Collection $projects;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?Employee $accountManager = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?Department $ownerDepartment = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->statusHistories = new ArrayCollection();
        $this->quotes = new ArrayCollection();
        $this->projects = new ArrayCollection();
        $this->isSupplier = false;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): static
    {
        $this->email = $email;

        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): static
    {
        $this->phone = $phone;

        return $this;
    }

    public function getWebsite(): ?string
    {
        return $this->website;
    }

    public function setWebsite(?string $website): static
    {
        $this->website = $website;

        return $this;
    }

    public function getCustomAttributes(): ?array
    {
        return $this->customAttributes;
    }

    public function setCustomAttributes(?array $customAttributes): static
    {
        $this->customAttributes = $customAttributes;

        return $this;
    }

    public function getStatus(): ?PartnerStatus
    {
        return $this->status;
    }

    public function setStatus(?PartnerStatus $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getType(): ?PartnerType
    {
        return $this->type;
    }

    public function setType(?PartnerType $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getNature(): ?PartnerNature
    {
        return $this->nature;
    }

    public function setNature(?PartnerNature $nature): static
    {
        $this->nature = $nature;

        return $this;
    }

    public function getScope(): ?PartnerScope
    {
        return $this->scope;
    }

    public function setScope(?PartnerScope $scope): static
    {
        $this->scope = $scope;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * @return Collection<int, PartnerStatusHistory>
     */
    public function getStatusHistories(): Collection
    {
        return $this->statusHistories;
    }

    public function addStatusHistory(PartnerStatusHistory $statusHistory): static
    {
        if (!$this->statusHistories->contains($statusHistory)) {
            $this->statusHistories->add($statusHistory);
            $statusHistory->setPartner($this);
        }

        return $this;
    }

    public function removeStatusHistory(PartnerStatusHistory $statusHistory): static
    {
        if ($this->statusHistories->removeElement($statusHistory)) {
            // set the owning side to null (unless already changed)
            if ($statusHistory->getPartner() === $this) {
                $statusHistory->setPartner(null);
            }
        }

        return $this;
    }

    // Supplier specific methods

    public function isSupplier(): bool
    {
        return $this->isSupplier;
    }

    public function setIsSupplier(bool $isSupplier): static
    {
        $this->isSupplier = $isSupplier;

        return $this;
    }

    public function getContactPerson(): ?string
    {
        return $this->contactPerson;
    }

    public function setContactPerson(?string $contactPerson): static
    {
        $this->contactPerson = $contactPerson;

        return $this;
    }

    public function getContactEmail(): ?string
    {
        return $this->contactEmail;
    }

    public function setContactEmail(?string $contactEmail): static
    {
        $this->contactEmail = $contactEmail;

        return $this;
    }

    public function getContactPhone(): ?string
    {
        return $this->contactPhone;
    }

    public function setContactPhone(?string $contactPhone): static
    {
        $this->contactPhone = $contactPhone;

        return $this;
    }

    public function getTaxId(): ?string
    {
        return $this->taxId;
    }

    public function setTaxId(?string $taxId): static
    {
        $this->taxId = $taxId;

        return $this;
    }

    public function getPaymentTerms(): ?string
    {
        return $this->paymentTerms;
    }

    public function setPaymentTerms(?string $paymentTerms): static
    {
        $this->paymentTerms = $paymentTerms;

        return $this;
    }

    public function getDeliveryLeadTime(): ?int
    {
        return $this->deliveryLeadTime;
    }

    public function setDeliveryLeadTime(?int $deliveryLeadTime): static
    {
        $this->deliveryLeadTime = $deliveryLeadTime;

        return $this;
    }

    public function getRating(): ?float
    {
        return $this->rating;
    }

    public function setRating(?float $rating): static
    {
        $this->rating = $rating;

        return $this;
    }

    public function isApproved(): ?bool
    {
        return $this->isApproved;
    }

    public function setIsApproved(?bool $isApproved): static
    {
        $this->isApproved = $isApproved;

        return $this;
    }

    /**
     * @return Collection<int, Quote>
     */
    public function getQuotes(): Collection
    {
        return $this->quotes;
    }

    public function addQuote(Quote $quote): static
    {
        if (!$this->quotes->contains($quote)) {
            $this->quotes->add($quote);
            $quote->setSupplier($this);
        }

        return $this;
    }

    public function removeQuote(Quote $quote): static
    {
        if ($this->quotes->removeElement($quote)) {
            // set the owning side to null (unless already changed)
            if ($quote->getSupplier() === $this) {
                $quote->setSupplier(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Project>
     */
    public function getProjects(): Collection
    {
        return $this->projects;
    }

    public function addProject(Project $project): static
    {
        if (!$this->projects->contains($project)) {
            $this->projects->add($project);
            $project->setPartner($this);
        }

        return $this;
    }

    public function removeProject(Project $project): static
    {
        if ($this->projects->removeElement($project)) {
            // set the owning side to null (unless already changed)
            if ($project->getPartner() === $this) {
                $project->setPartner(null);
            }
        }

        return $this;
    }

    public function getAccountManager(): ?Employee
    {
        return $this->accountManager;
    }

    public function setAccountManager(?Employee $accountManager): static
    {
        $this->accountManager = $accountManager;

        return $this;
    }

    public function getOwnerDepartment(): ?Department
    {
        return $this->ownerDepartment;
    }

    public function setOwnerDepartment(?Department $ownerDepartment): static
    {
        $this->ownerDepartment = $ownerDepartment;

        return $this;
    }
}
