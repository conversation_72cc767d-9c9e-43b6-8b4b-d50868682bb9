<?php

namespace App\Entity;

use App\Repository\ProjectRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: ProjectRepository::class)]
#[ORM\Table(name: 'project')]
#[ORM\HasLifecycleCallbacks]
class Project
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 100)]
    #[Assert\NotBlank(message: 'Le nom du projet est obligatoire')]
    #[Assert\Length(max: 100, maxMessage: 'Le nom du projet ne peut pas dépasser {{ limit }} caractères')]
    private ?string $name = null;

    #[ORM\Column(length: 20, unique: true)]
    #[Assert\NotBlank(message: 'Le code du projet est obligatoire')]
    #[Assert\Length(max: 20, maxMessage: 'Le code du projet ne peut pas dépasser {{ limit }} caractères')]
    #[Assert\Regex(pattern: '/^[A-Z0-9\-]+$/', message: 'Le code du projet ne peut contenir que des lettres majuscules, des chiffres et des tirets')]
    private ?string $code = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?Partner $partner = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?Department $ownerDepartment = null;

    #[ORM\Column(length: 20)]
    private ?string $status = null;

    #[ORM\Column(length: 20)]
    private ?string $priority = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $startDate = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $endDate = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $completedAt = null;

    #[ORM\Column]
    private ?float $budget = 0;

    #[ORM\Column]
    private ?float $costToDate = 0;

    #[ORM\Column]
    private ?int $progress = 0;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $manager = null;

    #[ORM\OneToMany(mappedBy: 'project', targetEntity: ProjectTask::class, orphanRemoval: true)]
    private Collection $tasks;

    #[ORM\OneToMany(mappedBy: 'project', targetEntity: ProjectMember::class, orphanRemoval: true)]
    private Collection $members;

    #[ORM\OneToMany(mappedBy: 'project', targetEntity: ProjectDocument::class, orphanRemoval: true)]
    private Collection $documents;

    #[ORM\OneToMany(mappedBy: 'project', targetEntity: ProjectBudgetLine::class, orphanRemoval: true)]
    private Collection $budgetLines;

    #[ORM\OneToMany(mappedBy: 'project', targetEntity: ProjectRisk::class, orphanRemoval: true)]
    private Collection $risks;

    #[ORM\OneToMany(mappedBy: 'project', targetEntity: ProjectDeliverable::class, orphanRemoval: true)]
    private Collection $deliverables;

    #[ORM\OneToMany(mappedBy: 'project', targetEntity: ProjectResource::class, orphanRemoval: true)]
    private Collection $resources;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $updatedAt = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $createdBy = null;

    #[ORM\Column]
    private ?bool $isTemplate = false;

    #[ORM\Column(nullable: true)]
    private ?string $color = null;

    #[ORM\Column(nullable: true)]
    private ?string $category = null;

    public function __construct()
    {
        $this->tasks = new ArrayCollection();
        $this->members = new ArrayCollection();
        $this->documents = new ArrayCollection();
        $this->budgetLines = new ArrayCollection();
        $this->risks = new ArrayCollection();
        $this->deliverables = new ArrayCollection();
        $this->resources = new ArrayCollection();
        $this->createdAt = new \DateTimeImmutable();
        $this->status = 'draft';
        $this->priority = 'medium';
        $this->budget = 0;
        $this->costToDate = 0;
        $this->progress = 0;
        $this->isTemplate = false;
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getPartner(): ?Partner
    {
        return $this->partner;
    }

    public function setPartner(?Partner $partner): static
    {
        $this->partner = $partner;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getPriority(): ?string
    {
        return $this->priority;
    }

    public function setPriority(string $priority): static
    {
        $this->priority = $priority;

        return $this;
    }

    public function getStartDate(): ?\DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(?\DateTimeInterface $startDate): static
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getEndDate(): ?\DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(?\DateTimeInterface $endDate): static
    {
        $this->endDate = $endDate;

        return $this;
    }

    public function getCompletedAt(): ?\DateTimeInterface
    {
        return $this->completedAt;
    }

    public function setCompletedAt(?\DateTimeInterface $completedAt): static
    {
        $this->completedAt = $completedAt;

        return $this;
    }

    public function getBudget(): ?float
    {
        return $this->budget;
    }

    public function setBudget(float $budget): static
    {
        $this->budget = $budget;

        return $this;
    }

    public function getCostToDate(): ?float
    {
        return $this->costToDate;
    }

    public function setCostToDate(float $costToDate): static
    {
        $this->costToDate = $costToDate;

        return $this;
    }

    public function getProgress(): ?int
    {
        return $this->progress;
    }

    public function setProgress(int $progress): static
    {
        $this->progress = $progress;

        return $this;
    }

    public function getManager(): ?User
    {
        return $this->manager;
    }

    public function setManager(?User $manager): static
    {
        $this->manager = $manager;

        return $this;
    }

    /**
     * @return Collection<int, ProjectTask>
     */
    public function getTasks(): Collection
    {
        return $this->tasks;
    }

    public function addTask(ProjectTask $task): static
    {
        if (!$this->tasks->contains($task)) {
            $this->tasks->add($task);
            $task->setProject($this);
        }

        return $this;
    }

    public function removeTask(ProjectTask $task): static
    {
        if ($this->tasks->removeElement($task)) {
            // set the owning side to null (unless already changed)
            if ($task->getProject() === $this) {
                $task->setProject(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ProjectMember>
     */
    public function getMembers(): Collection
    {
        return $this->members;
    }

    public function addMember(ProjectMember $member): static
    {
        if (!$this->members->contains($member)) {
            $this->members->add($member);
            $member->setProject($this);
        }

        return $this;
    }

    public function removeMember(ProjectMember $member): static
    {
        if ($this->members->removeElement($member)) {
            // set the owning side to null (unless already changed)
            if ($member->getProject() === $this) {
                $member->setProject(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ProjectDocument>
     */
    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    public function addDocument(ProjectDocument $document): static
    {
        if (!$this->documents->contains($document)) {
            $this->documents->add($document);
            $document->setProject($this);
        }

        return $this;
    }

    public function removeDocument(ProjectDocument $document): static
    {
        if ($this->documents->removeElement($document)) {
            // set the owning side to null (unless already changed)
            if ($document->getProject() === $this) {
                $document->setProject(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ProjectBudgetLine>
     */
    public function getBudgetLines(): Collection
    {
        return $this->budgetLines;
    }

    public function addBudgetLine(ProjectBudgetLine $budgetLine): static
    {
        if (!$this->budgetLines->contains($budgetLine)) {
            $this->budgetLines->add($budgetLine);
            $budgetLine->setProject($this);
        }

        return $this;
    }

    public function removeBudgetLine(ProjectBudgetLine $budgetLine): static
    {
        if ($this->budgetLines->removeElement($budgetLine)) {
            // set the owning side to null (unless already changed)
            if ($budgetLine->getProject() === $this) {
                $budgetLine->setProject(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ProjectRisk>
     */
    public function getRisks(): Collection
    {
        return $this->risks;
    }

    public function addRisk(ProjectRisk $risk): static
    {
        if (!$this->risks->contains($risk)) {
            $this->risks->add($risk);
            $risk->setProject($this);
        }

        return $this;
    }

    public function removeRisk(ProjectRisk $risk): static
    {
        if ($this->risks->removeElement($risk)) {
            // set the owning side to null (unless already changed)
            if ($risk->getProject() === $this) {
                $risk->setProject(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ProjectDeliverable>
     */
    public function getDeliverables(): Collection
    {
        return $this->deliverables;
    }

    public function addDeliverable(ProjectDeliverable $deliverable): static
    {
        if (!$this->deliverables->contains($deliverable)) {
            $this->deliverables->add($deliverable);
            $deliverable->setProject($this);
        }

        return $this;
    }

    public function removeDeliverable(ProjectDeliverable $deliverable): static
    {
        if ($this->deliverables->removeElement($deliverable)) {
            // set the owning side to null (unless already changed)
            if ($deliverable->getProject() === $this) {
                $deliverable->setProject(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ProjectResource>
     */
    public function getResources(): Collection
    {
        return $this->resources;
    }

    public function addResource(ProjectResource $resource): static
    {
        if (!$this->resources->contains($resource)) {
            $this->resources->add($resource);
            $resource->setProject($this);
        }

        return $this;
    }

    public function removeResource(ProjectResource $resource): static
    {
        if ($this->resources->removeElement($resource)) {
            // set the owning side to null (unless already changed)
            if ($resource->getProject() === $this) {
                $resource->setProject(null);
            }
        }

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy): static
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    public function isIsTemplate(): ?bool
    {
        return $this->isTemplate;
    }

    public function setIsTemplate(bool $isTemplate): static
    {
        $this->isTemplate = $isTemplate;

        return $this;
    }

    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(?string $color): static
    {
        $this->color = $color;

        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(?string $category): static
    {
        $this->category = $category;

        return $this;
    }

    /**
     * Get the status label
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'draft' => 'Brouillon',
            'planned' => 'Planifié',
            'in_progress' => 'En cours',
            'on_hold' => 'En attente',
            'completed' => 'Terminé',
            'cancelled' => 'Annulé',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get the priority label
     */
    public function getPriorityLabel(): string
    {
        return match($this->priority) {
            'low' => 'Basse',
            'medium' => 'Moyenne',
            'high' => 'Haute',
            'urgent' => 'Urgente',
            default => ucfirst($this->priority),
        };
    }

    /**
     * Check if the project is overdue
     */
    public function isOverdue(): bool
    {
        if (!$this->endDate) {
            return false;
        }

        if (in_array($this->status, ['completed', 'cancelled'])) {
            return false;
        }

        return $this->endDate < new \DateTime();
    }

    /**
     * Get the budget utilization percentage
     */
    public function getBudgetUtilizationPercentage(): float
    {
        if ($this->budget <= 0) {
            return 0;
        }

        return min(100, round(($this->costToDate / $this->budget) * 100, 2));
    }

    /**
     * Get the remaining budget
     */
    public function getRemainingBudget(): float
    {
        return max(0, $this->budget - $this->costToDate);
    }

    /**
     * Get the duration in days
     */
    public function getDurationInDays(): ?int
    {
        if (!$this->startDate || !$this->endDate) {
            return null;
        }

        $interval = $this->startDate->diff($this->endDate);
        return $interval->days;
    }

    /**
     * Get the number of completed tasks
     */
    public function getCompletedTasksCount(): int
    {
        $count = 0;

        foreach ($this->tasks as $task) {
            if ($task->getStatus() === 'completed') {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Get the number of overdue tasks
     */
    public function getOverdueTasksCount(): int
    {
        $count = 0;

        foreach ($this->tasks as $task) {
            if ($task->isOverdue()) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Calculate the project progress based on task completion
     */
    public function calculateProgress(): int
    {
        $totalTasks = count($this->tasks);

        if ($totalTasks === 0) {
            return 0;
        }

        $completedTasks = $this->getCompletedTasksCount();

        return (int) round(($completedTasks / $totalTasks) * 100);
    }

    /**
     * Update the project progress
     */
    public function updateProgress(): void
    {
        $this->progress = $this->calculateProgress();
    }

    /**
     * Get the project health status
     */
    public function getHealthStatus(): string
    {
        // If project is completed or cancelled, return status
        if (in_array($this->status, ['completed', 'cancelled'])) {
            return $this->status;
        }

        // Check if project is overdue
        if ($this->isOverdue()) {
            return 'at_risk';
        }

        // Check budget utilization
        $budgetUtilization = $this->getBudgetUtilizationPercentage();

        // Check progress vs time elapsed
        $timeElapsed = $this->getTimeElapsedPercentage();

        // If progress is significantly behind time elapsed, project is at risk
        if ($timeElapsed > 0 && $this->progress < $timeElapsed - 20) {
            return 'at_risk';
        }

        // If budget utilization is significantly higher than progress, project is at risk
        if ($budgetUtilization > $this->progress + 20) {
            return 'at_risk';
        }

        // If overdue tasks, project is in warning state
        if ($this->getOverdueTasksCount() > 0) {
            return 'warning';
        }

        // Otherwise, project is on track
        return 'on_track';
    }

    /**
     * Get the time elapsed percentage
     */
    public function getTimeElapsedPercentage(): float
    {
        if (!$this->startDate || !$this->endDate) {
            return 0;
        }

        $now = new \DateTime();

        // If project hasn't started yet
        if ($now < $this->startDate) {
            return 0;
        }

        // If project has ended
        if ($now > $this->endDate) {
            return 100;
        }

        $totalDuration = $this->startDate->diff($this->endDate)->days;

        if ($totalDuration === 0) {
            return 100;
        }

        $elapsedDays = $this->startDate->diff($now)->days;

        return min(100, round(($elapsedDays / $totalDuration) * 100, 2));
    }

    /**
     * Get the total allocated budget from budget lines
     */
    public function getTotalAllocatedBudget(): float
    {
        $total = 0;
        foreach ($this->budgetLines as $budgetLine) {
            $total += $budgetLine->getAllocatedAmount();
        }
        return $total;
    }

    /**
     * Get the total spent amount from budget lines
     */
    public function getTotalSpentAmount(): float
    {
        $total = 0;
        foreach ($this->budgetLines as $budgetLine) {
            $total += $budgetLine->getSpentAmount();
        }
        return $total;
    }

    /**
     * Get operating budget lines
     */
    public function getOperatingBudgetLines(): array
    {
        $operatingLines = [];
        foreach ($this->budgetLines as $budgetLine) {
            if ($budgetLine->getType() === 'operating') {
                $operatingLines[] = $budgetLine;
            }
        }
        return $operatingLines;
    }

    /**
     * Get investment budget lines
     */
    public function getInvestmentBudgetLines(): array
    {
        $investmentLines = [];
        foreach ($this->budgetLines as $budgetLine) {
            if ($budgetLine->getType() === 'investment') {
                $investmentLines[] = $budgetLine;
            }
        }
        return $investmentLines;
    }

    /**
     * Get total operating budget
     */
    public function getTotalOperatingBudget(): float
    {
        $total = 0;
        foreach ($this->getOperatingBudgetLines() as $budgetLine) {
            $total += $budgetLine->getAllocatedAmount();
        }
        return $total;
    }

    /**
     * Get total investment budget
     */
    public function getTotalInvestmentBudget(): float
    {
        $total = 0;
        foreach ($this->getInvestmentBudgetLines() as $budgetLine) {
            $total += $budgetLine->getAllocatedAmount();
        }
        return $total;
    }

    /**
     * Update budget from budget lines
     */
    public function updateBudgetFromBudgetLines(): void
    {
        $this->budget = $this->getTotalAllocatedBudget();
        $this->costToDate = $this->getTotalSpentAmount();
    }

    public function getOwnerDepartment(): ?Department
    {
        return $this->ownerDepartment;
    }

    public function setOwnerDepartment(?Department $ownerDepartment): static
    {
        $this->ownerDepartment = $ownerDepartment;

        return $this;
    }
}
