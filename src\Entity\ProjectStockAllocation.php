<?php

namespace App\Entity;

use App\Repository\ProjectStockAllocationRepository;
use App\Entity\Project;
use App\Entity\Stock\StockItem;
use App\Entity\User;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ProjectStockAllocationRepository::class)]
#[ORM\Table(name: 'project_stock_allocation')]
#[ORM\HasLifecycleCallbacks]
class ProjectStockAllocation
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Project::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?Project $project = null;

    #[ORM\ManyToOne(targetEntity: StockItem::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?StockItem $stockItem = null;

    #[ORM\Column]
    private ?int $allocatedQuantity = null;

    #[ORM\Column]
    private ?int $usedQuantity = 0;

    #[ORM\Column(length: 20)]
    private ?string $status = 'allocated'; // allocated, in_use, returned, consumed

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $allocatedAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $returnedAt = null;

    #[ORM\ManyToOne(targetEntity: User::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?User $allocatedBy = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $notes = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTimeImmutable();
        $this->allocatedAt = new \DateTime();
        $this->usedQuantity = 0;
        $this->status = 'allocated';
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProject(): ?Project
    {
        return $this->project;
    }

    public function setProject(?Project $project): static
    {
        $this->project = $project;

        return $this;
    }

    public function getStockItem(): ?StockItem
    {
        return $this->stockItem;
    }

    public function setStockItem(?StockItem $stockItem): static
    {
        $this->stockItem = $stockItem;

        return $this;
    }

    public function getAllocatedQuantity(): ?int
    {
        return $this->allocatedQuantity;
    }

    public function setAllocatedQuantity(int $allocatedQuantity): static
    {
        $this->allocatedQuantity = $allocatedQuantity;

        return $this;
    }

    public function getUsedQuantity(): ?int
    {
        return $this->usedQuantity;
    }

    public function setUsedQuantity(int $usedQuantity): static
    {
        $this->usedQuantity = $usedQuantity;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getAllocatedAt(): ?\DateTimeInterface
    {
        return $this->allocatedAt;
    }

    public function setAllocatedAt(?\DateTimeInterface $allocatedAt): static
    {
        $this->allocatedAt = $allocatedAt;

        return $this;
    }

    public function getReturnedAt(): ?\DateTimeInterface
    {
        return $this->returnedAt;
    }

    public function setReturnedAt(?\DateTimeInterface $returnedAt): static
    {
        $this->returnedAt = $returnedAt;

        return $this;
    }

    public function getAllocatedBy(): ?User
    {
        return $this->allocatedBy;
    }

    public function setAllocatedBy(?User $allocatedBy): static
    {
        $this->allocatedBy = $allocatedBy;

        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): static
    {
        $this->notes = $notes;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTimeImmutable $updatedAt): static
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /**
     * Get remaining quantity available
     */
    public function getRemainingQuantity(): int
    {
        return $this->allocatedQuantity - $this->usedQuantity;
    }

    /**
     * Get usage percentage
     */
    public function getUsagePercentage(): float
    {
        if ($this->allocatedQuantity <= 0) {
            return 0;
        }

        return round(($this->usedQuantity / $this->allocatedQuantity) * 100, 2);
    }

    /**
     * Check if allocation is fully used
     */
    public function isFullyUsed(): bool
    {
        return $this->usedQuantity >= $this->allocatedQuantity;
    }

    /**
     * Check if allocation is active
     */
    public function isActive(): bool
    {
        return in_array($this->status, ['allocated', 'in_use']);
    }

    /**
     * Mark as returned
     */
    public function markAsReturned(): static
    {
        $this->status = 'returned';
        $this->returnedAt = new \DateTime();

        return $this;
    }

    /**
     * Mark as consumed
     */
    public function markAsConsumed(): static
    {
        $this->status = 'consumed';
        $this->usedQuantity = $this->allocatedQuantity;

        return $this;
    }

    /**
     * Use quantity
     */
    public function useQuantity(int $quantity): static
    {
        $this->usedQuantity = min($this->allocatedQuantity, $this->usedQuantity + $quantity);
        
        if ($this->usedQuantity > 0) {
            $this->status = 'in_use';
        }

        return $this;
    }

    /**
     * Get status label
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'allocated' => 'Alloué',
            'in_use' => 'En cours d\'utilisation',
            'returned' => 'Retourné',
            'consumed' => 'Consommé',
            default => ucfirst($this->status),
        };
    }
}
