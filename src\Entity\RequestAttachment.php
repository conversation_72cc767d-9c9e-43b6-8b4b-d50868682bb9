<?php

namespace App\Entity;

use App\Repository\RequestAttachmentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: RequestAttachmentRepository::class)]
#[ORM\Table(name: 'request_attachment')]
#[ORM\HasLifecycleCallbacks]
class RequestAttachment
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'attachments')]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'La demande est obligatoire')]
    private ?EmployeeRequest $request = null;

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank(message: 'Le nom de fichier est obligatoire')]
    private ?string $filename = null;

    #[ORM\Column(length: 255)]
    #[Assert\NotBlank(message: 'Le nom original est obligatoire')]
    private ?string $originalName = null;

    #[ORM\Column(length: 100)]
    private ?string $mimeType = null;

    #[ORM\Column]
    #[Assert\Positive(message: 'La taille du fichier doit être positive')]
    private ?int $fileSize = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $uploadedAt = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'L\'utilisateur qui a uploadé est obligatoire')]
    private ?User $uploadedBy = null;

    #[ORM\Column(length: 500, nullable: true)]
    private ?string $description = null;

    public function __construct()
    {
        $this->uploadedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRequest(): ?EmployeeRequest
    {
        return $this->request;
    }

    public function setRequest(?EmployeeRequest $request): static
    {
        $this->request = $request;
        return $this;
    }

    public function getFilename(): ?string
    {
        return $this->filename;
    }

    public function setFilename(string $filename): static
    {
        $this->filename = $filename;
        return $this;
    }

    public function getOriginalName(): ?string
    {
        return $this->originalName;
    }

    public function setOriginalName(string $originalName): static
    {
        $this->originalName = $originalName;
        return $this;
    }

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function setMimeType(?string $mimeType): static
    {
        $this->mimeType = $mimeType;
        return $this;
    }

    public function getFileSize(): ?int
    {
        return $this->fileSize;
    }

    public function setFileSize(int $fileSize): static
    {
        $this->fileSize = $fileSize;
        return $this;
    }

    public function getUploadedAt(): ?\DateTimeInterface
    {
        return $this->uploadedAt;
    }

    public function getUploadedBy(): ?User
    {
        return $this->uploadedBy;
    }

    public function setUploadedBy(?User $uploadedBy): static
    {
        $this->uploadedBy = $uploadedBy;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;
        return $this;
    }

    /**
     * Get the file size in human readable format
     */
    public function getFormattedFileSize(): string
    {
        if ($this->fileSize === null) {
            return 'Taille inconnue';
        }

        $bytes = $this->fileSize;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the file extension
     */
    public function getFileExtension(): string
    {
        return strtolower(pathinfo($this->originalName ?? '', PATHINFO_EXTENSION));
    }

    /**
     * Check if the file is an image
     */
    public function isImage(): bool
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        return in_array($this->getFileExtension(), $imageExtensions);
    }

    /**
     * Check if the file is a PDF
     */
    public function isPdf(): bool
    {
        return $this->getFileExtension() === 'pdf';
    }

    /**
     * Check if the file is a document
     */
    public function isDocument(): bool
    {
        $documentExtensions = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'];
        return in_array($this->getFileExtension(), $documentExtensions);
    }

    /**
     * Get the file icon class for display
     */
    public function getIconClass(): string
    {
        $extension = $this->getFileExtension();
        
        return match($extension) {
            'pdf' => 'fas fa-file-pdf text-danger',
            'doc', 'docx' => 'fas fa-file-word text-primary',
            'xls', 'xlsx' => 'fas fa-file-excel text-success',
            'ppt', 'pptx' => 'fas fa-file-powerpoint text-warning',
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp' => 'fas fa-file-image text-info',
            'zip', 'rar', '7z' => 'fas fa-file-archive text-secondary',
            'txt' => 'fas fa-file-alt text-muted',
            default => 'fas fa-file text-muted',
        };
    }

    /**
     * Get the full file path
     */
    public function getFilePath(): string
    {
        return 'uploads/employee_requests/' . $this->filename;
    }

    public function __toString(): string
    {
        return $this->originalName ?? '';
    }
}
