<?php

namespace App\Entity;

use App\Repository\RequestCommentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: RequestCommentRepository::class)]
#[ORM\Table(name: 'request_comment')]
#[ORM\HasLifecycleCallbacks]
class RequestComment
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'comments')]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'La demande est obligatoire')]
    private ?EmployeeRequest $request = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'L\'auteur est obligatoire')]
    private ?User $author = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank(message: 'Le commentaire est obligatoire')]
    #[Assert\Length(min: 1, max: 2000, minMessage: 'Le commentaire doit faire au moins {{ limit }} caractère', maxMessage: 'Le commentaire ne peut pas dépasser {{ limit }} caractères')]
    private ?string $comment = null;

    #[ORM\Column(length: 30)]
    #[Assert\Choice(choices: ['comment', 'status_change', 'approval', 'rejection', 'system'], message: 'Type de commentaire invalide')]
    private ?string $type = 'comment';

    #[ORM\Column]
    private ?bool $isInternal = false;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->type = 'comment';
        $this->isInternal = false;
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRequest(): ?EmployeeRequest
    {
        return $this->request;
    }

    public function setRequest(?EmployeeRequest $request): static
    {
        $this->request = $request;
        return $this;
    }

    public function getAuthor(): ?User
    {
        return $this->author;
    }

    public function setAuthor(?User $author): static
    {
        $this->author = $author;
        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(string $comment): static
    {
        $this->comment = $comment;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;
        return $this;
    }

    public function isIsInternal(): ?bool
    {
        return $this->isInternal;
    }

    public function setIsInternal(bool $isInternal): static
    {
        $this->isInternal = $isInternal;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    /**
     * Get the type label in French
     */
    public function getTypeLabel(): string
    {
        return match($this->type) {
            'comment' => 'Commentaire',
            'status_change' => 'Changement de statut',
            'approval' => 'Approbation',
            'rejection' => 'Rejet',
            'system' => 'Système',
            default => ucfirst($this->type),
        };
    }

    /**
     * Check if the comment is visible to the employee
     */
    public function isVisibleToEmployee(): bool
    {
        return !$this->isInternal;
    }

    /**
     * Create a system comment for status change
     */
    public static function createStatusChangeComment(
        EmployeeRequest $request, 
        User $author, 
        string $oldStatus, 
        string $newStatus
    ): self {
        $comment = new self();
        $comment->setRequest($request);
        $comment->setAuthor($author);
        $comment->setType('status_change');
        $comment->setComment("Statut changé de '{$oldStatus}' vers '{$newStatus}'");
        $comment->setIsInternal(false);
        
        return $comment;
    }

    /**
     * Create an approval comment
     */
    public static function createApprovalComment(
        EmployeeRequest $request, 
        User $author, 
        ?string $message = null
    ): self {
        $comment = new self();
        $comment->setRequest($request);
        $comment->setAuthor($author);
        $comment->setType('approval');
        $comment->setComment($message ?? 'Demande approuvée');
        $comment->setIsInternal(false);
        
        return $comment;
    }

    /**
     * Create a rejection comment
     */
    public static function createRejectionComment(
        EmployeeRequest $request, 
        User $author, 
        string $reason
    ): self {
        $comment = new self();
        $comment->setRequest($request);
        $comment->setAuthor($author);
        $comment->setType('rejection');
        $comment->setComment("Demande rejetée: {$reason}");
        $comment->setIsInternal(false);
        
        return $comment;
    }

    public function __toString(): string
    {
        return substr($this->comment ?? '', 0, 50) . '...';
    }
}
