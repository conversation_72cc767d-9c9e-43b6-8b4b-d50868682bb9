<?php

namespace App\Entity;

use App\Repository\SalaryAdvanceRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: SalaryAdvanceRepository::class)]
#[ORM\Table(name: 'salary_advance')]
#[ORM\HasLifecycleCallbacks]
class SalaryAdvance
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\OneToOne(inversedBy: 'salaryAdvance')]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'La demande est obligatoire')]
    private ?EmployeeRequest $request = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'L\'employé est obligatoire')]
    private ?Employee $employee = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    #[Assert\NotBlank(message: 'Le montant demandé est obligatoire')]
    #[Assert\Positive(message: 'Le montant doit être positif')]
    #[Assert\Range(min: 1, max: 10000, notInRangeMessage: 'Le montant doit être entre {{ min }}€ et {{ max }}€')]
    private ?string $amount = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    #[Assert\Positive(message: 'Le montant approuvé doit être positif')]
    private ?string $approvedAmount = null;

    #[ORM\Column(type: Types::TEXT)]
    #[Assert\NotBlank(message: 'La raison est obligatoire')]
    #[Assert\Length(min: 10, max: 1000, minMessage: 'La raison doit faire au moins {{ limit }} caractères', maxMessage: 'La raison ne peut pas dépasser {{ limit }} caractères')]
    private ?string $reason = null;

    #[ORM\Column]
    #[Assert\NotBlank(message: 'Le nombre de mois de remboursement est obligatoire')]
    #[Assert\Range(min: 1, max: 24, notInRangeMessage: 'Le remboursement doit être entre {{ min }} et {{ max }} mois')]
    private ?int $repaymentMonths = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
    private ?string $monthlyDeduction = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $firstDeductionDate = null;

    #[ORM\Column(length: 30)]
    #[Assert\Choice(choices: ['pending', 'approved', 'rejected', 'paid', 'in_progress', 'completed'], message: 'Statut invalide')]
    private ?string $status = 'pending';

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $paidAt = null;

    #[ORM\ManyToOne]
    private ?User $paidBy = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    private ?string $remainingAmount = '0.00';

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    #[ORM\OneToMany(mappedBy: 'salaryAdvance', targetEntity: SalaryAdvanceRepayment::class, orphanRemoval: true)]
    private Collection $repayments;

    public function __construct()
    {
        $this->repayments = new ArrayCollection();
        $this->createdAt = new \DateTime();
        $this->status = 'pending';
        $this->remainingAmount = '0.00';
        $this->repaymentMonths = 1;
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRequest(): ?EmployeeRequest
    {
        return $this->request;
    }

    public function setRequest(?EmployeeRequest $request): static
    {
        $this->request = $request;
        return $this;
    }

    public function getEmployee(): ?Employee
    {
        return $this->employee;
    }

    public function setEmployee(?Employee $employee): static
    {
        $this->employee = $employee;
        return $this;
    }

    public function getAmount(): ?string
    {
        return $this->amount;
    }

    public function setAmount(string $amount): static
    {
        $this->amount = $amount;
        $this->calculateMonthlyDeduction();
        return $this;
    }

    public function getApprovedAmount(): ?string
    {
        return $this->approvedAmount;
    }

    public function setApprovedAmount(?string $approvedAmount): static
    {
        $this->approvedAmount = $approvedAmount;
        if ($approvedAmount !== null) {
            $this->remainingAmount = $approvedAmount;
            $this->calculateMonthlyDeduction();
        }
        return $this;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }

    public function setReason(string $reason): static
    {
        $this->reason = $reason;
        return $this;
    }

    public function getRepaymentMonths(): ?int
    {
        return $this->repaymentMonths;
    }

    public function setRepaymentMonths(int $repaymentMonths): static
    {
        $this->repaymentMonths = $repaymentMonths;
        $this->calculateMonthlyDeduction();
        return $this;
    }

    public function getMonthlyDeduction(): ?string
    {
        return $this->monthlyDeduction;
    }

    public function setMonthlyDeduction(?string $monthlyDeduction): static
    {
        $this->monthlyDeduction = $monthlyDeduction;
        return $this;
    }

    public function getFirstDeductionDate(): ?\DateTimeInterface
    {
        return $this->firstDeductionDate;
    }

    public function setFirstDeductionDate(?\DateTimeInterface $firstDeductionDate): static
    {
        $this->firstDeductionDate = $firstDeductionDate;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;
        return $this;
    }

    public function getPaidAt(): ?\DateTimeInterface
    {
        return $this->paidAt;
    }

    public function setPaidAt(?\DateTimeInterface $paidAt): static
    {
        $this->paidAt = $paidAt;
        return $this;
    }

    public function getPaidBy(): ?User
    {
        return $this->paidBy;
    }

    public function setPaidBy(?User $paidBy): static
    {
        $this->paidBy = $paidBy;
        return $this;
    }

    public function getRemainingAmount(): ?string
    {
        return $this->remainingAmount;
    }

    public function setRemainingAmount(string $remainingAmount): static
    {
        $this->remainingAmount = $remainingAmount;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    /**
     * @return Collection<int, SalaryAdvanceRepayment>
     */
    public function getRepayments(): Collection
    {
        return $this->repayments;
    }

    public function addRepayment(SalaryAdvanceRepayment $repayment): static
    {
        if (!$this->repayments->contains($repayment)) {
            $this->repayments->add($repayment);
            $repayment->setSalaryAdvance($this);
        }
        return $this;
    }

    public function removeRepayment(SalaryAdvanceRepayment $repayment): static
    {
        if ($this->repayments->removeElement($repayment)) {
            if ($repayment->getSalaryAdvance() === $this) {
                $repayment->setSalaryAdvance(null);
            }
        }
        return $this;
    }

    /**
     * Calculate monthly deduction based on approved amount and repayment months
     */
    private function calculateMonthlyDeduction(): void
    {
        $amount = $this->approvedAmount ?? $this->amount;
        if ($amount !== null && $this->repaymentMonths > 0) {
            $this->monthlyDeduction = number_format((float)$amount / $this->repaymentMonths, 2, '.', '');
        }
    }

    /**
     * Get the status label in French
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'pending' => 'En attente',
            'approved' => 'Approuvé',
            'rejected' => 'Rejeté',
            'paid' => 'Versé',
            'in_progress' => 'En cours de remboursement',
            'completed' => 'Remboursé',
            default => ucfirst($this->status),
        };
    }

    /**
     * Calculate the total repaid amount
     */
    public function getTotalRepaidAmount(): float
    {
        $total = 0;
        foreach ($this->repayments as $repayment) {
            $total += (float)$repayment->getAmount();
        }
        return $total;
    }

    /**
     * Calculate the remaining amount to be repaid
     */
    public function calculateRemainingAmount(): float
    {
        $approvedAmount = (float)($this->approvedAmount ?? $this->amount ?? 0);
        return $approvedAmount - $this->getTotalRepaidAmount();
    }

    /**
     * Check if the advance is fully repaid
     */
    public function isFullyRepaid(): bool
    {
        return $this->calculateRemainingAmount() <= 0.01; // Allow for small rounding differences
    }

    /**
     * Get the next deduction date
     */
    public function getNextDeductionDate(): ?\DateTimeInterface
    {
        if (!$this->firstDeductionDate) {
            return null;
        }

        $repaymentCount = $this->repayments->count();
        $nextDate = clone $this->firstDeductionDate;
        $nextDate->modify("+{$repaymentCount} month");

        return $nextDate;
    }

    public function __toString(): string
    {
        $employeeName = $this->employee?->getUser()?->getFirstName() . ' ' . $this->employee?->getUser()?->getLastName();
        return "Avance de {$this->amount}€ pour {$employeeName}";
    }
}
