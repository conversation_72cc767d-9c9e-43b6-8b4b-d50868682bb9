<?php

namespace App\Entity;

use App\Repository\SalaryAdvanceRepaymentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: SalaryAdvanceRepaymentRepository::class)]
#[ORM\Table(name: 'salary_advance_repayment')]
#[ORM\HasLifecycleCallbacks]
class SalaryAdvanceRepayment
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'repayments')]
    #[ORM\JoinColumn(nullable: false)]
    #[Assert\NotNull(message: 'L\'avance sur salaire est obligatoire')]
    private ?SalaryAdvance $salaryAdvance = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    #[Assert\NotBlank(message: 'Le montant est obligatoire')]
    #[Assert\Positive(message: 'Le montant doit être positif')]
    private ?string $amount = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Assert\NotNull(message: 'La date de déduction est obligatoire')]
    private ?\DateTimeInterface $deductionDate = null;

    #[ORM\Column(length: 20)]
    #[Assert\Choice(choices: ['scheduled', 'processed', 'failed'], message: 'Statut invalide')]
    private ?string $status = 'scheduled';

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $notes = null;

    #[ORM\ManyToOne]
    private ?User $processedBy = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $processedAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->status = 'scheduled';
    }

    #[ORM\PreUpdate]
    public function setUpdatedAtValue(): void
    {
        $this->updatedAt = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSalaryAdvance(): ?SalaryAdvance
    {
        return $this->salaryAdvance;
    }

    public function setSalaryAdvance(?SalaryAdvance $salaryAdvance): static
    {
        $this->salaryAdvance = $salaryAdvance;
        return $this;
    }

    public function getAmount(): ?string
    {
        return $this->amount;
    }

    public function setAmount(string $amount): static
    {
        $this->amount = $amount;
        return $this;
    }

    public function getDeductionDate(): ?\DateTimeInterface
    {
        return $this->deductionDate;
    }

    public function setDeductionDate(\DateTimeInterface $deductionDate): static
    {
        $this->deductionDate = $deductionDate;
        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;
        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): static
    {
        $this->notes = $notes;
        return $this;
    }

    public function getProcessedBy(): ?User
    {
        return $this->processedBy;
    }

    public function setProcessedBy(?User $processedBy): static
    {
        $this->processedBy = $processedBy;
        return $this;
    }

    public function getProcessedAt(): ?\DateTimeInterface
    {
        return $this->processedAt;
    }

    public function setProcessedAt(?\DateTimeInterface $processedAt): static
    {
        $this->processedAt = $processedAt;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }

    /**
     * Get the status label in French
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'scheduled' => 'Programmé',
            'processed' => 'Traité',
            'failed' => 'Échec',
            default => ucfirst($this->status),
        };
    }

    /**
     * Mark the repayment as processed
     */
    public function markAsProcessed(User $processedBy, ?string $notes = null): static
    {
        $this->status = 'processed';
        $this->processedBy = $processedBy;
        $this->processedAt = new \DateTime();
        if ($notes) {
            $this->notes = $notes;
        }
        return $this;
    }

    /**
     * Mark the repayment as failed
     */
    public function markAsFailed(?string $notes = null): static
    {
        $this->status = 'failed';
        if ($notes) {
            $this->notes = $notes;
        }
        return $this;
    }

    public function __toString(): string
    {
        return "Remboursement de {$this->amount}€ le {$this->deductionDate?->format('d/m/Y')}";
    }
}
