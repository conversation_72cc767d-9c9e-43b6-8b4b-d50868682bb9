<?php

namespace App\Repository;

use App\Entity\Contract;
use App\Entity\Partner;
use App\Entity\Project;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Contract>
 *
 * @method Contract|null find($id, $lockMode = null, $lockVersion = null)
 * @method Contract|null findOneBy(array $criteria, array $orderBy = null)
 * @method Contract[]    findAll()
 * @method Contract[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ContractRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Contract::class);
    }

    /**
     * Find contracts by project
     */
    public function findByProject(Project $project): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.project = :project')
            ->setParameter('project', $project)
            ->orderBy('c.startDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find contracts by supplier
     */
    public function findBySupplier(Partner $supplier): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.supplier = :supplier')
            ->setParameter('supplier', $supplier)
            ->orderBy('c.startDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find contracts by status
     */
    public function findByStatus(string $status): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.status = :status')
            ->setParameter('status', $status)
            ->orderBy('c.startDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find active contracts
     */
    public function findActiveContracts(): array
    {
        $now = new \DateTime();

        return $this->createQueryBuilder('c')
            ->andWhere('c.status = :status')
            ->andWhere('c.startDate <= :now')
            ->andWhere('c.endDate >= :now')
            ->setParameter('status', Contract::STATUS_ACTIVE)
            ->setParameter('now', $now)
            ->orderBy('c.endDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find contracts expiring soon (within the next 30 days)
     */
    public function findContractsExpiringSoon(int $days = 30): array
    {
        $now = new \DateTime();
        $future = (new \DateTime())->modify('+' . $days . ' days');

        return $this->createQueryBuilder('c')
            ->andWhere('c.status = :status')
            ->andWhere('c.endDate BETWEEN :now AND :future')
            ->setParameter('status', Contract::STATUS_ACTIVE)
            ->setParameter('now', $now)
            ->setParameter('future', $future)
            ->orderBy('c.endDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find contracts created by a user
     */
    public function findByCreatedBy(User $user): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.createdBy = :user')
            ->setParameter('user', $user)
            ->orderBy('c.startDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Generate a unique contract number
     */
    public function generateContractNumber(): string
    {
        $year = date('Y');
        $prefix = 'CT-' . $year . '-';

        $lastContract = $this->createQueryBuilder('c')
            ->andWhere('c.contractNumber LIKE :prefix')
            ->setParameter('prefix', $prefix . '%')
            ->orderBy('c.id', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        if (!$lastContract) {
            return $prefix . '0001';
        }

        $lastNumber = (int) substr($lastContract->getContractNumber(), strlen($prefix));
        $newNumber = $lastNumber + 1;

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Find contracts by year grouped by month
     */
    public function findContractsByYear(string $year): array
    {
        try {
            $conn = $this->getEntityManager()->getConnection();

            // Vérifier d'abord si la table contract existe et a les bonnes colonnes
            $tableExists = $conn->fetchOne("SHOW TABLES LIKE 'contract'");
            if (!$tableExists) {
                return []; // Retourner un tableau vide si la table n'existe pas
            }

            // Vérifier si les colonnes existent
            $columns = $conn->fetchAllAssociative("SHOW COLUMNS FROM contract");
            $hasStartDate = false;
            $hasTotalAmount = false;

            foreach ($columns as $column) {
                if ($column['Field'] === 'start_date') {
                    $hasStartDate = true;
                }
                if ($column['Field'] === 'total_amount') {
                    $hasTotalAmount = true;
                }
            }

            if (!$hasStartDate || !$hasTotalAmount) {
                return []; // Retourner un tableau vide si les colonnes n'existent pas
            }

            $sql = '
                SELECT MONTH(c.start_date) as month, SUM(c.total_amount) as total
                FROM contract c
                WHERE YEAR(c.start_date) = ?
                GROUP BY MONTH(c.start_date)
                ORDER BY month ASC
            ';

            $stmt = $conn->prepare($sql);
            $result = $stmt->executeQuery([$year]);

            return $result->fetchAllAssociative();
        } catch (\Exception $e) {
            // En cas d'erreur, retourner un tableau vide
            return [];
        }
    }

    /**
     * Find contracts by period
     */
    public function findContractsByPeriod(\DateTime $startDate, \DateTime $endDate): array
    {
        try {
            $conn = $this->getEntityManager()->getConnection();

            // Vérifier d'abord si la table contract existe et a les bonnes colonnes
            $tableExists = $conn->fetchOne("SHOW TABLES LIKE 'contract'");
            if (!$tableExists) {
                return []; // Retourner un tableau vide si la table n'existe pas
            }

            $sql = '
                SELECT
                    YEAR(c.start_date) as year,
                    MONTH(c.start_date) as month,
                    SUM(c.total_amount) as total
                FROM contract c
                WHERE c.start_date BETWEEN ? AND ?
                GROUP BY year, month
                ORDER BY year ASC, month ASC
            ';

            $stmt = $conn->prepare($sql);
            $result = $stmt->executeQuery([
                $startDate->format('Y-m-d'),
                $endDate->format('Y-m-d')
            ]);

            return $result->fetchAllAssociative();
        } catch (\Exception $e) {
            // En cas d'erreur, retourner un tableau vide
            return [];
        }
    }
}
