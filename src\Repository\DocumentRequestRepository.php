<?php

namespace App\Repository;

use App\Entity\DocumentRequest;
use App\Entity\Employee;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<DocumentRequest>
 *
 * @method DocumentRequest|null find($id, $lockMode = null, $lockVersion = null)
 * @method DocumentRequest|null findOneBy(array $criteria, array $orderBy = null)
 * @method DocumentRequest[]    findAll()
 * @method DocumentRequest[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class DocumentRequestRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DocumentRequest::class);
    }

    /**
     * Find document requests by employee
     */
    public function findByEmployee(Employee $employee, ?string $status = null): array
    {
        $qb = $this->createQueryBuilder('dr')
            ->andWhere('dr.employee = :employee')
            ->setParameter('employee', $employee)
            ->orderBy('dr.createdAt', 'DESC');

        if ($status) {
            $qb->andWhere('dr.status = :status')
               ->setParameter('status', $status);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find document requests by type
     */
    public function findByDocumentType(string $documentType, ?string $status = null): array
    {
        $qb = $this->createQueryBuilder('dr')
            ->andWhere('dr.documentType = :documentType')
            ->setParameter('documentType', $documentType)
            ->orderBy('dr.createdAt', 'DESC');

        if ($status) {
            $qb->andWhere('dr.status = :status')
               ->setParameter('status', $status);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find urgent document requests (needed within 3 days)
     */
    public function findUrgentRequests(): array
    {
        $urgentDate = new \DateTime('+3 days');
        
        return $this->createQueryBuilder('dr')
            ->andWhere('dr.neededBy <= :urgentDate')
            ->andWhere('dr.status IN (:statuses)')
            ->setParameter('urgentDate', $urgentDate)
            ->setParameter('statuses', ['pending', 'processing'])
            ->orderBy('dr.neededBy', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find document requests ready for delivery
     */
    public function findReadyForDelivery(): array
    {
        return $this->createQueryBuilder('dr')
            ->andWhere('dr.status = :status')
            ->setParameter('status', 'ready')
            ->orderBy('dr.neededBy', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find document requests by delivery method
     */
    public function findByDeliveryMethod(string $deliveryMethod): array
    {
        return $this->createQueryBuilder('dr')
            ->andWhere('dr.deliveryMethod = :deliveryMethod')
            ->setParameter('deliveryMethod', $deliveryMethod)
            ->orderBy('dr.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find document requests by language
     */
    public function findByLanguage(string $language): array
    {
        return $this->createQueryBuilder('dr')
            ->andWhere('dr.language = :language')
            ->setParameter('language', $language)
            ->orderBy('dr.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get document requests statistics
     */
    public function getStatistics(): array
    {
        $qb = $this->createQueryBuilder('dr')
            ->select('dr.status, dr.documentType, COUNT(dr.id) as count')
            ->groupBy('dr.status, dr.documentType');

        $results = $qb->getQuery()->getResult();
        
        $stats = [
            'total' => 0,
            'by_status' => [],
            'by_type' => [],
            'by_status_and_type' => []
        ];

        foreach ($results as $result) {
            $status = $result['status'];
            $type = $result['documentType'];
            $count = (int)$result['count'];

            $stats['total'] += $count;
            
            if (!isset($stats['by_status'][$status])) {
                $stats['by_status'][$status] = 0;
            }
            $stats['by_status'][$status] += $count;

            if (!isset($stats['by_type'][$type])) {
                $stats['by_type'][$type] = 0;
            }
            $stats['by_type'][$type] += $count;

            if (!isset($stats['by_status_and_type'][$status])) {
                $stats['by_status_and_type'][$status] = [];
            }
            $stats['by_status_and_type'][$status][$type] = $count;
        }

        return $stats;
    }

    /**
     * Find overdue document requests
     */
    public function findOverdueRequests(): array
    {
        $now = new \DateTime();
        
        return $this->createQueryBuilder('dr')
            ->andWhere('dr.neededBy < :now')
            ->andWhere('dr.status IN (:statuses)')
            ->setParameter('now', $now)
            ->setParameter('statuses', ['pending', 'processing'])
            ->orderBy('dr.neededBy', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Count pending document requests by employee
     */
    public function countPendingByEmployee(Employee $employee): int
    {
        return $this->createQueryBuilder('dr')
            ->select('COUNT(dr.id)')
            ->andWhere('dr.employee = :employee')
            ->andWhere('dr.status IN (:statuses)')
            ->setParameter('employee', $employee)
            ->setParameter('statuses', ['pending', 'processing'])
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function save(DocumentRequest $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(DocumentRequest $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
