<?php

namespace App\Repository;

use App\Entity\Employee;
use App\Entity\EmployeeRequest;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EmployeeRequest>
 *
 * @method EmployeeRequest|null find($id, $lockMode = null, $lockVersion = null)
 * @method EmployeeRequest|null findOneBy(array $criteria, array $orderBy = null)
 * @method EmployeeRequest[]    findAll()
 * @method EmployeeRequest[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EmployeeRequestRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EmployeeRequest::class);
    }

    /**
     * Find requests by employee
     */
    public function findByEmployee(Employee $employee, ?string $status = null): array
    {
        $qb = $this->createQueryBuilder('r')
            ->andWhere('r.employee = :employee')
            ->setParameter('employee', $employee)
            ->orderBy('r.createdAt', 'DESC');

        if ($status) {
            $qb->andWhere('r.status = :status')
               ->setParameter('status', $status);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find pending requests for manager approval
     */
    public function findPendingForManager(User $manager): array
    {
        return $this->createQueryBuilder('r')
            ->join('r.employee', 'e')
            ->andWhere('e.manager = :manager')
            ->andWhere('r.status = :status')
            ->setParameter('manager', $manager)
            ->setParameter('status', 'pending')
            ->orderBy('r.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find requests for HR review
     */
    public function findForHRReview(): array
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.status IN (:statuses)')
            ->setParameter('statuses', ['manager_approved', 'hr_review'])
            ->orderBy('r.priority', 'DESC')
            ->addOrderBy('r.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find requests by type
     */
    public function findByType(string $type, ?string $status = null): array
    {
        $qb = $this->createQueryBuilder('r')
            ->andWhere('r.type = :type')
            ->setParameter('type', $type)
            ->orderBy('r.createdAt', 'DESC');

        if ($status) {
            $qb->andWhere('r.status = :status')
               ->setParameter('status', $status);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find urgent requests (high priority or urgent)
     */
    public function findUrgentRequests(): array
    {
        return $this->createQueryBuilder('r')
            ->andWhere('r.priority IN (:priorities)')
            ->andWhere('r.status NOT IN (:excludedStatuses)')
            ->setParameter('priorities', ['high', 'urgent'])
            ->setParameter('excludedStatuses', ['completed', 'rejected', 'cancelled'])
            ->orderBy('r.priority', 'DESC')
            ->addOrderBy('r.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get requests statistics
     */
    public function getStatistics(): array
    {
        $qb = $this->createQueryBuilder('r')
            ->select('r.status, r.type, COUNT(r.id) as count')
            ->groupBy('r.status, r.type');

        $results = $qb->getQuery()->getResult();
        
        $stats = [
            'total' => 0,
            'by_status' => [],
            'by_type' => [],
            'by_status_and_type' => []
        ];

        foreach ($results as $result) {
            $status = $result['status'];
            $type = $result['type'];
            $count = (int)$result['count'];

            $stats['total'] += $count;
            
            if (!isset($stats['by_status'][$status])) {
                $stats['by_status'][$status] = 0;
            }
            $stats['by_status'][$status] += $count;

            if (!isset($stats['by_type'][$type])) {
                $stats['by_type'][$type] = 0;
            }
            $stats['by_type'][$type] += $count;

            $stats['by_status_and_type'][$status][$type] = $count;
        }

        return $stats;
    }

    /**
     * Find requests created in the last N days
     */
    public function findRecentRequests(int $days = 30): array
    {
        $date = new \DateTime("-{$days} days");
        
        return $this->createQueryBuilder('r')
            ->andWhere('r.createdAt >= :date')
            ->setParameter('date', $date)
            ->orderBy('r.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find requests by department
     */
    public function findByDepartment(string $departmentName): array
    {
        return $this->createQueryBuilder('r')
            ->join('r.employee', 'e')
            ->join('e.department', 'd')
            ->andWhere('d.name = :department')
            ->setParameter('department', $departmentName)
            ->orderBy('r.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Count pending requests by employee
     */
    public function countPendingByEmployee(Employee $employee): int
    {
        return $this->createQueryBuilder('r')
            ->select('COUNT(r.id)')
            ->andWhere('r.employee = :employee')
            ->andWhere('r.status IN (:statuses)')
            ->setParameter('employee', $employee)
            ->setParameter('statuses', ['pending', 'manager_approved', 'hr_review', 'processing'])
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Find overdue requests (requested date passed)
     */
    public function findOverdueRequests(): array
    {
        $now = new \DateTime();
        
        return $this->createQueryBuilder('r')
            ->andWhere('r.requestedDate < :now')
            ->andWhere('r.status NOT IN (:excludedStatuses)')
            ->setParameter('now', $now)
            ->setParameter('excludedStatuses', ['completed', 'rejected', 'cancelled'])
            ->orderBy('r.requestedDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Search requests by keyword
     */
    public function searchRequests(string $keyword): array
    {
        return $this->createQueryBuilder('r')
            ->join('r.employee', 'e')
            ->join('e.user', 'u')
            ->andWhere('r.title LIKE :keyword OR r.description LIKE :keyword OR u.firstName LIKE :keyword OR u.lastName LIKE :keyword')
            ->setParameter('keyword', '%' . $keyword . '%')
            ->orderBy('r.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function save(EmployeeRequest $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(EmployeeRequest $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
