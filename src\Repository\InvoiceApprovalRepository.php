<?php

namespace App\Repository;

use App\Entity\Invoice;
use App\Entity\InvoiceApproval;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<InvoiceApproval>
 *
 * @method InvoiceApproval|null find($id, $lockMode = null, $lockVersion = null)
 * @method InvoiceApproval|null findOneBy(array $criteria, array $orderBy = null)
 * @method InvoiceApproval[]    findAll()
 * @method InvoiceApproval[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class InvoiceApprovalRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, InvoiceApproval::class);
    }

    /**
     * Find approvals by invoice
     */
    public function findByInvoice(Invoice $invoice): array
    {
        return $this->createQueryBuilder('ia')
            ->andWhere('ia.invoice = :invoice')
            ->setParameter('invoice', $invoice)
            ->orderBy('ia.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find approvals by user
     */
    public function findByUser(User $user): array
    {
        return $this->createQueryBuilder('ia')
            ->andWhere('ia.user = :user')
            ->setParameter('user', $user)
            ->orderBy('ia.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find approvals by action
     */
    public function findByAction(string $action): array
    {
        return $this->createQueryBuilder('ia')
            ->andWhere('ia.action = :action')
            ->setParameter('action', $action)
            ->orderBy('ia.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find approvals delegated to a user
     */
    public function findDelegatedToUser(User $user): array
    {
        return $this->createQueryBuilder('ia')
            ->andWhere('ia.delegatedTo = :user')
            ->andWhere('ia.action = :action')
            ->setParameter('user', $user)
            ->setParameter('action', InvoiceApproval::ACTION_DELEGATE)
            ->orderBy('ia.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find latest approval for an invoice
     */
    public function findLatestApprovalForInvoice(Invoice $invoice): ?InvoiceApproval
    {
        return $this->createQueryBuilder('ia')
            ->andWhere('ia.invoice = :invoice')
            ->setParameter('invoice', $invoice)
            ->orderBy('ia.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find latest approval for an invoice by level
     */
    public function findLatestApprovalForInvoiceByLevel(Invoice $invoice, int $level): ?InvoiceApproval
    {
        return $this->createQueryBuilder('ia')
            ->andWhere('ia.invoice = :invoice')
            ->andWhere('ia.approvalLevel = :level')
            ->setParameter('invoice', $invoice)
            ->setParameter('level', $level)
            ->orderBy('ia.createdAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Count approvals by action
     */
    public function countByAction(string $action): int
    {
        return $this->createQueryBuilder('ia')
            ->select('COUNT(ia.id)')
            ->andWhere('ia.action = :action')
            ->setParameter('action', $action)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Count approvals by user and action
     */
    public function countByUserAndAction(User $user, string $action): int
    {
        return $this->createQueryBuilder('ia')
            ->select('COUNT(ia.id)')
            ->andWhere('ia.user = :user')
            ->andWhere('ia.action = :action')
            ->setParameter('user', $user)
            ->setParameter('action', $action)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Get approval statistics by user
     */
    public function getApprovalStatisticsByUser(User $user): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT ia.action, COUNT(ia.id) as count
            FROM invoice_approval ia
            WHERE ia.user_id = :userId
            GROUP BY ia.action
        ';
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue('userId', $user->getId());
        $result = $stmt->executeQuery();
        
        return $result->fetchAllAssociative();
    }

    /**
     * Get approval statistics by period
     */
    public function getApprovalStatisticsByPeriod(\DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT ia.action, COUNT(ia.id) as count
            FROM invoice_approval ia
            WHERE ia.created_at BETWEEN :startDate AND :endDate
            GROUP BY ia.action
        ';
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue('startDate', $startDate->format('Y-m-d H:i:s'));
        $stmt->bindValue('endDate', $endDate->format('Y-m-d H:i:s'));
        $result = $stmt->executeQuery();
        
        return $result->fetchAllAssociative();
    }

    /**
     * Get average approval time
     */
    public function getAverageApprovalTime(): float
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT AVG(TIMESTAMPDIFF(HOUR, i.created_at, ia.created_at)) as avg_hours
            FROM invoice i
            JOIN invoice_approval ia ON i.id = ia.invoice_id
            WHERE ia.action = :action
        ';
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue('action', InvoiceApproval::ACTION_APPROVE);
        $result = $stmt->executeQuery();
        
        return (float) $result->fetchOne() ?: 0;
    }
}
