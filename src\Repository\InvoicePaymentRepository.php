<?php

namespace App\Repository;

use App\Entity\Invoice;
use App\Entity\InvoicePayment;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<InvoicePayment>
 *
 * @method InvoicePayment|null find($id, $lockMode = null, $lockVersion = null)
 * @method InvoicePayment|null findOneBy(array $criteria, array $orderBy = null)
 * @method InvoicePayment[]    findAll()
 * @method InvoicePayment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class InvoicePaymentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, InvoicePayment::class);
    }

    /**
     * Find payments by invoice
     */
    public function findByInvoice(Invoice $invoice): array
    {
        return $this->createQueryBuilder('ip')
            ->andWhere('ip.invoice = :invoice')
            ->setParameter('invoice', $invoice)
            ->orderBy('ip.paymentDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get total payments by invoice
     */
    public function getTotalPaymentsByInvoice(Invoice $invoice): float
    {
        $result = $this->createQueryBuilder('ip')
            ->select('SUM(ip.amount) as total')
            ->andWhere('ip.invoice = :invoice')
            ->setParameter('invoice', $invoice)
            ->getQuery()
            ->getSingleScalarResult();

        return (float) $result ?: 0;
    }

    /**
     * Find payments by period
     */
    public function findByPeriod(\DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        return $this->createQueryBuilder('ip')
            ->andWhere('ip.paymentDate >= :startDate')
            ->andWhere('ip.paymentDate <= :endDate')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->orderBy('ip.paymentDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get total payments by period
     */
    public function getTotalPaymentsByPeriod(\DateTimeInterface $startDate, \DateTimeInterface $endDate): float
    {
        $result = $this->createQueryBuilder('ip')
            ->select('SUM(ip.amount) as total')
            ->andWhere('ip.paymentDate >= :startDate')
            ->andWhere('ip.paymentDate <= :endDate')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getSingleScalarResult();

        return (float) $result ?: 0;
    }

    /**
     * Find payments by payment method
     */
    public function findByPaymentMethod(string $paymentMethod): array
    {
        return $this->createQueryBuilder('ip')
            ->andWhere('ip.paymentMethod = :paymentMethod')
            ->setParameter('paymentMethod', $paymentMethod)
            ->orderBy('ip.paymentDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get total payments by payment method
     */
    public function getTotalPaymentsByPaymentMethod(string $paymentMethod): float
    {
        $result = $this->createQueryBuilder('ip')
            ->select('SUM(ip.amount) as total')
            ->andWhere('ip.paymentMethod = :paymentMethod')
            ->setParameter('paymentMethod', $paymentMethod)
            ->getQuery()
            ->getSingleScalarResult();

        return (float) $result ?: 0;
    }

    /**
     * Find unreconciled payments
     */
    public function findUnreconciled(): array
    {
        return $this->createQueryBuilder('ip')
            ->andWhere('ip.isReconciled = :isReconciled')
            ->setParameter('isReconciled', false)
            ->orderBy('ip.paymentDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get payments by month for the current year
     */
    public function findPaymentsByMonth(): array
    {
        $currentYear = date('Y');

        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT MONTH(ip.payment_date) as month, SUM(ip.amount) as total
            FROM invoice_payment ip
            WHERE YEAR(ip.payment_date) = :year
            GROUP BY MONTH(ip.payment_date)
            ORDER BY month ASC
        ';

        $stmt = $conn->prepare($sql);
        $stmt->bindValue('year', $currentYear);
        $result = $stmt->executeQuery();

        return $result->fetchAllAssociative();
    }

    /**
     * Get payments by payment method for the current year
     */
    public function findPaymentsByPaymentMethod(): array
    {
        $currentYear = date('Y');

        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT ip.payment_method, SUM(ip.amount) as total, COUNT(ip.id) as count
            FROM invoice_payment ip
            WHERE YEAR(ip.payment_date) = :year
            GROUP BY ip.payment_method
            ORDER BY total DESC
        ';

        $stmt = $conn->prepare($sql);
        $stmt->bindValue('year', $currentYear);
        $result = $stmt->executeQuery();

        return $result->fetchAllAssociative();
    }
}
