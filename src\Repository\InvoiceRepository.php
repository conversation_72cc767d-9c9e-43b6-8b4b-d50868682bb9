<?php

namespace App\Repository;

use App\Entity\Invoice;
use App\Entity\Partner;
use App\Entity\Project;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Invoice>
 *
 * @method Invoice|null find($id, $lockMode = null, $lockVersion = null)
 * @method Invoice|null findOneBy(array $criteria, array $orderBy = null)
 * @method Invoice[]    findAll()
 * @method Invoice[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class InvoiceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Invoice::class);
    }

    /**
     * Find invoices by supplier
     */
    public function findBySupplier(Partner $supplier): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.supplier = :supplier')
            ->setParameter('supplier', $supplier)
            ->orderBy('i.invoiceDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find invoices by project
     */
    public function findByProject(Project $project): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.project = :project')
            ->setParameter('project', $project)
            ->orderBy('i.invoiceDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find invoices pending approval
     */
    public function findPendingApproval(): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.status = :status')
            ->setParameter('status', Invoice::STATUS_PENDING_APPROVAL)
            ->orderBy('i.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find invoices pending approval for a specific user
     */
    public function findPendingApprovalForUser(User $user): array
    {
        // This is a simplified version. In a real application, you would need to check
        // approval workflows and permissions to determine which invoices a user can approve.
        return $this->createQueryBuilder('i')
            ->andWhere('i.status = :status')
            ->setParameter('status', Invoice::STATUS_PENDING_APPROVAL)
            ->orderBy('i.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find overdue invoices
     */
    public function findOverdue(): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.dueDate < :today')
            ->andWhere('i.status NOT IN (:paidStatuses)')
            ->setParameter('today', new \DateTime())
            ->setParameter('paidStatuses', [Invoice::STATUS_PAID, Invoice::STATUS_CANCELLED])
            ->orderBy('i.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find invoices due soon (within the next X days)
     */
    public function findDueSoon(int $days = 7): array
    {
        $today = new \DateTime();
        $future = (new \DateTime())->modify("+$days days");

        return $this->createQueryBuilder('i')
            ->andWhere('i.dueDate >= :today')
            ->andWhere('i.dueDate <= :future')
            ->andWhere('i.status NOT IN (:paidStatuses)')
            ->setParameter('today', $today)
            ->setParameter('future', $future)
            ->setParameter('paidStatuses', [Invoice::STATUS_PAID, Invoice::STATUS_CANCELLED])
            ->orderBy('i.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find invoices by period
     */
    public function findByPeriod(\DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.invoiceDate >= :startDate')
            ->andWhere('i.invoiceDate <= :endDate')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->orderBy('i.invoiceDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find invoices by status
     */
    public function findByStatus(string $status): array
    {
        return $this->createQueryBuilder('i')
            ->andWhere('i.status = :status')
            ->setParameter('status', $status)
            ->orderBy('i.invoiceDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get total amount by period
     */
    public function getTotalAmountByPeriod(\DateTimeInterface $startDate, \DateTimeInterface $endDate): float
    {
        $result = $this->createQueryBuilder('i')
            ->select('SUM(i.totalAmount) as total')
            ->andWhere('i.invoiceDate >= :startDate')
            ->andWhere('i.invoiceDate <= :endDate')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->getQuery()
            ->getSingleScalarResult();

        return (float) $result ?: 0;
    }

    /**
     * Get total amount by supplier
     */
    public function getTotalAmountBySupplier(Partner $supplier): float
    {
        $result = $this->createQueryBuilder('i')
            ->select('SUM(i.totalAmount) as total')
            ->andWhere('i.supplier = :supplier')
            ->setParameter('supplier', $supplier)
            ->getQuery()
            ->getSingleScalarResult();

        return (float) $result ?: 0;
    }

    /**
     * Get total amount by project
     */
    public function getTotalAmountByProject(Project $project): float
    {
        $result = $this->createQueryBuilder('i')
            ->select('SUM(i.totalAmount) as total')
            ->andWhere('i.project = :project')
            ->setParameter('project', $project)
            ->getQuery()
            ->getSingleScalarResult();

        return (float) $result ?: 0;
    }

    /**
     * Get total unpaid amount
     */
    public function getTotalUnpaidAmount(): float
    {
        $result = $this->createQueryBuilder('i')
            ->select('SUM(i.totalAmount - i.paidAmount) as total')
            ->andWhere('i.status NOT IN (:paidStatuses)')
            ->setParameter('paidStatuses', [Invoice::STATUS_PAID, Invoice::STATUS_CANCELLED])
            ->getQuery()
            ->getSingleScalarResult();

        return (float) $result ?: 0;
    }

    /**
     * Get total overdue amount
     */
    public function getTotalOverdueAmount(): float
    {
        $result = $this->createQueryBuilder('i')
            ->select('SUM(i.totalAmount - i.paidAmount) as total')
            ->andWhere('i.dueDate < :today')
            ->andWhere('i.status NOT IN (:paidStatuses)')
            ->setParameter('today', new \DateTime())
            ->setParameter('paidStatuses', [Invoice::STATUS_PAID, Invoice::STATUS_CANCELLED])
            ->getQuery()
            ->getSingleScalarResult();

        return (float) $result ?: 0;
    }

    /**
     * Get invoices by month for the current year
     */
    public function findInvoicesByMonth(): array
    {
        $currentYear = date('Y');

        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT MONTH(i.invoice_date) as month, SUM(i.total_amount) as total
            FROM invoice i
            WHERE YEAR(i.invoice_date) = :year
            GROUP BY MONTH(i.invoice_date)
            ORDER BY month ASC
        ';

        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery(['year' => $currentYear]);

        return $result->fetchAllAssociative();
    }

    /**
     * Get invoices by supplier for the current year
     */
    public function findInvoicesBySupplier(): array
    {
        $currentYear = date('Y');

        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT p.name as supplier_name, SUM(i.total_amount) as total
            FROM invoice i
            JOIN partner p ON i.supplier_id = p.id
            WHERE strftime(\'%Y\', i.invoice_date) = :year
            GROUP BY p.name
            ORDER BY total DESC
            LIMIT 10
        ';

        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery(['year' => $currentYear]);

        return $result->fetchAllAssociative();
    }

    /**
     * Get invoices by project for the current year
     */
    public function findInvoicesByProject(): array
    {
        $currentYear = date('Y');

        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT p.name as project_name, SUM(i.total_amount) as total
            FROM invoice i
            JOIN project p ON i.project_id = p.id
            WHERE strftime(\'%Y\', i.invoice_date) = :year
            GROUP BY p.name
            ORDER BY total DESC
            LIMIT 10
        ';

        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery(['year' => $currentYear]);

        return $result->fetchAllAssociative();
    }

    /**
     * Get invoices by status for the current year
     */
    public function findInvoicesByStatus(): array
    {
        $currentYear = date('Y');

        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT i.status, COUNT(i.id) as count, SUM(i.total_amount) as total
            FROM invoice i
            WHERE strftime(\'%Y\', i.invoice_date) = :year
            GROUP BY i.status
            ORDER BY total DESC
        ';

        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery(['year' => $currentYear]);

        return $result->fetchAllAssociative();
    }

    /**
     * Count pending invoices
     */
    public function countPending(): int
    {
        return $this->createQueryBuilder('i')
            ->select('COUNT(i.id)')
            ->andWhere('i.status = :status')
            ->setParameter('status', Invoice::STATUS_PENDING_APPROVAL)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Count paid invoices
     */
    public function countPaid(): int
    {
        return $this->createQueryBuilder('i')
            ->select('COUNT(i.id)')
            ->andWhere('i.status = :status')
            ->setParameter('status', Invoice::STATUS_PAID)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Count overdue invoices
     */
    public function countOverdue(): int
    {
        return $this->createQueryBuilder('i')
            ->select('COUNT(i.id)')
            ->andWhere('i.dueDate < :today')
            ->andWhere('i.status NOT IN (:paidStatuses)')
            ->setParameter('today', new \DateTime())
            ->setParameter('paidStatuses', [Invoice::STATUS_PAID, Invoice::STATUS_CANCELLED])
            ->getQuery()
            ->getSingleScalarResult();
    }
}
