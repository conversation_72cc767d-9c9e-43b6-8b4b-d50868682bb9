<?php

namespace App\Repository;

use App\Entity\ProjectStockAllocation;
use App\Entity\Project;
use App\Entity\StockItem;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ProjectStockAllocation>
 */
class ProjectStockAllocationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProjectStockAllocation::class);
    }

    /**
     * Find allocations by project
     */
    public function findByProject(Project $project): array
    {
        return $this->createQueryBuilder('psa')
            ->andWhere('psa.project = :project')
            ->setParameter('project', $project)
            ->orderBy('psa.allocatedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find active allocations by project
     */
    public function findActiveByProject(Project $project): array
    {
        return $this->createQueryBuilder('psa')
            ->andWhere('psa.project = :project')
            ->andWhere('psa.status IN (:statuses)')
            ->setParameter('project', $project)
            ->setParameter('statuses', ['allocated', 'in_use'])
            ->orderBy('psa.allocatedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find allocations by stock item
     */
    public function findByStockItem(StockItem $stockItem): array
    {
        return $this->createQueryBuilder('psa')
            ->andWhere('psa.stockItem = :stockItem')
            ->setParameter('stockItem', $stockItem)
            ->orderBy('psa.allocatedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get total allocated quantity for a stock item
     */
    public function getTotalAllocatedQuantity(StockItem $stockItem): int
    {
        $result = $this->createQueryBuilder('psa')
            ->select('SUM(psa.allocatedQuantity)')
            ->andWhere('psa.stockItem = :stockItem')
            ->andWhere('psa.status IN (:statuses)')
            ->setParameter('stockItem', $stockItem)
            ->setParameter('statuses', ['allocated', 'in_use'])
            ->getQuery()
            ->getSingleScalarResult();

        return $result ?? 0;
    }

    /**
     * Get available quantity for allocation
     */
    public function getAvailableQuantity(StockItem $stockItem): int
    {
        $totalAllocated = $this->getTotalAllocatedQuantity($stockItem);
        return max(0, $stockItem->getQuantity() - $totalAllocated);
    }

    /**
     * Find overdue allocations (allocated but not used for more than X days)
     */
    public function findOverdueAllocations(int $days = 30): array
    {
        $cutoffDate = new \DateTime("-{$days} days");

        return $this->createQueryBuilder('psa')
            ->andWhere('psa.status = :status')
            ->andWhere('psa.allocatedAt < :cutoffDate')
            ->setParameter('status', 'allocated')
            ->setParameter('cutoffDate', $cutoffDate)
            ->orderBy('psa.allocatedAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get allocation statistics by project
     */
    public function getProjectAllocationStats(Project $project): array
    {
        $qb = $this->createQueryBuilder('psa')
            ->select([
                'COUNT(psa.id) as total_allocations',
                'SUM(psa.allocatedQuantity) as total_allocated',
                'SUM(psa.usedQuantity) as total_used',
                'COUNT(CASE WHEN psa.status = \'allocated\' THEN 1 END) as pending_allocations',
                'COUNT(CASE WHEN psa.status = \'in_use\' THEN 1 END) as active_allocations',
                'COUNT(CASE WHEN psa.status = \'returned\' THEN 1 END) as returned_allocations',
                'COUNT(CASE WHEN psa.status = \'consumed\' THEN 1 END) as consumed_allocations'
            ])
            ->andWhere('psa.project = :project')
            ->setParameter('project', $project);

        return $qb->getQuery()->getSingleResult();
    }

    /**
     * Get allocation statistics by stock item
     */
    public function getStockItemAllocationStats(StockItem $stockItem): array
    {
        $qb = $this->createQueryBuilder('psa')
            ->select([
                'COUNT(psa.id) as total_allocations',
                'SUM(psa.allocatedQuantity) as total_allocated',
                'SUM(psa.usedQuantity) as total_used',
                'COUNT(DISTINCT psa.project) as projects_count'
            ])
            ->andWhere('psa.stockItem = :stockItem')
            ->setParameter('stockItem', $stockItem);

        return $qb->getQuery()->getSingleResult();
    }
}
