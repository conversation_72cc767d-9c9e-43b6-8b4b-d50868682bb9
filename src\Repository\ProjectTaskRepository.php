<?php

namespace App\Repository;

use App\Entity\Project;
use App\Entity\ProjectTask;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ProjectTask>
 */
class ProjectTaskRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProjectTask::class);
    }

    /**
     * Find tasks by project
     */
    public function findByProject(Project $project): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.project = :project')
            ->andWhere('t.parentTask IS NULL')
            ->setParameter('project', $project)
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tasks by status
     */
    public function findByStatus(string $status): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.status = :status')
            ->setParameter('status', $status)
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tasks by priority
     */
    public function findByPriority(string $priority): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.priority = :priority')
            ->setParameter('priority', $priority)
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tasks assigned to a user
     */
    public function findByAssignee(User $user): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.assignedTo = :user')
            ->setParameter('user', $user)
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find overdue tasks
     */
    public function findOverdueTasks(): array
    {
        $today = new \DateTime();

        return $this->createQueryBuilder('t')
            ->andWhere('t.dueDate < :today')
            ->andWhere('t.status NOT IN (:statuses)')
            ->setParameter('today', $today)
            ->setParameter('statuses', ['completed', 'cancelled'])
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find overdue tasks for a specific project
     */
    public function findOverdueByProject(Project $project): array
    {
        $today = new \DateTime();

        return $this->createQueryBuilder('t')
            ->andWhere('t.project = :project')
            ->andWhere('t.dueDate < :today')
            ->andWhere('t.status NOT IN (:statuses)')
            ->setParameter('project', $project)
            ->setParameter('today', $today)
            ->setParameter('statuses', ['completed', 'cancelled'])
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tasks due soon (within the next X days)
     */
    public function findTasksDueSoon(int $days = 7): array
    {
        $today = new \DateTime();
        $future = (new \DateTime())->modify('+' . $days . ' days');

        return $this->createQueryBuilder('t')
            ->andWhere('t.dueDate >= :today')
            ->andWhere('t.dueDate <= :future')
            ->andWhere('t.status NOT IN (:statuses)')
            ->setParameter('today', $today)
            ->setParameter('future', $future)
            ->setParameter('statuses', ['completed', 'cancelled'])
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find upcoming tasks for a specific project (within the next X days)
     */
    public function findUpcomingByProject(Project $project, int $limit = 5, int $days = 14): array
    {
        $today = new \DateTime();
        $future = (new \DateTime())->modify('+' . $days . ' days');

        return $this->createQueryBuilder('t')
            ->andWhere('t.project = :project')
            ->andWhere('t.dueDate >= :today')
            ->andWhere('t.dueDate <= :future')
            ->andWhere('t.status NOT IN (:statuses)')
            ->setParameter('project', $project)
            ->setParameter('today', $today)
            ->setParameter('future', $future)
            ->setParameter('statuses', ['completed', 'cancelled'])
            ->orderBy('t.dueDate', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tasks by project and status
     */
    public function findByProjectAndStatus(Project $project, string $status): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.project = :project')
            ->andWhere('t.status = :status')
            ->setParameter('project', $project)
            ->setParameter('status', $status)
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tasks by project and assignee
     */
    public function findByProjectAndAssignee(Project $project, User $user): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.project = :project')
            ->andWhere('t.assignedTo = :user')
            ->setParameter('project', $project)
            ->setParameter('user', $user)
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find subtasks by parent task
     */
    public function findSubtasks(ProjectTask $parentTask): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.parentTask = :parentTask')
            ->setParameter('parentTask', $parentTask)
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Search tasks by keyword
     */
    public function searchByKeyword(string $keyword): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.title LIKE :keyword OR t.description LIKE :keyword')
            ->setParameter('keyword', '%' . $keyword . '%')
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get task statistics for a project
     */
    public function getTaskStatisticsByProject(Project $project): array
    {
        $totalTasks = $this->count(['project' => $project]);
        $pendingTasks = $this->count(['project' => $project, 'status' => 'pending']);
        $inProgressTasks = $this->count(['project' => $project, 'status' => 'in_progress']);
        $completedTasks = $this->count(['project' => $project, 'status' => 'completed']);
        $onHoldTasks = $this->count(['project' => $project, 'status' => 'on_hold']);
        $cancelledTasks = $this->count(['project' => $project, 'status' => 'cancelled']);

        $today = new \DateTime();
        $qb = $this->createQueryBuilder('t');
        $overdueTasks = $qb
            ->select('COUNT(t.id)')
            ->andWhere('t.project = :project')
            ->andWhere('t.dueDate < :today')
            ->andWhere('t.status NOT IN (:statuses)')
            ->setParameter('project', $project)
            ->setParameter('today', $today)
            ->setParameter('statuses', ['completed', 'cancelled'])
            ->getQuery()
            ->getSingleScalarResult();

        // Get tasks by priority
        $qb = $this->createQueryBuilder('t');
        $priorityStats = $qb
            ->select('t.priority, COUNT(t.id) as count')
            ->andWhere('t.project = :project')
            ->setParameter('project', $project)
            ->groupBy('t.priority')
            ->getQuery()
            ->getResult();

        // Get tasks by assignee
        $qb = $this->createQueryBuilder('t');
        $assigneeStats = $qb
            ->select('IDENTITY(t.assignedTo) as user_id, COUNT(t.id) as count')
            ->andWhere('t.project = :project')
            ->andWhere('t.assignedTo IS NOT NULL')
            ->setParameter('project', $project)
            ->groupBy('t.assignedTo')
            ->getQuery()
            ->getResult();

        return [
            'total' => $totalTasks,
            'pending' => $pendingTasks,
            'in_progress' => $inProgressTasks,
            'completed' => $completedTasks,
            'on_hold' => $onHoldTasks,
            'cancelled' => $cancelledTasks,
            'overdue' => $overdueTasks,
            'by_priority' => $priorityStats,
            'by_assignee' => $assigneeStats,
        ];
    }

    /**
     * Count overdue tasks
     */
    public function countOverdueTasks(): int
    {
        $today = new \DateTime();

        $qb = $this->createQueryBuilder('t');
        return $qb
            ->select('COUNT(t.id)')
            ->andWhere('t.dueDate < :today')
            ->andWhere('t.status NOT IN (:statuses)')
            ->setParameter('today', $today)
            ->setParameter('statuses', ['completed', 'cancelled'])
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Find tasks by user and date range
     */
    public function findTasksByUserAndDateRange(User $user, \DateTime $startDate, \DateTime $endDate): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.assignedTo = :user')
            ->andWhere('(t.startDate <= :endDate AND (t.dueDate >= :startDate OR t.dueDate IS NULL)) OR (t.startDate IS NULL AND t.dueDate >= :startDate AND t.dueDate <= :endDate)')
            ->andWhere('t.status NOT IN (:statuses)')
            ->setParameter('user', $user)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('statuses', ['completed', 'cancelled'])
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tasks by category
     */
    public function findByCategory(string $category): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.category = :category')
            ->setParameter('category', $category)
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find tasks by tags
     */
    public function findByTag(string $tag): array
    {
        return $this->createQueryBuilder('t')
            ->andWhere('t.tags LIKE :tag')
            ->setParameter('tag', '%' . $tag . '%')
            ->orderBy('t.dueDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get all task categories
     */
    public function getAllCategories(): array
    {
        $qb = $this->createQueryBuilder('t');
        $result = $qb
            ->select('DISTINCT t.category')
            ->andWhere('t.category IS NOT NULL')
            ->orderBy('t.category', 'ASC')
            ->getQuery()
            ->getResult();

        return array_map(function($item) {
            return $item['category'];
        }, $result);
    }

    /**
     * Get all task tags
     */
    public function getAllTags(): array
    {
        $qb = $this->createQueryBuilder('t');
        $result = $qb
            ->select('t.tags')
            ->andWhere('t.tags IS NOT NULL')
            ->getQuery()
            ->getResult();

        $allTags = [];
        foreach ($result as $item) {
            $tags = explode(',', $item['tags']);
            foreach ($tags as $tag) {
                $tag = trim($tag);
                if (!empty($tag) && !in_array($tag, $allTags)) {
                    $allTags[] = $tag;
                }
            }
        }

        sort($allTags);
        return $allTags;
    }

    /**
     * Get task completion data over time for a project
     */
    public function getTaskCompletionOverTime(Project $project): array
    {
        // Get the start date of the project
        $startDate = $project->getStartDate() ?: new \DateTime('-6 months');
        $endDate = $project->getEndDate() ?: new \DateTime('+6 months');

        // Get completed tasks by month
        $conn = $this->getEntityManager()->getConnection();
        $sql = "
            SELECT
                DATE_FORMAT(t.completed_at, '%Y-%m') as month,
                COUNT(t.id) as count
            FROM project_task t
            WHERE t.project_id = :projectId
            AND t.status = 'completed'
            AND t.completed_at IS NOT NULL
            GROUP BY month
            ORDER BY month ASC
        ";

        $stmt = $conn->prepare($sql);
        $stmt->bindValue('projectId', $project->getId());
        $completedByMonth = $stmt->executeQuery()->fetchAllAssociative();

        // Format data for chart
        $months = [];
        $completedCounts = [];

        // Generate all months between start and end date
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $months[] = $currentDate->format('Y-m');
            $completedCounts[] = 0;
            $currentDate->modify('+1 month');
        }

        // Fill in actual data
        foreach ($completedByMonth as $item) {
            $monthIndex = array_search($item['month'], $months);
            if ($monthIndex !== false) {
                $completedCounts[$monthIndex] = (int)$item['count'];
            }
        }

        // Format month labels for display
        $monthLabels = array_map(function($month) {
            $date = \DateTime::createFromFormat('Y-m', $month);
            return $date->format('M Y');
        }, $months);

        return [
            'labels' => $monthLabels,
            'datasets' => [
                [
                    'label' => 'Tâches terminées',
                    'data' => $completedCounts,
                    'backgroundColor' => '#28a745',
                    'borderColor' => '#28a745',
                ]
            ]
        ];
    }
}
