<?php

namespace App\Repository;

use App\Entity\Partner;
use App\Entity\Project;
use App\Entity\PurchaseOrder;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<PurchaseOrder>
 *
 * @method PurchaseOrder|null find($id, $lockMode = null, $lockVersion = null)
 * @method PurchaseOrder|null findOneBy(array $criteria, array $orderBy = null)
 * @method PurchaseOrder[]    findAll()
 * @method PurchaseOrder[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PurchaseOrderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PurchaseOrder::class);
    }

    /**
     * Find purchase orders by project
     */
    public function findByProject(Project $project): array
    {
        return $this->createQueryBuilder('po')
            ->andWhere('po.project = :project')
            ->setParameter('project', $project)
            ->orderBy('po.orderDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find purchase orders by supplier
     */
    public function findBySupplier(Partner $supplier): array
    {
        return $this->createQueryBuilder('po')
            ->andWhere('po.supplier = :supplier')
            ->setParameter('supplier', $supplier)
            ->orderBy('po.orderDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find purchase orders by status
     */
    public function findByStatus(string $status): array
    {
        return $this->createQueryBuilder('po')
            ->andWhere('po.status = :status')
            ->setParameter('status', $status)
            ->orderBy('po.orderDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find purchase orders ready for goods receipt
     */
    public function findReadyForGoodsReceipt(): array
    {
        return $this->createQueryBuilder('po')
            ->andWhere('po.status IN (:statuses)')
            ->setParameter('statuses', [PurchaseOrder::STATUS_SENT, PurchaseOrder::STATUS_PARTIALLY_RECEIVED])
            ->orderBy('po.orderDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find purchase orders created by a user
     */
    public function findByCreatedBy(User $user): array
    {
        return $this->createQueryBuilder('po')
            ->andWhere('po.createdBy = :user')
            ->setParameter('user', $user)
            ->orderBy('po.orderDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Generate a unique order number
     */
    public function generateOrderNumber(): string
    {
        $year = date('Y');
        $prefix = 'PO-' . $year . '-';

        $lastOrder = $this->createQueryBuilder('po')
            ->andWhere('po.orderNumber LIKE :prefix')
            ->setParameter('prefix', $prefix . '%')
            ->orderBy('po.id', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        if (!$lastOrder) {
            return $prefix . '0001';
        }

        $lastNumber = (int) substr($lastOrder->getOrderNumber(), strlen($prefix));
        $newNumber = $lastNumber + 1;

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Find purchase orders by year grouped by month
     */
    public function findPurchaseOrdersByYear(string $year): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT MONTH(po.order_date) as month, SUM(po.total_amount) as total
            FROM purchase_order po
            WHERE YEAR(po.order_date) = :year
            GROUP BY MONTH(po.order_date)
            ORDER BY month ASC
        ';

        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery(['year' => $year]);

        return $result->fetchAllAssociative();
    }

    /**
     * Find purchases by category
     */
    public function findPurchasesByCategory(): array
    {
        // Puisque nous n'avons pas de relation directe avec les catégories de produits,
        // nous allons simplement regrouper par fournisseur pour l'instant
        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT p.name as category, SUM(po.total_amount) as total
            FROM purchase_order po
            JOIN partner p ON po.supplier_id = p.id
            GROUP BY p.name
            ORDER BY total DESC
            LIMIT 10
        ';

        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery();

        return $result->fetchAllAssociative();
    }

    /**
     * Find purchase orders by period
     */
    public function findPurchaseOrdersByPeriod(\DateTime $startDate, \DateTime $endDate): array
    {
        $conn = $this->getEntityManager()->getConnection();
        $sql = '
            SELECT
                YEAR(po.order_date) as year,
                MONTH(po.order_date) as month,
                SUM(po.total_amount) as total
            FROM purchase_order po
            WHERE po.order_date BETWEEN :startDate AND :endDate
            GROUP BY year, month
            ORDER BY year ASC, month ASC
        ';

        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery([
            'startDate' => $startDate->format('Y-m-d'),
            'endDate' => $endDate->format('Y-m-d')
        ]);

        return $result->fetchAllAssociative();
    }
}
