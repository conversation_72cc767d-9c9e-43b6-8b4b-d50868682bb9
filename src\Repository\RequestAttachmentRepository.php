<?php

namespace App\Repository;

use App\Entity\EmployeeRequest;
use App\Entity\RequestAttachment;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<RequestAttachment>
 *
 * @method RequestAttachment|null find($id, $lockMode = null, $lockVersion = null)
 * @method RequestAttachment|null findOneBy(array $criteria, array $orderBy = null)
 * @method RequestAttachment[]    findAll()
 * @method RequestAttachment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RequestAttachmentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RequestAttachment::class);
    }

    /**
     * Find attachments by request
     */
    public function findByRequest(EmployeeRequest $request): array
    {
        return $this->createQueryBuilder('ra')
            ->andWhere('ra.request = :request')
            ->setParameter('request', $request)
            ->orderBy('ra.uploadedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find attachments by uploader
     */
    public function findByUploader(User $uploader): array
    {
        return $this->createQueryBuilder('ra')
            ->andWhere('ra.uploadedBy = :uploader')
            ->setParameter('uploader', $uploader)
            ->orderBy('ra.uploadedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find attachments by file type
     */
    public function findByFileType(string $mimeType): array
    {
        return $this->createQueryBuilder('ra')
            ->andWhere('ra.mimeType LIKE :mimeType')
            ->setParameter('mimeType', $mimeType . '%')
            ->orderBy('ra.uploadedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find large attachments (above certain size)
     */
    public function findLargeAttachments(int $minSizeBytes = 5242880): array // 5MB default
    {
        return $this->createQueryBuilder('ra')
            ->andWhere('ra.fileSize >= :minSize')
            ->setParameter('minSize', $minSizeBytes)
            ->orderBy('ra.fileSize', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get total storage used by attachments
     */
    public function getTotalStorageUsed(): int
    {
        $result = $this->createQueryBuilder('ra')
            ->select('SUM(ra.fileSize)')
            ->getQuery()
            ->getSingleScalarResult();

        return (int)($result ?? 0);
    }

    /**
     * Get storage used by request
     */
    public function getStorageUsedByRequest(EmployeeRequest $request): int
    {
        $result = $this->createQueryBuilder('ra')
            ->select('SUM(ra.fileSize)')
            ->andWhere('ra.request = :request')
            ->setParameter('request', $request)
            ->getQuery()
            ->getSingleScalarResult();

        return (int)($result ?? 0);
    }

    /**
     * Find recent attachments
     */
    public function findRecentAttachments(int $limit = 10): array
    {
        return $this->createQueryBuilder('ra')
            ->orderBy('ra.uploadedAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function save(RequestAttachment $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(RequestAttachment $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
