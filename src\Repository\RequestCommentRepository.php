<?php

namespace App\Repository;

use App\Entity\EmployeeRequest;
use App\Entity\RequestComment;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<RequestComment>
 *
 * @method RequestComment|null find($id, $lockMode = null, $lockVersion = null)
 * @method RequestComment|null findOneBy(array $criteria, array $orderBy = null)
 * @method RequestComment[]    findAll()
 * @method RequestComment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RequestCommentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, RequestComment::class);
    }

    /**
     * Find comments by request
     */
    public function findByRequest(EmployeeRequest $request, bool $includeInternal = true): array
    {
        $qb = $this->createQueryBuilder('rc')
            ->andWhere('rc.request = :request')
            ->setParameter('request', $request)
            ->orderBy('rc.createdAt', 'ASC');

        if (!$includeInternal) {
            $qb->andWhere('rc.isInternal = :isInternal')
               ->setParameter('isInternal', false);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Find comments by author
     */
    public function findByAuthor(User $author): array
    {
        return $this->createQueryBuilder('rc')
            ->andWhere('rc.author = :author')
            ->setParameter('author', $author)
            ->orderBy('rc.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find comments by type
     */
    public function findByType(string $type): array
    {
        return $this->createQueryBuilder('rc')
            ->andWhere('rc.type = :type')
            ->setParameter('type', $type)
            ->orderBy('rc.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find recent comments
     */
    public function findRecentComments(int $limit = 10): array
    {
        return $this->createQueryBuilder('rc')
            ->orderBy('rc.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find internal comments
     */
    public function findInternalComments(): array
    {
        return $this->createQueryBuilder('rc')
            ->andWhere('rc.isInternal = :isInternal')
            ->setParameter('isInternal', true)
            ->orderBy('rc.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Search comments by content
     */
    public function searchByContent(string $keyword): array
    {
        return $this->createQueryBuilder('rc')
            ->andWhere('rc.comment LIKE :keyword')
            ->setParameter('keyword', '%' . $keyword . '%')
            ->orderBy('rc.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function save(RequestComment $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(RequestComment $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
