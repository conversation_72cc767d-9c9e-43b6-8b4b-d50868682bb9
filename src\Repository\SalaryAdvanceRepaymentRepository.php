<?php

namespace App\Repository;

use App\Entity\SalaryAdvance;
use App\Entity\SalaryAdvanceRepayment;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SalaryAdvanceRepayment>
 *
 * @method SalaryAdvanceRepayment|null find($id, $lockMode = null, $lockVersion = null)
 * @method SalaryAdvanceRepayment|null findOneBy(array $criteria, array $orderBy = null)
 * @method SalaryAdvanceRepayment[]    findAll()
 * @method SalaryAdvanceRepayment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SalaryAdvanceRepaymentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SalaryAdvanceRepayment::class);
    }

    /**
     * Find repayments by salary advance
     */
    public function findBySalaryAdvance(SalaryAdvance $salaryAdvance): array
    {
        return $this->createQueryBuilder('sar')
            ->andWhere('sar.salaryAdvance = :salaryAdvance')
            ->setParameter('salaryAdvance', $salaryAdvance)
            ->orderBy('sar.deductionDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find scheduled repayments for a specific date
     */
    public function findScheduledForDate(\DateTimeInterface $date): array
    {
        return $this->createQueryBuilder('sar')
            ->andWhere('sar.deductionDate = :date')
            ->andWhere('sar.status = :status')
            ->setParameter('date', $date)
            ->setParameter('status', 'scheduled')
            ->orderBy('sar.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find repayments due within a date range
     */
    public function findDueInRange(\DateTimeInterface $startDate, \DateTimeInterface $endDate): array
    {
        return $this->createQueryBuilder('sar')
            ->andWhere('sar.deductionDate >= :startDate')
            ->andWhere('sar.deductionDate <= :endDate')
            ->andWhere('sar.status = :status')
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->setParameter('status', 'scheduled')
            ->orderBy('sar.deductionDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find processed repayments
     */
    public function findProcessed(): array
    {
        return $this->createQueryBuilder('sar')
            ->andWhere('sar.status = :status')
            ->setParameter('status', 'processed')
            ->orderBy('sar.processedAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find failed repayments
     */
    public function findFailed(): array
    {
        return $this->createQueryBuilder('sar')
            ->andWhere('sar.status = :status')
            ->setParameter('status', 'failed')
            ->orderBy('sar.deductionDate', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find overdue repayments
     */
    public function findOverdue(): array
    {
        $now = new \DateTime();
        
        return $this->createQueryBuilder('sar')
            ->andWhere('sar.deductionDate < :now')
            ->andWhere('sar.status = :status')
            ->setParameter('now', $now)
            ->setParameter('status', 'scheduled')
            ->orderBy('sar.deductionDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get total repaid amount for a salary advance
     */
    public function getTotalRepaidAmount(SalaryAdvance $salaryAdvance): float
    {
        $result = $this->createQueryBuilder('sar')
            ->select('SUM(sar.amount)')
            ->andWhere('sar.salaryAdvance = :salaryAdvance')
            ->andWhere('sar.status = :status')
            ->setParameter('salaryAdvance', $salaryAdvance)
            ->setParameter('status', 'processed')
            ->getQuery()
            ->getSingleScalarResult();

        return (float)($result ?? 0);
    }

    /**
     * Get repayment statistics
     */
    public function getStatistics(): array
    {
        $qb = $this->createQueryBuilder('sar')
            ->select('sar.status, COUNT(sar.id) as count, SUM(sar.amount) as total_amount')
            ->groupBy('sar.status');

        $results = $qb->getQuery()->getResult();
        
        $stats = [
            'total_repayments' => 0,
            'total_amount' => 0,
            'by_status' => []
        ];

        foreach ($results as $result) {
            $status = $result['status'];
            $count = (int)$result['count'];
            $totalAmount = (float)($result['total_amount'] ?? 0);

            $stats['total_repayments'] += $count;
            $stats['total_amount'] += $totalAmount;

            $stats['by_status'][$status] = [
                'count' => $count,
                'total_amount' => $totalAmount
            ];
        }

        return $stats;
    }

    public function save(SalaryAdvanceRepayment $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(SalaryAdvanceRepayment $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
