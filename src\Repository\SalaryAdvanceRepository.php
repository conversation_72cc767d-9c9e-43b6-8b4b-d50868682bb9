<?php

namespace App\Repository;

use App\Entity\Employee;
use App\Entity\SalaryAdvance;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SalaryAdvance>
 *
 * @method SalaryAdvance|null find($id, $lockMode = null, $lockVersion = null)
 * @method SalaryAdvance|null findOneBy(array $criteria, array $orderBy = null)
 * @method SalaryAdvance[]    findAll()
 * @method SalaryAdvance[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SalaryAdvanceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SalaryAdvance::class);
    }

    /**
     * Find active advances for an employee
     */
    public function findActiveByEmployee(Employee $employee): array
    {
        return $this->createQueryBuilder('sa')
            ->andWhere('sa.employee = :employee')
            ->andWhere('sa.status IN (:statuses)')
            ->setParameter('employee', $employee)
            ->setParameter('statuses', ['approved', 'paid', 'in_progress'])
            ->orderBy('sa.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find advances pending approval
     */
    public function findPendingApproval(): array
    {
        return $this->createQueryBuilder('sa')
            ->andWhere('sa.status = :status')
            ->setParameter('status', 'pending')
            ->orderBy('sa.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find advances ready for payment
     */
    public function findReadyForPayment(): array
    {
        return $this->createQueryBuilder('sa')
            ->andWhere('sa.status = :status')
            ->setParameter('status', 'approved')
            ->orderBy('sa.createdAt', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find advances in repayment
     */
    public function findInRepayment(): array
    {
        return $this->createQueryBuilder('sa')
            ->andWhere('sa.status = :status')
            ->setParameter('status', 'in_progress')
            ->orderBy('sa.firstDeductionDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Calculate total outstanding amount for an employee
     */
    public function getTotalOutstandingByEmployee(Employee $employee): float
    {
        $result = $this->createQueryBuilder('sa')
            ->select('SUM(sa.remainingAmount)')
            ->andWhere('sa.employee = :employee')
            ->andWhere('sa.status IN (:statuses)')
            ->setParameter('employee', $employee)
            ->setParameter('statuses', ['paid', 'in_progress'])
            ->getQuery()
            ->getSingleScalarResult();

        return (float)($result ?? 0);
    }

    /**
     * Get advances statistics
     */
    public function getStatistics(): array
    {
        $qb = $this->createQueryBuilder('sa')
            ->select('sa.status, COUNT(sa.id) as count, SUM(sa.amount) as total_amount, SUM(sa.approvedAmount) as total_approved')
            ->groupBy('sa.status');

        $results = $qb->getQuery()->getResult();
        
        $stats = [
            'total_requests' => 0,
            'total_amount_requested' => 0,
            'total_amount_approved' => 0,
            'by_status' => []
        ];

        foreach ($results as $result) {
            $status = $result['status'];
            $count = (int)$result['count'];
            $totalAmount = (float)($result['total_amount'] ?? 0);
            $totalApproved = (float)($result['total_approved'] ?? 0);

            $stats['total_requests'] += $count;
            $stats['total_amount_requested'] += $totalAmount;
            $stats['total_amount_approved'] += $totalApproved;

            $stats['by_status'][$status] = [
                'count' => $count,
                'total_amount' => $totalAmount,
                'total_approved' => $totalApproved
            ];
        }

        return $stats;
    }

    /**
     * Find advances with upcoming deductions
     */
    public function findUpcomingDeductions(int $days = 7): array
    {
        $endDate = new \DateTime("+{$days} days");
        
        return $this->createQueryBuilder('sa')
            ->andWhere('sa.status = :status')
            ->andWhere('sa.firstDeductionDate <= :endDate')
            ->setParameter('status', 'in_progress')
            ->setParameter('endDate', $endDate)
            ->orderBy('sa.firstDeductionDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find advances by amount range
     */
    public function findByAmountRange(float $minAmount, float $maxAmount): array
    {
        return $this->createQueryBuilder('sa')
            ->andWhere('sa.amount >= :minAmount')
            ->andWhere('sa.amount <= :maxAmount')
            ->setParameter('minAmount', $minAmount)
            ->setParameter('maxAmount', $maxAmount)
            ->orderBy('sa.amount', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function save(SalaryAdvance $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(SalaryAdvance $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
