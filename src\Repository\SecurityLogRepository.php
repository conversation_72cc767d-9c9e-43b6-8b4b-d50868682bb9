<?php

namespace App\Repository;

use App\Entity\SecurityLog;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SecurityLog>
 */
class SecurityLogRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, SecurityLog::class);
    }

    public function save(SecurityLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(SecurityLog $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Trouve les logs de sécurité par type d'événement
     */
    public function findByEventType(string $eventType, int $limit = 100): array
    {
        return $this->createQueryBuilder('sl')
            ->andWhere('sl.eventType = :eventType')
            ->setParameter('eventType', $eventType)
            ->orderBy('sl.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les logs de sécurité par utilisateur
     */
    public function findByUser(User $user, int $limit = 100): array
    {
        return $this->createQueryBuilder('sl')
            ->andWhere('sl.user = :user')
            ->setParameter('user', $user)
            ->orderBy('sl.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les logs de sécurité par adresse IP
     */
    public function findByIpAddress(string $ipAddress, int $limit = 100): array
    {
        return $this->createQueryBuilder('sl')
            ->andWhere('sl.ipAddress = :ipAddress')
            ->setParameter('ipAddress', $ipAddress)
            ->orderBy('sl.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les logs de sécurité par niveau de sévérité
     */
    public function findBySeverity(string $severity, int $limit = 100): array
    {
        return $this->createQueryBuilder('sl')
            ->andWhere('sl.severity = :severity')
            ->setParameter('severity', $severity)
            ->orderBy('sl.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les tentatives de connexion échouées récentes pour une IP
     */
    public function findRecentFailedLogins(string $ipAddress, \DateTime $since): array
    {
        return $this->createQueryBuilder('sl')
            ->andWhere('sl.eventType = :eventType')
            ->andWhere('sl.ipAddress = :ipAddress')
            ->andWhere('sl.createdAt >= :since')
            ->setParameter('eventType', SecurityLog::EVENT_LOGIN_FAILED)
            ->setParameter('ipAddress', $ipAddress)
            ->setParameter('since', $since)
            ->orderBy('sl.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Compte les tentatives de connexion échouées pour une IP dans une période
     */
    public function countFailedLoginAttempts(string $ipAddress, \DateTime $since): int
    {
        return $this->createQueryBuilder('sl')
            ->select('COUNT(sl.id)')
            ->andWhere('sl.eventType = :eventType')
            ->andWhere('sl.ipAddress = :ipAddress')
            ->andWhere('sl.createdAt >= :since')
            ->setParameter('eventType', SecurityLog::EVENT_LOGIN_FAILED)
            ->setParameter('ipAddress', $ipAddress)
            ->setParameter('since', $since)
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Trouve les activités suspectes récentes
     */
    public function findSuspiciousActivities(\DateTime $since): array
    {
        return $this->createQueryBuilder('sl')
            ->andWhere('sl.eventType = :eventType OR sl.severity = :severity')
            ->andWhere('sl.createdAt >= :since')
            ->setParameter('eventType', SecurityLog::EVENT_SUSPICIOUS_ACTIVITY)
            ->setParameter('severity', SecurityLog::SEVERITY_HIGH)
            ->orderBy('sl.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Statistiques des événements de sécurité par type
     */
    public function getEventTypeStats(\DateTime $since): array
    {
        return $this->createQueryBuilder('sl')
            ->select('sl.eventType, COUNT(sl.id) as count')
            ->andWhere('sl.createdAt >= :since')
            ->setParameter('since', $since)
            ->groupBy('sl.eventType')
            ->orderBy('count', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Statistiques des événements de sécurité par sévérité
     */
    public function getSeverityStats(\DateTime $since): array
    {
        return $this->createQueryBuilder('sl')
            ->select('sl.severity, COUNT(sl.id) as count')
            ->andWhere('sl.createdAt >= :since')
            ->setParameter('since', $since)
            ->groupBy('sl.severity')
            ->orderBy('count', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Top des adresses IP avec le plus d'activité
     */
    public function getTopIpAddresses(\DateTime $since, int $limit = 10): array
    {
        return $this->createQueryBuilder('sl')
            ->select('sl.ipAddress, COUNT(sl.id) as count')
            ->andWhere('sl.createdAt >= :since')
            ->andWhere('sl.ipAddress IS NOT NULL')
            ->setParameter('since', $since)
            ->groupBy('sl.ipAddress')
            ->orderBy('count', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Nettoie les anciens logs de sécurité
     */
    public function cleanOldLogs(\DateTime $before): int
    {
        return $this->createQueryBuilder('sl')
            ->delete()
            ->andWhere('sl.createdAt < :before')
            ->setParameter('before', $before)
            ->getQuery()
            ->execute();
    }
}
