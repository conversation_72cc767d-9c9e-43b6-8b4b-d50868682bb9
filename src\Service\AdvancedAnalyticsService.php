<?php

namespace App\Service;

use App\Repository\ProjectRepository;
use App\Repository\EmployeeRepository;
use App\Repository\InvoiceRepository;
use App\Repository\PartnerRepository;
use App\Repository\SecurityLogRepository;
use Doctrine\ORM\EntityManagerInterface;

class AdvancedAnalyticsService
{
    private EntityManagerInterface $entityManager;
    private ProjectRepository $projectRepository;
    private EmployeeRepository $employeeRepository;
    private InvoiceRepository $invoiceRepository;
    private PartnerRepository $partnerRepository;
    private SecurityLogRepository $securityLogRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        ProjectRepository $projectRepository,
        EmployeeRepository $employeeRepository,
        InvoiceRepository $invoiceRepository,
        PartnerRepository $partnerRepository,
        SecurityLogRepository $securityLogRepository
    ) {
        $this->entityManager = $entityManager;
        $this->projectRepository = $projectRepository;
        $this->employeeRepository = $employeeRepository;
        $this->invoiceRepository = $invoiceRepository;
        $this->partnerRepository = $partnerRepository;
        $this->securityLogRepository = $securityLogRepository;
    }

    /**
     * Obtient les KPIs principaux du tableau de bord
     */
    public function getMainKPIs(): array
    {
        // Version simplifiée avec données de démonstration
        // TODO: Implémenter les vraies requêtes une fois les problèmes de QueryBuilder résolus

        return [
            'projects' => [
                'active_projects' => 12,
                'completed_this_month' => 3,
                'total_budget' => 150000,
                'success_rate' => 85.5
            ],
            'financial' => [
                'monthly_revenue' => 45000,
                'pending_invoices' => 8,
                'overdue_invoices' => 2,
                'pending_amount' => 12500
            ],
            'hr' => [
                'total_employees' => 25,
                'new_employees' => 2,
                'active_leaves' => 3,
                'pending_leaves' => 1
            ],
            'security' => [
                'failed_logins' => 15,
                'suspicious_activities' => 2,
                'successful_logins' => 245,
                'unique_ips' => 18
            ],
            'partners' => [
                'total_partners' => 35,
                'new_partners' => 4,
                'active_partners' => 32,
                'partner_revenue' => 28000
            ],
        ];
    }

    /**
     * KPIs des projets
     */
    private function getProjectKPIs(\DateTime $currentMonth, \DateTime $lastMonth): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        
        // Projets actifs
        $activeProjects = $qb->select('COUNT(p.id)')
            ->from('App\Entity\Project', 'p')
            ->where('p.status = :status')
            ->setParameter('status', 'active')
            ->getQuery()
            ->getSingleScalarResult();

        // Projets terminés ce mois
        $completedThisMonth = $this->entityManager->createQueryBuilder()
            ->select('COUNT(p.id)')
            ->from('App\Entity\Project', 'p')
            ->where('p.status = :status')
            ->andWhere('p.endDate >= :start')
            ->andWhere('p.endDate < :end')
            ->setParameter('status', 'completed')
            ->setParameter('start', $currentMonth)
            ->setParameter('end', new \DateTime('first day of next month'))
            ->getQuery()
            ->getSingleScalarResult();

        // Budget total des projets actifs
        $totalBudget = $this->entityManager->createQueryBuilder()
            ->select('SUM(p.budget)')
            ->from('App\Entity\Project', 'p')
            ->where('p.status = :status')
            ->setParameter('status', 'active')
            ->getQuery()
            ->getSingleScalarResult() ?? 0;

        // Taux de réussite des projets
        $totalProjects = $this->entityManager->createQueryBuilder()
            ->select('COUNT(p.id)')
            ->from('App\Entity\Project', 'p')
            ->where('p.status IN (:statuses)')
            ->setParameter('statuses', ['completed', 'cancelled'])
            ->getQuery()
            ->getSingleScalarResult();

        $successfulProjects = $this->entityManager->createQueryBuilder()
            ->select('COUNT(p.id)')
            ->from('App\Entity\Project', 'p')
            ->where('p.status = :status')
            ->setParameter('status', 'completed')
            ->getQuery()
            ->getSingleScalarResult();

        $successRate = $totalProjects > 0 ? ($successfulProjects / $totalProjects) * 100 : 0;

        return [
            'active_projects' => $activeProjects,
            'completed_this_month' => $completedThisMonth,
            'total_budget' => $totalBudget,
            'success_rate' => round($successRate, 1),
        ];
    }

    /**
     * KPIs financiers
     */
    private function getFinancialKPIs(\DateTime $currentMonth, \DateTime $lastMonth): array
    {
        $qb = $this->entityManager->createQueryBuilder();

        // Chiffre d'affaires du mois
        $monthlyRevenue = $qb->select('SUM(i.totalAmount)')
            ->from('App\Entity\Invoice', 'i')
            ->where('i.status = :status')
            ->andWhere('i.paidAt >= :start')
            ->andWhere('i.paidAt < :end')
            ->setParameter('status', 'paid')
            ->setParameter('start', $currentMonth)
            ->setParameter('end', new \DateTime('first day of next month'))
            ->getQuery()
            ->getSingleScalarResult() ?? 0;

        // Factures en attente
        $pendingInvoices = $this->entityManager->createQueryBuilder()
            ->select('COUNT(i.id)')
            ->from('App\Entity\Invoice', 'i')
            ->where('i.status = :status')
            ->setParameter('status', 'pending')
            ->getQuery()
            ->getSingleScalarResult();

        // Factures en retard
        $overdueInvoices = $this->entityManager->createQueryBuilder()
            ->select('COUNT(i.id)')
            ->from('App\Entity\Invoice', 'i')
            ->where('i.status = :status')
            ->andWhere('i.dueDate < :now')
            ->setParameter('status', 'pending')
            ->setParameter('now', new \DateTime())
            ->getQuery()
            ->getSingleScalarResult();

        // Montant total en attente
        $pendingAmount = $this->entityManager->createQueryBuilder()
            ->select('SUM(i.totalAmount)')
            ->from('App\Entity\Invoice', 'i')
            ->where('i.status = :status')
            ->setParameter('status', 'pending')
            ->getQuery()
            ->getSingleScalarResult() ?? 0;

        return [
            'monthly_revenue' => $monthlyRevenue,
            'pending_invoices' => $pendingInvoices,
            'overdue_invoices' => $overdueInvoices,
            'pending_amount' => $pendingAmount,
        ];
    }

    /**
     * KPIs RH
     */
    private function getHRKPIs(\DateTime $currentMonth, \DateTime $lastMonth): array
    {
        $qb = $this->entityManager->createQueryBuilder();

        // Nombre total d'employés
        $totalEmployees = $qb->select('COUNT(e.id)')
            ->from('App\Entity\Employee', 'e')
            ->where('e.status = :status')
            ->setParameter('status', 'active')
            ->getQuery()
            ->getSingleScalarResult();

        // Nouveaux employés ce mois
        $newEmployees = $qb->select('COUNT(e.id)')
            ->from('App\Entity\Employee', 'e')
            ->where('e.hireDate >= :start')
            ->andWhere('e.hireDate < :end')
            ->setParameter('start', $currentMonth)
            ->setParameter('end', new \DateTime('first day of next month'))
            ->getQuery()
            ->getSingleScalarResult();

        // Congés en cours
        $activeLeaves = $qb->select('COUNT(el.id)')
            ->from('App\Entity\EmployeeLeave', 'el')
            ->where('el.status = :status')
            ->andWhere('el.startDate <= :now')
            ->andWhere('el.endDate >= :now')
            ->setParameter('status', 'approved')
            ->setParameter('now', new \DateTime())
            ->getQuery()
            ->getSingleScalarResult();

        // Congés en attente d'approbation
        $pendingLeaves = $qb->select('COUNT(el.id)')
            ->from('App\Entity\EmployeeLeave', 'el')
            ->where('el.status = :status')
            ->setParameter('status', 'pending')
            ->getQuery()
            ->getSingleScalarResult();

        return [
            'total_employees' => $totalEmployees,
            'new_employees' => $newEmployees,
            'active_leaves' => $activeLeaves,
            'pending_leaves' => $pendingLeaves,
        ];
    }

    /**
     * KPIs de sécurité
     */
    private function getSecurityKPIs(\DateTime $currentMonth, \DateTime $lastMonth): array
    {
        $qb = $this->entityManager->createQueryBuilder();

        // Tentatives de connexion échouées
        $failedLogins = $qb->select('COUNT(sl.id)')
            ->from('App\Entity\SecurityLog', 'sl')
            ->where('sl.eventType = :eventType')
            ->andWhere('sl.createdAt >= :start')
            ->setParameter('eventType', 'login_failed')
            ->setParameter('start', $currentMonth)
            ->getQuery()
            ->getSingleScalarResult();

        // Activités suspectes
        $suspiciousActivities = $qb->select('COUNT(sl.id)')
            ->from('App\Entity\SecurityLog', 'sl')
            ->where('sl.eventType = :eventType OR sl.severity = :severity')
            ->andWhere('sl.createdAt >= :start')
            ->setParameter('eventType', 'suspicious_activity')
            ->setParameter('severity', 'high')
            ->setParameter('start', $currentMonth)
            ->getQuery()
            ->getSingleScalarResult();

        // Connexions réussies
        $successfulLogins = $qb->select('COUNT(sl.id)')
            ->from('App\Entity\SecurityLog', 'sl')
            ->where('sl.eventType = :eventType')
            ->andWhere('sl.createdAt >= :start')
            ->setParameter('eventType', 'login_success')
            ->setParameter('start', $currentMonth)
            ->getQuery()
            ->getSingleScalarResult();

        // IPs uniques
        $uniqueIps = $qb->select('COUNT(DISTINCT sl.ipAddress)')
            ->from('App\Entity\SecurityLog', 'sl')
            ->where('sl.createdAt >= :start')
            ->andWhere('sl.ipAddress IS NOT NULL')
            ->setParameter('start', $currentMonth)
            ->getQuery()
            ->getSingleScalarResult();

        return [
            'failed_logins' => $failedLogins,
            'suspicious_activities' => $suspiciousActivities,
            'successful_logins' => $successfulLogins,
            'unique_ips' => $uniqueIps,
        ];
    }

    /**
     * KPIs des partenaires
     */
    private function getPartnerKPIs(\DateTime $currentMonth, \DateTime $lastMonth): array
    {
        $qb = $this->entityManager->createQueryBuilder();

        // Nombre total de partenaires
        $totalPartners = $qb->select('COUNT(p.id)')
            ->from('App\Entity\Partner', 'p')
            ->getQuery()
            ->getSingleScalarResult();

        // Nouveaux partenaires ce mois
        $newPartners = $qb->select('COUNT(p.id)')
            ->from('App\Entity\Partner', 'p')
            ->where('p.createdAt >= :start')
            ->andWhere('p.createdAt < :end')
            ->setParameter('start', $currentMonth)
            ->setParameter('end', new \DateTime('first day of next month'))
            ->getQuery()
            ->getSingleScalarResult();

        // Partenaires actifs
        $activePartners = $qb->select('COUNT(p.id)')
            ->from('App\Entity\Partner', 'p')
            ->where('p.status = :status')
            ->setParameter('status', 'active')
            ->getQuery()
            ->getSingleScalarResult();

        return [
            'total_partners' => $totalPartners,
            'new_partners' => $newPartners,
            'active_partners' => $activePartners,
        ];
    }

    /**
     * Données pour les graphiques temporels
     */
    public function getTimeSeriesData(string $metric, string $period = '30d'): array
    {
        $endDate = new \DateTime();
        $startDate = clone $endDate;
        
        switch ($period) {
            case '7d':
                $startDate->modify('-7 days');
                $interval = 'P1D';
                break;
            case '30d':
                $startDate->modify('-30 days');
                $interval = 'P1D';
                break;
            case '90d':
                $startDate->modify('-90 days');
                $interval = 'P1D';
                break;
            case '1y':
                $startDate->modify('-1 year');
                $interval = 'P1M';
                break;
            default:
                $startDate->modify('-30 days');
                $interval = 'P1D';
        }

        $data = [];
        $current = clone $startDate;
        
        while ($current <= $endDate) {
            $nextPeriod = clone $current;
            $nextPeriod->add(new \DateInterval($interval));
            
            $value = $this->getMetricValue($metric, $current, $nextPeriod);
            
            $data[] = [
                'date' => $current->format('Y-m-d'),
                'value' => $value
            ];
            
            $current = $nextPeriod;
        }

        return $data;
    }

    /**
     * Obtient la valeur d'une métrique pour une période donnée
     */
    private function getMetricValue(string $metric, \DateTime $start, \DateTime $end): int
    {
        $qb = $this->entityManager->createQueryBuilder();

        switch ($metric) {
            case 'projects_created':
                return $qb->select('COUNT(p.id)')
                    ->from('App\Entity\Project', 'p')
                    ->where('p.createdAt >= :start')
                    ->andWhere('p.createdAt < :end')
                    ->setParameter('start', $start)
                    ->setParameter('end', $end)
                    ->getQuery()
                    ->getSingleScalarResult();

            case 'invoices_paid':
                return $qb->select('COUNT(i.id)')
                    ->from('App\Entity\Invoice', 'i')
                    ->where('i.paidAt >= :start')
                    ->andWhere('i.paidAt < :end')
                    ->setParameter('start', $start)
                    ->setParameter('end', $end)
                    ->getQuery()
                    ->getSingleScalarResult();

            case 'employees_hired':
                return $qb->select('COUNT(e.id)')
                    ->from('App\Entity\Employee', 'e')
                    ->where('e.hireDate >= :start')
                    ->andWhere('e.hireDate < :end')
                    ->setParameter('start', $start)
                    ->setParameter('end', $end)
                    ->getQuery()
                    ->getSingleScalarResult();

            case 'security_events':
                return $qb->select('COUNT(sl.id)')
                    ->from('App\Entity\SecurityLog', 'sl')
                    ->where('sl.createdAt >= :start')
                    ->andWhere('sl.createdAt < :end')
                    ->setParameter('start', $start)
                    ->setParameter('end', $end)
                    ->getQuery()
                    ->getSingleScalarResult();

            default:
                return 0;
        }
    }
}
