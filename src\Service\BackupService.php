<?php

namespace App\Service;

use Doctrine\DBAL\Connection;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Process\Process;
use Psr\Log\LoggerInterface;

class BackupService
{
    private Connection $connection;
    private Filesystem $filesystem;
    private LoggerInterface $logger;
    private string $projectDir;
    private string $backupDir;

    public function __construct(
        Connection $connection,
        Filesystem $filesystem,
        LoggerInterface $logger,
        string $projectDir
    ) {
        $this->connection = $connection;
        $this->filesystem = $filesystem;
        $this->logger = $logger;
        $this->projectDir = $projectDir;
        $this->backupDir = $projectDir . '/var/backups';
        
        // Créer le dossier de sauvegarde s'il n'existe pas
        if (!$this->filesystem->exists($this->backupDir)) {
            $this->filesystem->mkdir($this->backupDir);
        }
    }

    /**
     * Crée une sauvegarde complète du système
     */
    public function createFullBackup(): array
    {
        $timestamp = date('Y-m-d_H-i-s');
        $backupName = "full_backup_{$timestamp}";
        $backupPath = $this->backupDir . '/' . $backupName;
        
        $this->filesystem->mkdir($backupPath);
        
        $results = [
            'name' => $backupName,
            'timestamp' => $timestamp,
            'path' => $backupPath,
            'components' => []
        ];

        try {
            // Sauvegarde de la base de données
            $dbBackup = $this->backupDatabase($backupPath);
            $results['components']['database'] = $dbBackup;

            // Sauvegarde des fichiers uploadés
            $filesBackup = $this->backupFiles($backupPath);
            $results['components']['files'] = $filesBackup;

            // Sauvegarde de la configuration
            $configBackup = $this->backupConfiguration($backupPath);
            $results['components']['configuration'] = $configBackup;

            // Création d'un fichier de métadonnées
            $this->createBackupMetadata($backupPath, $results);

            // Compression de la sauvegarde
            $archivePath = $this->compressBackup($backupPath);
            $results['archive_path'] = $archivePath;
            $results['size'] = $this->filesystem->exists($archivePath) ? filesize($archivePath) : 0;

            $this->logger->info('Sauvegarde complète créée avec succès', [
                'backup_name' => $backupName,
                'size' => $results['size']
            ]);

            $results['success'] = true;

        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de la création de la sauvegarde', [
                'error' => $e->getMessage(),
                'backup_name' => $backupName
            ]);
            
            $results['success'] = false;
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Sauvegarde la base de données
     */
    private function backupDatabase(string $backupPath): array
    {
        $dbParams = $this->connection->getParams();
        $dbName = $dbParams['dbname'];
        $dbUser = $dbParams['user'];
        $dbPassword = $dbParams['password'] ?? '';
        $dbHost = $dbParams['host'] ?? 'localhost';
        $dbPort = $dbParams['port'] ?? 3306;

        $sqlFile = $backupPath . '/database.sql';
        
        // Commande mysqldump
        $command = [
            'mysqldump',
            '--host=' . $dbHost,
            '--port=' . $dbPort,
            '--user=' . $dbUser,
            '--single-transaction',
            '--routines',
            '--triggers',
            '--add-drop-table',
            '--extended-insert',
            '--create-options',
            '--quick',
            '--lock-tables=false'
        ];

        if (!empty($dbPassword)) {
            $command[] = '--password=' . $dbPassword;
        }

        $command[] = $dbName;

        $process = new Process($command);
        $process->setTimeout(3600); // 1 heure
        $process->run();

        if ($process->isSuccessful()) {
            file_put_contents($sqlFile, $process->getOutput());
            
            return [
                'success' => true,
                'file' => $sqlFile,
                'size' => filesize($sqlFile),
                'tables_count' => $this->countTables()
            ];
        } else {
            throw new \Exception('Erreur lors de la sauvegarde de la base de données: ' . $process->getErrorOutput());
        }
    }

    /**
     * Sauvegarde les fichiers uploadés
     */
    private function backupFiles(string $backupPath): array
    {
        $uploadsDir = $this->projectDir . '/public/uploads';
        $filesBackupDir = $backupPath . '/files';
        
        if (!$this->filesystem->exists($uploadsDir)) {
            return [
                'success' => true,
                'message' => 'Aucun dossier uploads à sauvegarder',
                'files_count' => 0,
                'size' => 0
            ];
        }

        $this->filesystem->mirror($uploadsDir, $filesBackupDir);
        
        $filesCount = $this->countFiles($filesBackupDir);
        $totalSize = $this->calculateDirectorySize($filesBackupDir);

        return [
            'success' => true,
            'source' => $uploadsDir,
            'destination' => $filesBackupDir,
            'files_count' => $filesCount,
            'size' => $totalSize
        ];
    }

    /**
     * Sauvegarde la configuration
     */
    private function backupConfiguration(string $backupPath): array
    {
        $configBackupDir = $backupPath . '/config';
        $this->filesystem->mkdir($configBackupDir);

        $configFiles = [
            '.env' => $this->projectDir . '/.env',
            '.env.local' => $this->projectDir . '/.env.local',
            'config' => $this->projectDir . '/config'
        ];

        $backedUpFiles = [];
        
        foreach ($configFiles as $name => $source) {
            if ($this->filesystem->exists($source)) {
                $destination = $configBackupDir . '/' . $name;
                
                if (is_file($source)) {
                    $this->filesystem->copy($source, $destination);
                } else {
                    $this->filesystem->mirror($source, $destination);
                }
                
                $backedUpFiles[] = $name;
            }
        }

        return [
            'success' => true,
            'files' => $backedUpFiles,
            'destination' => $configBackupDir
        ];
    }

    /**
     * Crée un fichier de métadonnées pour la sauvegarde
     */
    private function createBackupMetadata(string $backupPath, array $backupInfo): void
    {
        $metadata = [
            'backup_info' => $backupInfo,
            'system_info' => [
                'php_version' => PHP_VERSION,
                'symfony_version' => \Symfony\Component\HttpKernel\Kernel::VERSION,
                'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
                'backup_date' => date('c'),
                'backup_tool' => 'SI System Backup Service v1.0'
            ],
            'database_info' => [
                'driver' => $this->connection->getDriver()->getName(),
                'server_version' => $this->connection->getServerVersion(),
                'tables_count' => $this->countTables()
            ]
        ];

        $metadataFile = $backupPath . '/backup_metadata.json';
        file_put_contents($metadataFile, json_encode($metadata, JSON_PRETTY_PRINT));
    }

    /**
     * Compresse la sauvegarde en archive ZIP
     */
    private function compressBackup(string $backupPath): string
    {
        $archivePath = $backupPath . '.zip';
        
        $zip = new \ZipArchive();
        if ($zip->open($archivePath, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) !== TRUE) {
            throw new \Exception('Impossible de créer l\'archive ZIP');
        }

        $this->addDirectoryToZip($zip, $backupPath, '');
        $zip->close();

        // Supprimer le dossier temporaire
        $this->filesystem->remove($backupPath);

        return $archivePath;
    }

    /**
     * Ajoute récursivement un dossier à une archive ZIP
     */
    private function addDirectoryToZip(\ZipArchive $zip, string $dir, string $zipPath): void
    {
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($dir, \RecursiveDirectoryIterator::SKIP_DOTS),
            \RecursiveIteratorIterator::SELF_FIRST
        );

        foreach ($iterator as $file) {
            $filePath = $file->getRealPath();
            $relativePath = $zipPath . substr($filePath, strlen($dir) + 1);

            if ($file->isDir()) {
                $zip->addEmptyDir($relativePath);
            } else {
                $zip->addFile($filePath, $relativePath);
            }
        }
    }

    /**
     * Restaure une sauvegarde
     */
    public function restoreBackup(string $backupPath): array
    {
        if (!$this->filesystem->exists($backupPath)) {
            throw new \Exception('Fichier de sauvegarde non trouvé');
        }

        $restoreDir = $this->backupDir . '/restore_' . date('Y-m-d_H-i-s');
        $this->filesystem->mkdir($restoreDir);

        try {
            // Extraire l'archive
            $zip = new \ZipArchive();
            if ($zip->open($backupPath) !== TRUE) {
                throw new \Exception('Impossible d\'ouvrir l\'archive de sauvegarde');
            }
            
            $zip->extractTo($restoreDir);
            $zip->close();

            $results = [
                'success' => true,
                'restore_dir' => $restoreDir,
                'components' => []
            ];

            // Restaurer la base de données
            $dbFile = $restoreDir . '/database.sql';
            if ($this->filesystem->exists($dbFile)) {
                $this->restoreDatabase($dbFile);
                $results['components']['database'] = 'restored';
            }

            // Restaurer les fichiers
            $filesDir = $restoreDir . '/files';
            if ($this->filesystem->exists($filesDir)) {
                $this->restoreFiles($filesDir);
                $results['components']['files'] = 'restored';
            }

            $this->logger->info('Sauvegarde restaurée avec succès', [
                'backup_path' => $backupPath,
                'restore_dir' => $restoreDir
            ]);

            return $results;

        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de la restauration', [
                'error' => $e->getMessage(),
                'backup_path' => $backupPath
            ]);
            
            throw $e;
        }
    }

    /**
     * Restaure la base de données
     */
    private function restoreDatabase(string $sqlFile): void
    {
        $dbParams = $this->connection->getParams();
        $dbName = $dbParams['dbname'];
        $dbUser = $dbParams['user'];
        $dbPassword = $dbParams['password'] ?? '';
        $dbHost = $dbParams['host'] ?? 'localhost';
        $dbPort = $dbParams['port'] ?? 3306;

        $command = [
            'mysql',
            '--host=' . $dbHost,
            '--port=' . $dbPort,
            '--user=' . $dbUser
        ];

        if (!empty($dbPassword)) {
            $command[] = '--password=' . $dbPassword;
        }

        $command[] = $dbName;

        $process = new Process($command);
        $process->setInput(file_get_contents($sqlFile));
        $process->setTimeout(3600);
        $process->run();

        if (!$process->isSuccessful()) {
            throw new \Exception('Erreur lors de la restauration de la base de données: ' . $process->getErrorOutput());
        }
    }

    /**
     * Restaure les fichiers
     */
    private function restoreFiles(string $filesDir): void
    {
        $uploadsDir = $this->projectDir . '/public/uploads';
        
        // Sauvegarder les fichiers existants
        if ($this->filesystem->exists($uploadsDir)) {
            $backupUploadsDir = $uploadsDir . '_backup_' . date('Y-m-d_H-i-s');
            $this->filesystem->rename($uploadsDir, $backupUploadsDir);
        }

        // Restaurer les fichiers
        $this->filesystem->mirror($filesDir, $uploadsDir);
    }

    /**
     * Liste les sauvegardes disponibles
     */
    public function listBackups(): array
    {
        $backups = [];
        
        if (!$this->filesystem->exists($this->backupDir)) {
            return $backups;
        }

        $files = glob($this->backupDir . '/*.zip');
        
        foreach ($files as $file) {
            $backups[] = [
                'name' => basename($file, '.zip'),
                'path' => $file,
                'size' => filesize($file),
                'created_at' => date('c', filemtime($file)),
                'human_size' => $this->formatBytes(filesize($file))
            ];
        }

        // Trier par date de création (plus récent en premier)
        usort($backups, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return $backups;
    }

    /**
     * Supprime les anciennes sauvegardes
     */
    public function cleanOldBackups(int $keepDays = 30): int
    {
        $cutoffDate = time() - ($keepDays * 24 * 60 * 60);
        $deleted = 0;
        
        $files = glob($this->backupDir . '/*.zip');
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffDate) {
                $this->filesystem->remove($file);
                $deleted++;
                
                $this->logger->info('Ancienne sauvegarde supprimée', [
                    'file' => basename($file),
                    'age_days' => round((time() - filemtime($file)) / (24 * 60 * 60))
                ]);
            }
        }

        return $deleted;
    }

    /**
     * Utilitaires
     */
    private function countTables(): int
    {
        return count($this->connection->createSchemaManager()->listTableNames());
    }

    private function countFiles(string $directory): int
    {
        if (!$this->filesystem->exists($directory)) {
            return 0;
        }
        
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        return iterator_count($iterator);
    }

    private function calculateDirectorySize(string $directory): int
    {
        $size = 0;
        
        if (!$this->filesystem->exists($directory)) {
            return $size;
        }
        
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($directory, \RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }
        
        return $size;
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Vérifie l'espace disque disponible
     */
    public function checkDiskSpace(): array
    {
        $freeBytes = disk_free_space($this->backupDir);
        $totalBytes = disk_total_space($this->backupDir);
        $usedBytes = $totalBytes - $freeBytes;

        return [
            'free' => $freeBytes,
            'total' => $totalBytes,
            'used' => $usedBytes,
            'free_percent' => round(($freeBytes / $totalBytes) * 100, 2),
            'used_percent' => round(($usedBytes / $totalBytes) * 100, 2),
            'free_human' => $this->formatBytes($freeBytes),
            'total_human' => $this->formatBytes($totalBytes),
            'used_human' => $this->formatBytes($usedBytes)
        ];
    }

    /**
     * Estime la taille d'une sauvegarde
     */
    public function estimateBackupSize(): array
    {
        $dbSize = $this->estimateDatabaseSize();
        $filesSize = $this->calculateDirectorySize($this->projectDir . '/public/uploads');
        $configSize = $this->estimateConfigSize();

        $totalSize = $dbSize + $filesSize + $configSize;

        return [
            'database' => $dbSize,
            'files' => $filesSize,
            'config' => $configSize,
            'total' => $totalSize,
            'database_human' => $this->formatBytes($dbSize),
            'files_human' => $this->formatBytes($filesSize),
            'config_human' => $this->formatBytes($configSize),
            'total_human' => $this->formatBytes($totalSize)
        ];
    }

    private function estimateDatabaseSize(): int
    {
        try {
            $sql = "SELECT ROUND(SUM(data_length + index_length)) AS size
                    FROM information_schema.tables
                    WHERE table_schema = ?";

            $result = $this->connection->fetchOne($sql, [$this->connection->getDatabase()]);
            return (int) $result;
        } catch (\Exception $e) {
            // Fallback: estimation basée sur le nombre de tables
            return $this->countTables() * 1024 * 1024; // 1MB par table en moyenne
        }
    }

    private function estimateConfigSize(): int
    {
        $configDirs = [
            $this->projectDir . '/config',
            $this->projectDir . '/.env'
        ];

        $size = 0;
        foreach ($configDirs as $path) {
            if ($this->filesystem->exists($path)) {
                if (is_file($path)) {
                    $size += filesize($path);
                } else {
                    $size += $this->calculateDirectorySize($path);
                }
            }
        }

        return $size;
    }
}
