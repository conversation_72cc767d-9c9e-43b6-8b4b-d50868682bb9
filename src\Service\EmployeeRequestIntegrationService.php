<?php

namespace App\Service;

use App\Entity\Employee;
use App\Entity\EmployeeLeave;
use App\Entity\EmployeeRequest;
use App\Entity\RequestComment;
use App\Entity\User;
use App\Repository\EmployeeRequestRepository;
use App\Repository\RequestCommentRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service d'intégration entre le système de congés existant et le nouveau système de demandes
 */
class EmployeeRequestIntegrationService
{
    private EntityManagerInterface $entityManager;
    private EmployeeRequestRepository $requestRepository;
    private RequestCommentRepository $commentRepository;
    private LoggerInterface $logger;

    public function __construct(
        EntityManagerInterface $entityManager,
        EmployeeRequestRepository $requestRepository,
        RequestCommentRepository $commentRepository,
        LoggerInterface $logger
    ) {
        $this->entityManager = $entityManager;
        $this->requestRepository = $requestRepository;
        $this->commentRepository = $commentRepository;
        $this->logger = $logger;
    }

    /**
     * Créer une EmployeeRequest à partir d'une EmployeeLeave
     */
    public function createRequestFromLeave(EmployeeLeave $leave): EmployeeRequest
    {
        $request = new EmployeeRequest();
        $request->setEmployee($leave->getEmployee());
        $request->setType('leave');
        $request->setTitle($this->generateLeaveTitle($leave));
        $request->setDescription($this->generateLeaveDescription($leave));
        $request->setStatus($this->mapLeaveStatusToRequestStatus($leave->getStatus()));
        $request->setPriority($this->calculateLeavePriority($leave));
        $request->setRequestedDate($leave->getStartDate());

        // Si le congé est déjà approuvé, ajouter les informations d'approbation
        if ($leave->getApprovedBy()) {
            $request->setApprovedBy($leave->getApprovedBy());
            $request->setApprovedAt($leave->getApprovedAt());
        }

        $this->entityManager->persist($request);

        // Créer un commentaire initial
        $comment = new RequestComment();
        $comment->setRequest($request);
        $comment->setAuthor($leave->getEmployee()->getUser());
        $comment->setType('system');
        $comment->setComment('Demande de congé créée automatiquement depuis le système de congés existant.');
        $comment->setIsInternal(false);

        $this->entityManager->persist($comment);

        $this->logger->info('EmployeeRequest créée à partir d\'EmployeeLeave', [
            'leave_id' => $leave->getId(),
            'request_id' => $request->getId(),
            'employee_id' => $leave->getEmployee()->getId()
        ]);

        return $request;
    }

    /**
     * Synchroniser le statut d'une EmployeeLeave avec son EmployeeRequest
     */
    public function syncLeaveWithRequest(EmployeeLeave $leave, EmployeeRequest $request, User $updatedBy): void
    {
        $oldStatus = $request->getStatus();
        $newStatus = $this->mapLeaveStatusToRequestStatus($leave->getStatus());

        if ($oldStatus !== $newStatus) {
            $request->setStatus($newStatus);

            // Ajouter les informations d'approbation si nécessaire
            if ($leave->getApprovedBy() && !$request->getApprovedBy()) {
                $request->setApprovedBy($leave->getApprovedBy());
                $request->setApprovedAt($leave->getApprovedAt());
            }

            // Créer un commentaire de changement de statut
            $comment = RequestComment::createStatusChangeComment(
                $request,
                $updatedBy,
                $oldStatus,
                $newStatus
            );

            $this->entityManager->persist($comment);
            $this->entityManager->persist($request);

            $this->logger->info('Synchronisation EmployeeLeave -> EmployeeRequest', [
                'leave_id' => $leave->getId(),
                'request_id' => $request->getId(),
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]);
        }
    }

    /**
     * Synchroniser le statut d'une EmployeeRequest avec son EmployeeLeave
     */
    public function syncRequestWithLeave(EmployeeRequest $request, EmployeeLeave $leave, User $updatedBy): void
    {
        $oldStatus = $leave->getStatus();
        $newStatus = $this->mapRequestStatusToLeaveStatus($request->getStatus());

        if ($oldStatus !== $newStatus) {
            $leave->setStatus($newStatus);

            // Ajouter les informations d'approbation si nécessaire
            if ($request->getApprovedBy() && !$leave->getApprovedBy()) {
                $leave->setApprovedBy($request->getApprovedBy());
                $leave->setApprovedAt($request->getApprovedAt());
            }

            $this->entityManager->persist($leave);

            $this->logger->info('Synchronisation EmployeeRequest -> EmployeeLeave', [
                'request_id' => $request->getId(),
                'leave_id' => $leave->getId(),
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]);
        }
    }

    /**
     * Générer un titre pour la demande de congé
     */
    private function generateLeaveTitle(EmployeeLeave $leave): string
    {
        $leaveTypeLabel = $leave->getLeaveTypeLabel();
        $duration = $leave->getDuration();
        $startDate = $leave->getStartDate()->format('d/m/Y');

        return "Demande de {$leaveTypeLabel} - {$duration} jour(s) à partir du {$startDate}";
    }

    /**
     * Générer une description pour la demande de congé
     */
    private function generateLeaveDescription(EmployeeLeave $leave): string
    {
        $description = "Type de congé : " . $leave->getLeaveTypeLabel() . "\n";
        $description .= "Date de début : " . $leave->getStartDate()->format('d/m/Y') . "\n";
        $description .= "Date de fin : " . $leave->getEndDate()->format('d/m/Y') . "\n";
        $description .= "Durée : " . $leave->getDuration() . " jour(s)\n";

        if ($leave->getReason()) {
            $description .= "Raison : " . $leave->getReason() . "\n";
        }

        if ($leave->getComments()) {
            $description .= "Commentaires : " . $leave->getComments();
        }

        return $description;
    }

    /**
     * Mapper le statut d'EmployeeLeave vers EmployeeRequest
     */
    private function mapLeaveStatusToRequestStatus(string $leaveStatus): string
    {
        return match($leaveStatus) {
            'pending' => 'pending',
            'approved' => 'approved',
            'rejected' => 'rejected',
            'cancelled' => 'cancelled',
            default => 'pending',
        };
    }

    /**
     * Mapper le statut d'EmployeeRequest vers EmployeeLeave
     */
    private function mapRequestStatusToLeaveStatus(string $requestStatus): string
    {
        return match($requestStatus) {
            'pending', 'manager_approved', 'hr_review' => 'pending',
            'approved', 'completed' => 'approved',
            'rejected' => 'rejected',
            'cancelled' => 'cancelled',
            default => 'pending',
        };
    }

    /**
     * Calculer la priorité d'une demande de congé
     */
    private function calculateLeavePriority(EmployeeLeave $leave): string
    {
        $now = new \DateTime();
        $startDate = $leave->getStartDate();
        $duration = $leave->getDuration();

        // Congé urgent si commence dans moins de 3 jours
        $daysUntilStart = $now->diff($startDate)->days;
        if ($daysUntilStart <= 3) {
            return 'urgent';
        }

        // Congé haute priorité si longue durée (> 10 jours)
        if ($duration > 10) {
            return 'high';
        }

        // Congé haute priorité si commence dans moins de 7 jours
        if ($daysUntilStart <= 7) {
            return 'high';
        }

        return 'medium';
    }

    /**
     * Migrer toutes les EmployeeLeave existantes vers EmployeeRequest
     */
    public function migrateExistingLeaves(): int
    {
        $leaveRepository = $this->entityManager->getRepository(EmployeeLeave::class);
        $leaves = $leaveRepository->findAll();
        $migratedCount = 0;

        foreach ($leaves as $leave) {
            // Vérifier si une EmployeeRequest existe déjà pour ce congé
            $existingRequest = $this->requestRepository->findOneBy([
                'employee' => $leave->getEmployee(),
                'type' => 'leave',
                'requestedDate' => $leave->getStartDate()
            ]);

            if (!$existingRequest) {
                $this->createRequestFromLeave($leave);
                $migratedCount++;
            }
        }

        $this->entityManager->flush();

        $this->logger->info('Migration des congés existants terminée', [
            'total_leaves' => count($leaves),
            'migrated_count' => $migratedCount
        ]);

        return $migratedCount;
    }

    /**
     * Obtenir les statistiques d'intégration
     */
    public function getIntegrationStats(): array
    {
        $leaveRepository = $this->entityManager->getRepository(EmployeeLeave::class);
        $totalLeaves = $leaveRepository->count([]);

        $leaveRequests = $this->requestRepository->findBy(['type' => 'leave']);
        $totalLeaveRequests = count($leaveRequests);

        return [
            'total_leaves' => $totalLeaves,
            'total_leave_requests' => $totalLeaveRequests,
            'integration_rate' => $totalLeaves > 0 ? round(($totalLeaveRequests / $totalLeaves) * 100, 2) : 0,
            'pending_migration' => max(0, $totalLeaves - $totalLeaveRequests)
        ];
    }
}
