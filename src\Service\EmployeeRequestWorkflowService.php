<?php

namespace App\Service;

use App\Entity\EmployeeRequest;
use App\Entity\RequestComment;
use App\Entity\User;
use App\Entity\Employee;
use App\Repository\EmployeeRequestRepository;
use App\Repository\RequestCommentRepository;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Security\Core\Security;

/**
 * Service de workflow pour la gestion des demandes d'employés
 */
class EmployeeRequestWorkflowService
{
    private EntityManagerInterface $entityManager;
    private EmployeeRequestRepository $requestRepository;
    private RequestCommentRepository $commentRepository;
    private LoggerInterface $logger;
    private Security $security;

    // Règles d'approbation par type de demande
    private const APPROVAL_RULES = [
        'leave' => [
            'manager_required' => true,
            'hr_required' => true,
            'director_required' => false,
            'max_amount' => null,
            'max_duration' => 30 // jours
        ],
        'salary_advance' => [
            'manager_required' => true,
            'hr_required' => true,
            'director_required' => true, // pour montants > 1000€
            'max_amount' => 5000,
            'max_duration' => null
        ],
        'document' => [
            'manager_required' => false,
            'hr_required' => true,
            'director_required' => false,
            'max_amount' => null,
            'max_duration' => null
        ],
        'other' => [
            'manager_required' => true,
            'hr_required' => true,
            'director_required' => false,
            'max_amount' => null,
            'max_duration' => null
        ]
    ];

    public function __construct(
        EntityManagerInterface $entityManager,
        EmployeeRequestRepository $requestRepository,
        RequestCommentRepository $commentRepository,
        LoggerInterface $logger,
        Security $security
    ) {
        $this->entityManager = $entityManager;
        $this->requestRepository = $requestRepository;
        $this->commentRepository = $commentRepository;
        $this->logger = $logger;
        $this->security = $security;
    }

    /**
     * Soumettre une nouvelle demande
     */
    public function submitRequest(EmployeeRequest $request): bool
    {
        try {
            // Valider la demande
            if (!$this->validateRequest($request)) {
                return false;
            }

            // Définir le statut initial
            $request->setStatus('pending');

            // Déterminer le prochain niveau d'approbation
            $nextStatus = $this->getNextApprovalLevel($request);
            if ($nextStatus !== 'pending') {
                $request->setStatus($nextStatus);
            }

            $this->entityManager->persist($request);

            // Créer un commentaire de soumission
            $comment = new RequestComment();
            $comment->setRequest($request);
            $comment->setAuthor($request->getEmployee()->getUser());
            $comment->setType('system');
            $comment->setComment('Demande soumise et en attente d\'approbation.');
            $comment->setIsInternal(false);

            $this->entityManager->persist($comment);
            $this->entityManager->flush();

            $this->logger->info('Demande soumise avec succès', [
                'request_id' => $request->getId(),
                'type' => $request->getType(),
                'employee_id' => $request->getEmployee()->getId(),
                'status' => $request->getStatus()
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de la soumission de la demande', [
                'error' => $e->getMessage(),
                'request_type' => $request->getType()
            ]);
            return false;
        }
    }

    /**
     * Approuver une demande
     */
    public function approveRequest(EmployeeRequest $request, User $approver, ?string $comment = null): bool
    {
        try {
            // Vérifier si l'utilisateur peut approuver cette demande
            if (!$this->canUserApprove($request, $approver)) {
                $this->logger->warning('Tentative d\'approbation non autorisée', [
                    'request_id' => $request->getId(),
                    'approver_id' => $approver->getId()
                ]);
                return false;
            }

            $oldStatus = $request->getStatus();
            $newStatus = $this->getNextStatusAfterApproval($request);

            $request->setStatus($newStatus);

            // Si c'est l'approbation finale, marquer comme approuvé
            if ($newStatus === 'approved') {
                $request->setApprovedBy($approver);
                $request->setApprovedAt(new \DateTime());
            }

            $this->entityManager->persist($request);

            // Créer un commentaire d'approbation
            $approvalComment = RequestComment::createApprovalComment(
                $request,
                $approver,
                $comment ?? "Demande approuvée au niveau {$oldStatus}"
            );

            $this->entityManager->persist($approvalComment);

            // Créer un commentaire de changement de statut
            if ($oldStatus !== $newStatus) {
                $statusComment = RequestComment::createStatusChangeComment(
                    $request,
                    $approver,
                    $oldStatus,
                    $newStatus
                );
                $this->entityManager->persist($statusComment);
            }

            $this->entityManager->flush();

            $this->logger->info('Demande approuvée', [
                'request_id' => $request->getId(),
                'approver_id' => $approver->getId(),
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de l\'approbation', [
                'error' => $e->getMessage(),
                'request_id' => $request->getId()
            ]);
            return false;
        }
    }

    /**
     * Rejeter une demande
     */
    public function rejectRequest(EmployeeRequest $request, User $rejector, string $reason): bool
    {
        try {
            // Vérifier si l'utilisateur peut rejeter cette demande
            if (!$this->canUserApprove($request, $rejector)) {
                return false;
            }

            $oldStatus = $request->getStatus();
            $request->setStatus('rejected');
            $request->setRejectionReason($reason);
            $request->setApprovedBy($rejector);
            $request->setApprovedAt(new \DateTime());

            $this->entityManager->persist($request);

            // Créer un commentaire de rejet
            $rejectionComment = RequestComment::createRejectionComment(
                $request,
                $rejector,
                $reason
            );

            $this->entityManager->persist($rejectionComment);

            // Créer un commentaire de changement de statut
            $statusComment = RequestComment::createStatusChangeComment(
                $request,
                $rejector,
                $oldStatus,
                'rejected'
            );

            $this->entityManager->persist($statusComment);
            $this->entityManager->flush();

            $this->logger->info('Demande rejetée', [
                'request_id' => $request->getId(),
                'rejector_id' => $rejector->getId(),
                'reason' => $reason
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors du rejet', [
                'error' => $e->getMessage(),
                'request_id' => $request->getId()
            ]);
            return false;
        }
    }

    /**
     * Annuler une demande (par l'employé)
     */
    public function cancelRequest(EmployeeRequest $request, User $canceller, ?string $reason = null): bool
    {
        try {
            // Vérifier que seul l'employé ou un admin peut annuler
            if ($request->getEmployee()->getUser() !== $canceller && !$this->security->isGranted('ROLE_ADMIN')) {
                return false;
            }

            // Vérifier que la demande peut être annulée
            if (!$request->canBeCancelled()) {
                return false;
            }

            $oldStatus = $request->getStatus();
            $request->setStatus('cancelled');

            $this->entityManager->persist($request);

            // Créer un commentaire d'annulation
            $comment = new RequestComment();
            $comment->setRequest($request);
            $comment->setAuthor($canceller);
            $comment->setType('system');
            $comment->setComment('Demande annulée' . ($reason ? ': ' . $reason : '.'));
            $comment->setIsInternal(false);

            $this->entityManager->persist($comment);

            // Créer un commentaire de changement de statut
            $statusComment = RequestComment::createStatusChangeComment(
                $request,
                $canceller,
                $oldStatus,
                'cancelled'
            );

            $this->entityManager->persist($statusComment);
            $this->entityManager->flush();

            $this->logger->info('Demande annulée', [
                'request_id' => $request->getId(),
                'canceller_id' => $canceller->getId(),
                'reason' => $reason
            ]);

            return true;
        } catch (\Exception $e) {
            $this->logger->error('Erreur lors de l\'annulation', [
                'error' => $e->getMessage(),
                'request_id' => $request->getId()
            ]);
            return false;
        }
    }

    /**
     * Valider une demande avant soumission
     */
    private function validateRequest(EmployeeRequest $request): bool
    {
        $rules = self::APPROVAL_RULES[$request->getType()] ?? self::APPROVAL_RULES['other'];

        // Validation spécifique par type
        switch ($request->getType()) {
            case 'salary_advance':
                if ($request->getSalaryAdvance()) {
                    $amount = (float)$request->getSalaryAdvance()->getAmount();
                    if ($amount > $rules['max_amount']) {
                        return false;
                    }
                }
                break;

            case 'leave':
                // Validation des congés (durée, dates, etc.)
                if ($request->getRequestedDate() && $request->getRequestedDate() < new \DateTime()) {
                    return false; // Date dans le passé
                }
                break;
        }

        return true;
    }

    /**
     * Déterminer le prochain niveau d'approbation
     */
    private function getNextApprovalLevel(EmployeeRequest $request): string
    {
        $rules = self::APPROVAL_RULES[$request->getType()] ?? self::APPROVAL_RULES['other'];

        // Si pas besoin d'approbation manager, aller directement à HR
        if (!$rules['manager_required']) {
            return $rules['hr_required'] ? 'hr_review' : 'approved';
        }

        return 'pending'; // Commence par l'approbation manager
    }

    /**
     * Obtenir le statut suivant après approbation
     */
    private function getNextStatusAfterApproval(EmployeeRequest $request): string
    {
        $currentStatus = $request->getStatus();
        $rules = self::APPROVAL_RULES[$request->getType()] ?? self::APPROVAL_RULES['other'];

        switch ($currentStatus) {
            case 'pending':
                return $rules['hr_required'] ? 'manager_approved' : 'approved';

            case 'manager_approved':
                // Vérifier si direction requise pour les avances importantes
                if ($request->getType() === 'salary_advance' && $request->getSalaryAdvance()) {
                    $amount = (float)$request->getSalaryAdvance()->getAmount();
                    if ($amount > 1000 && $rules['director_required']) {
                        return 'hr_review';
                    }
                }
                return 'approved';

            case 'hr_review':
                return 'approved';

            default:
                return $currentStatus;
        }
    }

    /**
     * Vérifier si un utilisateur peut approuver une demande
     */
    private function canUserApprove(EmployeeRequest $request, User $user): bool
    {
        $currentStatus = $request->getStatus();
        $employee = $request->getEmployee();

        switch ($currentStatus) {
            case 'pending':
                // Manager direct ou admin
                return ($employee->getManager() && $employee->getManager()->getUser() === $user) 
                    || $this->security->isGranted('ROLE_ADMIN');

            case 'manager_approved':
            case 'hr_review':
                // RH ou admin
                return $this->security->isGranted('ROLE_HR') || $this->security->isGranted('ROLE_ADMIN');

            default:
                return false;
        }
    }

    /**
     * Obtenir les demandes en attente d'approbation pour un utilisateur
     */
    public function getPendingRequestsForUser(User $user): array
    {
        $requests = [];

        // Demandes en attente d'approbation manager
        if ($user->getEmployee()) {
            $managerRequests = $this->requestRepository->findPendingForManager($user);
            $requests = array_merge($requests, $managerRequests);
        }

        // Demandes en attente d'approbation RH
        if ($this->security->isGranted('ROLE_HR')) {
            $hrRequests = $this->requestRepository->findForHRReview();
            $requests = array_merge($requests, $hrRequests);
        }

        return $requests;
    }

    /**
     * Obtenir les statistiques de workflow
     */
    public function getWorkflowStatistics(): array
    {
        $stats = $this->requestRepository->getStatistics();
        
        // Ajouter des métriques de performance
        $pendingRequests = $this->requestRepository->findBy(['status' => 'pending']);
        $avgProcessingTime = $this->calculateAverageProcessingTime();

        return array_merge($stats, [
            'pending_count' => count($pendingRequests),
            'avg_processing_time_days' => $avgProcessingTime,
            'urgent_requests' => count($this->requestRepository->findUrgentRequests()),
            'overdue_requests' => count($this->requestRepository->findOverdueRequests())
        ]);
    }

    /**
     * Calculer le temps de traitement moyen
     */
    private function calculateAverageProcessingTime(): float
    {
        $completedRequests = $this->requestRepository->findBy(['status' => 'approved']);
        
        if (empty($completedRequests)) {
            return 0;
        }

        $totalDays = 0;
        $count = 0;

        foreach ($completedRequests as $request) {
            if ($request->getApprovedAt()) {
                $interval = $request->getCreatedAt()->diff($request->getApprovedAt());
                $totalDays += $interval->days;
                $count++;
            }
        }

        return $count > 0 ? round($totalDays / $count, 2) : 0;
    }
}
