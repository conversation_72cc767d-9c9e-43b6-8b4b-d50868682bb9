<?php

namespace App\Service;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\Expr;

class GlobalSearchService
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * Recherche globale dans tous les modules
     */
    public function globalSearch(string $query, array $options = []): array
    {
        $query = trim($query);
        
        if (strlen($query) < 2) {
            return [
                'success' => false,
                'message' => 'La recherche doit contenir au moins 2 caractères',
                'results' => []
            ];
        }

        $limit = $options['limit'] ?? 50;
        $modules = $options['modules'] ?? ['all'];
        $results = [];

        // Recherche dans les projets
        if (in_array('all', $modules) || in_array('projects', $modules)) {
            $projectResults = $this->searchProjects($query, $limit);
            $results['projects'] = $projectResults;
        }

        // Recherche dans les employés
        if (in_array('all', $modules) || in_array('employees', $modules)) {
            $employeeResults = $this->searchEmployees($query, $limit);
            $results['employees'] = $employeeResults;
        }

        // Recherche dans les factures
        if (in_array('all', $modules) || in_array('invoices', $modules)) {
            $invoiceResults = $this->searchInvoices($query, $limit);
            $results['invoices'] = $invoiceResults;
        }

        // Recherche dans les partenaires
        if (in_array('all', $modules) || in_array('partners', $modules)) {
            $partnerResults = $this->searchPartners($query, $limit);
            $results['partners'] = $partnerResults;
        }

        // Recherche dans les achats
        if (in_array('all', $modules) || in_array('purchases', $modules)) {
            $purchaseResults = $this->searchPurchases($query, $limit);
            $results['purchases'] = $purchaseResults;
        }

        // Recherche dans le stock
        if (in_array('all', $modules) || in_array('stock', $modules)) {
            $stockResults = $this->searchStock($query, $limit);
            $results['stock'] = $stockResults;
        }

        // Recherche dans les départements
        if (in_array('all', $modules) || in_array('departments', $modules)) {
            $departmentResults = $this->searchDepartments($query, $limit);
            $results['departments'] = $departmentResults;
        }

        // Calculer le total des résultats
        $totalResults = array_sum(array_map('count', $results));

        return [
            'success' => true,
            'query' => $query,
            'total_results' => $totalResults,
            'results' => $results,
            'search_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
        ];
    }

    /**
     * Recherche dans les projets
     */
    private function searchProjects(string $query, int $limit): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        
        $projects = $qb->select('p')
            ->from('App\Entity\Project', 'p')
            ->where($qb->expr()->orX(
                $qb->expr()->like('p.name', ':query'),
                $qb->expr()->like('p.description', ':query'),
                $qb->expr()->like('p.status', ':query')
            ))
            ->setParameter('query', '%' . $query . '%')
            ->setMaxResults($limit)
            ->orderBy('p.createdAt', 'DESC')
            ->getQuery()
            ->getResult();

        $results = [];
        foreach ($projects as $project) {
            $results[] = [
                'id' => $project->getId(),
                'type' => 'project',
                'title' => $project->getName(),
                'subtitle' => $project->getDescription(),
                'status' => $project->getStatus(),
                'url' => '/projects/' . $project->getId(),
                'icon' => 'bi-kanban',
                'color' => 'primary',
                'created_at' => $project->getCreatedAt()->format('Y-m-d H:i'),
                'relevance' => $this->calculateRelevance($query, [
                    $project->getName(),
                    $project->getDescription()
                ])
            ];
        }

        return $results;
    }

    /**
     * Recherche dans les employés
     */
    private function searchEmployees(string $query, int $limit): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        
        $employees = $qb->select('e', 'd', 'p')
            ->from('App\Entity\Employee', 'e')
            ->leftJoin('e.department', 'd')
            ->leftJoin('e.position', 'p')
            ->where($qb->expr()->orX(
                $qb->expr()->like('e.firstName', ':query'),
                $qb->expr()->like('e.lastName', ':query'),
                $qb->expr()->like('e.email', ':query'),
                $qb->expr()->like('e.phone', ':query'),
                $qb->expr()->like('d.name', ':query'),
                $qb->expr()->like('p.name', ':query')
            ))
            ->setParameter('query', '%' . $query . '%')
            ->setMaxResults($limit)
            ->orderBy('e.firstName', 'ASC')
            ->getQuery()
            ->getResult();

        $results = [];
        foreach ($employees as $employee) {
            $results[] = [
                'id' => $employee->getId(),
                'type' => 'employee',
                'title' => $employee->getFirstName() . ' ' . $employee->getLastName(),
                'subtitle' => $employee->getEmail(),
                'department' => $employee->getDepartment()?->getName(),
                'position' => $employee->getPosition()?->getName(),
                'url' => '/employees/' . $employee->getId(),
                'icon' => 'bi-person',
                'color' => 'success',
                'created_at' => $employee->getHireDate()?->format('Y-m-d'),
                'relevance' => $this->calculateRelevance($query, [
                    $employee->getFirstName(),
                    $employee->getLastName(),
                    $employee->getEmail()
                ])
            ];
        }

        return $results;
    }

    /**
     * Recherche dans les factures
     */
    private function searchInvoices(string $query, int $limit): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        
        $invoices = $qb->select('i')
            ->from('App\Entity\Invoice', 'i')
            ->where($qb->expr()->orX(
                $qb->expr()->like('i.number', ':query'),
                $qb->expr()->like('i.status', ':query'),
                $qb->expr()->like('CAST(i.totalAmount AS string)', ':query')
            ))
            ->setParameter('query', '%' . $query . '%')
            ->setMaxResults($limit)
            ->orderBy('i.createdAt', 'DESC')
            ->getQuery()
            ->getResult();

        $results = [];
        foreach ($invoices as $invoice) {
            $results[] = [
                'id' => $invoice->getId(),
                'type' => 'invoice',
                'title' => 'Facture ' . $invoice->getNumber(),
                'subtitle' => number_format($invoice->getTotalAmount(), 2) . ' €',
                'status' => $invoice->getStatus(),
                'url' => '/invoices/' . $invoice->getId(),
                'icon' => 'bi-receipt',
                'color' => $invoice->getStatus() === 'paid' ? 'success' : 'warning',
                'created_at' => $invoice->getCreatedAt()->format('Y-m-d H:i'),
                'relevance' => $this->calculateRelevance($query, [
                    $invoice->getNumber(),
                    (string) $invoice->getTotalAmount()
                ])
            ];
        }

        return $results;
    }

    /**
     * Recherche dans les partenaires
     */
    private function searchPartners(string $query, int $limit): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        
        $partners = $qb->select('p')
            ->from('App\Entity\Partner', 'p')
            ->where($qb->expr()->orX(
                $qb->expr()->like('p.name', ':query'),
                $qb->expr()->like('p.email', ':query'),
                $qb->expr()->like('p.phone', ':query'),
                $qb->expr()->like('p.address', ':query'),
                $qb->expr()->like('p.city', ':query')
            ))
            ->setParameter('query', '%' . $query . '%')
            ->setMaxResults($limit)
            ->orderBy('p.name', 'ASC')
            ->getQuery()
            ->getResult();

        $results = [];
        foreach ($partners as $partner) {
            $results[] = [
                'id' => $partner->getId(),
                'type' => 'partner',
                'title' => $partner->getName(),
                'subtitle' => $partner->getEmail(),
                'address' => $partner->getAddress() . ', ' . $partner->getCity(),
                'url' => '/partners/' . $partner->getId(),
                'icon' => 'bi-building',
                'color' => 'info',
                'created_at' => $partner->getCreatedAt()?->format('Y-m-d H:i'),
                'relevance' => $this->calculateRelevance($query, [
                    $partner->getName(),
                    $partner->getEmail()
                ])
            ];
        }

        return $results;
    }

    /**
     * Recherche dans les achats
     */
    private function searchPurchases(string $query, int $limit): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        
        $purchases = $qb->select('pr')
            ->from('App\Entity\PurchaseRequest', 'pr')
            ->where($qb->expr()->orX(
                $qb->expr()->like('pr.description', ':query'),
                $qb->expr()->like('pr.status', ':query'),
                $qb->expr()->like('pr.justification', ':query')
            ))
            ->setParameter('query', '%' . $query . '%')
            ->setMaxResults($limit)
            ->orderBy('pr.createdAt', 'DESC')
            ->getQuery()
            ->getResult();

        $results = [];
        foreach ($purchases as $purchase) {
            $results[] = [
                'id' => $purchase->getId(),
                'type' => 'purchase',
                'title' => 'Demande d\'achat #' . $purchase->getId(),
                'subtitle' => $purchase->getDescription(),
                'status' => $purchase->getStatus(),
                'url' => '/purchases/' . $purchase->getId(),
                'icon' => 'bi-cart',
                'color' => 'secondary',
                'created_at' => $purchase->getCreatedAt()->format('Y-m-d H:i'),
                'relevance' => $this->calculateRelevance($query, [
                    $purchase->getDescription(),
                    $purchase->getJustification()
                ])
            ];
        }

        return $results;
    }

    /**
     * Recherche dans le stock
     */
    private function searchStock(string $query, int $limit): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        
        $stockItems = $qb->select('s')
            ->from('App\Entity\StockItem', 's')
            ->where($qb->expr()->orX(
                $qb->expr()->like('s.name', ':query'),
                $qb->expr()->like('s.description', ':query'),
                $qb->expr()->like('s.reference', ':query'),
                $qb->expr()->like('s.category', ':query')
            ))
            ->setParameter('query', '%' . $query . '%')
            ->setMaxResults($limit)
            ->orderBy('s.name', 'ASC')
            ->getQuery()
            ->getResult();

        $results = [];
        foreach ($stockItems as $item) {
            $results[] = [
                'id' => $item->getId(),
                'type' => 'stock',
                'title' => $item->getName(),
                'subtitle' => $item->getDescription(),
                'reference' => $item->getReference(),
                'quantity' => $item->getQuantity(),
                'url' => '/stock/' . $item->getId(),
                'icon' => 'bi-box',
                'color' => $item->getQuantity() > $item->getMinQuantity() ? 'success' : 'danger',
                'created_at' => $item->getCreatedAt()?->format('Y-m-d H:i'),
                'relevance' => $this->calculateRelevance($query, [
                    $item->getName(),
                    $item->getDescription(),
                    $item->getReference()
                ])
            ];
        }

        return $results;
    }

    /**
     * Recherche dans les départements
     */
    private function searchDepartments(string $query, int $limit): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        
        $departments = $qb->select('d')
            ->from('App\Entity\Department', 'd')
            ->where($qb->expr()->orX(
                $qb->expr()->like('d.name', ':query'),
                $qb->expr()->like('d.description', ':query')
            ))
            ->setParameter('query', '%' . $query . '%')
            ->setMaxResults($limit)
            ->orderBy('d.name', 'ASC')
            ->getQuery()
            ->getResult();

        $results = [];
        foreach ($departments as $department) {
            $results[] = [
                'id' => $department->getId(),
                'type' => 'department',
                'title' => $department->getName(),
                'subtitle' => $department->getDescription(),
                'url' => '/departments/' . $department->getId(),
                'icon' => 'bi-diagram-3',
                'color' => 'primary',
                'created_at' => $department->getCreatedAt()?->format('Y-m-d H:i'),
                'relevance' => $this->calculateRelevance($query, [
                    $department->getName(),
                    $department->getDescription()
                ])
            ];
        }

        return $results;
    }

    /**
     * Calcule la pertinence d'un résultat
     */
    private function calculateRelevance(string $query, array $fields): float
    {
        $query = strtolower($query);
        $relevance = 0;
        
        foreach ($fields as $field) {
            if (!$field) continue;
            
            $field = strtolower($field);
            
            // Correspondance exacte
            if ($field === $query) {
                $relevance += 100;
            }
            // Commence par la requête
            elseif (strpos($field, $query) === 0) {
                $relevance += 80;
            }
            // Contient la requête
            elseif (strpos($field, $query) !== false) {
                $relevance += 60;
            }
            // Correspondance partielle (mots)
            else {
                $words = explode(' ', $query);
                foreach ($words as $word) {
                    if (strpos($field, $word) !== false) {
                        $relevance += 20;
                    }
                }
            }
        }
        
        return $relevance;
    }

    /**
     * Recherche avec suggestions
     */
    public function searchWithSuggestions(string $query, array $options = []): array
    {
        $results = $this->globalSearch($query, $options);
        
        // Ajouter des suggestions si peu de résultats
        if ($results['total_results'] < 5) {
            $suggestions = $this->generateSuggestions($query);
            $results['suggestions'] = $suggestions;
        }
        
        return $results;
    }

    /**
     * Génère des suggestions de recherche
     */
    private function generateSuggestions(string $query): array
    {
        $suggestions = [];
        
        // Suggestions basées sur des termes similaires
        $similarTerms = [
            'projet' => ['project', 'projets', 'tâche', 'task'],
            'employé' => ['employee', 'employés', 'personnel', 'staff'],
            'facture' => ['invoice', 'factures', 'billing', 'facturation'],
            'partenaire' => ['partner', 'partenaires', 'client', 'fournisseur'],
            'stock' => ['inventory', 'inventaire', 'produit', 'article'],
            'achat' => ['purchase', 'achats', 'commande', 'order']
        ];
        
        $queryLower = strtolower($query);
        
        foreach ($similarTerms as $term => $alternatives) {
            if (strpos($queryLower, $term) !== false) {
                foreach ($alternatives as $alt) {
                    $suggestions[] = str_ireplace($term, $alt, $query);
                }
            }
        }
        
        return array_unique($suggestions);
    }
}
