<?php

namespace App\Service;

use App\Entity\Notification;
use App\Entity\User;
use App\Repository\NotificationRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Mercure\HubInterface;
use Symfony\Component\Mercure\Update;
use Symfony\Component\Mailer\MailerInterface;
use Symfony\Component\Mime\Email;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Twig\Environment;
use Psr\Log\LoggerInterface;

class NotificationService
{
    private EntityManagerInterface $entityManager;
    private NotificationRepository $notificationRepository;
    private ?HubInterface $hub;
    private ?MailerInterface $mailer;
    private ?UrlGeneratorInterface $urlGenerator;
    private ?Environment $twig;
    private ?LoggerInterface $logger;

    public function __construct(
        EntityManagerInterface $entityManager,
        NotificationRepository $notificationRepository,
        ?HubInterface $hub = null,
        ?MailerInterface $mailer = null,
        ?UrlGeneratorInterface $urlGenerator = null,
        ?Environment $twig = null,
        ?LoggerInterface $logger = null
    ) {
        $this->entityManager = $entityManager;
        $this->notificationRepository = $notificationRepository;
        $this->hub = $hub;
        $this->mailer = $mailer;
        $this->urlGenerator = $urlGenerator;
        $this->twig = $twig;
        $this->logger = $logger;
    }

    /**
     * Crée et envoie une notification
     */
    public function createNotification(
        User $user,
        string $type,
        string $title,
        string $content,
        array $options = []
    ): Notification {
        $notification = new Notification();
        $notification->setUserId((string) $user->getId());
        $notification->setType($type);
        $notification->setTitle($title);
        $notification->setContent($content);

        // Options avancées
        if (isset($options['priority'])) {
            $notification->setPriority($options['priority']);
        }

        if (isset($options['data'])) {
            $notification->setData($options['data']);
        }

        if (isset($options['actionUrl'])) {
            $notification->setActionUrl($options['actionUrl']);
        }

        if (isset($options['actionLabel'])) {
            $notification->setActionLabel($options['actionLabel']);
        }

        if (isset($options['icon'])) {
            $notification->setIcon($options['icon']);
        }

        if (isset($options['color'])) {
            $notification->setColor($options['color']);
        }

        if (isset($options['expiresAt'])) {
            $notification->setExpiresAt($options['expiresAt']);
        }

        if (isset($options['link'])) {
            $notification->setLink($options['link']);
        }

        if (isset($options['relatedEntityType'])) {
            $notification->setRelatedEntityType($options['relatedEntityType']);
        }

        if (isset($options['relatedEntityId'])) {
            $notification->setRelatedEntityId($options['relatedEntityId']);
        }

        // Paramètres d'envoi
        $notification->setIsPushEnabled($options['pushEnabled'] ?? true);
        $notification->setIsEmailEnabled($options['emailEnabled'] ?? false);

        $this->entityManager->persist($notification);
        $this->entityManager->flush();

        // Envoi en temps réel
        if ($notification->isPushEnabled() && $this->hub) {
            $this->sendPushNotification($notification, $user);
        }

        // Envoi par email si activé
        if ($notification->isEmailEnabled() && $this->mailer && $this->twig) {
            $this->sendEmailNotification($notification, $user);
        }

        return $notification;
    }

    /**
     * Envoie une notification push en temps réel via Mercure
     */
    private function sendPushNotification(Notification $notification, User $user): void
    {
        try {
            $data = [
                'id' => $notification->getId(),
                'type' => $notification->getType(),
                'title' => $notification->getTitle(),
                'content' => $notification->getContent(),
                'priority' => $notification->getPriority(),
                'icon' => $notification->getIcon(),
                'color' => $notification->getColor(),
                'actionUrl' => $notification->getActionUrl(),
                'actionLabel' => $notification->getActionLabel(),
                'createdAt' => $notification->getCreatedAt()->format('c'),
                'data' => $notification->getData(),
            ];

            $update = new Update(
                "user/{$user->getId()}/notifications",
                json_encode($data)
            );

            $this->hub->publish($update);

            $notification->setPushSentAt(new \DateTime());
            $this->entityManager->flush();

            if ($this->logger) {
                $this->logger->info('Push notification sent', [
                    'notification_id' => $notification->getId(),
                    'user_id' => $user->getId(),
                    'type' => $notification->getType()
                ]);
            }

        } catch (\Exception $e) {
            if ($this->logger) {
                $this->logger->error('Failed to send push notification', [
                    'notification_id' => $notification->getId(),
                    'user_id' => $user->getId(),
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Envoie une notification par email
     */
    private function sendEmailNotification(Notification $notification, User $user): void
    {
        try {
            $email = (new Email())
                ->from('<EMAIL>')
                ->to($user->getEmail())
                ->subject($notification->getTitle())
                ->html($this->twig->render('emails/notification.html.twig', [
                    'notification' => $notification,
                    'user' => $user,
                ]));

            $this->mailer->send($email);

            $notification->setEmailSentAt(new \DateTime());
            $this->entityManager->flush();

            if ($this->logger) {
                $this->logger->info('Email notification sent', [
                    'notification_id' => $notification->getId(),
                    'user_id' => $user->getId(),
                    'email' => $user->getEmail()
                ]);
            }

        } catch (\Exception $e) {
            if ($this->logger) {
                $this->logger->error('Failed to send email notification', [
                    'notification_id' => $notification->getId(),
                    'user_id' => $user->getId(),
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Marque une notification comme lue
     */
    public function markAsRead(int $notificationId, User $user): bool
    {
        $notification = $this->notificationRepository->findOneBy([
            'id' => $notificationId,
            'userId' => (string) $user->getId()
        ]);

        if (!$notification) {
            return false;
        }

        $notification->markAsRead();
        $this->entityManager->flush();

        // Notifier en temps réel que la notification a été lue
        if ($this->hub) {
            $this->sendReadStatusUpdate($notification, $user);
        }

        return true;
    }

    /**
     * Marque toutes les notifications d'un utilisateur comme lues
     */
    public function markAllAsRead(User $user): int
    {
        $notifications = $this->notificationRepository->findBy([
            'userId' => (string) $user->getId(),
            'isRead' => false
        ]);

        $count = 0;
        foreach ($notifications as $notification) {
            $notification->markAsRead();
            $count++;
        }

        $this->entityManager->flush();

        // Notifier en temps réel
        if ($this->hub) {
            $this->sendBulkReadStatusUpdate($user, $count);
        }

        return $count;
    }

    /**
     * Obtient les notifications non lues d'un utilisateur
     */
    public function getUnreadNotifications(User $user, int $limit = 10): array
    {
        return $this->notificationRepository->findBy([
            'userId' => (string) $user->getId(),
            'isRead' => false
        ], ['createdAt' => 'DESC'], $limit);
    }

    /**
     * Compte les notifications non lues d'un utilisateur
     */
    public function getUnreadCount(User $user): int
    {
        return $this->notificationRepository->count([
            'userId' => (string) $user->getId(),
            'isRead' => false
        ]);
    }

    /**
     * Marque une notification comme lue (version simplifiée)
     */
    public function markAsReadSimple(int $notificationId, User $user): bool
    {
        $notification = $this->notificationRepository->findOneBy([
            'id' => $notificationId,
            'userId' => (string) $user->getId()
        ]);

        if (!$notification) {
            return false;
        }

        $notification->setIsRead(true);
        $this->entityManager->flush();

        return true;
    }

    /**
     * Marque toutes les notifications d'un utilisateur comme lues (version simplifiée)
     */
    public function markAllAsReadSimple(User $user): int
    {
        $notifications = $this->notificationRepository->findBy([
            'userId' => (string) $user->getId(),
            'isRead' => false
        ]);

        $count = 0;
        foreach ($notifications as $notification) {
            $notification->setIsRead(true);
            $count++;
        }

        $this->entityManager->flush();
        return $count;
    }

    /**
     * Envoie une mise à jour du statut de lecture
     */
    private function sendReadStatusUpdate(Notification $notification, User $user): void
    {
        try {
            $data = [
                'action' => 'notification_read',
                'notificationId' => $notification->getId(),
                'unreadCount' => $this->getUnreadCount($user)
            ];

            $update = new Update(
                "user/{$user->getId()}/notifications",
                json_encode($data)
            );

            $this->hub->publish($update);
        } catch (\Exception $e) {
            if ($this->logger) {
                $this->logger->error('Failed to send read status update', [
                    'notification_id' => $notification->getId(),
                    'user_id' => $user->getId(),
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Envoie une mise à jour en lot du statut de lecture
     */
    private function sendBulkReadStatusUpdate(User $user, int $readCount): void
    {
        try {
            $data = [
                'action' => 'notifications_bulk_read',
                'readCount' => $readCount,
                'unreadCount' => $this->getUnreadCount($user)
            ];

            $update = new Update(
                "user/{$user->getId()}/notifications",
                json_encode($data)
            );

            $this->hub->publish($update);
        } catch (\Exception $e) {
            if ($this->logger) {
                $this->logger->error('Failed to send bulk read status update', [
                    'user_id' => $user->getId(),
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Méthodes de convenance pour créer des notifications spécifiques
     */
    public function notifyProjectUpdate(User $user, string $projectName, string $message, array $data = []): Notification
    {
        $notification = new Notification();
        $notification->setUserId((string) $user->getId());
        $notification->setType('project');
        $notification->setTitle("Mise à jour du projet {$projectName}");
        $notification->setContent($message);

        $this->entityManager->persist($notification);
        $this->entityManager->flush();

        return $notification;
    }

    public function notifySecurityAlert(User $user, string $message, array $data = []): Notification
    {
        $notification = new Notification();
        $notification->setUserId((string) $user->getId());
        $notification->setType('security');
        $notification->setTitle('Alerte de sécurité');
        $notification->setContent($message);

        $this->entityManager->persist($notification);
        $this->entityManager->flush();

        return $notification;
    }

    /**
     * Version simplifiée pour compatibilité
     */
    public function createSimpleNotification(User $user, string $type, string $title, string $content): Notification
    {
        $notification = new Notification();
        $notification->setUserId((string) $user->getId());
        $notification->setType($type);
        $notification->setTitle($title);
        $notification->setContent($content);

        $this->entityManager->persist($notification);
        $this->entityManager->flush();

        return $notification;
    }
}
