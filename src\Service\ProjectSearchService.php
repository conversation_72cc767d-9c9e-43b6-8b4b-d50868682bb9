<?php

namespace App\Service;

use App\Entity\Project;
use App\Repository\ProjectRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;

class ProjectSearchService
{
    private EntityManagerInterface $entityManager;
    private ProjectRepository $projectRepository;
    private Security $security;

    public function __construct(
        EntityManagerInterface $entityManager,
        ProjectRepository $projectRepository,
        Security $security
    ) {
        $this->entityManager = $entityManager;
        $this->projectRepository = $projectRepository;
        $this->security = $security;
    }

    /**
     * Search projects with advanced filters
     */
    public function searchProjects(array $criteria, int $page = 1, int $limit = 10): array
    {
        $queryBuilder = $this->projectRepository->createQueryBuilder('p')
            ->leftJoin('p.partner', 'partner')
            ->leftJoin('p.manager', 'manager');

        // Apply filters
        if (!empty($criteria['name'])) {
            $queryBuilder->andWhere('p.name LIKE :name')
                ->setParameter('name', '%' . $criteria['name'] . '%');
        }

        if (!empty($criteria['code'])) {
            $queryBuilder->andWhere('p.code LIKE :code')
                ->setParameter('code', '%' . $criteria['code'] . '%');
        }

        if (!empty($criteria['status'])) {
            $queryBuilder->andWhere('p.status = :status')
                ->setParameter('status', $criteria['status']);
        }

        if (!empty($criteria['priority'])) {
            $queryBuilder->andWhere('p.priority = :priority')
                ->setParameter('priority', $criteria['priority']);
        }

        if (!empty($criteria['partner'])) {
            $queryBuilder->andWhere('p.partner = :partner')
                ->setParameter('partner', $criteria['partner']);
        }

        if (!empty($criteria['manager'])) {
            $queryBuilder->andWhere('p.manager = :manager')
                ->setParameter('manager', $criteria['manager']);
        }

        if (!empty($criteria['start_date_from'])) {
            $queryBuilder->andWhere('p.startDate >= :startDateFrom')
                ->setParameter('startDateFrom', $criteria['start_date_from']);
        }

        if (!empty($criteria['start_date_to'])) {
            $queryBuilder->andWhere('p.startDate <= :startDateTo')
                ->setParameter('startDateTo', $criteria['start_date_to']);
        }

        if (!empty($criteria['end_date_from'])) {
            $queryBuilder->andWhere('p.endDate >= :endDateFrom')
                ->setParameter('endDateFrom', $criteria['end_date_from']);
        }

        if (!empty($criteria['end_date_to'])) {
            $queryBuilder->andWhere('p.endDate <= :endDateTo')
                ->setParameter('endDateTo', $criteria['end_date_to']);
        }

        if (!empty($criteria['budget_min'])) {
            $queryBuilder->andWhere('p.budget >= :budgetMin')
                ->setParameter('budgetMin', $criteria['budget_min']);
        }

        if (!empty($criteria['budget_max'])) {
            $queryBuilder->andWhere('p.budget <= :budgetMax')
                ->setParameter('budgetMax', $criteria['budget_max']);
        }

        if (!empty($criteria['progress_min'])) {
            $queryBuilder->andWhere('p.progress >= :progressMin')
                ->setParameter('progressMin', $criteria['progress_min']);
        }

        if (!empty($criteria['progress_max'])) {
            $queryBuilder->andWhere('p.progress <= :progressMax')
                ->setParameter('progressMax', $criteria['progress_max']);
        }

        // Filter out template projects
        $queryBuilder->andWhere('p.isTemplate = :isTemplate')
            ->setParameter('isTemplate', false);

        // Apply sorting
        $sortField = !empty($criteria['sort_field']) ? $criteria['sort_field'] : 'createdAt';
        $sortDirection = !empty($criteria['sort_direction']) ? $criteria['sort_direction'] : 'DESC';

        // Handle special sort fields
        if ($sortField === 'partner') {
            $queryBuilder->orderBy('partner.name', $sortDirection);
        } elseif ($sortField === 'manager') {
            $queryBuilder->orderBy('manager.firstName', $sortDirection);
        } else {
            $queryBuilder->orderBy('p.' . $sortField, $sortDirection);
        }

        // Get total count
        $countQueryBuilder = clone $queryBuilder;
        $countQueryBuilder->select('COUNT(p.id)');
        $total = $countQueryBuilder->getQuery()->getSingleScalarResult();

        // Apply pagination
        $queryBuilder->setMaxResults($limit)
            ->setFirstResult(($page - 1) * $limit);

        // Get results
        $projects = $queryBuilder->getQuery()->getResult();

        return [
            'projects' => $projects,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }

    /**
     * Get project statistics
     */
    public function getProjectStatistics(): array
    {
        // Get total projects
        $totalProjects = $this->projectRepository->count(['isTemplate' => false]);

        // Get projects by status
        $projectsByStatus = $this->projectRepository->createQueryBuilder('p')
            ->select('p.status, COUNT(p.id) as count')
            ->where('p.isTemplate = :isTemplate')
            ->setParameter('isTemplate', false)
            ->groupBy('p.status')
            ->getQuery()
            ->getResult();

        // Get projects by priority
        $projectsByPriority = $this->projectRepository->createQueryBuilder('p')
            ->select('p.priority, COUNT(p.id) as count')
            ->where('p.isTemplate = :isTemplate')
            ->setParameter('isTemplate', false)
            ->groupBy('p.priority')
            ->getQuery()
            ->getResult();

        // Get projects by month (last 12 months)
        $startDate = new \DateTime();
        $startDate->modify('-11 months');
        $startDate->setDate($startDate->format('Y'), $startDate->format('m'), 1);
        $startDate->setTime(0, 0, 0);

        // Utiliser une approche compatible avec MySQL
        $conn = $this->entityManager->getConnection();
        $sql = "
            SELECT
                YEAR(p.created_at) as year,
                MONTH(p.created_at) as month,
                COUNT(p.id) as count
            FROM project p
            WHERE p.created_at >= :startDate
            AND p.is_template = 0
            GROUP BY year, month
            ORDER BY year, month ASC
        ";

        $stmt = $conn->prepare($sql);
        $stmt->bindValue('startDate', $startDate->format('Y-m-d H:i:s'));
        $projectsByMonth = $stmt->executeQuery()->fetchAllAssociative();

        // Format projects by month for chart
        $monthLabels = [];
        $monthData = [];

        // Initialize with zeros for all months
        for ($i = 0; $i < 12; $i++) {
            $date = clone $startDate;
            $date->modify('+' . $i . ' months');
            $monthLabels[] = $date->format('M Y');
            $monthData[] = 0;
        }

        // Fill in actual data
        foreach ($projectsByMonth as $item) {
            $date = new \DateTime();
            $date->setDate($item['year'], $item['month'], 1);
            $monthIndex = (int)$date->diff($startDate)->format('%m') + (12 * (int)$date->diff($startDate)->format('%y'));
            if ($monthIndex >= 0 && $monthIndex < 12) {
                $monthData[$monthIndex] = (int)$item['count'];
            }
        }

        return [
            'total_projects' => $totalProjects,
            'projects_by_status' => $projectsByStatus,
            'projects_by_priority' => $projectsByPriority,
            'projects_by_month' => [
                'labels' => $monthLabels,
                'data' => $monthData
            ]
        ];
    }
}
