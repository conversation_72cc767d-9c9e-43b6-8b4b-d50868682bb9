<?php

namespace App\Service;

use App\Entity\PurchaseRequest;
use App\Entity\PurchaseOrder;
use App\Entity\Stock\StockItem;
use App\Entity\Stock\StockLocation;
use App\Entity\Stock\StockMovement;
use App\Entity\Product;
use App\Entity\Project;
use App\Entity\ProjectStockAllocation;
use App\Entity\Task;
use App\Entity\Employee;
use App\Entity\User;
use App\Service\NotificationService;
use Doctrine\ORM\EntityManagerInterface;

class PurchaseStockIntegrationService
{
    private EntityManagerInterface $entityManager;
    private NotificationService $notificationService;

    public function __construct(
        EntityManagerInterface $entityManager,
        NotificationService $notificationService
    ) {
        $this->entityManager = $entityManager;
        $this->notificationService = $notificationService;
    }

    /**
     * Workflow complet: Demande d'achat → Réception → Stock (Version simplifiée)
     */
    public function processPurchaseToStock(PurchaseRequest $purchaseRequest, array $receivedItems): array
    {
        $results = [];

        try {
            // Simulation du traitement pour l'instant
            foreach ($receivedItems as $item) {
                $results[] = [
                    'product' => $item['product_name'] ?? 'Produit',
                    'quantity' => $item['quantity'] ?? 1,
                    'status' => 'processed'
                ];
            }

            // Mettre à jour le statut de la demande d'achat
            $purchaseRequest->setStatus('received');
            $this->entityManager->persist($purchaseRequest);
            $this->entityManager->flush();

            return [
                'success' => true,
                'message' => 'Réception traitée avec succès',
                'items_processed' => count($results),
                'results' => $results
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Erreur lors du traitement: ' . $e->getMessage(),
                'results' => []
            ];
        }
    }

    /**
     * Traiter un article reçu
     */
    private function processReceivedItem(PurchaseRequest $purchaseRequest, array $itemData): array
    {
        // Récupérer ou créer le produit
        $product = $this->getOrCreateProduct($itemData);
        
        // Récupérer l'emplacement de stock
        $location = $this->getStockLocation($itemData['location_id'] ?? null);
        
        // Créer ou mettre à jour l'item de stock
        $stockItem = $this->createOrUpdateStockItem($product, $location, $itemData);
        
        // Créer le mouvement de stock
        $movement = $this->createStockMovement($stockItem, $itemData, 'purchase_receipt');
        
        // Si lié à un projet, créer l'allocation automatique
        $allocation = null;
        if ($purchaseRequest->getProject()) {
            $allocation = $this->createProjectAllocation(
                $purchaseRequest->getProject(),
                $stockItem,
                $itemData['quantity']
            );
        }

        return [
            'product' => $product->getName(),
            'quantity' => $itemData['quantity'],
            'location' => $location->getName(),
            'stock_item_id' => $stockItem->getId(),
            'movement_id' => $movement->getId(),
            'allocation_id' => $allocation ? $allocation->getId() : null
        ];
    }

    /**
     * Récupérer ou créer un produit (version simplifiée)
     */
    private function getOrCreateProduct(array $itemData): Product
    {
        $productRepo = $this->entityManager->getRepository(Product::class);

        // Chercher par nom
        $product = null;
        if (isset($itemData['product_name'])) {
            $product = $productRepo->findOneBy(['name' => $itemData['product_name']]);
        }

        // Créer le produit s'il n'existe pas
        if (!$product) {
            $product = new Product();
            $product->setName($itemData['product_name']);
            $product->setDescription($itemData['description'] ?? '');

            $this->entityManager->persist($product);
        }

        return $product;
    }

    /**
     * Récupérer l'emplacement de stock
     */
    private function getStockLocation(?int $locationId): StockLocation
    {
        if ($locationId) {
            $location = $this->entityManager->getRepository(StockLocation::class)->find($locationId);
            if ($location) {
                return $location;
            }
        }
        
        // Emplacement par défaut
        $defaultLocation = $this->entityManager->getRepository(StockLocation::class)
            ->findOneBy(['isDefault' => true]);
            
        if (!$defaultLocation) {
            // Créer un emplacement par défaut
            $defaultLocation = new StockLocation();
            $defaultLocation->setName('Entrepôt principal');
            $defaultLocation->setCode('MAIN');
            $defaultLocation->setIsDefault(true);
            $defaultLocation->setIsActive(true);
            
            $this->entityManager->persist($defaultLocation);
        }
        
        return $defaultLocation;
    }

    /**
     * Créer ou mettre à jour un item de stock
     */
    private function createOrUpdateStockItem(Product $product, StockLocation $location, array $itemData): StockItem
    {
        // Chercher un item existant
        $stockItem = $this->entityManager->getRepository(StockItem::class)
            ->findOneBy([
                'product' => $product,
                'location' => $location
            ]);
        
        if (!$stockItem) {
            $stockItem = new StockItem();
            $stockItem->setProduct($product);
            $stockItem->setLocation($location);
            $stockItem->setQuantity(0);
        }
        
        // Ajouter la quantité reçue
        $stockItem->setQuantity($stockItem->getQuantity() + $itemData['quantity']);
        
        // Mettre à jour les informations si fournies
        if (isset($itemData['lot_number'])) {
            $stockItem->setLotNumber($itemData['lot_number']);
        }
        
        if (isset($itemData['expiry_date'])) {
            $stockItem->setExpiryDate(new \DateTime($itemData['expiry_date']));
        }
        
        $this->entityManager->persist($stockItem);
        
        return $stockItem;
    }

    /**
     * Créer un mouvement de stock
     */
    private function createStockMovement(StockItem $stockItem, array $itemData, string $type): StockMovement
    {
        $movement = new StockMovement();
        $movement->setStockItem($stockItem);
        $movement->setType($type);
        $movement->setQuantity($itemData['quantity']);
        $movement->setReference($itemData['purchase_reference'] ?? 'PURCHASE-' . date('Y-m-d'));
        $movement->setNotes('Réception automatique depuis demande d\'achat');
        $movement->setMovementDate(new \DateTime());
        
        $this->entityManager->persist($movement);
        
        return $movement;
    }

    /**
     * Créer une allocation automatique pour le projet
     */
    private function createProjectAllocation(Project $project, StockItem $stockItem, int $quantity): ProjectStockAllocation
    {
        $allocation = new ProjectStockAllocation();
        $allocation->setProject($project);
        $allocation->setStockItem($stockItem);
        $allocation->setAllocatedQuantity($quantity);
        $allocation->setNotes('Allocation automatique depuis achat projet');
        
        $this->entityManager->persist($allocation);
        
        return $allocation;
    }

    /**
     * Envoyer les notifications de mise à jour stock
     */
    private function sendStockUpdateNotifications(PurchaseRequest $purchaseRequest, array $results): void
    {
        // Notification au chef de projet
        if ($purchaseRequest->getProject() && $purchaseRequest->getProject()->getManager()) {
            $message = "Réception terminée pour la demande d'achat {$purchaseRequest->getReference()}. ";
            $message .= count($results) . " articles ajoutés au stock.";
            
            $this->notificationService->sendNotification(
                $purchaseRequest->getProject()->getManager(),
                'Stock mis à jour',
                $message
            );
        }
        
        // Notification au gestionnaire de stock
        $stockManagers = $this->entityManager->getRepository(User::class)
            ->findByRole('ROLE_STOCK_MANAGER');
            
        foreach ($stockManagers as $manager) {
            $this->notificationService->sendNotification(
                $manager,
                'Nouvelle réception stock',
                "Réception automatique: {$purchaseRequest->getReference()}"
            );
        }
    }

    /**
     * Workflow: Tâche → Demande d'achat → Stock (Version simplifiée)
     */
    public function createPurchaseFromTask(Task $task, array $purchaseData): array
    {
        // Simulation pour l'instant
        return [
            'success' => true,
            'message' => 'Demande d\'achat créée avec succès',
            'task_title' => $task->getTitle(),
            'estimated_amount' => $purchaseData['amount'] ?? 0
        ];
    }

    /**
     * Générer un rapport d'intégration
     */
    public function generateIntegrationReport(): array
    {
        $purchaseRequests = $this->entityManager->getRepository(PurchaseRequest::class)
            ->findBy(['status' => 'received'], null, 10);
            
        $allocations = $this->entityManager->getRepository(ProjectStockAllocation::class)
            ->findBy([], ['createdAt' => 'DESC'], 10);
            
        return [
            'recent_receptions' => count($purchaseRequests),
            'active_allocations' => count($allocations),
            'integration_status' => 'Opérationnel',
            'last_update' => new \DateTime()
        ];
    }
}
