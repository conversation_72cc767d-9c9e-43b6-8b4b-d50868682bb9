<?php

namespace App\Service;

use App\Entity\SecurityLog;
use App\Entity\User;
use App\Repository\SecurityLogRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\SecurityBundle\Security;
use Psr\Log\LoggerInterface;

class SecurityService
{
    private EntityManagerInterface $entityManager;
    private SecurityLogRepository $securityLogRepository;
    private LoggerInterface $logger;
    private Security $security;

    public function __construct(
        EntityManagerInterface $entityManager,
        SecurityLogRepository $securityLogRepository,
        LoggerInterface $logger,
        Security $security
    ) {
        $this->entityManager = $entityManager;
        $this->securityLogRepository = $securityLogRepository;
        $this->logger = $logger;
        $this->security = $security;
    }

    /**
     * Enregistre un événement de sécurité
     */
    public function logSecurityEvent(
        string $eventType,
        ?User $user = null,
        ?Request $request = null,
        string $severity = SecurityLog::SEVERITY_MEDIUM,
        ?string $details = null,
        ?array $metadata = null
    ): SecurityLog {
        $securityLog = new SecurityLog();
        $securityLog->setEventType($eventType);
        $securityLog->setSeverity($severity);
        
        if ($user) {
            $securityLog->setUser($user);
        } elseif ($this->security->getUser()) {
            $securityLog->setUser($this->security->getUser());
        }

        if ($request) {
            $securityLog->setIpAddress($this->getClientIp($request));
            $securityLog->setUserAgent($request->headers->get('User-Agent'));
            $securityLog->setResource($request->getPathInfo());
            $securityLog->setMethod($request->getMethod());
            $securityLog->setSessionId($request->getSession()->getId());
        }

        if ($details) {
            $securityLog->setDetails($details);
        }

        if ($metadata) {
            $securityLog->setMetadata($metadata);
        }

        $this->entityManager->persist($securityLog);
        $this->entityManager->flush();

        // Log également dans les logs système
        $this->logger->info('Security event logged', [
            'event_type' => $eventType,
            'user_id' => $user?->getId(),
            'ip_address' => $securityLog->getIpAddress(),
            'severity' => $severity
        ]);

        return $securityLog;
    }

    /**
     * Vérifie si une adresse IP est bloquée pour tentatives de connexion
     */
    public function isIpBlocked(string $ipAddress): bool
    {
        $since = new \DateTime('-1 hour');
        $failedAttempts = $this->securityLogRepository->countFailedLoginAttempts($ipAddress, $since);
        
        return $failedAttempts >= 5; // Bloque après 5 tentatives échouées en 1 heure
    }

    /**
     * Enregistre une tentative de connexion réussie
     */
    public function logSuccessfulLogin(User $user, Request $request): void
    {
        $this->logSecurityEvent(
            SecurityLog::EVENT_LOGIN_SUCCESS,
            $user,
            $request,
            SecurityLog::SEVERITY_LOW,
            'User logged in successfully'
        );
    }

    /**
     * Enregistre une tentative de connexion échouée
     */
    public function logFailedLogin(?string $username, Request $request, string $reason = ''): void
    {
        $details = 'Failed login attempt';
        if ($username) {
            $details .= " for username: {$username}";
        }
        if ($reason) {
            $details .= " - Reason: {$reason}";
        }

        $this->logSecurityEvent(
            SecurityLog::EVENT_LOGIN_FAILED,
            null,
            $request,
            SecurityLog::SEVERITY_MEDIUM,
            $details,
            ['username' => $username, 'reason' => $reason]
        );
    }

    /**
     * Enregistre une déconnexion
     */
    public function logLogout(User $user, Request $request): void
    {
        $this->logSecurityEvent(
            SecurityLog::EVENT_LOGOUT,
            $user,
            $request,
            SecurityLog::SEVERITY_LOW,
            'User logged out'
        );
    }

    /**
     * Enregistre un changement de mot de passe
     */
    public function logPasswordChange(User $user, Request $request): void
    {
        $this->logSecurityEvent(
            SecurityLog::EVENT_PASSWORD_CHANGE,
            $user,
            $request,
            SecurityLog::SEVERITY_MEDIUM,
            'User changed password'
        );
    }

    /**
     * Enregistre l'activation de la 2FA
     */
    public function log2FAEnabled(User $user, Request $request): void
    {
        $this->logSecurityEvent(
            SecurityLog::EVENT_2FA_ENABLED,
            $user,
            $request,
            SecurityLog::SEVERITY_MEDIUM,
            'Two-factor authentication enabled'
        );
    }

    /**
     * Enregistre la désactivation de la 2FA
     */
    public function log2FADisabled(User $user, Request $request): void
    {
        $this->logSecurityEvent(
            SecurityLog::EVENT_2FA_DISABLED,
            $user,
            $request,
            SecurityLog::SEVERITY_HIGH,
            'Two-factor authentication disabled'
        );
    }

    /**
     * Enregistre un accès refusé
     */
    public function logPermissionDenied(User $user, Request $request, string $resource): void
    {
        $this->logSecurityEvent(
            SecurityLog::EVENT_PERMISSION_DENIED,
            $user,
            $request,
            SecurityLog::SEVERITY_HIGH,
            "Access denied to resource: {$resource}",
            ['resource' => $resource]
        );
    }

    /**
     * Enregistre une activité suspecte
     */
    public function logSuspiciousActivity(
        ?User $user,
        Request $request,
        string $description,
        array $metadata = []
    ): void {
        $this->logSecurityEvent(
            SecurityLog::EVENT_SUSPICIOUS_ACTIVITY,
            $user,
            $request,
            SecurityLog::SEVERITY_HIGH,
            "Suspicious activity detected: {$description}",
            $metadata
        );
    }

    /**
     * Obtient l'adresse IP réelle du client
     */
    private function getClientIp(Request $request): string
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP'];
        
        foreach ($ipKeys as $key) {
            if ($request->server->has($key)) {
                $ip = $request->server->get($key);
                if (!empty($ip) && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $request->getClientIp() ?? '0.0.0.0';
    }

    /**
     * Obtient les statistiques de sécurité récentes
     */
    public function getSecurityStats(?\DateTime $since = null): array
    {
        if (!$since) {
            $since = new \DateTime('-7 days');
        }

        return [
            'event_types' => $this->securityLogRepository->getEventTypeStats($since),
            'severity_levels' => $this->securityLogRepository->getSeverityStats($since),
            'top_ips' => $this->securityLogRepository->getTopIpAddresses($since),
            'suspicious_activities' => $this->securityLogRepository->findSuspiciousActivities($since),
            'period' => $since->format('Y-m-d H:i:s') . ' to ' . (new \DateTime())->format('Y-m-d H:i:s')
        ];
    }

    /**
     * Nettoie les anciens logs de sécurité
     */
    public function cleanOldLogs(int $daysToKeep = 90): int
    {
        $before = new \DateTime("-{$daysToKeep} days");
        return $this->securityLogRepository->cleanOldLogs($before);
    }
}
