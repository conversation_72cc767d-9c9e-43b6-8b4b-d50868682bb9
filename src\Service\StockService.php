<?php

namespace App\Service;

use App\Entity\Product;
use App\Entity\Stock\Inventory;
use App\Entity\Stock\InventoryLine;
use App\Entity\StockAlert;
use App\Entity\Stock\StockItem;
use App\Entity\Stock\StockLocation;
use App\Entity\Stock\StockMovement;
use App\Entity\User;
use App\Repository\Stock\InventoryRepository;
// use App\Repository\StockAlertRepository; // TODO: Fix compatibility
use App\Repository\Stock\StockItemRepository;
use App\Repository\Stock\StockLocationRepository;
use App\Repository\Stock\StockMovementRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;

class StockService
{
    private EntityManagerInterface $entityManager;
    private StockItemRepository $stockItemRepository;
    private StockLocationRepository $stockLocationRepository;
    private StockMovementRepository $stockMovementRepository;
    // private StockAlertRepository $stockAlertRepository; // TODO: Fix compatibility
    private InventoryRepository $inventoryRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        StockItemRepository $stockItemRepository,
        StockLocationRepository $stockLocationRepository,
        StockMovementRepository $stockMovementRepository,
        StockAlertRepository $stockAlertRepository,
        InventoryRepository $inventoryRepository
    ) {
        $this->entityManager = $entityManager;
        $this->stockItemRepository = $stockItemRepository;
        $this->stockLocationRepository = $stockLocationRepository;
        $this->stockMovementRepository = $stockMovementRepository;
        $this->stockAlertRepository = $stockAlertRepository;
        $this->inventoryRepository = $inventoryRepository;
    }

    /**
     * Get or create a stock item for a product at a specific location
     */
    public function getOrCreateStockItem(Product $product, StockLocation $location, ?string $lotNumber = null, ?\DateTimeInterface $expiryDate = null): StockItem
    {
        $criteria = [
            'product' => $product,
            'location' => $location
        ];
        
        if ($lotNumber) {
            $criteria['lotNumber'] = $lotNumber;
        }
        
        if ($expiryDate) {
            $criteria['expiryDate'] = $expiryDate;
        }
        
        $stockItem = $this->stockItemRepository->findOneBy($criteria);
        
        if (!$stockItem) {
            $stockItem = new StockItem();
            $stockItem->setProduct($product);
            $stockItem->setLocation($location);
            $stockItem->setQuantity(0);
            $stockItem->setReservedQuantity(0);
            
            if ($lotNumber) {
                $stockItem->setLotNumber($lotNumber);
            }
            
            if ($expiryDate) {
                $stockItem->setExpiryDate($expiryDate);
            }
            
            $this->entityManager->persist($stockItem);
            $this->entityManager->flush();
        }
        
        return $stockItem;
    }

    /**
     * Add stock (inbound movement)
     */
    public function addStock(
        Product $product,
        StockLocation $location,
        float $quantity,
        User $user,
        string $source = StockMovement::SOURCE_MANUAL,
        ?string $referenceType = null,
        ?string $referenceNumber = null,
        ?string $reason = null,
        ?string $lotNumber = null,
        ?\DateTimeInterface $expiryDate = null
    ): StockMovement {
        if ($quantity <= 0) {
            throw new \InvalidArgumentException('Quantity must be greater than zero');
        }
        
        $stockItem = $this->getOrCreateStockItem($product, $location, $lotNumber, $expiryDate);
        
        // Create the movement
        $movement = new StockMovement();
        $movement->setType(StockMovement::TYPE_IN);
        $movement->setSource($source);
        $movement->setStockItem($stockItem);
        $movement->setQuantity($quantity);
        $movement->setCreatedBy($user);
        $movement->setMovementDate(new \DateTime());
        $movement->setDestinationLocation($location);
        
        if ($referenceType) {
            $movement->setReferenceType($referenceType);
        }
        
        if ($referenceNumber) {
            $movement->setReferenceNumber($referenceNumber);
        }
        
        if ($reason) {
            $movement->setReason($reason);
        }
        
        // Update the stock item quantity
        $stockItem->setQuantity($stockItem->getQuantity() + $quantity);
        $stockItem->setUpdatedAt(new \DateTime());
        
        $this->entityManager->persist($movement);
        $this->entityManager->flush();
        
        // Check if we need to clear any low stock alerts
        $this->checkAndUpdateLowStockAlert($stockItem);
        
        return $movement;
    }

    /**
     * Remove stock (outbound movement)
     */
    public function removeStock(
        Product $product,
        StockLocation $location,
        float $quantity,
        User $user,
        string $source = StockMovement::SOURCE_MANUAL,
        ?string $referenceType = null,
        ?string $referenceNumber = null,
        ?string $reason = null,
        ?string $lotNumber = null
    ): StockMovement {
        if ($quantity <= 0) {
            throw new \InvalidArgumentException('Quantity must be greater than zero');
        }
        
        $criteria = [
            'product' => $product,
            'location' => $location
        ];
        
        if ($lotNumber) {
            $criteria['lotNumber'] = $lotNumber;
        }
        
        $stockItem = $this->stockItemRepository->findOneBy($criteria);
        
        if (!$stockItem) {
            throw new \InvalidArgumentException('No stock found for this product at the specified location');
        }
        
        if ($stockItem->getAvailableQuantity() < $quantity) {
            throw new \InvalidArgumentException('Not enough available stock');
        }
        
        // Create the movement
        $movement = new StockMovement();
        $movement->setType(StockMovement::TYPE_OUT);
        $movement->setSource($source);
        $movement->setStockItem($stockItem);
        $movement->setQuantity(-$quantity); // Negative for outbound
        $movement->setCreatedBy($user);
        $movement->setMovementDate(new \DateTime());
        $movement->setSourceLocation($location);
        
        if ($referenceType) {
            $movement->setReferenceType($referenceType);
        }
        
        if ($referenceNumber) {
            $movement->setReferenceNumber($referenceNumber);
        }
        
        if ($reason) {
            $movement->setReason($reason);
        }
        
        // Update the stock item quantity
        $stockItem->setQuantity($stockItem->getQuantity() - $quantity);
        $stockItem->setUpdatedAt(new \DateTime());
        
        $this->entityManager->persist($movement);
        $this->entityManager->flush();
        
        // Check if we need to create a low stock alert
        $this->checkAndUpdateLowStockAlert($stockItem);
        
        return $movement;
    }

    /**
     * Transfer stock between locations
     */
    public function transferStock(
        Product $product,
        StockLocation $sourceLocation,
        StockLocation $destinationLocation,
        float $quantity,
        User $user,
        ?string $referenceType = null,
        ?string $referenceNumber = null,
        ?string $reason = null,
        ?string $lotNumber = null,
        ?\DateTimeInterface $expiryDate = null
    ): StockMovement {
        if ($quantity <= 0) {
            throw new \InvalidArgumentException('Quantity must be greater than zero');
        }
        
        $criteria = [
            'product' => $product,
            'location' => $sourceLocation
        ];
        
        if ($lotNumber) {
            $criteria['lotNumber'] = $lotNumber;
        }
        
        $sourceStockItem = $this->stockItemRepository->findOneBy($criteria);
        
        if (!$sourceStockItem) {
            throw new \InvalidArgumentException('No stock found for this product at the source location');
        }
        
        if ($sourceStockItem->getAvailableQuantity() < $quantity) {
            throw new \InvalidArgumentException('Not enough available stock at the source location');
        }
        
        $destinationStockItem = $this->getOrCreateStockItem(
            $product,
            $destinationLocation,
            $lotNumber,
            $expiryDate ?? $sourceStockItem->getExpiryDate()
        );
        
        // Create the movement
        $movement = new StockMovement();
        $movement->setType(StockMovement::TYPE_TRANSFER);
        $movement->setSource(StockMovement::SOURCE_MANUAL);
        $movement->setStockItem($sourceStockItem);
        $movement->setQuantity($quantity);
        $movement->setCreatedBy($user);
        $movement->setMovementDate(new \DateTime());
        $movement->setSourceLocation($sourceLocation);
        $movement->setDestinationLocation($destinationLocation);
        
        if ($referenceType) {
            $movement->setReferenceType($referenceType);
        }
        
        if ($referenceNumber) {
            $movement->setReferenceNumber($referenceNumber);
        }
        
        if ($reason) {
            $movement->setReason($reason);
        }
        
        // Update the stock quantities
        $sourceStockItem->setQuantity($sourceStockItem->getQuantity() - $quantity);
        $sourceStockItem->setUpdatedAt(new \DateTime());
        
        $destinationStockItem->setQuantity($destinationStockItem->getQuantity() + $quantity);
        $destinationStockItem->setUpdatedAt(new \DateTime());
        
        $this->entityManager->persist($movement);
        $this->entityManager->flush();
        
        // Check if we need to update low stock alerts
        $this->checkAndUpdateLowStockAlert($sourceStockItem);
        $this->checkAndUpdateLowStockAlert($destinationStockItem);
        
        return $movement;
    }

    /**
     * Adjust stock quantity
     */
    public function adjustStock(
        Product $product,
        StockLocation $location,
        float $newQuantity,
        User $user,
        ?string $reason = null,
        ?string $lotNumber = null,
        ?\DateTimeInterface $expiryDate = null
    ): StockMovement {
        $stockItem = $this->getOrCreateStockItem($product, $location, $lotNumber, $expiryDate);
        
        $difference = $newQuantity - $stockItem->getQuantity();
        
        if ($difference == 0) {
            throw new \InvalidArgumentException('New quantity is the same as current quantity');
        }
        
        // Create the movement
        $movement = new StockMovement();
        $movement->setType(StockMovement::TYPE_ADJUSTMENT);
        $movement->setSource(StockMovement::SOURCE_INVENTORY);
        $movement->setStockItem($stockItem);
        $movement->setQuantity($difference);
        $movement->setCreatedBy($user);
        $movement->setMovementDate(new \DateTime());
        
        if ($difference > 0) {
            $movement->setDestinationLocation($location);
        } else {
            $movement->setSourceLocation($location);
        }
        
        if ($reason) {
            $movement->setReason($reason);
        }
        
        // Update the stock item quantity
        $stockItem->setQuantity($newQuantity);
        $stockItem->setUpdatedAt(new \DateTime());
        
        $this->entityManager->persist($movement);
        $this->entityManager->flush();
        
        // Check if we need to update low stock alerts
        $this->checkAndUpdateLowStockAlert($stockItem);
        
        return $movement;
    }

    /**
     * Reserve stock
     */
    public function reserveStock(Product $product, float $quantity, ?string $lotNumber = null): bool
    {
        if ($quantity <= 0) {
            throw new \InvalidArgumentException('Quantity must be greater than zero');
        }
        
        $criteria = ['product' => $product];
        
        if ($lotNumber) {
            $criteria['lotNumber'] = $lotNumber;
        }
        
        $stockItems = $this->stockItemRepository->findBy($criteria, ['expiryDate' => 'ASC']);
        
        $remainingQuantity = $quantity;
        $success = false;
        
        foreach ($stockItems as $stockItem) {
            $availableQuantity = $stockItem->getAvailableQuantity();
            
            if ($availableQuantity > 0) {
                $reserveQuantity = min($availableQuantity, $remainingQuantity);
                $stockItem->reserveQuantity($reserveQuantity);
                $stockItem->setUpdatedAt(new \DateTime());
                
                $remainingQuantity -= $reserveQuantity;
                $success = true;
                
                if ($remainingQuantity <= 0) {
                    break;
                }
            }
        }
        
        $this->entityManager->flush();
        
        return $success && $remainingQuantity <= 0;
    }

    /**
     * Release reserved stock
     */
    public function releaseReservedStock(Product $product, float $quantity, ?string $lotNumber = null): bool
    {
        if ($quantity <= 0) {
            throw new \InvalidArgumentException('Quantity must be greater than zero');
        }
        
        $criteria = ['product' => $product];
        
        if ($lotNumber) {
            $criteria['lotNumber'] = $lotNumber;
        }
        
        $stockItems = $this->stockItemRepository->findBy($criteria, ['expiryDate' => 'DESC']);
        
        $remainingQuantity = $quantity;
        $success = false;
        
        foreach ($stockItems as $stockItem) {
            $reservedQuantity = $stockItem->getReservedQuantity();
            
            if ($reservedQuantity > 0) {
                $releaseQuantity = min($reservedQuantity, $remainingQuantity);
                $stockItem->releaseReservedQuantity($releaseQuantity);
                $stockItem->setUpdatedAt(new \DateTime());
                
                $remainingQuantity -= $releaseQuantity;
                $success = true;
                
                if ($remainingQuantity <= 0) {
                    break;
                }
            }
        }
        
        $this->entityManager->flush();
        
        return $success && $remainingQuantity <= 0;
    }

    /**
     * Create a new inventory
     */
    public function createInventory(string $name, array $locationIds, User $user): Inventory
    {
        $inventory = new Inventory();
        $inventory->setName($name);
        $inventory->setInventoryNumber($this->inventoryRepository->generateInventoryNumber());
        $inventory->setCreatedBy($user);
        $inventory->setStatus(Inventory::STATUS_DRAFT);
        
        foreach ($locationIds as $locationId) {
            $location = $this->stockLocationRepository->find($locationId);
            if ($location) {
                $inventory->addLocation($location);
            }
        }
        
        $this->entityManager->persist($inventory);
        $this->entityManager->flush();
        
        return $inventory;
    }

    /**
     * Generate inventory lines for an inventory
     */
    public function generateInventoryLines(Inventory $inventory): void
    {
        if ($inventory->getStatus() !== Inventory::STATUS_DRAFT) {
            throw new \InvalidArgumentException('Inventory must be in draft status to generate lines');
        }
        
        // Clear existing lines
        foreach ($inventory->getLines() as $line) {
            $inventory->removeLine($line);
            $this->entityManager->remove($line);
        }
        
        // Get all locations for this inventory
        $locations = $inventory->getLocations();
        
        foreach ($locations as $location) {
            $stockItems = $this->stockItemRepository->findByLocation($location->getId());
            
            foreach ($stockItems as $stockItem) {
                $line = new InventoryLine();
                $line->setInventory($inventory);
                $line->setProduct($stockItem->getProduct());
                $line->setLocation($location);
                $line->setExpectedQuantity($stockItem->getQuantity());
                $line->setLotNumber($stockItem->getLotNumber());
                $line->setExpiryDate($stockItem->getExpiryDate());
                $line->setStockItem($stockItem);
                
                $this->entityManager->persist($line);
                $inventory->addLine($line);
            }
        }
        
        $this->entityManager->flush();
    }

    /**
     * Start an inventory
     */
    public function startInventory(Inventory $inventory, User $user): void
    {
        if (!$inventory->canBeStarted()) {
            throw new \InvalidArgumentException('Inventory cannot be started');
        }
        
        $inventory->setStatus(Inventory::STATUS_IN_PROGRESS);
        $inventory->setUpdatedAt(new \DateTime());
        
        $this->entityManager->flush();
    }

    /**
     * Complete an inventory
     */
    public function completeInventory(Inventory $inventory, User $user, bool $applyAdjustments = true): void
    {
        if (!$inventory->canBeCompleted()) {
            throw new \InvalidArgumentException('Inventory cannot be completed');
        }
        
        if (!$inventory->areAllLinesCompleted()) {
            throw new \InvalidArgumentException('All inventory lines must be completed before completing the inventory');
        }
        
        $inventory->setStatus(Inventory::STATUS_COMPLETED);
        $inventory->setEndDate(new \DateTime());
        $inventory->setCompletedBy($user);
        $inventory->setUpdatedAt(new \DateTime());
        
        if ($applyAdjustments) {
            foreach ($inventory->getLines() as $line) {
                if ($line->getDifference() != 0) {
                    $this->adjustStock(
                        $line->getProduct(),
                        $line->getLocation(),
                        $line->getCountedQuantity(),
                        $user,
                        'Inventory adjustment: ' . $inventory->getInventoryNumber(),
                        $line->getLotNumber(),
                        $line->getExpiryDate()
                    );
                    
                    // Create discrepancy alert
                    $this->createDiscrepancyAlert($line);
                }
            }
        }
        
        $this->entityManager->flush();
    }

    /**
     * Cancel an inventory
     */
    public function cancelInventory(Inventory $inventory): void
    {
        if (!$inventory->canBeCancelled()) {
            throw new \InvalidArgumentException('Inventory cannot be cancelled');
        }
        
        $inventory->setStatus(Inventory::STATUS_CANCELLED);
        $inventory->setUpdatedAt(new \DateTime());
        
        $this->entityManager->flush();
    }

    /**
     * Update inventory line count
     */
    public function updateInventoryLineCount(InventoryLine $line, float $countedQuantity, User $user): void
    {
        if (!$line->getInventory()->canBeEdited()) {
            throw new \InvalidArgumentException('Inventory cannot be edited');
        }
        
        $line->setCountedQuantity($countedQuantity);
        $line->setIsCompleted(true);
        $line->setCountedAt(new \DateTime());
        
        $this->entityManager->flush();
    }

    /**
     * Check and update low stock alert
     */
    private function checkAndUpdateLowStockAlert(StockItem $stockItem): void
    {
        // TODO: Implement after fixing StockAlert entity compatibility
        return;
    }

    /**
     * Create discrepancy alert
     */
    private function createDiscrepancyAlert(InventoryLine $line): void
    {
        // TODO: Implement after fixing StockAlert entity compatibility
        return;
    }

    /**
     * Check for expiring products and create alerts
     */
    public function checkForExpiringProducts(int $daysThreshold = 30): int
    {
        // TODO: Implement after fixing StockAlert entity compatibility
        return 0;
    }

    /**
     * Get stock status for a product
     */
    public function getProductStockStatus(Product $product): array
    {
        $stockItems = $this->stockItemRepository->findByProduct($product->getId());
        $totalQuantity = 0;
        $reservedQuantity = 0;
        $availableQuantity = 0;
        $locations = [];
        
        foreach ($stockItems as $item) {
            $totalQuantity += $item->getQuantity();
            $reservedQuantity += $item->getReservedQuantity();
            $availableQuantity += $item->getAvailableQuantity();
            
            $locations[] = [
                'location' => $item->getLocation(),
                'quantity' => $item->getQuantity(),
                'reserved' => $item->getReservedQuantity(),
                'available' => $item->getAvailableQuantity(),
                'lotNumber' => $item->getLotNumber(),
                'expiryDate' => $item->getExpiryDate()
            ];
        }
        
        return [
            'product' => $product,
            'totalQuantity' => $totalQuantity,
            'reservedQuantity' => $reservedQuantity,
            'availableQuantity' => $availableQuantity,
            'locations' => $locations,
            'minStockLevel' => $product->getMinStockLevel(),
            'isLowStock' => $product->getMinStockLevel() && $totalQuantity < $product->getMinStockLevel()
        ];
    }

    /**
     * Get stock movements for a product
     */
    public function getProductMovements(Product $product, \DateTimeInterface $startDate = null, \DateTimeInterface $endDate = null): array
    {
        return $this->stockMovementRepository->findByProduct($product->getId(), $startDate, $endDate);
    }

    /**
     * Get stock dashboard data
     */
    public function getDashboardData(): array
    {
        $lowStockItems = $this->stockItemRepository->findLowStock();
        $expiringItems = $this->stockItemRepository->findAboutToExpire();
        $expiredItems = $this->stockItemRepository->findExpired();
        $recentMovements = $this->stockMovementRepository->findRecent(10);
        $activeInventories = $this->inventoryRepository->findActive();
        $alertCounts = []; // TODO: Implement after fixing StockAlert entity compatibility
        
        return [
            'lowStockCount' => count($lowStockItems),
            'expiringCount' => count($expiringItems),
            'expiredCount' => count($expiredItems),
            'recentMovements' => $recentMovements,
            'activeInventories' => $activeInventories,
            'alertCounts' => $alertCounts
        ];
    }
}
