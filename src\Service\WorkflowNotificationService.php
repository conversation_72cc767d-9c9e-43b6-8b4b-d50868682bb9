<?php

namespace App\Service;

use App\Entity\Project;
use App\Entity\Partner;
use App\Entity\Employee;
use App\Entity\Task;
use App\Entity\PurchaseRequest;
use App\Entity\Invoice;
use App\Entity\ProjectStockAllocation;
use App\Entity\User;
use App\Entity\Department;
use App\Service\NotificationService;
use Doctrine\ORM\EntityManagerInterface;

class WorkflowNotificationService
{
    private EntityManagerInterface $entityManager;
    private NotificationService $notificationService;

    public function __construct(
        EntityManagerInterface $entityManager,
        NotificationService $notificationService
    ) {
        $this->entityManager = $entityManager;
        $this->notificationService = $notificationService;
    }

    /**
     * Notifications automatiques pour les projets
     */
    public function handleProjectWorkflow(Project $project, string $event, array $context = []): void
    {
        switch ($event) {
            case 'project_created':
                $this->notifyProjectCreated($project, $context);
                break;
            case 'project_started':
                $this->notifyProjectStarted($project);
                break;
            case 'project_completed':
                $this->notifyProjectCompleted($project);
                break;
            case 'project_overdue':
                $this->notifyProjectOverdue($project);
                break;
            case 'budget_exceeded':
                $this->notifyBudgetExceeded($project, $context);
                break;
        }
    }

    /**
     * Notifications pour les partenaires
     */
    public function handlePartnerWorkflow(Partner $partner, string $event, array $context = []): void
    {
        switch ($event) {
            case 'partner_created':
                $this->notifyPartnerCreated($partner);
                break;
            case 'new_project_assigned':
                $this->notifyNewProjectAssigned($partner, $context['project']);
                break;
            case 'invoice_overdue':
                $this->notifyInvoiceOverdue($partner, $context['invoice']);
                break;
            case 'contract_renewal':
                $this->notifyContractRenewal($partner);
                break;
        }
    }

    /**
     * Notifications pour les achats
     */
    public function handlePurchaseWorkflow(PurchaseRequest $purchaseRequest, string $event, array $context = []): void
    {
        switch ($event) {
            case 'purchase_created':
                $this->notifyPurchaseCreated($purchaseRequest);
                break;
            case 'purchase_approved':
                $this->notifyPurchaseApproved($purchaseRequest);
                break;
            case 'purchase_rejected':
                $this->notifyPurchaseRejected($purchaseRequest, $context);
                break;
            case 'purchase_received':
                $this->notifyPurchaseReceived($purchaseRequest);
                break;
        }
    }

    /**
     * Notifications pour les tâches
     */
    public function handleTaskWorkflow(Task $task, string $event, array $context = []): void
    {
        switch ($event) {
            case 'task_created':
                $this->notifyTaskCreated($task);
                break;
            case 'task_assigned':
                $this->notifyTaskAssigned($task);
                break;
            case 'task_completed':
                $this->notifyTaskCompleted($task);
                break;
            case 'task_overdue':
                $this->notifyTaskOverdue($task);
                break;
        }
    }

    /**
     * Notifications pour les allocations stock
     */
    public function handleStockAllocationWorkflow(ProjectStockAllocation $allocation, string $event, array $context = []): void
    {
        switch ($event) {
            case 'stock_allocated':
                $this->notifyStockAllocated($allocation);
                break;
            case 'stock_low':
                $this->notifyStockLow($allocation);
                break;
            case 'allocation_expired':
                $this->notifyAllocationExpired($allocation);
                break;
        }
    }

    // Méthodes de notification spécifiques

    private function notifyProjectCreated(Project $project, array $context): void
    {
        // Notifier le chef de projet
        if ($project->getManager()) {
            $this->notificationService->sendNotification(
                $project->getManager(),
                'Nouveau projet assigné',
                "Vous avez été assigné comme chef du projet '{$project->getName()}'"
            );
        }

        // Notifier le département porteur
        if ($project->getOwnerDepartment()) {
            $departmentHead = $this->getDepartmentHead($project->getOwnerDepartment());
            if ($departmentHead) {
                $this->notificationService->sendNotification(
                    $departmentHead,
                    'Nouveau projet dans votre département',
                    "Le projet '{$project->getName()}' a été créé dans votre département"
                );
            }
        }

        // Notifier le responsable commercial du partenaire
        if ($project->getPartner() && $project->getPartner()->getAccountManager()) {
            $this->notificationService->sendNotification(
                $project->getPartner()->getAccountManager()->getUser(),
                'Nouveau projet client',
                "Un nouveau projet '{$project->getName()}' a été créé pour votre client {$project->getPartner()->getName()}"
            );
        }
    }

    private function notifyProjectCompleted(Project $project): void
    {
        // Déclencher le workflow de facturation client
        $this->triggerClientBillingWorkflow($project);

        // Notifier les parties prenantes
        $this->notificationService->sendNotification(
            $project->getManager(),
            'Projet terminé',
            "Le projet '{$project->getName()}' est maintenant terminé. Facturation client à générer."
        );
    }

    private function notifyPurchaseCreated(PurchaseRequest $purchaseRequest): void
    {
        // Notifier le chef de projet
        if ($purchaseRequest->getProject() && $purchaseRequest->getProject()->getManager()) {
            $this->notificationService->sendNotification(
                $purchaseRequest->getProject()->getManager(),
                'Nouvelle demande d\'achat',
                "Demande d'achat '{$purchaseRequest->getTitle()}' créée pour le projet '{$purchaseRequest->getProject()->getName()}'"
            );
        }

        // Notifier les approbateurs
        $approvers = $this->getApprovers($purchaseRequest);
        foreach ($approvers as $approver) {
            $this->notificationService->sendNotification(
                $approver,
                'Demande d\'achat à approuver',
                "Nouvelle demande d'achat en attente d'approbation: '{$purchaseRequest->getTitle()}'"
            );
        }
    }

    private function notifyStockAllocated(ProjectStockAllocation $allocation): void
    {
        // Notifier le chef de projet
        if ($allocation->getProject()->getManager()) {
            $this->notificationService->sendNotification(
                $allocation->getProject()->getManager(),
                'Stock alloué au projet',
                "Stock alloué: {$allocation->getAllocatedQuantity()} unités de {$allocation->getStockItem()->getProduct()->getName()}"
            );
        }

        // Notifier le gestionnaire de stock
        $stockManagers = $this->getStockManagers();
        foreach ($stockManagers as $manager) {
            $this->notificationService->sendNotification(
                $manager,
                'Nouvelle allocation stock',
                "Allocation de stock pour le projet '{$allocation->getProject()->getName()}'"
            );
        }
    }

    private function triggerClientBillingWorkflow(Project $project): void
    {
        // Calculer le montant à facturer
        $billingAmount = $this->calculateProjectBillingAmount($project);

        // Créer une notification pour le service comptabilité
        $accountingUsers = $this->getAccountingUsers();
        foreach ($accountingUsers as $user) {
            $this->notificationService->sendNotification(
                $user,
                'Facturation client à générer',
                "Projet '{$project->getName()}' terminé. Montant à facturer: {$billingAmount}€"
            );
        }

        // Marquer le projet pour facturation
        $project->setCategory('ready_for_billing');
        $this->entityManager->persist($project);
        $this->entityManager->flush();
    }

    // Méthodes utilitaires

    private function getDepartmentHead(Department $department): ?User
    {
        // Logique pour trouver le chef de département
        return null; // À implémenter selon la structure organisationnelle
    }

    private function getApprovers(PurchaseRequest $purchaseRequest): array
    {
        // Logique pour déterminer les approbateurs selon le montant et le type
        $users = [];
        
        if ($purchaseRequest->getEstimatedAmount() > 1000) {
            // Approbation niveau supérieur requise
            $managers = $this->entityManager->getRepository(User::class)
                ->findByRole('ROLE_PURCHASE_MANAGER');
            $users = array_merge($users, $managers);
        }
        
        return $users;
    }

    private function getStockManagers(): array
    {
        return $this->entityManager->getRepository(User::class)
            ->findByRole('ROLE_STOCK_MANAGER');
    }

    private function getAccountingUsers(): array
    {
        return $this->entityManager->getRepository(User::class)
            ->findByRole('ROLE_ACCOUNTING');
    }

    private function calculateProjectBillingAmount(Project $project): float
    {
        // Logique de calcul du montant de facturation
        return $project->getBudget(); // Simplifié pour l'exemple
    }

    /**
     * Traitement automatique des notifications en batch (Version simplifiée)
     */
    public function processAutomaticNotifications(): array
    {
        $processed = [];

        // Simulation du traitement pour l'instant
        $processed[] = "Vérification des projets en retard";
        $processed[] = "Vérification des tâches en retard";
        $processed[] = "Vérification des allocations expirées";
        $processed[] = "Notifications automatiques traitées";

        return $processed;
    }
}
