{% extends 'base.html.twig' %}

{% block title %}Gestion des sauvegardes{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .backup-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
            border: none;
        }
        
        .backup-card:hover {
            transform: translateY(-2px);
        }
        
        .status-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .disk-space-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .backup-item {
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
            border-radius: 0 8px 8px 0;
        }
        
        .backup-item:hover {
            background-color: #f8f9fa;
            border-left-color: #007bff;
        }
        
        .backup-actions {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .backup-item:hover .backup-actions {
            opacity: 1;
        }
        
        .progress-ring {
            width: 60px;
            height: 60px;
        }
        
        .progress-ring circle {
            fill: transparent;
            stroke-width: 4;
            stroke-linecap: round;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }
        
        .progress-ring .background {
            stroke: rgba(255, 255, 255, 0.3);
        }
        
        .progress-ring .progress {
            stroke: #fff;
            stroke-dasharray: 157;
            stroke-dashoffset: 157;
            transition: stroke-dashoffset 0.5s ease;
        }
        
        .backup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .create-backup-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .create-backup-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .create-backup-btn:disabled {
            background: #6c757d;
            transform: none;
            box-shadow: none;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .backup-size {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        class BackupManager {
            constructor() {
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.updateDiskSpaceProgress();
                this.checkBackupStatus();
            }

            setupEventListeners() {
                // Créer une sauvegarde
                document.getElementById('createBackupBtn')?.addEventListener('click', () => {
                    this.createBackup();
                });

                // Supprimer une sauvegarde
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('delete-backup-btn')) {
                        const filename = e.target.dataset.filename;
                        this.deleteBackup(filename);
                    }
                });

                // Restaurer une sauvegarde
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('restore-backup-btn')) {
                        const filename = e.target.dataset.filename;
                        this.restoreBackup(filename);
                    }
                });

                // Nettoyer les anciennes sauvegardes
                document.getElementById('cleanupBtn')?.addEventListener('click', () => {
                    this.cleanupBackups();
                });

                // Actualiser
                document.getElementById('refreshBtn')?.addEventListener('click', () => {
                    location.reload();
                });
            }

            async createBackup() {
                const btn = document.getElementById('createBackupBtn');
                const spinner = document.querySelector('.loading-spinner');
                
                btn.disabled = true;
                btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Création en cours...';
                spinner.style.display = 'block';

                try {
                    const response = await fetch('/admin/backup/create', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        this.showAlert('success', data.message);
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        this.showAlert('danger', data.message);
                    }
                } catch (error) {
                    this.showAlert('danger', 'Erreur lors de la création de la sauvegarde');
                } finally {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="bi bi-plus-circle"></i> Créer une sauvegarde';
                    spinner.style.display = 'none';
                }
            }

            async deleteBackup(filename) {
                if (!confirm('Êtes-vous sûr de vouloir supprimer cette sauvegarde ?')) {
                    return;
                }

                try {
                    const response = await fetch('/admin/backup/delete', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `filename=${encodeURIComponent(filename)}`
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        this.showAlert('success', data.message);
                        document.querySelector(`[data-backup="${filename}"]`).remove();
                    } else {
                        this.showAlert('danger', data.message);
                    }
                } catch (error) {
                    this.showAlert('danger', 'Erreur lors de la suppression');
                }
            }

            async restoreBackup(filename) {
                if (!confirm('Êtes-vous sûr de vouloir restaurer cette sauvegarde ? Cette action remplacera les données actuelles.')) {
                    return;
                }

                try {
                    const response = await fetch('/admin/backup/restore', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `filename=${encodeURIComponent(filename)}`
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        this.showAlert('success', data.message);
                    } else {
                        this.showAlert('danger', data.message);
                    }
                } catch (error) {
                    this.showAlert('danger', 'Erreur lors de la restauration');
                }
            }

            async cleanupBackups() {
                const keepDays = prompt('Conserver les sauvegardes des derniers (jours) :', '30');
                
                if (!keepDays || keepDays < 7) {
                    this.showAlert('warning', 'La période de rétention doit être d\'au moins 7 jours');
                    return;
                }

                try {
                    const response = await fetch('/admin/backup/cleanup', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `keep_days=${keepDays}`
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        this.showAlert('success', data.message);
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        this.showAlert('danger', data.message);
                    }
                } catch (error) {
                    this.showAlert('danger', 'Erreur lors du nettoyage');
                }
            }

            updateDiskSpaceProgress() {
                const progressRing = document.querySelector('.progress-ring .progress');
                const usedPercent = {{ disk_space.used_percent }};
                
                if (progressRing) {
                    const circumference = 157;
                    const offset = circumference - (usedPercent / 100) * circumference;
                    progressRing.style.strokeDashoffset = offset;
                    
                    // Changer la couleur selon l'utilisation
                    if (usedPercent > 90) {
                        progressRing.style.stroke = '#dc3545';
                    } else if (usedPercent > 75) {
                        progressRing.style.stroke = '#ffc107';
                    } else {
                        progressRing.style.stroke = '#28a745';
                    }
                }
            }

            async checkBackupStatus() {
                try {
                    const response = await fetch('/admin/backup/status');
                    const data = await response.json();
                    
                    if (data.success) {
                        const canCreate = data.data.can_create_backup;
                        const btn = document.getElementById('createBackupBtn');
                        
                        if (!canCreate) {
                            btn.disabled = true;
                            btn.title = 'Espace disque insuffisant';
                        }
                    }
                } catch (error) {
                    console.error('Erreur lors de la vérification du statut:', error);
                }
            }

            showAlert(type, message) {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                
                const container = document.querySelector('.container-fluid');
                container.insertBefore(alertDiv, container.firstChild);
                
                setTimeout(() => {
                    alertDiv.remove();
                }, 5000);
            }
        }

        // Initialiser le gestionnaire de sauvegardes
        document.addEventListener('DOMContentLoaded', () => {
            new BackupManager();
        });
    </script>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="backup-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-shield-check"></i> Gestion des sauvegardes
                </h1>
                <p class="mb-0 opacity-75">
                    Sauvegarde et restauration complète du système
                </p>
            </div>
            <div class="col-md-4 text-end">
                <button id="refreshBtn" class="btn btn-outline-light btn-sm me-2">
                    <i class="bi bi-arrow-clockwise"></i> Actualiser
                </button>
                <a href="{{ path('app_admin_backup_schedule') }}" class="btn btn-light btn-sm">
                    <i class="bi bi-calendar"></i> Programmation
                </a>
            </div>
        </div>
    </div>

    <!-- Statut et actions -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card backup-card status-card">
                <div class="card-body text-center">
                    <h5 class="card-title">Statut du système</h5>
                    <div class="mb-3">
                        <i class="bi bi-database fs-1"></i>
                    </div>
                    <p class="mb-2">Taille estimée de sauvegarde</p>
                    <h4 class="backup-size">{{ estimated_size.total_human }}</h4>
                    <small class="opacity-75">
                        DB: {{ estimated_size.database_human }} | 
                        Fichiers: {{ estimated_size.files_human }}
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card backup-card disk-space-card">
                <div class="card-body text-center">
                    <h5 class="card-title">Espace disque</h5>
                    <div class="mb-3">
                        <svg class="progress-ring">
                            <circle class="background" cx="30" cy="30" r="25"></circle>
                            <circle class="progress" cx="30" cy="30" r="25"></circle>
                        </svg>
                    </div>
                    <p class="mb-2">{{ disk_space.used_percent }}% utilisé</p>
                    <h6>{{ disk_space.free_human }} libre</h6>
                    <small class="opacity-75">sur {{ disk_space.total_human }}</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card backup-card">
                <div class="card-body text-center">
                    <h5 class="card-title">Actions</h5>
                    <div class="mb-3">
                        <button id="createBackupBtn" class="create-backup-btn btn">
                            <i class="bi bi-plus-circle"></i> Créer une sauvegarde
                        </button>
                    </div>
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Création en cours...</span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button id="cleanupBtn" class="btn btn-outline-warning btn-sm">
                            <i class="bi bi-trash"></i> Nettoyer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des sauvegardes -->
    <div class="card backup-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-archive"></i> Sauvegardes disponibles
                <span class="badge bg-secondary">{{ backups|length }}</span>
            </h5>
        </div>
        <div class="card-body">
            {% if backups|length > 0 %}
                {% for backup in backups %}
                    <div class="backup-item p-3 mb-3" data-backup="{{ backup.name }}">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="mb-1">{{ backup.name }}</h6>
                                <small class="text-muted">
                                    <i class="bi bi-calendar"></i> {{ backup.created_at|date('d/m/Y H:i') }}
                                </small>
                            </div>
                            <div class="col-md-3">
                                <span class="backup-size">{{ backup.human_size }}</span>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="backup-actions">
                                    <a href="{{ path('app_admin_backup_download', {filename: backup.name}) }}" 
                                       class="btn btn-sm btn-outline-primary" title="Télécharger">
                                        <i class="bi bi-download"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-success restore-backup-btn" 
                                            data-filename="{{ backup.name }}" title="Restaurer">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger delete-backup-btn" 
                                            data-filename="{{ backup.name }}" title="Supprimer">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-archive fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune sauvegarde</h5>
                    <p class="text-muted">Créez votre première sauvegarde pour sécuriser vos données</p>
                    <button class="btn btn-primary" onclick="document.getElementById('createBackupBtn').click()">
                        <i class="bi bi-plus-circle"></i> Créer une sauvegarde
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
