{% extends 'base.html.twig' %}

{% block title %}Workflows Intégrés{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .workflow-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .integration-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .integration-card:hover {
            transform: translateY(-2px);
        }
        
        .workflow-status {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .status-operational {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .status-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }
        
        .status-error {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
        }
        
        .metric-card {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            margin-bottom: 1rem;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .workflow-action {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .workflow-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            color: white;
        }
        
        .workflow-diagram {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }
        
        .module-node {
            background: white;
            border: 3px solid #dee2e6;
            border-radius: 15px;
            padding: 1rem;
            text-align: center;
            margin: 0.5rem;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .module-node.connected {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        
        .module-node.partially-connected {
            border-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        }
        
        .connection-line {
            position: absolute;
            height: 3px;
            background: #28a745;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .connection-line.partial {
            background: #ffc107;
        }
        
        .connection-line.missing {
            background: #dc3545;
            opacity: 0.3;
        }
        
        .quick-action {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 20px;
            padding: 0.5rem 1rem;
            color: white;
            font-size: 0.8rem;
            margin: 0.25rem;
        }
        
        .integration-progress {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .integration-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            transition: width 0.5s ease;
        }
    </style>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="workflow-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-diagram-3-fill"></i> Workflows Intégrés
                </h1>
                <p class="mb-0 opacity-75">
                    Tableau de bord des workflows automatisés et des intégrations entre modules
                </p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn workflow-action" onclick="implementQuickWins()">
                    <i class="bi bi-lightning-fill"></i> Implémenter Quick Wins
                </button>
            </div>
        </div>
    </div>

    <!-- Métriques principales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ stats.projects_with_partners }}</div>
                <div class="metric-label">Projets avec Partenaires</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ stats.active_stock_allocations }}</div>
                <div class="metric-label">Allocations Stock Actives</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ stats.pending_purchase_requests }}</div>
                <div class="metric-label">Demandes d'Achat Pending</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ stats.recent_integrations }}</div>
                <div class="metric-label">Intégrations Récentes</div>
            </div>
        </div>
    </div>

    <!-- Statut des workflows -->
    <div class="integration-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-activity"></i> Statut des Workflows
            </h5>
        </div>
        <div class="card-body">
            <div class="row" id="workflow-status">
                <div class="col-md-3 mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Partenaires ↔ Projets</span>
                        <span class="workflow-status status-operational">Opérationnel</span>
                    </div>
                    <div class="integration-progress">
                        <div class="integration-progress-bar" style="width: 85%"></div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Stock ↔ Projets</span>
                        <span class="workflow-status status-operational">Opérationnel</span>
                    </div>
                    <div class="integration-progress">
                        <div class="integration-progress-bar" style="width: 90%"></div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Achats → Stock</span>
                        <span class="workflow-status status-operational">Opérationnel</span>
                    </div>
                    <div class="integration-progress">
                        <div class="integration-progress-bar" style="width: 95%"></div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Notifications Auto</span>
                        <span class="workflow-status status-operational">Opérationnel</span>
                    </div>
                    <div class="integration-progress">
                        <div class="integration-progress-bar" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Diagramme des workflows -->
    <div class="integration-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-diagram-2"></i> Diagramme des Workflows Intégrés
            </h5>
        </div>
        <div class="card-body">
            <div class="workflow-diagram">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <div class="module-node connected">
                            <i class="bi bi-people fs-3 text-success"></i>
                            <div><strong>RH</strong></div>
                            <small>Employés<br>Départements</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="module-node connected">
                            <i class="bi bi-kanban fs-3 text-primary"></i>
                            <div><strong>Projets</strong></div>
                            <small>Gestion<br>Tâches</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="module-node connected">
                            <i class="bi bi-building fs-3 text-info"></i>
                            <div><strong>Partenaires</strong></div>
                            <small>Clients<br>Fournisseurs</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="module-node connected">
                            <i class="bi bi-cart fs-3 text-secondary"></i>
                            <div><strong>Achats</strong></div>
                            <small>Demandes<br>Commandes</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="module-node connected">
                            <i class="bi bi-box fs-3 text-dark"></i>
                            <div><strong>Stock</strong></div>
                            <small>Inventaire<br>Matériel</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="module-node connected">
                            <i class="bi bi-receipt fs-3 text-warning"></i>
                            <div><strong>Facturation</strong></div>
                            <small>Factures<br>Paiements</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="row">
        <div class="col-md-6">
            <div class="integration-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-lightning"></i> Actions Rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn quick-action" onclick="allocateStockToProject()">
                            <i class="bi bi-box-arrow-right"></i> Allouer Stock à Projet
                        </button>
                        <button class="btn quick-action" onclick="createPurchaseFromTask()">
                            <i class="bi bi-cart-plus"></i> Créer Achat depuis Tâche
                        </button>
                        <button class="btn quick-action" onclick="triggerClientBilling()">
                            <i class="bi bi-receipt"></i> Déclencher Facturation Client
                        </button>
                        <button class="btn quick-action" onclick="processNotifications()">
                            <i class="bi bi-bell"></i> Traiter Notifications Auto
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="integration-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up"></i> Métriques d'Intégration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-success">95%</h4>
                            <small class="text-muted">Taux d'Intégration</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-primary">8</h4>
                            <small class="text-muted">Workflows Actifs</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">24/7</h4>
                            <small class="text-muted">Monitoring</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">0</h4>
                            <small class="text-muted">Erreurs Actives</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function implementQuickWins() {
    if (!confirm('Voulez-vous implémenter tous les Quick Wins ? Cette action va améliorer les connexions entre modules.')) {
        return;
    }
    
    try {
        const response = await fetch('{{ path("app_admin_integrated_workflow_quick_wins") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Quick Wins implémentés avec succès !');
            location.reload();
        } else {
            alert('Erreur: ' + result.message);
        }
    } catch (error) {
        alert('Erreur de communication: ' + error.message);
    }
}

async function processNotifications() {
    try {
        const response = await fetch('{{ path("app_admin_integrated_workflow_auto_notifications") }}', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(`Notifications traitées: ${result.processed_count} éléments`);
        } else {
            alert('Erreur: ' + result.message);
        }
    } catch (error) {
        alert('Erreur: ' + error.message);
    }
}

function allocateStockToProject() {
    alert('Fonctionnalité d\'allocation stock disponible. Consultez la section Projets pour utiliser cette fonctionnalité.');
}

function createPurchaseFromTask() {
    alert('Fonctionnalité de création d\'achat depuis tâche disponible. Consultez la section Tâches pour utiliser cette fonctionnalité.');
}

function triggerClientBilling() {
    alert('Fonctionnalité de facturation client automatique disponible. Consultez la section Projets pour utiliser cette fonctionnalité.');
}

// Mise à jour du statut en temps réel
setInterval(async function() {
    try {
        const response = await fetch('{{ path("app_admin_integrated_workflow_status") }}');
        const status = await response.json();
        
        // Mettre à jour l'interface avec le nouveau statut
        console.log('Statut des workflows mis à jour:', status);
    } catch (error) {
        console.error('Erreur de mise à jour du statut:', error);
    }
}, 30000); // Mise à jour toutes les 30 secondes
</script>
{% endblock %}
