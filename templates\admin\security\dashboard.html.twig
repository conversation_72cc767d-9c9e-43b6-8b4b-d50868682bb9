{% extends 'base.html.twig' %}

{% block title %}Tableau de bord sécurité{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .security-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        
        .security-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        
        .alert-card {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
        }
        
        .success-card {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
            border: none;
        }
        
        .warning-card {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            border: none;
        }
        
        .security-log-item {
            border-left: 4px solid #dee2e6;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 0 5px 5px 0;
        }
        
        .security-log-item.severity-high {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .security-log-item.severity-critical {
            border-left-color: #721c24;
            background: #f5c6cb;
        }
        
        .security-log-item.severity-medium {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .security-log-item.severity-low {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-shield-lock"></i> Tableau de bord sécurité</h1>
        <div class="btn-group">
            <a href="{{ path('app_admin_security_logs') }}" class="btn btn-outline-primary">
                <i class="bi bi-list-ul"></i> Voir tous les logs
            </a>
            <a href="{{ path('app_admin_security_settings') }}" class="btn btn-outline-secondary">
                <i class="bi bi-gear"></i> Paramètres
            </a>
        </div>
    </div>

    <!-- Filtres de période -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">Période d'analyse</h5>
                    <small class="text-muted">{{ since|date('d/m/Y H:i') }} - {{ "now"|date('d/m/Y H:i') }}</small>
                </div>
                <div class="col-md-6 text-end">
                    <div class="btn-group" role="group">
                        <a href="?period=1" class="btn btn-sm {{ period == '1' ? 'btn-primary' : 'btn-outline-primary' }}">24h</a>
                        <a href="?period=7" class="btn btn-sm {{ period == '7' ? 'btn-primary' : 'btn-outline-primary' }}">7j</a>
                        <a href="?period=30" class="btn btn-sm {{ period == '30' ? 'btn-primary' : 'btn-outline-primary' }}">30j</a>
                        <a href="?period=90" class="btn btn-sm {{ period == '90' ? 'btn-primary' : 'btn-outline-primary' }}">90j</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card security-card stat-card">
                <div class="card-body text-center">
                    <i class="bi bi-activity fs-1 mb-2"></i>
                    <h3>{{ stats.event_types|length > 0 ? stats.event_types|map(e => e.count)|reduce((a, b) => a + b) : 0 }}</h3>
                    <p class="mb-0">Événements totaux</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card security-card alert-card">
                <div class="card-body text-center">
                    <i class="bi bi-exclamation-triangle fs-1 mb-2"></i>
                    <h3>{{ suspicious_activities|length }}</h3>
                    <p class="mb-0">Activités suspectes</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card security-card warning-card">
                <div class="card-body text-center">
                    <i class="bi bi-x-circle fs-1 mb-2"></i>
                    <h3>{{ failed_logins|length }}</h3>
                    <p class="mb-0">Connexions échouées</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card security-card success-card">
                <div class="card-body text-center">
                    <i class="bi bi-check-circle fs-1 mb-2"></i>
                    <h3>{{ stats.top_ips|length }}</h3>
                    <p class="mb-0">IPs actives</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Événements par type -->
        <div class="col-md-6 mb-4">
            <div class="card security-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-pie-chart"></i> Événements par type</h5>
                </div>
                <div class="card-body">
                    {% if stats.event_types %}
                        {% for event in stats.event_types %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ event.eventType|replace({'_': ' '})|title }}</span>
                                <span class="badge bg-primary">{{ event.count }}</span>
                            </div>
                            <div class="progress mb-3" style="height: 6px;">
                                <div class="progress-bar" style="width: {{ (event.count / stats.event_types[0].count * 100)|round }}%"></div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted text-center">Aucun événement dans cette période</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top IPs -->
        <div class="col-md-6 mb-4">
            <div class="card security-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-globe"></i> Top adresses IP</h5>
                </div>
                <div class="card-body">
                    {% if stats.top_ips %}
                        {% for ip in stats.top_ips %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="font-monospace">{{ ip.ipAddress }}</span>
                                <span class="badge bg-info">{{ ip.count }} événements</span>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted text-center">Aucune activité IP détectée</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Activités suspectes -->
        <div class="col-md-6 mb-4">
            <div class="card security-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-exclamation-triangle text-danger"></i> Activités suspectes</h5>
                    <span class="badge bg-danger">{{ suspicious_activities|length }}</span>
                </div>
                <div class="card-body">
                    {% if suspicious_activities %}
                        {% for activity in suspicious_activities|slice(0, 5) %}
                            <div class="security-log-item severity-{{ activity.severity }}">
                                <div class="d-flex justify-content-between">
                                    <strong>{{ activity.eventType|replace({'_': ' '})|title }}</strong>
                                    <small class="text-muted">{{ activity.createdAt|date('d/m H:i') }}</small>
                                </div>
                                <div class="mt-1">
                                    <small>{{ activity.details|slice(0, 100) }}{% if activity.details|length > 100 %}...{% endif %}</small>
                                </div>
                                {% if activity.ipAddress %}
                                    <div class="mt-1">
                                        <small class="font-monospace text-muted">IP: {{ activity.ipAddress }}</small>
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                        {% if suspicious_activities|length > 5 %}
                            <div class="text-center mt-3">
                                <a href="{{ path('app_admin_security_logs', {severity: 'high'}) }}" class="btn btn-sm btn-outline-danger">
                                    Voir toutes les activités suspectes
                                </a>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center text-success">
                            <i class="bi bi-check-circle fs-1 mb-2"></i>
                            <p class="mb-0">Aucune activité suspecte détectée</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Logs récents -->
        <div class="col-md-6 mb-4">
            <div class="card security-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-clock-history"></i> Événements récents</h5>
                </div>
                <div class="card-body">
                    {% if recent_logs %}
                        {% for log in recent_logs|slice(0, 8) %}
                            <div class="security-log-item severity-{{ log.severity }}">
                                <div class="d-flex justify-content-between">
                                    <strong>{{ log.eventType|replace({'_': ' '})|title }}</strong>
                                    <small class="text-muted">{{ log.createdAt|date('d/m H:i') }}</small>
                                </div>
                                {% if log.user %}
                                    <div class="mt-1">
                                        <small>Utilisateur: {{ log.user.fullName }}</small>
                                    </div>
                                {% endif %}
                                {% if log.ipAddress %}
                                    <div class="mt-1">
                                        <small class="font-monospace text-muted">{{ log.ipAddress }}</small>
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                        <div class="text-center mt-3">
                            <a href="{{ path('app_admin_security_logs') }}" class="btn btn-sm btn-outline-primary">
                                Voir tous les logs
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">Aucun événement récent</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="card security-card">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-tools"></i> Actions rapides</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <a href="{{ path('app_admin_security_blocked_ips') }}" class="btn btn-outline-warning w-100">
                        <i class="bi bi-ban"></i><br>
                        IPs bloquées
                    </a>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-outline-danger w-100" data-bs-toggle="modal" data-bs-target="#cleanupModal">
                        <i class="bi bi-trash"></i><br>
                        Nettoyer les logs
                    </button>
                </div>
                <div class="col-md-3">
                    <a href="{{ path('app_admin_security_settings') }}" class="btn btn-outline-secondary w-100">
                        <i class="bi bi-gear"></i><br>
                        Paramètres
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="{{ path('app_admin_security_logs') }}" class="btn btn-outline-primary w-100">
                        <i class="bi bi-search"></i><br>
                        Rechercher
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de nettoyage -->
<div class="modal fade" id="cleanupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nettoyer les logs de sécurité</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{{ path('app_admin_security_cleanup') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="days_to_keep" class="form-label">Conserver les logs des derniers (jours) :</label>
                        <input type="number" class="form-control" id="days_to_keep" name="days_to_keep" value="90" min="30" max="365">
                        <div class="form-text">Minimum 30 jours, maximum 365 jours</div>
                    </div>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        Cette action supprimera définitivement les logs plus anciens que la période spécifiée.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">Nettoyer</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
