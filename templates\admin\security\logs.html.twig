{% extends 'base.html.twig' %}

{% block title %}Logs de sécurité{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .log-row {
            transition: background-color 0.2s;
        }
        
        .log-row:hover {
            background-color: #f8f9fa;
        }
        
        .severity-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .severity-low {
            background-color: #28a745;
        }
        
        .severity-medium {
            background-color: #ffc107;
            color: #212529;
        }
        
        .severity-high {
            background-color: #dc3545;
        }
        
        .severity-critical {
            background-color: #721c24;
        }
        
        .filter-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-list-ul"></i> Logs de sécurité</h1>
        <a href="{{ path('app_admin_security_dashboard') }}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left"></i> Retour au tableau de bord
        </a>
    </div>

    <!-- Filtres -->
    <div class="card filter-card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="event_type" class="form-label">Type d'événement</label>
                    <select class="form-select" id="event_type" name="event_type">
                        <option value="">Tous les types</option>
                        {% for key, label in event_types %}
                            <option value="{{ key }}" {{ filters.event_type == key ? 'selected' : '' }}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="severity" class="form-label">Sévérité</label>
                    <select class="form-select" id="severity" name="severity">
                        <option value="">Toutes</option>
                        {% for key, label in severities %}
                            <option value="{{ key }}" {{ filters.severity == key ? 'selected' : '' }}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="ip_address" class="form-label">Adresse IP</label>
                    <input type="text" class="form-control" id="ip_address" name="ip_address" value="{{ filters.ip_address }}" placeholder="***********">
                </div>
                <div class="col-md-2">
                    <label for="user_id" class="form-label">ID Utilisateur</label>
                    <input type="number" class="form-control" id="user_id" name="user_id" value="{{ filters.user_id }}" placeholder="123">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="bi bi-search"></i> Filtrer
                    </button>
                    <a href="{{ path('app_admin_security_logs') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-x"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Résultats -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-table"></i> Résultats 
                <span class="badge bg-secondary">{{ total }} logs</span>
            </h5>
            <div class="text-muted">
                Page {{ current_page }} sur {{ total_pages }}
            </div>
        </div>
        <div class="card-body p-0">
            {% if logs %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th>Date/Heure</th>
                                <th>Type d'événement</th>
                                <th>Utilisateur</th>
                                <th>IP</th>
                                <th>Sévérité</th>
                                <th>Détails</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in logs %}
                                <tr class="log-row">
                                    <td class="text-nowrap">
                                        <small>{{ log.createdAt|date('d/m/Y') }}</small><br>
                                        <small class="text-muted">{{ log.createdAt|date('H:i:s') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ event_types[log.eventType] ?? log.eventType|replace({'_': ' '})|title }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if log.user %}
                                            <div>{{ log.user.fullName }}</div>
                                            <small class="text-muted">{{ log.user.email }}</small>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if log.ipAddress %}
                                            <span class="font-monospace">{{ log.ipAddress }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge severity-badge severity-{{ log.severity }}">
                                            {{ severities[log.severity] ?? log.severity|title }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if log.details %}
                                            <div class="text-truncate" style="max-width: 300px;" title="{{ log.details }}">
                                                {{ log.details }}
                                            </div>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ path('app_admin_security_log_show', {id: log.id}) }}" 
                                           class="btn btn-sm btn-outline-primary" title="Voir les détails">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-search fs-1 text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun log trouvé</h5>
                    <p class="text-muted">Essayez de modifier vos critères de recherche</p>
                </div>
            {% endif %}
        </div>
        
        {% if total_pages > 1 %}
            <div class="card-footer">
                <nav aria-label="Navigation des logs">
                    <ul class="pagination justify-content-center mb-0">
                        {% if current_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ current_page - 1 }}{% for key, value in filters %}{% if value %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endif %}{% endfor %}">
                                    <i class="bi bi-chevron-left"></i> Précédent
                                </a>
                            </li>
                        {% endif %}
                        
                        {% set start_page = max(1, current_page - 2) %}
                        {% set end_page = min(total_pages, current_page + 2) %}
                        
                        {% if start_page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% for key, value in filters %}{% if value %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endif %}{% endfor %}">1</a>
                            </li>
                            {% if start_page > 2 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endif %}
                        
                        {% for page in start_page..end_page %}
                            <li class="page-item {{ page == current_page ? 'active' : '' }}">
                                <a class="page-link" href="?page={{ page }}{% for key, value in filters %}{% if value %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endif %}{% endfor %}">{{ page }}</a>
                            </li>
                        {% endfor %}
                        
                        {% if end_page < total_pages %}
                            {% if end_page < total_pages - 1 %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ total_pages }}{% for key, value in filters %}{% if value %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endif %}{% endfor %}">{{ total_pages }}</a>
                            </li>
                        {% endif %}
                        
                        {% if current_page < total_pages %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ current_page + 1 }}{% for key, value in filters %}{% if value %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endif %}{% endfor %}">
                                    Suivant <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
