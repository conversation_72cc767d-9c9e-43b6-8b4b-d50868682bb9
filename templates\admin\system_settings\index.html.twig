﻿{% extends 'base.html.twig' %}

{% block title %}Paramètres système{% endblock %}

{% block body %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Paramètres système</h1>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-cogs me-1"></i>
                Paramètres système
            </div>
            <div>
                <a href="{{ path('app_admin_system_settings_new') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Nouveau paramètre
                </a>
                <a href="{{ path('app_admin_dashboard') }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left"></i> Retour au tableau de bord
                </a>
            </div>
        </div>
        <div class="card-body">
            {% for message in app.flashes('success') %}
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
            
            <form method="post" action="{{ path('app_admin_system_settings') }}">
                {% for category, settings in settingsByCategory %}
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">{{ category|capitalize }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nom</th>
                                            <th>Clé</th>
                                            <th>Valeur</th>
                                            <th>Description</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for setting in settings %}
                                            <tr>
                                                <td>{{ setting.name }}</td>
                                                <td><code>{{ setting.key }}</code></td>
                                                <td>
                                                    {% if setting.type == 'text' %}
                                                        <input type="text" class="form-control" name="settings[{{ setting.id }}]" value="{{ setting.value }}">
                                                    {% elseif setting.type == 'textarea' %}
                                                        <textarea class="form-control" name="settings[{{ setting.id }}]" rows="3">{{ setting.value }}</textarea>
                                                    {% elseif setting.type == 'boolean' %}
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" name="settings[{{ setting.id }}]" value="1" {% if setting.value == '1' %}checked{% endif %}>
                                                        </div>
                                                    {% elseif setting.type == 'select' %}
                                                        <select class="form-select" name="settings[{{ setting.id }}]">
                                                            {% for option in setting.options|split(',') %}
                                                                <option value="{{ option }}" {% if setting.value == option %}selected{% endif %}>{{ option }}</option>
                                                            {% endfor %}
                                                        </select>
                                                    {% else %}
                                                        <input type="text" class="form-control" name="settings[{{ setting.id }}]" value="{{ setting.value }}">
                                                    {% endif %}
                                                </td>
                                                <td>{{ setting.description }}</td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ path('app_admin_system_settings_edit', {'id': setting.id}) }}" class="btn btn-primary btn-sm" title="Modifier">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal{{ setting.id }}" title="Supprimer">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        {% else %}
                                            <tr>
                                                <td colspan="5" class="text-center">Aucun paramètre trouvé dans cette catégorie</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        Aucun paramètre système trouvé. Cliquez sur "Nouveau paramètre" pour en ajouter un.
                    </div>
                {% endfor %}
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer les modifications
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
