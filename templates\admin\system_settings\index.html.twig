{% extends 'base.html.twig' %}

{% block title %}Paramètres système{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .coming-soon-container {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            margin: 2rem 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .coming-soon-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            opacity: 0.9;
        }

        .coming-soon-title {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .coming-soon-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            font-weight: 300;
        }

        .feature-preview {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            backdrop-filter: blur(10px);
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .feature-list li i {
            margin-right: 0.5rem;
            color: #ffd700;
        }

        .notification-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            display: inline-block;
            margin-top: 1rem;
            box-shadow: 0 4px 15px rgba(238, 90, 36, 0.4);
        }

        .admin-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .admin-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px 10px 0 0;
            border: none;
        }

        .admin-card-body {
            padding: 2rem;
        }

        .breadcrumb-custom {
            background: transparent;
            padding: 0;
            margin-bottom: 2rem;
        }

        .breadcrumb-custom .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
        }

        .breadcrumb-custom .breadcrumb-item.active {
            color: #6c757d;
        }
    </style>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-custom">
            <li class="breadcrumb-item">
                <a href="{{ path('app_admin_dashboard') }}">
                    <i class="bi bi-house"></i> Administration
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="bi bi-gear"></i> Paramètres système
            </li>
        </ol>
    </nav>

    <div class="admin-card">
        <div class="admin-card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0">
                        <i class="bi bi-gear me-2"></i>
                        Paramètres système
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">Configuration avancée du système</p>
                </div>
                <div>
                    <a href="{{ path('app_admin_dashboard') }}" class="btn btn-light btn-sm">
                        <i class="bi bi-arrow-left"></i> Retour au tableau de bord
                    </a>
                </div>
            </div>
        </div>
        <div class="admin-card-body">
            <!-- Message À venir -->
            <div class="coming-soon-container">
                <div class="coming-soon-icon">
                    <i class="bi bi-gear-wide-connected"></i>
                </div>
                <h2 class="coming-soon-title">À venir</h2>
                <p class="coming-soon-subtitle">
                    La page de configuration des paramètres système est en cours de développement
                </p>

                <div class="feature-preview">
                    <h4 class="mb-3">
                        <i class="bi bi-stars text-warning"></i>
                        Fonctionnalités prévues
                    </h4>
                    <ul class="feature-list">
                        <li><i class="bi bi-check-circle"></i> Configuration générale du système</li>
                        <li><i class="bi bi-check-circle"></i> Paramètres de sécurité avancés</li>
                        <li><i class="bi bi-check-circle"></i> Gestion des notifications</li>
                        <li><i class="bi bi-check-circle"></i> Configuration des emails</li>
                        <li><i class="bi bi-check-circle"></i> Paramètres de performance</li>
                        <li><i class="bi bi-check-circle"></i> Options de sauvegarde automatique</li>
                        <li><i class="bi bi-check-circle"></i> Configuration des intégrations</li>
                    </ul>
                </div>

                <div class="notification-badge">
                    <i class="bi bi-clock"></i>
                    Disponible dans une prochaine mise à jour
                </div>
            </div>

            <!-- Informations temporaires -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-info-circle"></i>
                                Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">
                                En attendant la mise en place de cette interface, les paramètres système
                                peuvent être configurés directement dans les fichiers de configuration
                                ou via l'interface en ligne de commande.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="bi bi-exclamation-triangle"></i>
                                Contact
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">
                                Pour toute configuration urgente, contactez votre administrateur système
                                ou l'équipe de développement.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
