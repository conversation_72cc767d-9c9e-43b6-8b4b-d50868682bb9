{% extends 'base.html.twig' %}

{% block title %}Test Paramètres système{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="alert alert-success">
        <h1>TEST - Page Paramètres système</h1>
        <p>Si vous voyez ce message, la page fonctionne correctement.</p>
        <p>Utilisateur connecté : {{ app.user ? app.user.email : 'Non connecté' }}</p>
        <p>Rôles : {{ app.user ? app.user.roles|join(', ') : 'Aucun' }}</p>
    </div>
    
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 15px; text-align: center;">
        <h2>À venir</h2>
        <p>La page de configuration des paramètres système est en cours de développement</p>
        <div style="background: rgba(255, 255, 255, 0.1); padding: 1rem; border-radius: 10px; margin: 1rem 0;">
            <h4>Fonctionnalités prévues :</h4>
            <ul style="list-style: none; padding: 0;">
                <li>✓ Configuration générale du système</li>
                <li>✓ Paramètres de sécurité avancés</li>
                <li>✓ Gestion des notifications</li>
                <li>✓ Configuration des emails</li>
            </ul>
        </div>
        <div style="background: #ff6b6b; padding: 0.5rem 1rem; border-radius: 25px; display: inline-block; margin-top: 1rem;">
            Disponible dans une prochaine mise à jour
        </div>
    </div>
    
    <div class="mt-4">
        <a href="{{ path('app_admin_dashboard') }}" class="btn btn-primary">
            ← Retour au tableau de bord
        </a>
    </div>
</div>
{% endblock %}
