{% extends 'base.html.twig' %}

{% block title %}Connexions entre Modules{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .connection-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .connection-card:hover {
            transform: translateY(-2px);
        }
        
        .status-implemented {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .status-partial {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }
        
        .status-missing {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
            color: white;
        }
        
        .impact-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .impact-high {
            background: #dc3545;
            color: white;
        }
        
        .impact-medium {
            background: #ffc107;
            color: #212529;
        }
        
        .impact-low {
            background: #28a745;
            color: white;
        }
        
        .usage-level {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .usage-level-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .usage-high {
            background: #28a745;
            width: 90%;
        }
        
        .usage-medium {
            background: #ffc107;
            width: 60%;
        }
        
        .usage-low {
            background: #dc3545;
            width: 30%;
        }
        
        .module-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-right: 1rem;
        }
        
        .module-projects {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        
        .module-partners {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        
        .module-employees {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        }
        
        .module-invoices {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        }
        
        .module-purchases {
            background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
        }
        
        .module-stock {
            background: linear-gradient(135deg, #fd7e14 0%, #e8650e 100%);
        }
        
        .workflow-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
    </style>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="workflow-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-link-45deg"></i> Connexions entre Modules
                </h1>
                <p class="mb-0 opacity-75">
                    Analyse détaillée des connexions actuelles et identification des liens manquants
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ path('app_admin_workflow_analysis_index') }}" class="btn btn-light">
                    <i class="bi bi-arrow-left"></i> Retour à l'analyse
                </a>
            </div>
        </div>
    </div>

    <!-- Connexions actuelles -->
    <div class="row mb-4">
        <div class="col-12">
            <h3 class="mb-3">
                <i class="bi bi-check-circle text-success"></i> Connexions Actuelles
            </h3>
        </div>
        
        {% for key, connection in current_connections %}
            <div class="col-md-6 mb-3">
                <div class="connection-card status-{{ connection.status|lower }}">
                    <div class="card-body text-white">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                {% set modules = key|split('_to_') %}
                                <div class="module-icon module-{{ modules[0] }}">
                                    {% if modules[0] == 'project' %}
                                        <i class="bi bi-kanban"></i>
                                    {% elseif modules[0] == 'partner' %}
                                        <i class="bi bi-building"></i>
                                    {% elseif modules[0] == 'employee' %}
                                        <i class="bi bi-person"></i>
                                    {% else %}
                                        <i class="bi bi-gear"></i>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col">
                                <h6 class="mb-1">{{ modules[0]|title }} → {{ modules[1]|title }}</h6>
                                <p class="mb-2 opacity-75">{{ connection.description }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-light text-dark">{{ connection.status|upper }}</span>
                                    <small>Usage: {{ connection.usage_level }}</small>
                                </div>
                                <div class="usage-level mt-2">
                                    <div class="usage-level-fill usage-{{ connection.usage_level|lower }}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Connexions manquantes -->
    <div class="row">
        <div class="col-12">
            <h3 class="mb-3">
                <i class="bi bi-exclamation-triangle text-warning"></i> Connexions Manquantes
            </h3>
        </div>
        
        {% for key, missing in missing_connections %}
            <div class="col-md-6 mb-3">
                <div class="connection-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                {% set modules = key|replace({'_to_': '_', '_direct': '', '_workflows': ''})|split('_') %}
                                <div class="module-icon module-{{ modules[0] }}">
                                    {% if modules[0] == 'partner' %}
                                        <i class="bi bi-building"></i>
                                    {% elseif modules[0] == 'employee' %}
                                        <i class="bi bi-person"></i>
                                    {% elseif modules[0] == 'stock' %}
                                        <i class="bi bi-box"></i>
                                    {% elseif modules[0] == 'department' %}
                                        <i class="bi bi-diagram-3"></i>
                                    {% elseif modules[0] == 'task' %}
                                        <i class="bi bi-check-square"></i>
                                    {% elseif modules[0] == 'notification' %}
                                        <i class="bi bi-bell"></i>
                                    {% else %}
                                        <i class="bi bi-link"></i>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0">{{ missing.description }}</h6>
                                    <span class="impact-badge impact-{{ missing.impact|lower }}">{{ missing.impact }}</span>
                                </div>
                                <p class="text-muted mb-2">{{ missing.business_value }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">Effort: {{ missing.implementation_effort }}</small>
                                    <button class="btn btn-sm btn-outline-primary" onclick="implementConnection('{{ key }}')">
                                        <i class="bi bi-plus"></i> Implémenter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Matrice de connexions -->
    <div class="connection-card mt-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-grid"></i> Matrice des Connexions
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th></th>
                            <th class="text-center">Projets</th>
                            <th class="text-center">Partenaires</th>
                            <th class="text-center">RH</th>
                            <th class="text-center">Factures</th>
                            <th class="text-center">Achats</th>
                            <th class="text-center">Stock</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <th>Projets</th>
                            <td class="text-center">-</td>
                            <td class="text-center"><span class="badge bg-warning">Partiel</span></td>
                            <td class="text-center"><span class="badge bg-success">Fort</span></td>
                            <td class="text-center"><span class="badge bg-success">Fort</span></td>
                            <td class="text-center"><span class="badge bg-success">Fort</span></td>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                        </tr>
                        <tr>
                            <th>Partenaires</th>
                            <td class="text-center"><span class="badge bg-warning">Partiel</span></td>
                            <td class="text-center">-</td>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                            <td class="text-center"><span class="badge bg-success">Fort</span></td>
                            <td class="text-center"><span class="badge bg-warning">Partiel</span></td>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                        </tr>
                        <tr>
                            <th>RH</th>
                            <td class="text-center"><span class="badge bg-success">Fort</span></td>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                            <td class="text-center">-</td>
                            <td class="text-center"><span class="badge bg-warning">Partiel</span></td>
                            <td class="text-center"><span class="badge bg-warning">Partiel</span></td>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                        </tr>
                        <tr>
                            <th>Factures</th>
                            <td class="text-center"><span class="badge bg-success">Fort</span></td>
                            <td class="text-center"><span class="badge bg-success">Fort</span></td>
                            <td class="text-center"><span class="badge bg-warning">Partiel</span></td>
                            <td class="text-center">-</td>
                            <td class="text-center"><span class="badge bg-warning">Partiel</span></td>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                        </tr>
                        <tr>
                            <th>Achats</th>
                            <td class="text-center"><span class="badge bg-success">Fort</span></td>
                            <td class="text-center"><span class="badge bg-warning">Partiel</span></td>
                            <td class="text-center"><span class="badge bg-warning">Partiel</span></td>
                            <td class="text-center"><span class="badge bg-warning">Partiel</span></td>
                            <td class="text-center">-</td>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                        </tr>
                        <tr>
                            <th>Stock</th>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                            <td class="text-center"><span class="badge bg-danger">Manquant</span></td>
                            <td class="text-center">-</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function implementConnection(connectionKey) {
    if (confirm('Voulez-vous implémenter cette connexion ? Cette action peut nécessiter des modifications de la base de données.')) {
        // Ici on pourrait faire un appel AJAX pour implémenter la connexion
        alert('Implémentation de la connexion "' + connectionKey + '" programmée. Consultez la roadmap pour plus de détails.');
    }
}
</script>
{% endblock %}
