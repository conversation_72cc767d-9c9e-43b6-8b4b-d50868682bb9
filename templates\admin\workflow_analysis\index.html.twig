{% extends 'base.html.twig' %}

{% block title %}Analyse des Workflows{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .workflow-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .analysis-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .analysis-card:hover {
            transform: translateY(-2px);
        }
        
        .connection-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-implemented {
            background: #d4edda;
            color: #155724;
        }
        
        .status-partial {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-missing {
            background: #f8d7da;
            color: #721c24;
        }
        
        .impact-high {
            color: #dc3545;
            font-weight: bold;
        }
        
        .impact-medium {
            color: #ffc107;
            font-weight: bold;
        }
        
        .impact-low {
            color: #28a745;
            font-weight: bold;
        }
        
        .progress-ring {
            width: 120px;
            height: 120px;
        }
        
        .progress-ring circle {
            fill: transparent;
            stroke-width: 8;
            stroke-linecap: round;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }
        
        .progress-ring .background {
            stroke: rgba(255, 255, 255, 0.3);
        }
        
        .progress-ring .progress {
            stroke: #fff;
            stroke-dasharray: 314;
            stroke-dashoffset: 125.6; /* 60% de 314 */
            transition: stroke-dashoffset 0.5s ease;
        }
        
        .workflow-diagram {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            margin: 1rem 0;
        }
        
        .module-box {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .module-box:hover {
            border-color: #007bff;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
        }
        
        .connection-arrow {
            color: #6c757d;
            font-size: 1.5rem;
            margin: 0 1rem;
        }
        
        .connection-strong {
            color: #28a745;
        }
        
        .connection-weak {
            color: #ffc107;
        }
        
        .connection-missing {
            color: #dc3545;
            opacity: 0.5;
        }
        
        .recommendation-priority {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .priority-1 {
            background: #d4edda;
            color: #155724;
        }
        
        .priority-2 {
            background: #fff3cd;
            color: #856404;
        }
        
        .priority-3 {
            background: #f8d7da;
            color: #721c24;
        }
        
        .metric-card {
            text-align: center;
            padding: 1.5rem;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="workflow-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-diagram-3"></i> Analyse des Workflows
                </h1>
                <p class="mb-0 opacity-75">
                    Analyse complète des interconnexions entre modules et recommandations d'optimisation
                </p>
            </div>
            <div class="col-md-4 text-end">
                <svg class="progress-ring">
                    <circle class="background" cx="60" cy="60" r="50"></circle>
                    <circle class="progress" cx="60" cy="60" r="50"></circle>
                </svg>
                <div class="mt-2">
                    <h4>{{ report.executive_summary.current_integration_level }}</h4>
                    <small class="opacity-75">Niveau d'intégration actuel</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Résumé exécutif -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="analysis-card">
                <div class="card-body metric-card">
                    <div class="metric-value">{{ report.executive_summary.quick_wins_available }}</div>
                    <div class="metric-label">Quick Wins Disponibles</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analysis-card">
                <div class="card-body metric-card">
                    <div class="metric-value">{{ report.executive_summary.high_impact_opportunities }}</div>
                    <div class="metric-label">Opportunités Haut Impact</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analysis-card">
                <div class="card-body metric-card">
                    <div class="metric-value">{{ report.executive_summary.estimated_roi }}</div>
                    <div class="metric-label">ROI Estimé</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analysis-card">
                <div class="card-body metric-card">
                    <div class="metric-value">{{ analysis.missing_connections|length }}</div>
                    <div class="metric-label">Connexions Manquantes</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation des sections -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="analysis-card">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <a href="{{ path('app_admin_workflow_analysis_connections') }}" class="btn btn-outline-primary w-100">
                                <i class="bi bi-link-45deg"></i><br>
                                <strong>Connexions</strong><br>
                                <small>Actuelles et manquantes</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ path('app_admin_workflow_analysis_opportunities') }}" class="btn btn-outline-success w-100">
                                <i class="bi bi-lightbulb"></i><br>
                                <strong>Opportunités</strong><br>
                                <small>Workflows possibles</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ path('app_admin_workflow_analysis_recommendations') }}" class="btn btn-outline-warning w-100">
                                <i class="bi bi-star"></i><br>
                                <strong>Recommandations</strong><br>
                                <small>Actions prioritaires</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ path('app_admin_workflow_analysis_roadmap') }}" class="btn btn-outline-info w-100">
                                <i class="bi bi-map"></i><br>
                                <strong>Roadmap</strong><br>
                                <small>Plan d'implémentation</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Diagramme des connexions actuelles -->
    <div class="analysis-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-diagram-2"></i> Vue d'ensemble des connexions
            </h5>
        </div>
        <div class="card-body">
            <div class="workflow-diagram">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <div class="module-box">
                            <i class="bi bi-people fs-3 text-success"></i>
                            <div><strong>RH</strong></div>
                            <small>Employés<br>Départements</small>
                        </div>
                    </div>
                    <div class="col-md-1 text-center">
                        <i class="bi bi-arrow-right connection-arrow connection-strong"></i>
                    </div>
                    <div class="col-md-2">
                        <div class="module-box">
                            <i class="bi bi-kanban fs-3 text-primary"></i>
                            <div><strong>Projets</strong></div>
                            <small>Gestion<br>Tâches</small>
                        </div>
                    </div>
                    <div class="col-md-1 text-center">
                        <i class="bi bi-arrow-right connection-arrow connection-weak"></i>
                    </div>
                    <div class="col-md-2">
                        <div class="module-box">
                            <i class="bi bi-building fs-3 text-info"></i>
                            <div><strong>Partenaires</strong></div>
                            <small>Clients<br>Fournisseurs</small>
                        </div>
                    </div>
                    <div class="col-md-1 text-center">
                        <i class="bi bi-arrow-right connection-arrow connection-strong"></i>
                    </div>
                    <div class="col-md-2">
                        <div class="module-box">
                            <i class="bi bi-receipt fs-3 text-warning"></i>
                            <div><strong>Facturation</strong></div>
                            <small>Factures<br>Paiements</small>
                        </div>
                    </div>
                </div>
                
                <div class="row align-items-center mt-3">
                    <div class="col-md-2">
                        <div class="module-box">
                            <i class="bi bi-cart fs-3 text-secondary"></i>
                            <div><strong>Achats</strong></div>
                            <small>Demandes<br>Commandes</small>
                        </div>
                    </div>
                    <div class="col-md-1 text-center">
                        <i class="bi bi-arrow-right connection-arrow connection-missing"></i>
                    </div>
                    <div class="col-md-2">
                        <div class="module-box">
                            <i class="bi bi-box fs-3 text-dark"></i>
                            <div><strong>Stock</strong></div>
                            <small>Inventaire<br>Matériel</small>
                        </div>
                    </div>
                    <div class="col-md-1 text-center">
                        <i class="bi bi-arrow-up connection-arrow connection-missing"></i>
                    </div>
                    <div class="col-md-2">
                        <!-- Connexion vers projets -->
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-4">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-arrow-right connection-strong me-2"></i>
                        <span>Connexion forte</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-arrow-right connection-weak me-2"></i>
                        <span>Connexion partielle</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-arrow-right connection-missing me-2"></i>
                        <span>Connexion manquante</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions prioritaires -->
    <div class="analysis-card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-lightning"></i> Actions prioritaires (Quick Wins)
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                {% for key, action in report.immediate_actions %}
                    <div class="col-md-4 mb-3">
                        <div class="border rounded p-3 h-100">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">{{ action.title }}</h6>
                                <span class="recommendation-priority priority-1">{{ action.effort }}</span>
                            </div>
                            <p class="text-muted small mb-2">{{ action.description }}</p>
                            <div class="d-flex justify-content-between">
                                <span class="impact-{{ action.impact|lower }}">Impact: {{ action.impact }}</span>
                                <button class="btn btn-sm btn-outline-primary">Implémenter</button>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Bénéfices business -->
    <div class="row">
        <div class="col-md-6">
            <div class="analysis-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up"></i> Gains d'efficacité
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        {% for gain in report.business_impact.efficiency_gains %}
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                {{ gain }}
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="analysis-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-currency-euro"></i> Bénéfices financiers
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        {% for benefit in report.business_impact.financial_benefits %}
                            <li class="mb-2">
                                <i class="bi bi-check-circle text-success me-2"></i>
                                {{ benefit }}
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
