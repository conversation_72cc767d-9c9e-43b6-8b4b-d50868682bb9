{% extends 'base.html.twig' %}

{% block title %}Tableau de bord analytique{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .analytics-card {
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
        }
        
        .analytics-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .kpi-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
        }
        
        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
            pointer-events: none;
        }
        
        .kpi-projects {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .kpi-financial {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .kpi-hr {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .kpi-security {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
        }
        
        .kpi-partners {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }
        
        .kpi-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .kpi-label {
            font-size: 0.9rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .kpi-icon {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 2rem;
            opacity: 0.3;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            padding: 1rem;
        }
        
        .chart-controls {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .metric-selector {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .metric-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #dee2e6;
            background: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }
        
        .metric-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .metric-btn:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        
        .period-selector {
            display: flex;
            gap: 0.25rem;
        }
        
        .period-btn {
            padding: 0.25rem 0.75rem;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.8rem;
        }
        
        .period-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .export-controls {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        
        .real-time-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .widget-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .widget {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .widget-header {
            display: flex;
            justify-content-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .widget-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }
        
        .refresh-btn {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 50%;
            transition: all 0.2s ease;
        }
        
        .refresh-btn:hover {
            background: #f8f9fa;
            color: #007bff;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        class AdvancedDashboard {
            constructor() {
                this.charts = {};
                this.currentMetric = 'projects_created';
                this.currentPeriod = '30d';
                this.refreshInterval = null;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.loadChartData();
                this.startAutoRefresh();
            }

            setupEventListeners() {
                // Sélecteurs de métriques
                document.querySelectorAll('.metric-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.selectMetric(e.target.dataset.metric);
                    });
                });

                // Sélecteurs de période
                document.querySelectorAll('.period-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.selectPeriod(e.target.dataset.period);
                    });
                });

                // Boutons de rafraîchissement
                document.querySelectorAll('.refresh-btn').forEach(btn => {
                    btn.addEventListener('click', () => {
                        this.refreshData();
                    });
                });

                // Export
                document.getElementById('exportForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.exportData();
                });
            }

            selectMetric(metric) {
                this.currentMetric = metric;
                document.querySelectorAll('.metric-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`[data-metric="${metric}"]`).classList.add('active');
                this.loadChartData();
            }

            selectPeriod(period) {
                this.currentPeriod = period;
                document.querySelectorAll('.period-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`[data-period="${period}"]`).classList.add('active');
                this.loadChartData();
            }

            async loadChartData() {
                this.showLoading();
                try {
                    const response = await fetch(`/advanced-dashboard/api/chart-data/${this.currentMetric}?period=${this.currentPeriod}`);
                    const data = await response.json();
                    this.updateChart(data);
                } catch (error) {
                    console.error('Erreur lors du chargement des données:', error);
                } finally {
                    this.hideLoading();
                }
            }

            updateChart(data) {
                const ctx = document.getElementById('mainChart').getContext('2d');
                
                if (this.charts.main) {
                    this.charts.main.destroy();
                }

                this.charts.main = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.data.map(item => new Date(item.date).toLocaleDateString('fr-FR')),
                        datasets: [{
                            label: this.getMetricLabel(data.metric),
                            data: data.data.map(item => item.value),
                            borderColor: '#007bff',
                            backgroundColor: 'rgba(0, 123, 255, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#007bff',
                            pointBorderColor: '#fff',
                            pointBorderWidth: 2,
                            pointRadius: 5,
                            pointHoverRadius: 8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: '#fff',
                                bodyColor: '#fff',
                                borderColor: '#007bff',
                                borderWidth: 1,
                                cornerRadius: 8,
                                displayColors: false
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    color: '#6c757d'
                                }
                            },
                            y: {
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                },
                                ticks: {
                                    color: '#6c757d'
                                }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        }
                    }
                });
            }

            getMetricLabel(metric) {
                const labels = {
                    'projects_created': 'Projets créés',
                    'invoices_paid': 'Factures payées',
                    'employees_hired': 'Employés embauchés',
                    'security_events': 'Événements de sécurité'
                };
                return labels[metric] || metric;
            }

            async refreshData() {
                // Rafraîchir les KPIs
                try {
                    const response = await fetch('/advanced-dashboard/api/kpis');
                    const kpis = await response.json();
                    this.updateKPIs(kpis);
                } catch (error) {
                    console.error('Erreur lors du rafraîchissement des KPIs:', error);
                }

                // Rafraîchir le graphique
                this.loadChartData();
            }

            updateKPIs(kpis) {
                // Mettre à jour les valeurs des KPIs
                Object.keys(kpis).forEach(category => {
                    Object.keys(kpis[category]).forEach(metric => {
                        const element = document.querySelector(`[data-kpi="${category}-${metric}"]`);
                        if (element) {
                            element.textContent = this.formatValue(kpis[category][metric], metric);
                        }
                    });
                });

                // Mettre à jour l'indicateur de temps réel
                const indicator = document.querySelector('.real-time-indicator');
                if (indicator) {
                    indicator.style.animation = 'none';
                    setTimeout(() => {
                        indicator.style.animation = 'pulse 2s infinite';
                    }, 10);
                }
            }

            formatValue(value, metric) {
                if (metric.includes('amount') || metric.includes('budget') || metric.includes('revenue')) {
                    return new Intl.NumberFormat('fr-FR', {
                        style: 'currency',
                        currency: 'EUR'
                    }).format(value);
                } else if (metric.includes('rate')) {
                    return value + '%';
                } else {
                    return new Intl.NumberFormat('fr-FR').format(value);
                }
            }

            showLoading() {
                document.querySelector('.loading-spinner').style.display = 'block';
            }

            hideLoading() {
                document.querySelector('.loading-spinner').style.display = 'none';
            }

            startAutoRefresh() {
                // Rafraîchir toutes les 5 minutes
                this.refreshInterval = setInterval(() => {
                    this.refreshData();
                }, 300000);
            }

            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                }
            }

            exportData() {
                const form = document.getElementById('exportForm');
                const formData = new FormData(form);
                
                fetch('/advanced-dashboard/export', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = 'dashboard-report.' + formData.get('format');
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                })
                .catch(error => {
                    console.error('Erreur lors de l\'export:', error);
                });
            }
        }

        // Initialiser le tableau de bord
        document.addEventListener('DOMContentLoaded', () => {
            new AdvancedDashboard();
        });
    </script>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- En-tête du tableau de bord -->
    <div class="dashboard-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-graph-up"></i> Tableau de bord analytique
                </h1>
                <p class="mb-0 opacity-75">
                    <span class="real-time-indicator"></span>
                    Données en temps réel - Dernière mise à jour : {{ "now"|date('d/m/Y H:i') }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="export-controls">
                    <form id="exportForm" class="d-flex gap-2">
                        <select name="format" class="form-select form-select-sm">
                            <option value="pdf">PDF</option>
                            <option value="excel">Excel</option>
                            <option value="csv">CSV</option>
                        </select>
                        <button type="submit" class="btn btn-light btn-sm">
                            <i class="bi bi-download"></i> Exporter
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- KPIs principaux -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card analytics-card kpi-card kpi-projects">
                <div class="card-body text-center position-relative">
                    <i class="bi bi-kanban kpi-icon"></i>
                    <div class="kpi-value" data-kpi="projects-active_projects">{{ kpis.projects.active_projects }}</div>
                    <div class="kpi-label">Projets actifs</div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card analytics-card kpi-card kpi-financial">
                <div class="card-body text-center position-relative">
                    <i class="bi bi-currency-euro kpi-icon"></i>
                    <div class="kpi-value" data-kpi="financial-monthly_revenue">{{ (kpis.financial.monthly_revenue / 1000)|number_format(0) }}k€</div>
                    <div class="kpi-label">CA mensuel</div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card analytics-card kpi-card kpi-hr">
                <div class="card-body text-center position-relative">
                    <i class="bi bi-people kpi-icon"></i>
                    <div class="kpi-value" data-kpi="hr-total_employees">{{ kpis.hr.total_employees }}</div>
                    <div class="kpi-label">Employés</div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card analytics-card kpi-card kpi-security">
                <div class="card-body text-center position-relative">
                    <i class="bi bi-shield-check kpi-icon"></i>
                    <div class="kpi-value" data-kpi="security-successful_logins">{{ kpis.security.successful_logins }}</div>
                    <div class="kpi-label">Connexions</div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card analytics-card kpi-card kpi-partners">
                <div class="card-body text-center position-relative">
                    <i class="bi bi-building kpi-icon"></i>
                    <div class="kpi-value" data-kpi="partners-active_partners">{{ kpis.partners.active_partners }}</div>
                    <div class="kpi-label">Partenaires</div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card analytics-card kpi-card">
                <div class="card-body text-center position-relative">
                    <i class="bi bi-graph-up kpi-icon"></i>
                    <div class="kpi-value" data-kpi="projects-success_rate">{{ kpis.projects.success_rate }}%</div>
                    <div class="kpi-label">Taux succès</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphique principal -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card analytics-card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0">
                                <i class="bi bi-graph-up"></i> Évolution temporelle
                                <button class="refresh-btn ms-2" title="Actualiser">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-controls">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="metric-selector">
                                            <button class="metric-btn active" data-metric="projects_created">Projets</button>
                                            <button class="metric-btn" data-metric="invoices_paid">Factures</button>
                                            <button class="metric-btn" data-metric="employees_hired">Employés</button>
                                            <button class="metric-btn" data-metric="security_events">Sécurité</button>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="period-selector">
                                            <button class="period-btn" data-period="7d">7j</button>
                                            <button class="period-btn active" data-period="30d">30j</button>
                                            <button class="period-btn" data-period="90d">90j</button>
                                            <button class="period-btn" data-period="1y">1an</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="loading-spinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="mainChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
