<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>{% block title %}Welcome!{% endblock %}</title>
        <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 128 128%22><text y=%221.2em%22 font-size=%2296%22>⚫️</text><text y=%221.3em%22 x=%220.2em%22 font-size=%2276%22 fill=%22%23fff%22>sf</text></svg>">
        {% block stylesheets %}
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
            <link rel="stylesheet" href="{{ asset('css/themes.css') }}">
            <link rel="stylesheet" href="{{ asset('css/mobile.css') }}">
            <link rel="stylesheet" href="{{ asset('css/feature-indicators.css') }}">
        {% endblock %}

        {% block javascripts %}
            {% block importmap %}{{ importmap('app') }}{% endblock %}
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
            <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
            <script src="{{ asset('js/theme-switcher.js') }}"></script>
            <script src="{{ asset('js/keyboard-shortcuts.js') }}"></script>
            <script>
                // Ensure Bootstrap is properly initialized
                document.addEventListener('DOMContentLoaded', function() {
                    // Initialize all dropdowns
                    var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
                    dropdownElementList.forEach(function(dropdownToggleEl) {
                        new bootstrap.Dropdown(dropdownToggleEl);
                    });

                    // Initialize all tooltips
                    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                    tooltipTriggerList.forEach(function(tooltipTriggerEl) {
                        new bootstrap.Tooltip(tooltipTriggerEl);
                    });

                    // Initialize all popovers
                    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                    popoverTriggerList.forEach(function(popoverTriggerEl) {
                        new bootstrap.Popover(popoverTriggerEl);
                    });

                    // Initialize all modals
                    var modalTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="modal"]'));
                    modalTriggerList.forEach(function(modalTriggerEl) {
                        modalTriggerEl.addEventListener('click', function() {
                            var targetModal = document.querySelector(this.getAttribute('data-bs-target'));
                            if (targetModal) {
                                var modal = new bootstrap.Modal(targetModal);
                                modal.show();
                            }
                        });
                    });

                    console.log('Bootstrap components initialized from base template');

                    // Initialiser le gestionnaire de thèmes
                    if (typeof ThemeSwitcher !== 'undefined') {
                        window.themeSwitcher = new ThemeSwitcher();
                    }
                });
            </script>
        {% endblock %}
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
            <div class="container">
                <a class="navbar-brand" href="{{ path('app_home') }}">SI</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ path('app_home') }}">Accueil</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="dashboardDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Tableaux de bord
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="dashboardDropdown">
                                <li><a class="dropdown-item" href="{{ path('app_dashboard') }}">Tableau de bord principal</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_project_dashboard') }}">Projets</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_employee_dashboard') }}">Ressources Humaines</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_purchasing_dashboard') }}">Achats</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_stock_dashboard') }}">Stocks</a></li>
                                {% if is_granted('ROLE_FINANCE') or is_granted('ROLE_ADMIN') %}
                                <li><a class="dropdown-item" href="{{ path('app_financial_dashboard_index') }}">Finance</a></li>
                                {% endif %}
                                {% if is_granted('ROLE_ACCOUNTING') or is_granted('ROLE_ADMIN') %}
                                <li><a class="dropdown-item" href="{{ path('app_accounting_dashboard_index') }}">Comptabilité</a></li>
                                {% endif %}
                                {% if is_granted('ROLE_ADMIN') %}
                                <li><a class="dropdown-item" href="{{ path('app_admin_dashboard') }}">Administration</a></li>
                                {% endif %}
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="partnersDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Partenaires
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="partnersDropdown">
                                <li><a class="dropdown-item" href="{{ path('app_partner') }}">Tableau de bord</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_partner_crud_index') }}">Liste des partenaires</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_partner_crud_new') }}">Nouveau partenaire</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_partner_search') }}">Recherche avancée</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_partner_status_index') }}">Statuts</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_partner_type_index') }}">Types</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_partner_nature_index') }}">Natures</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_partner_scope_index') }}">Structures</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ path('app_message_index') }}">Messages</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="projectsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Projets
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="projectsDropdown">
                                <li><a class="dropdown-item" href="{{ path('app_project_dashboard') }}">Tableau de bord</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_project_list') }}">Liste des projets</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_project_new') }}">Nouveau projet</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_project_search') }}">Recherche avancée</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_project_my_projects') }}">Mes projets</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_project_my_tasks') }}">Mes tâches</a></li>
                                {# Commenté temporairement car les routes n'existent pas encore
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_project_calendar') }}">Calendrier</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_project_report') }}">Rapports</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_project_resource') }}">Ressources</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_project_template') }}">Modèles</a></li>
                                #}
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ path('app_task_index') }}">Tâches</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ path('app_notification_index') }}">
                                Notifications
                                {% if app.user and unread_notifications_count() > 0 %}
                                    <span class="badge bg-danger">{{ unread_notifications_count() }}</span>
                                {% endif %}
                            </a>
                        </li>
                        {% if is_granted('ROLE_HR') or is_granted('ROLE_ADMIN') %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="hrDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Ressources Humaines
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="hrDropdown">
                                <li><a class="dropdown-item" href="{{ path('app_employee_dashboard') }}">Tableau de bord RH</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_employee_list') }}">Liste des employés</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_employee_new') }}">Nouvel employé</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_employee_search') }}">Recherche avancée</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_department_index') }}">Départements</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_position_index') }}">Postes</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_department_org_chart') }}">Organigramme</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_medical_dashboard') }}">Tableau de bord médical</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_medical_record_index') }}">Dossiers médicaux</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_medical_examination_index') }}">Examens médicaux</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_medical_record_upcoming_examinations') }}">Examens à venir</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_medical_record_advanced_search') }}">Recherche avancée</a></li>
                            </ul>
                        </li>
                        {% endif %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="missionDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Missions & Frais
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="missionDropdown">
                                <li><a class="dropdown-item" href="{{ path('app_mission_order_index') }}">Ordres de mission</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_mission_order_new') }}">Nouvel ordre de mission</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_expense_report_index') }}">Notes de frais</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_expense_report_new') }}">Nouvelle note de frais</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="purchasingDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Achats
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="purchasingDropdown">
                                <li><a class="dropdown-item" href="{{ path('app_purchasing_dashboard') }}">Tableau de bord</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_alerts_index') }}">
                                    Alertes
                                    {% if low_stock_count() > 0 or expiring_contracts_count() > 0 %}
                                        <span class="badge bg-danger">{{ low_stock_count() + expiring_contracts_count() }}</span>
                                    {% endif %}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_purchase_request_index') }}">Demandes d'achat</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_purchase_request_new') }}">Nouvelle demande</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_quote_index') }}">Devis</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_purchase_order_index') }}">Bons de commande</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_contract_index') }}">Contrats</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_goods_receipt_index') }}">Bons de réception</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_product_index') }}">Produits</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_product_category_index') }}">Catégories de produits</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_purchasing_dashboard_supplier_performance') }}">Performance fournisseurs</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_purchasing_dashboard_price_analysis') }}">Analyse des prix</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_supplier_rating_index') }}">Évaluations fournisseurs</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_supplier_recommendation_index') }}">Recommandation fournisseurs</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_stock_forecast_index') }}">Prévisions de stock</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="stockDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Stocks
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="stockDropdown">
                                <li><a class="dropdown-item" href="{{ path('app_stock_dashboard') }}">Tableau de bord</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_stock_alert_index') }}">Alertes</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_stock_item_index') }}">Articles en stock</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_stock_location_index') }}">Emplacements</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_stock_movement_index') }}">Mouvements</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_stock_movement_new') }}">Nouveau mouvement</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_stock_transfer_new') }}">Nouveau transfert</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_stock_adjustment_new') }}">Nouvel ajustement</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_stock_inventory_index') }}">Inventaires</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_stock_inventory_new') }}">Nouvel inventaire</a></li>
                            </ul>
                        </li>
                        {% if is_granted('ROLE_FINANCE') or is_granted('ROLE_ADMIN') %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="financeDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Finance
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="financeDropdown">
                                <li><a class="dropdown-item" href="{{ path('app_financial_dashboard_index') }}">Tableau de bord financier</a></li>
                                <li><a class="dropdown-item new" href="{{ path('app_analytics_financial') }}">Tableaux de bord analytiques</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_invoice_index') }}">Factures</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_invoice_new') }}">Nouvelle facture</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_invoice_status_pending_approval') }}">Factures en attente d'approbation</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_invoice_status_overdue') }}">Factures en retard</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_invoice_status_due_soon') }}">Factures à échéance proche</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_financial_dashboard_aging_report') }}">Rapport de vieillissement</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_financial_dashboard_cash_flow') }}">Flux de trésorerie</a></li>
                            </ul>
                        </li>
                        {% endif %}

                        {% if is_granted('ROLE_ACCOUNTING') or is_granted('ROLE_ADMIN') %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="accountingDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Comptabilité
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="accountingDropdown">
                                <li><a class="dropdown-item" href="{{ path('app_accounting_dashboard_index') }}">Tableau de bord comptable</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_simple_index') }}">Comptabilité simplifiée</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_analytics_index') }}">Tableaux de bord analytiques</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_account_index') }}">Plan comptable</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_journal_index') }}">Journaux</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_fiscal_year_index') }}">Exercices fiscaux</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_dashboard_trial_balance') }}">Balance</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_dashboard_general_ledger') }}">Grand Livre</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_dashboard_financial_statements') }}">États financiers</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_tax_declaration_index') }}">Déclarations fiscales</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_tax_declaration_new_tva') }}">Nouvelle déclaration TVA</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_dashboard_tax_summary') }}">Synthèse fiscale</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_fiscal_report_index') }}">Rapports fiscaux</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_accounting_import_export_index') }}">Import/Export</a></li>
                            </ul>
                        </li>
                        {% endif %}
                        {% if is_granted('ROLE_ADMIN') %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Administration
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                                <li><a class="dropdown-item" href="{{ path('app_admin_dashboard') }}">Tableau de bord</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_user_index') }}">Utilisateurs</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_role_index') }}">Rôles</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_permission_index') }}">Permissions</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_partner_status_index') }}">Statuts de partenaire</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_partner_type_index') }}">Types de partenaire</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_partner_nature_index') }}">Natures de partenaire</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_partner_scope_index') }}">Structures de partenaire</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_department_index') }}">Gestion des départements</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_department_permission_index') }}">Permissions des départements</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_position_index') }}">Gestion des postes</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_product_category_index') }}">Gestion des catégories de produits</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_system_settings') }}">Paramètres système</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_security_dashboard') }}">Sécurité</a></li>
                                <li><a class="dropdown-item" href="{{ path('app_admin_logs') }}">Journaux d'activité</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item beta" href="{{ path('app_admin_workflow_test') }}">Tests des workflows</a></li>
                                <li><a class="dropdown-item new" href="{{ path('app_admin_roadmap') }}">Feuille de route</a></li>
                            </ul>
                        </li>
                        {% endif %}
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-translate"></i>
                                {% if app.request.locale == 'fr' %}
                                    <span class="d-none d-md-inline">Français</span>
                                {% elseif app.request.locale == 'en' %}
                                    <span class="d-none d-md-inline">English</span>
                                {% elseif app.request.locale == 'es' %}
                                    <span class="d-none d-md-inline">Español</span>
                                {% elseif app.request.locale == 'ar' %}
                                    <span class="d-none d-md-inline">العربية</span>
                                {% endif %}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                                <li><a class="dropdown-item {% if app.request.locale == 'fr' %}active{% endif %}" href="{{ path('app_change_locale', {'locale': 'fr'}) }}">Français</a></li>
                                <li><a class="dropdown-item {% if app.request.locale == 'en' %}active{% endif %}" href="{{ path('app_change_locale', {'locale': 'en'}) }}">English</a></li>
                                <li><a class="dropdown-item disabled" href="#">Español (Bientôt disponible)</a></li>
                                <li><a class="dropdown-item disabled" href="#">العربية (Bientôt disponible)</a></li>
                            </ul>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link badge-new" href="{{ path('app_integration_index') }}">
                                <i class="bi bi-gear-wide-connected"></i>
                                <span class="d-none d-md-inline">Intégrations</span>
                            </a>
                        </li>

                        {% if app.user %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-person-circle"></i> {{ app.user.fullName }}
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="{{ path('app_profile') }}">Mon profil</a></li>
                                    <li><a class="dropdown-item" href="{{ path('app_profile_security') }}">Sécurité</a></li>
                                    <li><a class="dropdown-item" href="{{ path('app_profile_settings') }}">Paramètres</a></li>
                                    <li><a class="dropdown-item" href="{{ path('app_documentation_index') }}">Documentation</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ path('app_logout') }}">Déconnexion</a></li>
                                </ul>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ path('app_login') }}">Connexion</a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            {% for label, messages in app.flashes %}
                {% for message in messages %}
                    <div class="alert alert-{{ label }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endfor %}
        </div>

        {% block body %}{% endblock %}

        <footer class="bg-dark text-white mt-5 py-4">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h5>SI - Système d'Information</h5>
                        <p>© {{ "now"|date("Y") }} Tous droits réservés.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p>Version 1.0.0</p>
                    </div>
                </div>
            </div>
        </footer>
    </body>
</html>
