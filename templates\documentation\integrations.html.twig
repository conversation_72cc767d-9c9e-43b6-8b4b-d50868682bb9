{% extends 'base.html.twig' %}

{% block title %}Documentation - Intégrations{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .doc-section {
            margin-bottom: 3rem;
        }
        
        .doc-section h3 {
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #20c997;
            color: #20c997;
        }
        
        .doc-section h4 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: #495057;
        }
        
        .integration-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: box-shadow 0.15s ease-in-out;
        }
        
        .integration-card:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .integration-logo {
            width: 64px;
            height: 64px;
            margin-bottom: 1rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-planned {
            background-color: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }
        
        .status-beta {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .feature-list {
            list-style: none;
            padding-left: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li i {
            color: #28a745;
            margin-right: 0.5rem;
        }
        
        .doc-note {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .doc-warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .config-example {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
        }
    </style>
{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Documentation</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ path('app_documentation_index') }}" class="list-group-item list-group-item-action">Accueil</a>
                    <a href="{{ path('app_documentation_user_guide') }}" class="list-group-item list-group-item-action">Guide utilisateur</a>
                    <a href="{{ path('app_documentation_admin_guide') }}" class="list-group-item list-group-item-action">Guide administrateur</a>
                    <a href="{{ path('app_documentation_developer_guide') }}" class="list-group-item list-group-item-action">Guide développeur</a>
                    <a href="{{ path('app_documentation_two_factor_auth') }}" class="list-group-item list-group-item-action">Authentification 2FA</a>
                    <a href="{{ path('app_documentation_internationalization') }}" class="list-group-item list-group-item-action">Internationalisation</a>
                    <a href="{{ path('app_documentation_integrations') }}" class="list-group-item list-group-item-action active">Intégrations</a>
                    <a href="{{ path('app_documentation_api') }}" class="list-group-item list-group-item-action">API</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header" style="background-color: #20c997; color: white;">
                    <h3 class="mb-0">Intégrations externes</h3>
                </div>
                <div class="card-body">
                    <div class="doc-section">
                        <h3>Vue d'ensemble</h3>
                        <p>Le système SI propose de nombreuses intégrations avec des services externes pour étendre ses fonctionnalités et améliorer la productivité. Ces intégrations permettent de synchroniser les données, automatiser les processus et connecter votre système avec l'écosystème numérique de votre organisation.</p>
                        
                        <div class="doc-note">
                            <h5><i class="bi bi-puzzle"></i> Intégrations disponibles</h5>
                            <p>Découvrez les intégrations disponibles, en cours de développement et planifiées pour enrichir votre expérience utilisateur.</p>
                        </div>
                    </div>

                    <div class="doc-section">
                        <h3>Intégrations de productivité</h3>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="integration-card">
                                    <div class="integration-logo" style="background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);">
                                        <i class="bi bi-google"></i>
                                    </div>
                                    <div class="status-badge status-active">
                                        <i class="bi bi-check-circle"></i> Actif
                                    </div>
                                    <h5>Google Workspace</h5>
                                    <p>Synchronisation avec Gmail, Google Calendar, Google Drive et Google Docs.</p>
                                    <ul class="feature-list">
                                        <li><i class="bi bi-check"></i> Authentification SSO</li>
                                        <li><i class="bi bi-check"></i> Synchronisation calendrier</li>
                                        <li><i class="bi bi-check"></i> Partage de documents</li>
                                        <li><i class="bi bi-check"></i> Notifications email</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="integration-card">
                                    <div class="integration-logo" style="background: linear-gradient(45deg, #0078d4, #00bcf2);">
                                        <i class="bi bi-microsoft"></i>
                                    </div>
                                    <div class="status-badge status-active">
                                        <i class="bi bi-check-circle"></i> Actif
                                    </div>
                                    <h5>Microsoft 365</h5>
                                    <p>Intégration complète avec la suite Microsoft Office et Teams.</p>
                                    <ul class="feature-list">
                                        <li><i class="bi bi-check"></i> Azure AD SSO</li>
                                        <li><i class="bi bi-check"></i> Outlook Calendar</li>
                                        <li><i class="bi bi-check"></i> SharePoint</li>
                                        <li><i class="bi bi-check"></i> Microsoft Teams</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="integration-card">
                                    <div class="integration-logo" style="background: linear-gradient(45deg, #ff6900, #fcb900);">
                                        <i class="bi bi-slack"></i>
                                    </div>
                                    <div class="status-badge status-beta">
                                        <i class="bi bi-flask"></i> Bêta
                                    </div>
                                    <h5>Slack</h5>
                                    <p>Notifications et interactions directement dans Slack.</p>
                                    <ul class="feature-list">
                                        <li><i class="bi bi-check"></i> Notifications temps réel</li>
                                        <li><i class="bi bi-check"></i> Commandes slash</li>
                                        <li><i class="bi bi-check"></i> Partage de rapports</li>
                                        <li><i class="bi bi-clock"></i> Workflows automatisés</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="integration-card">
                                    <div class="integration-logo" style="background: linear-gradient(45deg, #7b68ee, #9370db);">
                                        <i class="bi bi-trello"></i>
                                    </div>
                                    <div class="status-badge status-planned">
                                        <i class="bi bi-clock"></i> Planifié
                                    </div>
                                    <h5>Trello</h5>
                                    <p>Synchronisation des tâches et projets avec Trello.</p>
                                    <ul class="feature-list">
                                        <li><i class="bi bi-clock"></i> Sync bidirectionnelle</li>
                                        <li><i class="bi bi-clock"></i> Création automatique de cartes</li>
                                        <li><i class="bi bi-clock"></i> Suivi des deadlines</li>
                                        <li><i class="bi bi-clock"></i> Rapports de progression</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="doc-section">
                        <h3>Intégrations financières</h3>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="integration-card">
                                    <div class="integration-logo" style="background: linear-gradient(45deg, #00d4aa, #00b894);">
                                        <i class="bi bi-bank"></i>
                                    </div>
                                    <div class="status-badge status-active">
                                        <i class="bi bi-check-circle"></i> Actif
                                    </div>
                                    <h5>Banques partenaires</h5>
                                    <p>Connexion sécurisée avec les principales banques françaises.</p>
                                    <ul class="feature-list">
                                        <li><i class="bi bi-check"></i> Relevés automatiques</li>
                                        <li><i class="bi bi-check"></i> Rapprochement bancaire</li>
                                        <li><i class="bi bi-check"></i> Virements SEPA</li>
                                        <li><i class="bi bi-check"></i> Notifications de paiement</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="integration-card">
                                    <div class="integration-logo" style="background: linear-gradient(45deg, #6772e5, #5469d4);">
                                        <i class="bi bi-credit-card"></i>
                                    </div>
                                    <div class="status-badge status-beta">
                                        <i class="bi bi-flask"></i> Bêta
                                    </div>
                                    <h5>Stripe</h5>
                                    <p>Traitement des paiements en ligne et facturation automatique.</p>
                                    <ul class="feature-list">
                                        <li><i class="bi bi-check"></i> Paiements en ligne</li>
                                        <li><i class="bi bi-check"></i> Abonnements récurrents</li>
                                        <li><i class="bi bi-check"></i> Webhooks temps réel</li>
                                        <li><i class="bi bi-clock"></i> Facturation automatique</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="doc-section">
                        <h3>Intégrations CRM et Marketing</h3>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="integration-card">
                                    <div class="integration-logo" style="background: linear-gradient(45deg, #ff6b35, #f7931e);">
                                        <i class="bi bi-people"></i>
                                    </div>
                                    <div class="status-badge status-planned">
                                        <i class="bi bi-clock"></i> Planifié
                                    </div>
                                    <h5>HubSpot</h5>
                                    <p>Synchronisation des contacts et opportunités commerciales.</p>
                                    <ul class="feature-list">
                                        <li><i class="bi bi-clock"></i> Sync contacts bidirectionnelle</li>
                                        <li><i class="bi bi-clock"></i> Suivi des opportunités</li>
                                        <li><i class="bi bi-clock"></i> Campagnes marketing</li>
                                        <li><i class="bi bi-clock"></i> Analytics avancés</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="integration-card">
                                    <div class="integration-logo" style="background: linear-gradient(45deg, #1f8ceb, #1a73e8);">
                                        <i class="bi bi-envelope"></i>
                                    </div>
                                    <div class="status-badge status-active">
                                        <i class="bi bi-check-circle"></i> Actif
                                    </div>
                                    <h5>Mailchimp</h5>
                                    <p>Gestion des campagnes email et newsletters.</p>
                                    <ul class="feature-list">
                                        <li><i class="bi bi-check"></i> Synchronisation listes</li>
                                        <li><i class="bi bi-check"></i> Campagnes automatisées</li>
                                        <li><i class="bi bi-check"></i> Segmentation avancée</li>
                                        <li><i class="bi bi-check"></i> Rapports détaillés</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="doc-section">
                        <h3>Configuration des intégrations</h3>

                        <h4>Prérequis</h4>
                        <p>Avant de configurer une intégration, assurez-vous de :</p>
                        <ul>
                            <li>Avoir les droits administrateur sur le système SI</li>
                            <li>Posséder un compte sur le service à intégrer</li>
                            <li>Disposer des clés API ou tokens d'authentification</li>
                            <li>Avoir configuré les permissions appropriées</li>
                        </ul>

                        <h4>Processus de configuration</h4>
                        <ol>
                            <li><strong>Accès aux paramètres :</strong> Administration → Intégrations</li>
                            <li><strong>Sélection du service :</strong> Choisir l'intégration à configurer</li>
                            <li><strong>Authentification :</strong> Saisir les identifiants ou autoriser l'accès</li>
                            <li><strong>Configuration :</strong> Définir les paramètres de synchronisation</li>
                            <li><strong>Test :</strong> Vérifier le bon fonctionnement</li>
                            <li><strong>Activation :</strong> Mettre en service l'intégration</li>
                        </ol>

                        <h4>Exemple de configuration Google Workspace</h4>
                        <div class="config-example">
# Configuration OAuth 2.0
CLIENT_ID=your-google-client-id
CLIENT_SECRET=your-google-client-secret
REDIRECT_URI=https://your-domain.com/auth/google/callback

# Scopes requis
GOOGLE_SCOPES=email,profile,calendar,drive.readonly

# Configuration de synchronisation
SYNC_CALENDAR=true
SYNC_CONTACTS=true
SYNC_FREQUENCY=15 # minutes
                        </div>

                        <div class="doc-warning">
                            <h5><i class="bi bi-exclamation-triangle"></i> Sécurité</h5>
                            <p>Ne partagez jamais vos clés API ou tokens d'authentification. Stockez-les de manière sécurisée et renouvelez-les régulièrement.</p>
                        </div>
                    </div>

                    <div class="doc-section">
                        <h3>Gestion et surveillance</h3>

                        <h4>Tableau de bord des intégrations</h4>
                        <p>Le tableau de bord vous permet de :</p>
                        <ul>
                            <li><strong>Surveiller l'état :</strong> Statut en temps réel de chaque intégration</li>
                            <li><strong>Consulter les logs :</strong> Historique des synchronisations et erreurs</li>
                            <li><strong>Gérer les paramètres :</strong> Modifier la configuration</li>
                            <li><strong>Analyser les performances :</strong> Statistiques d'utilisation</li>
                        </ul>

                        <h4>Résolution des problèmes</h4>
                        <p>En cas de dysfonctionnement :</p>
                        <ol>
                            <li>Vérifiez l'état de l'intégration dans le tableau de bord</li>
                            <li>Consultez les logs d'erreur pour identifier le problème</li>
                            <li>Vérifiez la validité des tokens d'authentification</li>
                            <li>Testez la connectivité avec le service externe</li>
                            <li>Contactez le support si le problème persiste</li>
                        </ol>
                    </div>

                    <div class="doc-section">
                        <h3>Support et assistance</h3>

                        <h4>Ressources disponibles</h4>
                        <ul>
                            <li><strong>Documentation technique :</strong> Guides détaillés pour chaque intégration</li>
                            <li><strong>Tutoriels vidéo :</strong> Démonstrations pas à pas</li>
                            <li><strong>FAQ :</strong> Réponses aux questions fréquentes</li>
                            <li><strong>Support technique :</strong> Assistance personnalisée</li>
                        </ul>

                        <div class="doc-note">
                            <h5><i class="bi bi-lightbulb"></i> Suggestion d'intégration</h5>
                            <p>Vous avez besoin d'une intégration qui n'est pas encore disponible ? Contactez notre équipe pour étudier la faisabilité et la planification.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
