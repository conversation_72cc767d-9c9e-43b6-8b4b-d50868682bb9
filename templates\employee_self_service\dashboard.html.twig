{% extends 'base.html.twig' %}

{% block title %}Mon Espace Employé - {{ employee.user.firstName }} {{ employee.user.lastName }}{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-user-circle text-primary"></i>
                    Mon Espace Employé
                </h1>
                <div>
                    <a href="{{ path('app_employee_self_service_request_new') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Nouvelle demande
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques personnelles -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.total_requests }}</h4>
                            <p class="card-text">Total demandes</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.pending_requests }}</h4>
                            <p class="card-text">En attente</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.approved_requests }}</h4>
                            <p class="card-text">Approuvées</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.rejected_requests }}</h4>
                            <p class="card-text">Rejetées</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Demandes en attente -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock text-warning"></i>
                        Demandes en attente
                    </h5>
                    {% if pending_requests|length > 0 %}
                        <span class="badge bg-warning">{{ pending_requests|length }}</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if pending_requests|length > 0 %}
                        <div class="list-group list-group-flush">
                            {% for request in pending_requests %}
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">{{ request.title }}</div>
                                        <small class="text-muted">
                                            {{ request.typeLabel }} - {{ request.createdAt|date('d/m/Y H:i') }}
                                        </small>
                                    </div>
                                    <div>
                                        <span class="badge bg-{{ request.priority == 'urgent' ? 'danger' : (request.priority == 'high' ? 'warning' : 'secondary') }} rounded-pill">
                                            {{ request.priorityLabel }}
                                        </span>
                                        <a href="{{ path('app_employee_self_service_request_show', {id: request.id}) }}" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <p>Aucune demande en attente</p>
                        </div>
                    {% endif %}
                </div>
                {% if pending_requests|length > 0 %}
                    <div class="card-footer text-center">
                        <a href="{{ path('app_employee_self_service_requests', {status: 'pending'}) }}" class="btn btn-sm btn-outline-primary">
                            Voir toutes les demandes en attente
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Demandes récentes -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history text-info"></i>
                        Demandes récentes
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_requests|length > 0 %}
                        <div class="list-group list-group-flush">
                            {% for request in recent_requests %}
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">{{ request.title }}</div>
                                        <small class="text-muted">
                                            {{ request.typeLabel }} - {{ request.createdAt|date('d/m/Y H:i') }}
                                        </small>
                                    </div>
                                    <div>
                                        <span class="badge bg-{{ request.status == 'approved' ? 'success' : (request.status == 'rejected' ? 'danger' : 'warning') }} rounded-pill">
                                            {{ request.statusLabel }}
                                        </span>
                                        <a href="{{ path('app_employee_self_service_request_show', {id: request.id}) }}" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-file-alt fa-3x mb-3"></i>
                            <p>Aucune demande récente</p>
                            <a href="{{ path('app_employee_self_service_request_new') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Créer ma première demande
                            </a>
                        </div>
                    {% endif %}
                </div>
                {% if recent_requests|length > 0 %}
                    <div class="card-footer text-center">
                        <a href="{{ path('app_employee_self_service_requests') }}" class="btn btn-sm btn-outline-primary">
                            Voir toutes mes demandes
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt text-warning"></i>
                        Actions rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ path('app_employee_self_service_salary_advance_new') }}" class="btn btn-outline-primary w-100 mb-2">
                                <i class="fas fa-money-bill-wave d-block mb-2"></i>
                                Demande d'avance sur salaire
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ path('app_employee_self_service_document_new') }}" class="btn btn-outline-info w-100 mb-2">
                                <i class="fas fa-file-download d-block mb-2"></i>
                                Demande de document
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ path('app_employee_leave_new', {employeeId: employee.id}) }}" class="btn btn-outline-success w-100 mb-2">
                                <i class="fas fa-calendar-alt d-block mb-2"></i>
                                Demande de congé
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ path('app_employee_self_service_requests') }}" class="btn btn-outline-secondary w-100 mb-2">
                                <i class="fas fa-list d-block mb-2"></i>
                                Toutes mes demandes
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
    // Auto-refresh des statistiques toutes les 5 minutes
    setInterval(function() {
        // Ici on pourrait ajouter un appel AJAX pour rafraîchir les stats
        console.log('Refresh stats...');
    }, 300000);
</script>
{% endblock %}
