{% extends 'base.html.twig' %}

{% block title %}Nouvelle demande - {{ employee.user.firstName }} {{ employee.user.lastName }}{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-plus-circle text-primary"></i>
                    Nouvelle demande
                </h1>
                <a href="{{ path('app_employee_self_service_dashboard') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour au dashboard
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Choi<PERSON><PERSON>z le type de demande</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Demande d'avance sur salaire -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-money-bill-wave fa-4x text-primary"></i>
                                    </div>
                                    <h5 class="card-title">Avance sur salaire</h5>
                                    <p class="card-text">
                                        Demandez une avance sur votre salaire pour faire face à des besoins financiers urgents.
                                    </p>
                                    <ul class="list-unstyled text-start small text-muted">
                                        <li><i class="fas fa-check text-success"></i> Montant jusqu'à 5000€</li>
                                        <li><i class="fas fa-check text-success"></i> Remboursement étalé</li>
                                        <li><i class="fas fa-check text-success"></i> Traitement rapide</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ path('app_employee_self_service_salary_advance_new') }}" class="btn btn-primary w-100">
                                        <i class="fas fa-arrow-right"></i> Commencer
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Demande de document -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-info">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-file-download fa-4x text-info"></i>
                                    </div>
                                    <h5 class="card-title">Document administratif</h5>
                                    <p class="card-text">
                                        Demandez des documents officiels comme des attestations, certificats ou bulletins de paie.
                                    </p>
                                    <ul class="list-unstyled text-start small text-muted">
                                        <li><i class="fas fa-check text-success"></i> Certificat de travail</li>
                                        <li><i class="fas fa-check text-success"></i> Attestation d'emploi</li>
                                        <li><i class="fas fa-check text-success"></i> Bulletins de paie</li>
                                        <li><i class="fas fa-check text-success"></i> Et plus encore...</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ path('app_employee_self_service_document_new') }}" class="btn btn-info w-100">
                                        <i class="fas fa-arrow-right"></i> Commencer
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Demande de congé -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-calendar-alt fa-4x text-success"></i>
                                    </div>
                                    <h5 class="card-title">Demande de congé</h5>
                                    <p class="card-text">
                                        Planifiez vos congés payés, RTT, congés sans solde ou autres types d'absences.
                                    </p>
                                    <ul class="list-unstyled text-start small text-muted">
                                        <li><i class="fas fa-check text-success"></i> Congés payés</li>
                                        <li><i class="fas fa-check text-success"></i> RTT</li>
                                        <li><i class="fas fa-check text-success"></i> Congés sans solde</li>
                                        <li><i class="fas fa-check text-success"></i> Récupération</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <a href="{{ path('app_employee_leave_new', {employeeId: employee.id}) }}" class="btn btn-success w-100">
                                        <i class="fas fa-arrow-right"></i> Commencer
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Autre demande -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-secondary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-question-circle fa-4x text-secondary"></i>
                                    </div>
                                    <h5 class="card-title">Autre demande</h5>
                                    <p class="card-text">
                                        Pour toute autre demande spécifique qui ne rentre pas dans les catégories précédentes.
                                    </p>
                                    <ul class="list-unstyled text-start small text-muted">
                                        <li><i class="fas fa-check text-success"></i> Changement d'informations</li>
                                        <li><i class="fas fa-check text-success"></i> Demande de formation</li>
                                        <li><i class="fas fa-check text-success"></i> Demande de matériel</li>
                                        <li><i class="fas fa-check text-success"></i> Télétravail</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <button class="btn btn-secondary w-100" onclick="showOtherRequestForm()">
                                        <i class="fas fa-arrow-right"></i> Commencer
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Informations utiles -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 border-warning">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-info-circle fa-4x text-warning"></i>
                                    </div>
                                    <h5 class="card-title">Informations utiles</h5>
                                    <p class="card-text">
                                        Consultez le guide des demandes et les délais de traitement.
                                    </p>
                                    <ul class="list-unstyled text-start small text-muted">
                                        <li><i class="fas fa-clock text-info"></i> Délais de traitement</li>
                                        <li><i class="fas fa-users text-info"></i> Processus d'approbation</li>
                                        <li><i class="fas fa-question text-info"></i> FAQ</li>
                                        <li><i class="fas fa-phone text-info"></i> Contact RH</li>
                                    </ul>
                                </div>
                                <div class="card-footer">
                                    <button class="btn btn-warning w-100" onclick="showInfoModal()">
                                        <i class="fas fa-book"></i> Consulter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour autre demande -->
<div class="modal fade" id="otherRequestModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Autre demande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Cette fonctionnalité sera bientôt disponible. En attendant, vous pouvez :</p>
                <ul>
                    <li>Contacter directement le service RH</li>
                    <li>Envoyer un email à <EMAIL></li>
                    <li>Utiliser le formulaire de contact interne</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'informations -->
<div class="modal fade" id="infoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Guide des demandes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Délais de traitement moyens :</h6>
                <ul>
                    <li><strong>Avance sur salaire :</strong> 3-5 jours ouvrés</li>
                    <li><strong>Documents administratifs :</strong> 1-3 jours ouvrés</li>
                    <li><strong>Demandes de congé :</strong> 2-7 jours ouvrés</li>
                </ul>
                
                <h6>Processus d'approbation :</h6>
                <ol>
                    <li>Soumission de la demande par l'employé</li>
                    <li>Validation par le manager direct (si requis)</li>
                    <li>Traitement par le service RH</li>
                    <li>Approbation finale et notification</li>
                </ol>
                
                <h6>Contact :</h6>
                <p>Pour toute question, contactez le service RH :</p>
                <ul>
                    <li>Email : <EMAIL></li>
                    <li>Téléphone : 01 23 45 67 89</li>
                    <li>Bureau : RDC, salle 105</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Compris</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
    function showOtherRequestForm() {
        const modal = new bootstrap.Modal(document.getElementById('otherRequestModal'));
        modal.show();
    }
    
    function showInfoModal() {
        const modal = new bootstrap.Modal(document.getElementById('infoModal'));
        modal.show();
    }
</script>
{% endblock %}
