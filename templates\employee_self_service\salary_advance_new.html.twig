{% extends 'base.html.twig' %}

{% block title %}Demande d'avance sur salaire - {{ employee.user.firstName }} {{ employee.user.lastName }}{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-money-bill-wave text-primary"></i>
                    Demande d'avance sur salaire
                </h1>
                <a href="{{ path('app_employee_self_service_request_new') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Formulaire de demande</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ path('app_employee_self_service_salary_advance_new') }}" id="salary-advance-form">
                        <div class="mb-3">
                            <label for="amount" class="form-label">Montant demandé (€) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="amount" name="amount" min="100" max="5000" step="50" required>
                                <span class="input-group-text">€</span>
                            </div>
                            <div class="form-text">Montant maximum autorisé : 5000€</div>
                        </div>

                        <div class="mb-3">
                            <label for="repayment_months" class="form-label">Durée de remboursement <span class="text-danger">*</span></label>
                            <select class="form-select" id="repayment_months" name="repayment_months" required>
                                <option value="">Choisir...</option>
                                <option value="1">1 mois</option>
                                <option value="2">2 mois</option>
                                <option value="3">3 mois</option>
                                <option value="4">4 mois</option>
                                <option value="6">6 mois</option>
                                <option value="12">12 mois</option>
                                <option value="24">24 mois</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="monthly_deduction" class="form-label">Déduction mensuelle estimée</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="monthly_deduction" readonly>
                                <span class="input-group-text">€</span>
                            </div>
                            <div class="form-text">Ce montant sera déduit de votre salaire chaque mois</div>
                        </div>

                        <div class="mb-3">
                            <label for="reason" class="form-label">Motif de la demande <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="reason" name="reason" rows="4" required minlength="10"></textarea>
                            <div class="form-text">Veuillez expliquer brièvement la raison de cette demande d'avance</div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                Je comprends que cette avance sera déduite de mes prochains salaires selon le plan de remboursement choisi
                            </label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Soumettre ma demande
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        Informations
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Processus d'approbation :</h6>
                    <ol class="small">
                        <li>Validation par votre manager</li>
                        <li>Examen par le service RH</li>
                        <li>Approbation finale (montants > 1000€)</li>
                        <li>Versement sur votre compte</li>
                    </ol>

                    <h6>Délai de traitement :</h6>
                    <p class="small">3 à 5 jours ouvrés en moyenne</p>

                    <h6>Conditions :</h6>
                    <ul class="small">
                        <li>Montant maximum : 5000€</li>
                        <li>Durée maximum : 24 mois</li>
                        <li>Une seule avance active à la fois</li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <div class="card-header bg-warning">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator"></i>
                        Simulateur
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="sim_amount" class="form-label">Montant</label>
                        <input type="range" class="form-range" id="sim_amount" min="100" max="5000" step="100" value="1000">
                        <div class="text-center" id="sim_amount_display">1000€</div>
                    </div>

                    <div class="mb-3">
                        <label for="sim_months" class="form-label">Durée (mois)</label>
                        <input type="range" class="form-range" id="sim_months" min="1" max="24" step="1" value="6">
                        <div class="text-center" id="sim_months_display">6 mois</div>
                    </div>

                    <div class="alert alert-info">
                        <div class="d-flex justify-content-between">
                            <span>Mensualité :</span>
                            <strong id="sim_monthly">166.67€</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Total remboursé :</span>
                            <strong id="sim_total">1000€</strong>
                        </div>
                    </div>

                    <button type="button" class="btn btn-sm btn-outline-primary w-100" id="apply_simulation">
                        Appliquer ces valeurs
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calculer la déduction mensuelle
        function calculateMonthlyDeduction() {
            const amount = parseFloat(document.getElementById('amount').value) || 0;
            const months = parseInt(document.getElementById('repayment_months').value) || 1;
            
            if (amount > 0 && months > 0) {
                const monthly = (amount / months).toFixed(2);
                document.getElementById('monthly_deduction').value = monthly;
            } else {
                document.getElementById('monthly_deduction').value = '';
            }
        }

        // Mettre à jour le simulateur
        function updateSimulator() {
            const amount = document.getElementById('sim_amount').value;
            const months = document.getElementById('sim_months').value;
            
            document.getElementById('sim_amount_display').textContent = amount + '€';
            document.getElementById('sim_months_display').textContent = months + ' mois';
            
            const monthly = (amount / months).toFixed(2);
            document.getElementById('sim_monthly').textContent = monthly + '€';
            document.getElementById('sim_total').textContent = amount + '€';
        }

        // Appliquer les valeurs du simulateur au formulaire
        function applySimulation() {
            const amount = document.getElementById('sim_amount').value;
            const months = document.getElementById('sim_months').value;
            
            document.getElementById('amount').value = amount;
            document.getElementById('repayment_months').value = months;
            
            calculateMonthlyDeduction();
        }

        // Événements
        document.getElementById('amount').addEventListener('input', calculateMonthlyDeduction);
        document.getElementById('repayment_months').addEventListener('change', calculateMonthlyDeduction);
        
        document.getElementById('sim_amount').addEventListener('input', updateSimulator);
        document.getElementById('sim_months').addEventListener('input', updateSimulator);
        
        document.getElementById('apply_simulation').addEventListener('click', applySimulation);
        
        // Initialisation
        updateSimulator();
    });
</script>
{% endblock %}
