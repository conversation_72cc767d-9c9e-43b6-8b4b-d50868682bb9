{% extends 'base.html.twig' %}

{% block title %}Tableau de bord - CRM System{% endblock %}

{% block body %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">Bienvenue, {{ app.user.fullName }}</h3>
                </div>
                <div class="card-body">
                    <p class="lead">Bienvenue dans votre tableau de bord CRM.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-chart-line me-2"></i>Performance du système</h5>
                </div>
                <div class="card-body">
                    <canvas id="mainChart" width="100%" height="50"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-tasks me-2"></i>Répartition des éléments</h5>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" width="100%" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-handshake me-2"></i>Partenaires</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-0">{{ app.session.get('partner_count', 0) }}</h2>
                            <p class="text-muted">Partenaires actifs</p>
                        </div>
                        <i class="bi bi-building fs-1"></i>
                    </div>
                    <a href="{{ path('app_partner') }}" class="btn btn-sm btn-outline-primary mt-3">Voir les partenaires</a>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-list-check me-2"></i>Tâches</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-0">{{ app.session.get('task_count', 0) }}</h2>
                            <p class="text-muted">Tâches en attente</p>
                        </div>
                        <i class="bi bi-list-check fs-1"></i>
                    </div>
                    <a href="{{ path('app_task_index') }}" class="btn btn-sm btn-outline-primary mt-3">Voir les tâches</a>
                </div>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Notifications</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-0">{{ app.session.get('notification_count', 0) }}</h2>
                            <p class="text-muted">Notifications non lues</p>
                        </div>
                        <i class="bi bi-bell fs-1"></i>
                    </div>
                    <a href="{{ path('app_notifications_index') }}" class="btn btn-sm btn-outline-primary mt-3">Voir les notifications</a>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2"></i>Répartition des tâches</h5>
                </div>
                <div class="card-body">
                    <canvas id="taskChart" width="100%" height="200"></canvas>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i>Activité récente</h5>
                </div>
                <div class="card-body">
                    <canvas id="activityChart" width="100%" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Activité récente</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        {% for i in 1..5 %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="badge bg-primary rounded-pill me-2">{{ i }}</span>
                                    Activité {{ i }}
                                </div>
                                <small class="text-muted">Il y a {{ i }} heure(s)</small>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="card-footer text-end">
                    <a href="#" class="btn btn-sm btn-outline-secondary">Voir toutes les activités</a>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Tâches à venir</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        {% for i in 1..5 %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="badge bg-warning rounded-pill me-2">{{ i }}</span>
                                    Tâche {{ i }}
                                </div>
                                <small class="text-muted">Dans {{ i }} jour(s)</small>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="card-footer text-end">
                    <a href="{{ path('app_task_index') }}" class="btn btn-sm btn-outline-secondary">Voir toutes les tâches</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Performance Overview Chart
            var mainCtx = document.getElementById('mainChart').getContext('2d');
            var mainChart = new Chart(mainCtx, {
                type: 'line',
                data: {
                    labels: ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet'],
                    datasets: [
                        {
                            label: 'Partenaires',
                            data: [{{ app.session.get('partner_count', 0) - 5 }}, {{ app.session.get('partner_count', 0) - 3 }}, {{ app.session.get('partner_count', 0) - 2 }}, {{ app.session.get('partner_count', 0) - 1 }}, {{ app.session.get('partner_count', 0) }}, {{ app.session.get('partner_count', 0) + 2 }}, {{ app.session.get('partner_count', 0) + 4 }}],
                            backgroundColor: 'rgba(13, 110, 253, 0.2)',
                            borderColor: 'rgba(13, 110, 253, 1)',
                            borderWidth: 2,
                            tension: 0.4
                        },
                        {
                            label: 'Tâches',
                            data: [{{ app.session.get('task_count', 0) - 10 }}, {{ app.session.get('task_count', 0) - 7 }}, {{ app.session.get('task_count', 0) - 5 }}, {{ app.session.get('task_count', 0) - 2 }}, {{ app.session.get('task_count', 0) }}, {{ app.session.get('task_count', 0) + 3 }}, {{ app.session.get('task_count', 0) + 5 }}],
                            backgroundColor: 'rgba(255, 193, 7, 0.2)',
                            borderColor: 'rgba(255, 193, 7, 1)',
                            borderWidth: 2,
                            tension: 0.4
                        },
                        {
                            label: 'Notifications',
                            data: [{{ app.session.get('notification_count', 0) + 5 }}, {{ app.session.get('notification_count', 0) + 8 }}, {{ app.session.get('notification_count', 0) + 12 }}, {{ app.session.get('notification_count', 0) + 7 }}, {{ app.session.get('notification_count', 0) + 3 }}, {{ app.session.get('notification_count', 0) + 1 }}, {{ app.session.get('notification_count', 0) }}],
                            backgroundColor: 'rgba(220, 53, 69, 0.2)',
                            borderColor: 'rgba(220, 53, 69, 1)',
                            borderWidth: 2,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Évolution du système'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // System Status Chart
            var statusCtx = document.getElementById('statusChart').getContext('2d');
            var statusChart = new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Partenaires', 'Tâches', 'Notifications'],
                    datasets: [{
                        data: [{{ app.session.get('partner_count', 0) }}, {{ app.session.get('task_count', 0) }}, {{ app.session.get('notification_count', 0) }}],
                        backgroundColor: [
                            'rgba(13, 110, 253, 0.8)',
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(220, 53, 69, 0.8)'
                        ],
                        borderColor: [
                            'rgba(13, 110, 253, 1)',
                            'rgba(255, 193, 7, 1)',
                            'rgba(220, 53, 69, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        },
                        title: {
                            display: true,
                            text: 'Répartition des éléments'
                        }
                    }
                }
            });

            // Task Chart
            var taskCtx = document.getElementById('taskChart').getContext('2d');
            var taskChart = new Chart(taskCtx, {
                type: 'pie',
                data: {
                    labels: ['En attente', 'En cours', 'Terminées'],
                    datasets: [{
                        data: [{{ app.session.get('task_count', 0) }}, {{ app.session.get('task_count', 0) // 2 }}, {{ app.session.get('task_count', 0) // 3 }}],
                        backgroundColor: [
                            'rgba(255, 193, 7, 0.8)',
                            'rgba(13, 110, 253, 0.8)',
                            'rgba(40, 167, 69, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 193, 7, 1)',
                            'rgba(13, 110, 253, 1)',
                            'rgba(40, 167, 69, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });

            // Activity Chart
            var activityCtx = document.getElementById('activityChart').getContext('2d');
            var activityChart = new Chart(activityCtx, {
                type: 'bar',
                data: {
                    labels: ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'],
                    datasets: [{
                        label: 'Activités',
                        data: [8, 12, 15, 10, 7, 3, 1],
                        backgroundColor: 'rgba(23, 162, 184, 0.8)',
                        borderColor: 'rgba(23, 162, 184, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
        });
    </script>
{% endblock %}
