{% extends 'base.html.twig' %}

{% block title %}Tableau de bord RH - Gestion des demandes{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-users-cog text-primary"></i>
                    Tableau de bord RH - Gestion des demandes
                </h1>
                <div>
                    <a href="{{ path('app_hr_requests_statistics') }}" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i> Statistiques
                    </a>
                    <a href="{{ path('app_hr_requests_list') }}" class="btn btn-primary">
                        <i class="fas fa-list"></i> Toutes les demandes
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.total ?? 0 }}</h4>
                            <p class="card-text">Total demandes</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.by_status.pending ?? 0 }}</h4>
                            <p class="card-text">En attente</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ urgent_requests|length }}</h4>
                            <p class="card-text">Urgentes</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.by_status.approved ?? 0 }}</h4>
                            <p class="card-text">Approuvées</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Demandes urgentes -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle text-danger"></i>
                        Demandes urgentes
                    </h5>
                    {% if urgent_requests|length > 0 %}
                        <span class="badge bg-danger">{{ urgent_requests|length }}</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if urgent_requests|length > 0 %}
                        <div class="list-group list-group-flush">
                            {% for request in urgent_requests|slice(0, 5) %}
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">{{ request.title }}</div>
                                        <small class="text-muted">
                                            {{ request.employee.user.firstName }} {{ request.employee.user.lastName }} - 
                                            {{ request.typeLabel }} - {{ request.createdAt|date('d/m/Y H:i') }}
                                        </small>
                                    </div>
                                    <div>
                                        <span class="badge bg-danger rounded-pill">{{ request.priorityLabel }}</span>
                                        <a href="{{ path('app_hr_requests_show', {id: request.id}) }}" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                            <p>Aucune demande urgente</p>
                        </div>
                    {% endif %}
                </div>
                {% if urgent_requests|length > 5 %}
                    <div class="card-footer text-center">
                        <a href="{{ path('app_hr_requests_list', {priority: 'urgent'}) }}" class="btn btn-sm btn-outline-danger">
                            Voir toutes les demandes urgentes ({{ urgent_requests|length }})
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Demandes en attente -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock text-warning"></i>
                        Demandes en attente
                    </h5>
                    {% if pending_requests|length > 0 %}
                        <span class="badge bg-warning">{{ pending_requests|length }}</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if pending_requests|length > 0 %}
                        <div class="list-group list-group-flush">
                            {% for request in pending_requests %}
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">{{ request.title }}</div>
                                        <small class="text-muted">
                                            {{ request.employee.user.firstName }} {{ request.employee.user.lastName }} - 
                                            {{ request.typeLabel }} - {{ request.createdAt|date('d/m/Y H:i') }}
                                        </small>
                                    </div>
                                    <div>
                                        <span class="badge bg-{{ request.priority == 'urgent' ? 'danger' : (request.priority == 'high' ? 'warning' : 'secondary') }} rounded-pill">
                                            {{ request.priorityLabel }}
                                        </span>
                                        <a href="{{ path('app_hr_requests_show', {id: request.id}) }}" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                            <p>Aucune demande en attente</p>
                        </div>
                    {% endif %}
                </div>
                {% if pending_requests|length > 0 %}
                    <div class="card-footer text-center">
                        <a href="{{ path('app_hr_requests_pending') }}" class="btn btn-sm btn-outline-warning">
                            Voir toutes les demandes en attente
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Statistiques par type -->
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-money-bill-wave text-success"></i>
                        Avances sur salaire
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-warning">{{ salary_advance_stats.by_status.pending.count ?? 0 }}</h4>
                            <small>En attente</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ salary_advance_stats.by_status.approved.count ?? 0 }}</h4>
                            <small>Approuvées</small>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <p class="mb-1"><strong>Montant total demandé</strong></p>
                        <h5 class="text-primary">{{ salary_advance_stats.total_amount_requested|number_format(2, ',', ' ') }}€</h5>
                    </div>
                </div>
                <div class="card-footer text-center">
                    <a href="{{ path('app_hr_requests_salary_advances') }}" class="btn btn-sm btn-outline-success">
                        Gérer les avances
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-download text-info"></i>
                        Documents
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-danger">{{ urgent_documents|length }}</h4>
                            <small>Urgents</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ stats.by_type.document ?? 0 }}</h4>
                            <small>Total</small>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <p class="mb-1"><strong>Types les plus demandés</strong></p>
                        <small class="text-muted">Certificats, Attestations, Bulletins</small>
                    </div>
                </div>
                <div class="card-footer text-center">
                    <a href="{{ path('app_hr_requests_documents') }}" class="btn btn-sm btn-outline-info">
                        Gérer les documents
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt text-primary"></i>
                        Congés
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-warning">{{ stats.by_type.leave ?? 0 }}</h4>
                            <small>Demandes</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ stats.by_status.approved ?? 0 }}</h4>
                            <small>Approuvées</small>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <p class="mb-1"><strong>Système intégré</strong></p>
                        <small class="text-muted">Gestion via module existant</small>
                    </div>
                </div>
                <div class="card-footer text-center">
                    <a href="{{ path('app_employee_dashboard') }}" class="btn btn-sm btn-outline-primary">
                        Gérer les congés
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt text-warning"></i>
                        Actions rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ path('app_hr_requests_pending') }}" class="btn btn-outline-warning w-100 mb-2">
                                <i class="fas fa-clock d-block mb-2"></i>
                                Traiter les demandes en attente
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ path('app_hr_requests_list', {priority: 'urgent'}) }}" class="btn btn-outline-danger w-100 mb-2">
                                <i class="fas fa-exclamation-triangle d-block mb-2"></i>
                                Demandes urgentes
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ path('app_hr_requests_salary_advances') }}" class="btn btn-outline-success w-100 mb-2">
                                <i class="fas fa-money-bill-wave d-block mb-2"></i>
                                Avances sur salaire
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ path('app_hr_requests_statistics') }}" class="btn btn-outline-info w-100 mb-2">
                                <i class="fas fa-chart-bar d-block mb-2"></i>
                                Rapports et statistiques
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
    // Auto-refresh des statistiques toutes les 2 minutes
    setInterval(function() {
        fetch('{{ path('app_hr_requests_api_stats') }}')
            .then(response => response.json())
            .then(data => {
                // Mettre à jour les statistiques en temps réel
                console.log('Stats updated:', data);
            })
            .catch(error => console.error('Error updating stats:', error));
    }, 120000);
</script>
{% endblock %}
