{% extends 'base.html.twig' %}

{% block title %}Liste des demandes - Gestion RH{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-list text-primary"></i>
                    Liste des demandes
                </h1>
                <div>
                    <a href="{{ path('app_hr_requests_dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour au dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter"></i>
                        Filtres
                    </h5>
                </div>
                <div class="card-body">
                    <form method="get" action="{{ path('app_hr_requests_list') }}" class="row g-3">
                        <div class="col-md-2">
                            <label for="status" class="form-label">Statut</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Tous</option>
                                <option value="pending" {{ current_status == 'pending' ? 'selected' : '' }}>En attente</option>
                                <option value="manager_approved" {{ current_status == 'manager_approved' ? 'selected' : '' }}>Approuvé manager</option>
                                <option value="hr_review" {{ current_status == 'hr_review' ? 'selected' : '' }}>Examen RH</option>
                                <option value="approved" {{ current_status == 'approved' ? 'selected' : '' }}>Approuvé</option>
                                <option value="rejected" {{ current_status == 'rejected' ? 'selected' : '' }}>Rejeté</option>
                                <option value="cancelled" {{ current_status == 'cancelled' ? 'selected' : '' }}>Annulé</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="type" class="form-label">Type</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">Tous</option>
                                <option value="leave" {{ current_type == 'leave' ? 'selected' : '' }}>Congé</option>
                                <option value="salary_advance" {{ current_type == 'salary_advance' ? 'selected' : '' }}>Avance salaire</option>
                                <option value="document" {{ current_type == 'document' ? 'selected' : '' }}>Document</option>
                                <option value="other" {{ current_type == 'other' ? 'selected' : '' }}>Autre</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="priority" class="form-label">Priorité</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="">Toutes</option>
                                <option value="urgent" {{ current_priority == 'urgent' ? 'selected' : '' }}>Urgente</option>
                                <option value="high" {{ current_priority == 'high' ? 'selected' : '' }}>Haute</option>
                                <option value="medium" {{ current_priority == 'medium' ? 'selected' : '' }}>Moyenne</option>
                                <option value="low" {{ current_priority == 'low' ? 'selected' : '' }}>Basse</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="search" class="form-label">Recherche</label>
                            <input type="text" class="form-control" id="search" name="search" value="{{ current_search }}" placeholder="Nom, titre, description...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filtrer
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions en masse -->
    <div class="row mb-3">
        <div class="col-12">
            <form id="bulk-action-form" method="post" action="{{ path('app_hr_requests_bulk_action') }}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <button type="button" id="select-all" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-check-square"></i> Tout sélectionner
                        </button>
                        <button type="button" id="deselect-all" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-square"></i> Tout désélectionner
                        </button>
                    </div>
                    <div>
                        <select name="action" class="form-select form-select-sm d-inline-block w-auto me-2">
                            <option value="">Action en masse...</option>
                            <option value="approve">Approuver</option>
                            <option value="reject">Rejeter</option>
                        </select>
                        <button type="submit" class="btn btn-sm btn-warning" onclick="return confirm('Êtes-vous sûr de vouloir effectuer cette action sur les demandes sélectionnées ?')">
                            <i class="fas fa-bolt"></i> Exécuter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des demandes -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        Demandes ({{ requests|length }} résultat{{ requests|length > 1 ? 's' : '' }})
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if requests|length > 0 %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" id="master-checkbox" class="form-check-input">
                                        </th>
                                        <th>Employé</th>
                                        <th>Type</th>
                                        <th>Titre</th>
                                        <th>Priorité</th>
                                        <th>Statut</th>
                                        <th>Date création</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for request in requests %}
                                        <tr>
                                            <td>
                                                <input type="checkbox" name="request_ids[]" value="{{ request.id }}" class="form-check-input request-checkbox" form="bulk-action-form">
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        {{ request.employee.user.firstName|first }}{{ request.employee.user.lastName|first }}
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">{{ request.employee.user.firstName }} {{ request.employee.user.lastName }}</div>
                                                        <small class="text-muted">{{ request.employee.department.name ?? 'N/A' }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ request.type == 'salary_advance' ? 'success' : (request.type == 'document' ? 'info' : (request.type == 'leave' ? 'primary' : 'secondary')) }}">
                                                    {{ request.typeLabel }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="fw-bold">{{ request.title }}</div>
                                                <small class="text-muted">{{ request.description|length > 50 ? request.description|slice(0, 50) ~ '...' : request.description }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ request.priority == 'urgent' ? 'danger' : (request.priority == 'high' ? 'warning' : (request.priority == 'medium' ? 'info' : 'secondary')) }}">
                                                    {{ request.priorityLabel }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ request.status == 'approved' ? 'success' : (request.status == 'rejected' ? 'danger' : 'warning') }}">
                                                    {{ request.statusLabel }}
                                                </span>
                                            </td>
                                            <td>
                                                <div>{{ request.createdAt|date('d/m/Y') }}</div>
                                                <small class="text-muted">{{ request.createdAt|date('H:i') }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ path('app_hr_requests_show', {id: request.id}) }}" class="btn btn-sm btn-outline-primary" title="Voir détails">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    {% if request.canBeApproved() %}
                                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="quickApprove({{ request.id }})" title="Approuver rapidement">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                    {% endif %}
                                                    {% if request.canBeRejected() %}
                                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="quickReject({{ request.id }})" title="Rejeter rapidement">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucune demande trouvée</h5>
                            <p class="text-muted">Essayez de modifier vos critères de recherche</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'approbation rapide -->
<div class="modal fade" id="quickApproveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Approbation rapide</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="quick-approve-form" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="approve-comment" class="form-label">Commentaire (optionnel)</label>
                        <textarea class="form-control" id="approve-comment" name="comment" rows="3" placeholder="Commentaire d'approbation..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Approuver
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de rejet rapide -->
<div class="modal fade" id="quickRejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Rejet rapide</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="quick-reject-form" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reject-reason" class="form-label">Raison du rejet <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reject-reason" name="reason" rows="3" placeholder="Veuillez expliquer la raison du rejet..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i> Rejeter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
    // Gestion des cases à cocher
    document.getElementById('master-checkbox').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.request-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    document.getElementById('select-all').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('.request-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        document.getElementById('master-checkbox').checked = true;
    });

    document.getElementById('deselect-all').addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('.request-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        document.getElementById('master-checkbox').checked = false;
    });

    // Actions rapides
    function quickApprove(requestId) {
        const form = document.getElementById('quick-approve-form');
        form.action = `{{ path('app_hr_requests_approve', {id: '__ID__'}) }}`.replace('__ID__', requestId);
        const modal = new bootstrap.Modal(document.getElementById('quickApproveModal'));
        modal.show();
    }

    function quickReject(requestId) {
        const form = document.getElementById('quick-reject-form');
        form.action = `{{ path('app_hr_requests_reject', {id: '__ID__'}) }}`.replace('__ID__', requestId);
        const modal = new bootstrap.Modal(document.getElementById('quickRejectModal'));
        modal.show();
    }
</script>
{% endblock %}
