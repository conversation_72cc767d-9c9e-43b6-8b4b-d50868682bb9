{% extends 'base.html.twig' %}

{% block title %}Gestion des avances sur salaire - RH{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-money-bill-wave text-success"></i>
                    Gestion des avances sur salaire
                </h1>
                <div>
                    <a href="{{ path('app_hr_requests_dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour au dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques des avances -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.by_status.pending.count ?? 0 }}</h4>
                            <p class="card-text">En attente</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.by_status.approved.count ?? 0 }}</h4>
                            <p class="card-text">Approuvées</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.by_status.in_progress.count ?? 0 }}</h4>
                            <p class="card-text">En remboursement</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-sync-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ stats.total_amount_approved|number_format(0, ',', ' ') }}€</h4>
                            <p class="card-text">Total approuvé</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-euro-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Avances en attente d'approbation -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock text-warning"></i>
                        En attente d'approbation
                    </h5>
                    {% if pending_advances|length > 0 %}
                        <span class="badge bg-warning">{{ pending_advances|length }}</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if pending_advances|length > 0 %}
                        <div class="list-group list-group-flush">
                            {% for advance in pending_advances %}
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="ms-2 me-auto">
                                            <div class="fw-bold">{{ advance.employee.user.firstName }} {{ advance.employee.user.lastName }}</div>
                                            <div class="text-primary fw-bold">{{ advance.amount|number_format(2, ',', ' ') }}€</div>
                                            <small class="text-muted">{{ advance.repaymentMonths }} mois - {{ advance.createdAt|date('d/m/Y') }}</small>
                                            <div class="mt-1">
                                                <small class="text-muted">{{ advance.reason|length > 50 ? advance.reason|slice(0, 50) ~ '...' : advance.reason }}</small>
                                            </div>
                                        </div>
                                        <div>
                                            <a href="{{ path('app_hr_requests_show', {id: advance.request.id}) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> Voir
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                            <p>Aucune avance en attente</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Avances prêtes pour paiement -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card text-success"></i>
                        Prêtes pour paiement
                    </h5>
                    {% if ready_for_payment|length > 0 %}
                        <span class="badge bg-success">{{ ready_for_payment|length }}</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if ready_for_payment|length > 0 %}
                        <div class="list-group list-group-flush">
                            {% for advance in ready_for_payment %}
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="ms-2 me-auto">
                                            <div class="fw-bold">{{ advance.employee.user.firstName }} {{ advance.employee.user.lastName }}</div>
                                            <div class="text-success fw-bold">{{ advance.approvedAmount|number_format(2, ',', ' ') }}€</div>
                                            <small class="text-muted">Approuvé le {{ advance.request.approvedAt|date('d/m/Y') }}</small>
                                            <div class="mt-1">
                                                <small class="text-muted">{{ advance.repaymentMonths }} mois - {{ advance.monthlyDeduction|number_format(2, ',', ' ') }}€/mois</small>
                                            </div>
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-success" onclick="markAsPaid({{ advance.id }})">
                                                <i class="fas fa-check"></i> Payé
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                            <p>Aucune avance à payer</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Avances en cours de remboursement -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-sync-alt text-info"></i>
                        Avances en cours de remboursement
                    </h5>
                    {% if in_repayment|length > 0 %}
                        <span class="badge bg-info">{{ in_repayment|length }}</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if in_repayment|length > 0 %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Employé</th>
                                        <th>Montant initial</th>
                                        <th>Montant restant</th>
                                        <th>Progression</th>
                                        <th>Prochaine déduction</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for advance in in_repayment %}
                                        {% set totalRepaid = advance.totalRepaidAmount %}
                                        {% set remaining = advance.calculateRemainingAmount %}
                                        {% set progress = ((totalRepaid / advance.approvedAmount) * 100)|round %}
                                        <tr>
                                            <td>
                                                <div class="fw-bold">{{ advance.employee.user.firstName }} {{ advance.employee.user.lastName }}</div>
                                                <small class="text-muted">{{ advance.employee.department.name ?? 'N/A' }}</small>
                                            </td>
                                            <td>
                                                <div class="fw-bold">{{ advance.approvedAmount|number_format(2, ',', ' ') }}€</div>
                                                <small class="text-muted">{{ advance.repaymentMonths }} mois</small>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-{{ remaining > 0 ? 'warning' : 'success' }}">{{ remaining|number_format(2, ',', ' ') }}€</div>
                                                <small class="text-muted">Remboursé: {{ totalRepaid|number_format(2, ',', ' ') }}€</small>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-{{ progress == 100 ? 'success' : 'info' }}" role="progressbar" 
                                                         style="width: {{ progress }}%" aria-valuenow="{{ progress }}" aria-valuemin="0" aria-valuemax="100">
                                                        {{ progress }}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                {% if advance.nextDeductionDate %}
                                                    <div>{{ advance.nextDeductionDate|date('d/m/Y') }}</div>
                                                    <small class="text-muted">{{ advance.monthlyDeduction|number_format(2, ',', ' ') }}€</small>
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ path('app_hr_requests_show', {id: advance.request.id}) }}" class="btn btn-sm btn-outline-primary" title="Voir détails">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="showRepaymentHistory({{ advance.id }})" title="Historique">
                                                        <i class="fas fa-history"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                            <p>Aucune avance en cours de remboursement</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour marquer comme payé -->
<div class="modal fade" id="markPaidModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Marquer comme payé</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="mark-paid-form" method="post">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment-date" class="form-label">Date de paiement</label>
                        <input type="date" class="form-control" id="payment-date" name="payment_date" value="{{ 'now'|date('Y-m-d') }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="payment-notes" class="form-label">Notes (optionnel)</label>
                        <textarea class="form-control" id="payment-notes" name="notes" rows="3" placeholder="Notes sur le paiement..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Confirmer le paiement
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal historique des remboursements -->
<div class="modal fade" id="repaymentHistoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Historique des remboursements</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="repayment-history-content">
                <!-- Contenu chargé dynamiquement -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
    function markAsPaid(advanceId) {
        const form = document.getElementById('mark-paid-form');
        form.action = `/hr/salary-advance/${advanceId}/mark-paid`; // Route à créer
        const modal = new bootstrap.Modal(document.getElementById('markPaidModal'));
        modal.show();
    }

    function showRepaymentHistory(advanceId) {
        // Charger l'historique via AJAX
        fetch(`/hr/salary-advance/${advanceId}/repayment-history`) // Route à créer
            .then(response => response.text())
            .then(html => {
                document.getElementById('repayment-history-content').innerHTML = html;
                const modal = new bootstrap.Modal(document.getElementById('repaymentHistoryModal'));
                modal.show();
            })
            .catch(error => {
                console.error('Erreur lors du chargement de l\'historique:', error);
                alert('Erreur lors du chargement de l\'historique');
            });
    }
</script>
{% endblock %}
