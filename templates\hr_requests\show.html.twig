{% extends 'base.html.twig' %}

{% block title %}Demande #{{ request.id }} - {{ request.title }}{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-file-alt text-primary"></i>
                    Demande #{{ request.id }}
                </h1>
                <div>
                    <a href="{{ path('app_hr_requests_list') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Informations principales -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ request.title }}</h5>
                    <div>
                        <span class="badge bg-{{ request.type == 'salary_advance' ? 'success' : (request.type == 'document' ? 'info' : (request.type == 'leave' ? 'primary' : 'secondary')) }} me-2">
                            {{ request.typeLabel }}
                        </span>
                        <span class="badge bg-{{ request.priority == 'urgent' ? 'danger' : (request.priority == 'high' ? 'warning' : (request.priority == 'medium' ? 'info' : 'secondary')) }}">
                            {{ request.priorityLabel }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>Informations de la demande</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Statut :</strong></td>
                                    <td>
                                        <span class="badge bg-{{ request.status == 'approved' ? 'success' : (request.status == 'rejected' ? 'danger' : 'warning') }}">
                                            {{ request.statusLabel }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Date de création :</strong></td>
                                    <td>{{ request.createdAt|date('d/m/Y H:i') }}</td>
                                </tr>
                                {% if request.requestedDate %}
                                <tr>
                                    <td><strong>Date souhaitée :</strong></td>
                                    <td>{{ request.requestedDate|date('d/m/Y') }}</td>
                                </tr>
                                {% endif %}
                                {% if request.approvedBy %}
                                <tr>
                                    <td><strong>Approuvé par :</strong></td>
                                    <td>{{ request.approvedBy.firstName }} {{ request.approvedBy.lastName }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Date d'approbation :</strong></td>
                                    <td>{{ request.approvedAt|date('d/m/Y H:i') }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Informations employé</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Nom :</strong></td>
                                    <td>{{ request.employee.user.firstName }} {{ request.employee.user.lastName }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email :</strong></td>
                                    <td>{{ request.employee.user.email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Département :</strong></td>
                                    <td>{{ request.employee.department.name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Poste :</strong></td>
                                    <td>{{ request.employee.position.title ?? 'N/A' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6>Description</h6>
                        <div class="bg-light p-3 rounded">
                            {{ request.description|nl2br }}
                        </div>
                    </div>

                    {% if request.rejectionReason %}
                    <div class="mb-4">
                        <h6>Raison du rejet</h6>
                        <div class="alert alert-danger">
                            {{ request.rejectionReason|nl2br }}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Détails spécifiques par type -->
                    {% if request.type == 'salary_advance' and request.salaryAdvance %}
                        <div class="mb-4">
                            <h6>Détails de l'avance sur salaire</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Montant demandé :</strong></td>
                                            <td class="text-end"><strong>{{ request.salaryAdvance.amount|number_format(2, ',', ' ') }}€</strong></td>
                                        </tr>
                                        {% if request.salaryAdvance.approvedAmount %}
                                        <tr>
                                            <td><strong>Montant approuvé :</strong></td>
                                            <td class="text-end"><strong class="text-success">{{ request.salaryAdvance.approvedAmount|number_format(2, ',', ' ') }}€</strong></td>
                                        </tr>
                                        {% endif %}
                                        <tr>
                                            <td><strong>Durée de remboursement :</strong></td>
                                            <td class="text-end">{{ request.salaryAdvance.repaymentMonths }} mois</td>
                                        </tr>
                                        {% if request.salaryAdvance.monthlyDeduction %}
                                        <tr>
                                            <td><strong>Déduction mensuelle :</strong></td>
                                            <td class="text-end">{{ request.salaryAdvance.monthlyDeduction|number_format(2, ',', ' ') }}€</td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>Raison de l'avance</h6>
                                    <div class="bg-light p-3 rounded">
                                        {{ request.salaryAdvance.reason|nl2br }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    {% if request.type == 'document' and request.documentRequest %}
                        <div class="mb-4">
                            <h6>Détails de la demande de document</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Type de document :</strong></td>
                                            <td>{{ request.documentRequest.documentTypeLabel }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Quantité :</strong></td>
                                            <td>{{ request.documentRequest.quantity }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Langue :</strong></td>
                                            <td>{{ request.documentRequest.languageLabel }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Mode de livraison :</strong></td>
                                            <td>{{ request.documentRequest.deliveryMethodLabel }}</td>
                                        </tr>
                                        {% if request.documentRequest.neededBy %}
                                        <tr>
                                            <td><strong>Nécessaire avant le :</strong></td>
                                            <td>
                                                {{ request.documentRequest.neededBy|date('d/m/Y') }}
                                                {% if request.documentRequest.isUrgent() %}
                                                    <span class="badge bg-danger ms-2">URGENT</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>Objectif du document</h6>
                                    <div class="bg-light p-3 rounded">
                                        {{ request.documentRequest.purpose|nl2br }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Pièces jointes -->
                    {% if request.attachments|length > 0 %}
                    <div class="mb-4">
                        <h6>Pièces jointes ({{ request.attachments|length }})</h6>
                        <div class="list-group">
                            {% for attachment in request.attachments %}
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="{{ attachment.iconClass }} me-2"></i>
                                        <strong>{{ attachment.originalName }}</strong>
                                        <small class="text-muted ms-2">({{ attachment.formattedFileSize }})</small>
                                        {% if attachment.description %}
                                            <br><small class="text-muted">{{ attachment.description }}</small>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <small class="text-muted">{{ attachment.uploadedAt|date('d/m/Y H:i') }}</small>
                                        <a href="#" class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Actions et statut -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs"></i>
                        Actions
                    </h5>
                </div>
                <div class="card-body">
                    {% if request.canBeApproved() %}
                        <button type="button" class="btn btn-success w-100 mb-2" data-bs-toggle="modal" data-bs-target="#approveModal">
                            <i class="fas fa-check"></i> Approuver la demande
                        </button>
                    {% endif %}

                    {% if request.canBeRejected() %}
                        <button type="button" class="btn btn-danger w-100 mb-2" data-bs-toggle="modal" data-bs-target="#rejectModal">
                            <i class="fas fa-times"></i> Rejeter la demande
                        </button>
                    {% endif %}

                    {% if request.status == 'approved' and request.type == 'salary_advance' %}
                        <button type="button" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-money-bill-wave"></i> Traiter le paiement
                        </button>
                    {% endif %}

                    {% if request.status == 'approved' and request.type == 'document' %}
                        <button type="button" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-file-download"></i> Générer le document
                        </button>
                    {% endif %}

                    <hr>

                    <div class="d-grid gap-2">
                        <a href="{{ path('app_employee_show', {id: request.employee.id}) }}" class="btn btn-outline-primary">
                            <i class="fas fa-user"></i> Voir le profil employé
                        </a>
                        <a href="{{ path('app_hr_requests_list', {employee: request.employee.id}) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-history"></i> Autres demandes de cet employé
                        </a>
                    </div>
                </div>
            </div>

            <!-- Historique des commentaires -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-comments"></i>
                        Historique
                    </h5>
                </div>
                <div class="card-body">
                    {% if request.comments|length > 0 %}
                        <div class="timeline">
                            {% for comment in request.comments %}
                                <div class="timeline-item mb-3">
                                    <div class="d-flex">
                                        <div class="avatar-sm bg-{{ comment.type == 'approval' ? 'success' : (comment.type == 'rejection' ? 'danger' : 'primary') }} text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 32px; height: 32px; font-size: 12px;">
                                            {% if comment.type == 'approval' %}
                                                <i class="fas fa-check"></i>
                                            {% elseif comment.type == 'rejection' %}
                                                <i class="fas fa-times"></i>
                                            {% else %}
                                                <i class="fas fa-comment"></i>
                                            {% endif %}
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between">
                                                <strong>{{ comment.author.firstName }} {{ comment.author.lastName }}</strong>
                                                <small class="text-muted">{{ comment.createdAt|date('d/m/Y H:i') }}</small>
                                            </div>
                                            <div class="mt-1">{{ comment.comment|nl2br }}</div>
                                            <small class="text-muted">{{ comment.typeLabel }}</small>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-comments fa-2x mb-2"></i>
                            <p>Aucun commentaire pour le moment</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'approbation -->
<div class="modal fade" id="approveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Approuver la demande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{{ path('app_hr_requests_approve', {id: request.id}) }}">
                <div class="modal-body">
                    {% if request.type == 'salary_advance' and request.salaryAdvance %}
                        <div class="mb-3">
                            <label for="approved-amount" class="form-label">Montant approuvé (€)</label>
                            <input type="number" class="form-control" id="approved-amount" name="approved_amount" 
                                   value="{{ request.salaryAdvance.amount }}" min="1" max="{{ request.salaryAdvance.amount }}" step="0.01">
                            <div class="form-text">Montant demandé : {{ request.salaryAdvance.amount|number_format(2, ',', ' ') }}€</div>
                        </div>
                    {% endif %}
                    <div class="mb-3">
                        <label for="approve-comment" class="form-label">Commentaire (optionnel)</label>
                        <textarea class="form-control" id="approve-comment" name="comment" rows="3" placeholder="Commentaire d'approbation..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> Approuver
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de rejet -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Rejeter la demande</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{{ path('app_hr_requests_reject', {id: request.id}) }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reject-reason" class="form-label">Raison du rejet <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reject-reason" name="reason" rows="4" placeholder="Veuillez expliquer la raison du rejet..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times"></i> Rejeter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
