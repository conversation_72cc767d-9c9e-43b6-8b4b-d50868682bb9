{% extends 'base.html.twig' %}

{% block title %}Notifications{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .notification-item {
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
            border-radius: 0 8px 8px 0;
        }
        
        .notification-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }
        
        .notification-item.unread {
            background-color: #f8f9fa;
            border-left-color: #007bff;
        }
        
        .notification-item.priority-high {
            border-left-color: #dc3545;
        }
        
        .notification-item.priority-urgent {
            border-left-color: #721c24;
            background-color: #f8d7da;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .notification-icon.primary {
            background-color: #007bff;
            color: white;
        }
        
        .notification-icon.success {
            background-color: #28a745;
            color: white;
        }
        
        .notification-icon.warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .notification-icon.danger {
            background-color: #dc3545;
            color: white;
        }
        
        .notification-icon.info {
            background-color: #17a2b8;
            color: white;
        }
        
        .notification-time {
            font-size: 0.85rem;
            color: #6c757d;
        }
        
        .notification-actions {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .notification-item:hover .notification-actions {
            opacity: 1;
        }
        
        .real-time-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .notification-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .notification-controls {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        class NotificationManager {
            constructor() {
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.connectToRealTime();
                this.refreshNotifications();
            }

            setupEventListeners() {
                // Marquer comme lu
                document.addEventListener('click', (e) => {
                    if (e.target.classList.contains('mark-read-btn')) {
                        e.preventDefault();
                        const notificationId = e.target.dataset.notificationId;
                        this.markAsRead(notificationId);
                    }
                });

                // Marquer tout comme lu
                document.getElementById('markAllReadBtn')?.addEventListener('click', () => {
                    this.markAllAsRead();
                });

                // Actualiser
                document.getElementById('refreshBtn')?.addEventListener('click', () => {
                    this.refreshNotifications();
                });
            }

            async markAsRead(notificationId) {
                try {
                    const response = await fetch(`/notifications/api/${notificationId}/read`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        // Supprimer visuellement la notification
                        const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
                        if (notificationElement) {
                            notificationElement.style.transition = 'all 0.3s ease';
                            notificationElement.style.opacity = '0';
                            notificationElement.style.transform = 'translateX(-100%)';
                            
                            setTimeout(() => {
                                notificationElement.remove();
                                this.updateUnreadCount(data.unreadCount);
                            }, 300);
                        }
                    }
                } catch (error) {
                    console.error('Erreur lors du marquage comme lu:', error);
                }
            }

            async markAllAsRead() {
                try {
                    const response = await fetch('/notifications/api/mark-all-read', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        // Supprimer toutes les notifications visuellement
                        const notifications = document.querySelectorAll('.notification-item');
                        notifications.forEach((notification, index) => {
                            setTimeout(() => {
                                notification.style.transition = 'all 0.3s ease';
                                notification.style.opacity = '0';
                                notification.style.transform = 'translateX(-100%)';
                                
                                setTimeout(() => {
                                    notification.remove();
                                }, 300);
                            }, index * 100);
                        });

                        setTimeout(() => {
                            this.updateUnreadCount(0);
                            this.showEmptyState();
                        }, notifications.length * 100 + 300);
                    }
                } catch (error) {
                    console.error('Erreur lors du marquage global:', error);
                }
            }

            async refreshNotifications() {
                try {
                    const response = await fetch('/notifications/api/unread?limit=20');
                    const data = await response.json();
                    
                    if (data.success) {
                        this.renderNotifications(data.notifications);
                        this.updateUnreadCount(data.unreadCount);
                    }
                } catch (error) {
                    console.error('Erreur lors du rafraîchissement:', error);
                }
            }

            renderNotifications(notifications) {
                const container = document.getElementById('notificationsContainer');
                
                if (notifications.length === 0) {
                    this.showEmptyState();
                    return;
                }

                container.innerHTML = '';
                
                notifications.forEach(notification => {
                    const element = this.createNotificationElement(notification);
                    container.appendChild(element);
                });
            }

            createNotificationElement(notification) {
                const div = document.createElement('div');
                div.className = `notification-item p-3 mb-3 ${notification.priority ? 'priority-' + notification.priority : ''}`;
                div.setAttribute('data-notification-id', notification.id);
                
                const timeAgo = this.timeAgo(new Date(notification.createdAt));
                
                div.innerHTML = `
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <div class="notification-icon ${notification.color || 'primary'}">
                                <i class="${notification.icon || 'bi-bell'}"></i>
                            </div>
                        </div>
                        <div class="col">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">${notification.title}</h6>
                                    <p class="mb-1">${notification.content}</p>
                                    <small class="notification-time">${timeAgo}</small>
                                </div>
                                <div class="notification-actions">
                                    <button class="btn btn-sm btn-outline-primary mark-read-btn" 
                                            data-notification-id="${notification.id}">
                                        <i class="bi bi-check"></i>
                                    </button>
                                    ${notification.actionUrl ? `
                                        <a href="${notification.actionUrl}" class="btn btn-sm btn-primary">
                                            ${notification.actionLabel || 'Voir'}
                                        </a>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                return div;
            }

            updateUnreadCount(count) {
                const badge = document.querySelector('.notification-badge');
                if (badge) {
                    if (count > 0) {
                        badge.textContent = count;
                        badge.style.display = 'inline-block';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            }

            showEmptyState() {
                const container = document.getElementById('notificationsContainer');
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="bi bi-bell-slash"></i>
                        <h5>Aucune notification</h5>
                        <p>Vous n'avez aucune notification non lue.</p>
                    </div>
                `;
            }

            connectToRealTime() {
                // Connexion WebSocket pour les notifications en temps réel
                // À implémenter avec Mercure ou WebSockets
                console.log('Connexion temps réel initialisée');
            }

            timeAgo(date) {
                const now = new Date();
                const diffInSeconds = Math.floor((now - date) / 1000);
                
                if (diffInSeconds < 60) return 'À l\'instant';
                if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} min`;
                if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} h`;
                return `${Math.floor(diffInSeconds / 86400)} j`;
            }
        }

        // Initialiser le gestionnaire de notifications
        document.addEventListener('DOMContentLoaded', () => {
            new NotificationManager();
        });
    </script>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="notification-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-bell"></i> Notifications
                </h1>
                <p class="mb-0 opacity-75">
                    <span class="real-time-indicator"></span>
                    Notifications en temps réel - {{ unread_count }} non lues
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ path('app_notifications_test') }}" class="btn btn-light btn-sm me-2">
                    <i class="bi bi-plus"></i> Test
                </a>
                <a href="{{ path('app_advanced_dashboard') }}" class="btn btn-outline-light btn-sm">
                    <i class="bi bi-arrow-left"></i> Retour
                </a>
            </div>
        </div>
    </div>

    <!-- Contrôles -->
    <div class="notification-controls">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">
                    {{ unread_count }} notification{{ unread_count > 1 ? 's' : '' }} non lue{{ unread_count > 1 ? 's' : '' }}
                </h5>
            </div>
            <div class="col-md-6 text-end">
                <button id="refreshBtn" class="btn btn-outline-primary btn-sm me-2">
                    <i class="bi bi-arrow-clockwise"></i> Actualiser
                </button>
                {% if unread_count > 0 %}
                    <button id="markAllReadBtn" class="btn btn-primary btn-sm">
                        <i class="bi bi-check-all"></i> Tout marquer comme lu
                    </button>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Liste des notifications -->
    <div class="card">
        <div class="card-body">
            <div id="notificationsContainer">
                {% if notifications|length > 0 %}
                    {% for notification in notifications %}
                        <div class="notification-item p-3 mb-3 unread {{ notification.priority ? 'priority-' ~ notification.priority : '' }}" 
                             data-notification-id="{{ notification.id }}">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <div class="notification-icon {{ notification.color ?: 'primary' }}">
                                        <i class="{{ notification.icon ?: 'bi-bell' }}"></i>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">{{ notification.title }}</h6>
                                            <p class="mb-1">{{ notification.content }}</p>
                                            <small class="notification-time">
                                                {{ notification.createdAt|date('d/m/Y H:i') }}
                                            </small>
                                        </div>
                                        <div class="notification-actions">
                                            <button class="btn btn-sm btn-outline-primary mark-read-btn" 
                                                    data-notification-id="{{ notification.id }}">
                                                <i class="bi bi-check"></i>
                                            </button>
                                            {% if notification.actionUrl %}
                                                <a href="{{ notification.actionUrl }}" class="btn btn-sm btn-primary">
                                                    {{ notification.actionLabel ?: 'Voir' }}
                                                </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="empty-state">
                        <i class="bi bi-bell-slash"></i>
                        <h5>Aucune notification</h5>
                        <p>Vous n'avez aucune notification non lue.</p>
                        <a href="{{ path('app_notifications_test') }}" class="btn btn-primary">
                            Créer des notifications de test
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
