<!-- Widget de notifications pour la barre de navigation -->
<div class="dropdown">
    <button class="btn btn-outline-light position-relative" type="button" id="notificationDropdown" 
            data-bs-toggle="dropdown" aria-expanded="false">
        <i class="bi bi-bell"></i>
        {% if unread_count > 0 %}
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge">
                {{ unread_count > 99 ? '99+' : unread_count }}
                <span class="visually-hidden">notifications non lues</span>
            </span>
        {% endif %}
    </button>
    
    <div class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationDropdown">
        <div class="dropdown-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">Notifications</h6>
            {% if unread_count > 0 %}
                <button class="btn btn-sm btn-link p-0 mark-all-read-widget" type="button">
                    <small>Tout marquer comme lu</small>
                </button>
            {% endif %}
        </div>
        
        <div class="notification-list" style="max-height: 400px; overflow-y: auto;">
            {% if notifications|length > 0 %}
                {% for notification in notifications %}
                    <div class="dropdown-item notification-item-widget" data-notification-id="{{ notification.id }}">
                        <div class="d-flex align-items-start">
                            <div class="notification-icon-small me-2">
                                <i class="{{ notification.icon ?: 'bi-bell' }} text-{{ notification.color ?: 'primary' }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1 fs-6">{{ notification.title }}</h6>
                                        <p class="mb-1 small text-muted">
                                            {{ notification.content|length > 80 ? notification.content|slice(0, 80) ~ '...' : notification.content }}
                                        </p>
                                        <small class="text-muted">
                                            {{ notification.createdAt|date('H:i') }}
                                        </small>
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary mark-read-widget-btn" 
                                            data-notification-id="{{ notification.id }}" 
                                            title="Marquer comme lu">
                                        <i class="bi bi-check"></i>
                                    </button>
                                </div>
                                {% if notification.actionUrl %}
                                    <div class="mt-2">
                                        <a href="{{ notification.actionUrl }}" class="btn btn-sm btn-primary">
                                            {{ notification.actionLabel ?: 'Voir' }}
                                        </a>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% if not loop.last %}
                        <div class="dropdown-divider"></div>
                    {% endif %}
                {% endfor %}
            {% else %}
                <div class="dropdown-item text-center text-muted py-4">
                    <i class="bi bi-bell-slash fs-3 mb-2"></i>
                    <div>Aucune notification</div>
                </div>
            {% endif %}
        </div>
        
        {% if notifications|length > 0 %}
            <div class="dropdown-divider"></div>
            <div class="dropdown-item text-center">
                <a href="{{ path('app_notifications_index') }}" class="btn btn-sm btn-outline-primary">
                    Voir toutes les notifications
                </a>
            </div>
        {% endif %}
    </div>
</div>

<style>
.notification-dropdown {
    width: 350px;
    max-width: 90vw;
}

.notification-item-widget {
    padding: 0.75rem;
    border: none;
    background: none;
}

.notification-item-widget:hover {
    background-color: #f8f9fa;
}

.notification-icon-small {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #f8f9fa;
}

.notification-badge {
    font-size: 0.6rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.1); }
    100% { transform: translate(-50%, -50%) scale(1); }
}

.mark-read-widget-btn {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.notification-item-widget:hover .mark-read-widget-btn {
    opacity: 1;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Marquer une notification comme lue depuis le widget
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('mark-read-widget-btn') || 
            e.target.closest('.mark-read-widget-btn')) {
            e.preventDefault();
            e.stopPropagation();
            
            const btn = e.target.closest('.mark-read-widget-btn');
            const notificationId = btn.dataset.notificationId;
            
            markNotificationAsRead(notificationId, btn);
        }
    });

    // Marquer toutes les notifications comme lues depuis le widget
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('mark-all-read-widget')) {
            e.preventDefault();
            markAllNotificationsAsRead();
        }
    });

    async function markNotificationAsRead(notificationId, button) {
        try {
            const response = await fetch(`/notifications/api/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();
            
            if (data.success) {
                // Supprimer l'élément de notification
                const notificationItem = button.closest('.notification-item-widget');
                const divider = notificationItem.nextElementSibling;
                
                notificationItem.style.transition = 'all 0.3s ease';
                notificationItem.style.opacity = '0';
                notificationItem.style.transform = 'translateX(-100%)';
                
                setTimeout(() => {
                    notificationItem.remove();
                    if (divider && divider.classList.contains('dropdown-divider')) {
                        divider.remove();
                    }
                    
                    // Mettre à jour le badge
                    updateNotificationBadge(data.unreadCount);
                    
                    // Vérifier s'il reste des notifications
                    const remainingNotifications = document.querySelectorAll('.notification-item-widget');
                    if (remainingNotifications.length === 0) {
                        showEmptyState();
                    }
                }, 300);
            }
        } catch (error) {
            console.error('Erreur lors du marquage comme lu:', error);
        }
    }

    async function markAllNotificationsAsRead() {
        try {
            const response = await fetch('/notifications/api/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();
            
            if (data.success) {
                // Supprimer toutes les notifications
                const notifications = document.querySelectorAll('.notification-item-widget');
                notifications.forEach((notification, index) => {
                    setTimeout(() => {
                        notification.style.transition = 'all 0.3s ease';
                        notification.style.opacity = '0';
                        notification.style.transform = 'translateX(-100%)';
                        
                        setTimeout(() => {
                            notification.remove();
                        }, 300);
                    }, index * 100);
                });

                setTimeout(() => {
                    updateNotificationBadge(0);
                    showEmptyState();
                    
                    // Masquer le bouton "Tout marquer comme lu"
                    const markAllBtn = document.querySelector('.mark-all-read-widget');
                    if (markAllBtn) {
                        markAllBtn.style.display = 'none';
                    }
                }, notifications.length * 100 + 300);
            }
        } catch (error) {
            console.error('Erreur lors du marquage global:', error);
        }
    }

    function updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'inline-block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    function showEmptyState() {
        const notificationList = document.querySelector('.notification-list');
        notificationList.innerHTML = `
            <div class="dropdown-item text-center text-muted py-4">
                <i class="bi bi-bell-slash fs-3 mb-2"></i>
                <div>Aucune notification</div>
            </div>
        `;
    }

    // Actualiser les notifications toutes les 30 secondes
    setInterval(async function() {
        try {
            const response = await fetch('/notifications/api/count');
            const data = await response.json();
            
            if (data.success) {
                const currentBadge = document.querySelector('.notification-badge');
                const currentCount = currentBadge ? parseInt(currentBadge.textContent) : 0;
                
                if (data.count !== currentCount) {
                    // Recharger le widget si le nombre a changé
                    location.reload();
                }
            }
        } catch (error) {
            console.error('Erreur lors de la vérification des notifications:', error);
        }
    }, 30000);
});
</script>
