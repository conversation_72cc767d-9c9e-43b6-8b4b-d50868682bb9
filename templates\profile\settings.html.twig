{% extends 'base.html.twig' %}

{% block title %}Paramètres{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .coming-soon-container {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            color: white;
            margin: 2rem 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .coming-soon-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            opacity: 0.9;
        }
        
        .coming-soon-title {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .coming-soon-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            font-weight: 300;
        }
        
        .feature-preview {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            backdrop-filter: blur(10px);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .feature-list li i {
            margin-right: 0.5rem;
            color: #ffd700;
        }
        
        .notification-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            display: inline-block;
            margin-top: 1rem;
            box-shadow: 0 4px 15px rgba(238, 90, 36, 0.4);
        }
        
        .profile-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .profile-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px 10px 0 0;
            border: none;
        }
        
        .profile-card-body {
            padding: 2rem;
        }
        
        .breadcrumb-custom {
            background: transparent;
            padding: 0;
            margin-bottom: 2rem;
        }
        
        .breadcrumb-custom .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
        }
        
        .breadcrumb-custom .breadcrumb-item.active {
            color: #6c757d;
        }
    </style>
{% endblock %}

{% block body %}
<div class="container">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb breadcrumb-custom">
            <li class="breadcrumb-item">
                <a href="{{ path('app_profile') }}">
                    <i class="bi bi-person-circle"></i> Mon profil
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <i class="bi bi-gear"></i> Paramètres
            </li>
        </ol>
    </nav>

    <div class="profile-card">
        <div class="profile-card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0">
                        <i class="bi bi-gear me-2"></i>
                        Paramètres utilisateur
                    </h3>
                    <p class="mb-0 mt-2 opacity-75">Configuration personnalisée de votre compte</p>
                </div>
                <div>
                    <a href="{{ path('app_profile') }}" class="btn btn-light btn-sm">
                        <i class="bi bi-arrow-left"></i> Retour au profil
                    </a>
                </div>
            </div>
        </div>
        <div class="profile-card-body">
            <!-- Message À venir -->
            <div class="coming-soon-container">
                <div class="coming-soon-icon">
                    <i class="bi bi-sliders"></i>
                </div>
                <h2 class="coming-soon-title">À venir</h2>
                <p class="coming-soon-subtitle">
                    La page de paramètres utilisateur est en cours de développement
                </p>
                
                <div class="feature-preview">
                    <h4 class="mb-3">
                        <i class="bi bi-stars text-warning"></i>
                        Fonctionnalités prévues
                    </h4>
                    <ul class="feature-list">
                        <li><i class="bi bi-check-circle"></i> Préférences d'affichage</li>
                        <li><i class="bi bi-check-circle"></i> Paramètres de notifications</li>
                        <li><i class="bi bi-check-circle"></i> Configuration de la langue</li>
                        <li><i class="bi bi-check-circle"></i> Fuseau horaire personnel</li>
                        <li><i class="bi bi-check-circle"></i> Thème sombre/clair</li>
                        <li><i class="bi bi-check-circle"></i> Paramètres de confidentialité</li>
                        <li><i class="bi bi-check-circle"></i> Intégrations personnelles</li>
                    </ul>
                </div>
                
                <div class="notification-badge">
                    <i class="bi bi-clock"></i>
                    Disponible dans une prochaine mise à jour
                </div>
            </div>
            
            <!-- Informations temporaires -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-info-circle"></i>
                                Paramètres actuels
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-2"><strong>Utilisateur :</strong> {{ user.fullName }}</p>
                            <p class="mb-2"><strong>Email :</strong> {{ user.email }}</p>
                            <p class="mb-2"><strong>Rôles :</strong> {{ user.roles|join(', ') }}</p>
                            <p class="mb-0">
                                En attendant cette interface, vous pouvez modifier vos informations 
                                de base via la page "Modifier le profil".
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-link-45deg"></i>
                                Actions disponibles
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ path('app_profile_edit') }}" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-pencil"></i> Modifier le profil
                                </a>
                                <a href="{{ path('app_profile_change_password') }}" class="btn btn-outline-warning btn-sm">
                                    <i class="bi bi-key"></i> Changer le mot de passe
                                </a>
                                <a href="{{ path('app_profile_security') }}" class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-shield-lock"></i> Sécurité du compte
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
