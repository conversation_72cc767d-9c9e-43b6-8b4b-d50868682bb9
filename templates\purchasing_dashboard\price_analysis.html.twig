{% extends 'base.html.twig' %}

{% block title %}Analyse des Prix{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .analysis-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .analysis-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
            border: none;
            margin-bottom: 2rem;
        }
        
        .analysis-card:hover {
            transform: translateY(-2px);
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            padding: 1rem;
        }
        
        .price-metric {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            margin-bottom: 1rem;
        }
        
        .price-value {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .price-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .price-up {
            color: #dc3545;
        }
        
        .price-down {
            color: #28a745;
        }
        
        .price-stable {
            color: #6c757d;
        }
        
        .product-selector {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .supplier-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.2s;
        }
        
        .supplier-card:hover {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
        }
        
        .supplier-best {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        .supplier-worst {
            border-color: #dc3545;
            background: #fff8f8;
        }
        
        .price-trend-icon {
            font-size: 1.2rem;
            margin-left: 0.5rem;
        }
        
        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }
        
        .btn-analysis {
            border-radius: 20px;
            padding: 0.5rem 1.5rem;
            margin: 0.25rem;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="analysis-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-graph-up-arrow"></i> Analyse des Prix
                </h1>
                <p class="mb-0 opacity-75">
                    Analyse comparative des prix, évolution temporelle et optimisation des achats
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ path('app_purchasing_dashboard') }}" class="btn btn-light">
                    <i class="bi bi-arrow-left"></i> Retour au Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Sélecteur de produit -->
    <div class="product-selector">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h6 class="mb-2">Sélectionner un produit pour l'analyse :</h6>
                <select class="form-select" id="productSelector">
                    <option value="">-- Choisir un produit --</option>
                    {% for product in products %}
                        <option value="{{ product.id }}" {% if selected_product and selected_product.id == product.id %}selected{% endif %}>
                            {{ product.name }} ({{ product.reference }})
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-analysis btn-primary" onclick="analyzeProduct()">
                    <i class="bi bi-search"></i> Analyser
                </button>
                <button class="btn btn-analysis btn-outline-secondary" onclick="exportAnalysis()">
                    <i class="bi bi-download"></i> Exporter
                </button>
            </div>
        </div>
    </div>

    {% if selected_product %}
    <!-- Métriques de prix -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="price-metric">
                <div class="price-value price-stable">
                    {{ price_analysis_data.statistics.current_price|default('N/A')|number_format(2, ',', ' ') }} €
                </div>
                <div class="price-label">Prix Actuel</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="price-metric">
                <div class="price-value price-down">
                    {{ price_analysis_data.statistics.min_price|default('N/A')|number_format(2, ',', ' ') }} €
                </div>
                <div class="price-label">Prix Minimum</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="price-metric">
                <div class="price-value price-up">
                    {{ price_analysis_data.statistics.max_price|default('N/A')|number_format(2, ',', ' ') }} €
                </div>
                <div class="price-label">Prix Maximum</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="price-metric">
                <div class="price-value price-stable">
                    {{ price_analysis_data.statistics.avg_price|default('N/A')|number_format(2, ',', ' ') }} €
                </div>
                <div class="price-label">Prix Moyen</div>
            </div>
        </div>
    </div>

    <!-- Graphiques d'analyse -->
    <div class="row">
        <!-- Évolution des prix -->
        <div class="col-md-8 mb-4">
            <div class="analysis-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up"></i> Évolution des Prix
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="priceHistoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistiques rapides -->
        <div class="col-md-4 mb-4">
            <div class="analysis-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-bar-chart"></i> Statistiques
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Variation sur 12 mois</small>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>{{ ((price_analysis_data.statistics.price_variation|default(0)) * 100)|number_format(1) }}%</span>
                            <i class="bi bi-arrow-up price-trend-icon {% if price_analysis_data.statistics.price_variation|default(0) > 0 %}price-up{% else %}price-down{% endif %}"></i>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Volatilité</small>
                        <div class="progress">
                            <div class="progress-bar" style="width: {{ (price_analysis_data.statistics.volatility|default(0) * 100) }}%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Nombre de fournisseurs</small>
                        <div class="h4">{{ supplier_comparison_data.suppliers|length }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Dernière mise à jour</small>
                        <div>{{ "now"|date("d/m/Y H:i") }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Comparaison des fournisseurs -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="analysis-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-building"></i> Comparaison des Fournisseurs
                    </h5>
                </div>
                <div class="card-body">
                    {% if supplier_comparison_data.suppliers is not empty %}
                        <div class="row">
                            {% for supplier in supplier_comparison_data.suppliers %}
                                <div class="col-md-4 mb-3">
                                    <div class="supplier-card {% if loop.first %}supplier-best{% elseif loop.last and loop.length > 2 %}supplier-worst{% endif %}">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="mb-0">{{ supplier.name }}</h6>
                                            {% if loop.first %}
                                                <span class="badge bg-success">Meilleur prix</span>
                                            {% elseif loop.last and loop.length > 2 %}
                                                <span class="badge bg-danger">Prix élevé</span>
                                            {% endif %}
                                        </div>
                                        <div class="h4 mb-2">{{ supplier.price|number_format(2, ',', ' ') }} €</div>
                                        <div class="small text-muted">
                                            <i class="bi bi-calendar"></i> Dernière commande : {{ supplier.last_order_date|default('N/A') }}
                                        </div>
                                        <div class="small text-muted">
                                            <i class="bi bi-star"></i> Note : {{ supplier.rating|default('N/A') }}/5
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-info-circle text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-2">Aucune donnée de fournisseur disponible pour ce produit</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recommandations -->
    <div class="row">
        <div class="col-md-6">
            <div class="analysis-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-lightbulb"></i> Recommandations
                    </h5>
                </div>
                <div class="card-body">
                    {% if price_analysis_data.statistics.price_variation|default(0) > 0.1 %}
                        <div class="alert alert-custom alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Prix en hausse :</strong> Considérez un achat groupé ou négociez avec le fournisseur.
                        </div>
                    {% endif %}
                    
                    {% if supplier_comparison_data.suppliers|length > 1 %}
                        <div class="alert alert-custom alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Optimisation :</strong> Économie potentielle de {{ ((supplier_comparison_data.suppliers|last.price - supplier_comparison_data.suppliers|first.price)|number_format(2, ',', ' ')) }} € par unité.
                        </div>
                    {% endif %}
                    
                    <div class="alert alert-custom alert-success">
                        <i class="bi bi-check-circle"></i>
                        <strong>Conseil :</strong> Surveillez les tendances saisonnières pour optimiser vos achats.
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="analysis-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-down"></i> Opportunités d'Économie
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Changement de fournisseur</span>
                            <span class="text-success">-15%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 15%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Achat en volume</span>
                            <span class="text-success">-8%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 8%"></div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Négociation contrat</span>
                            <span class="text-success">-12%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 12%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Message si aucun produit sélectionné -->
    <div class="row">
        <div class="col-md-12">
            <div class="analysis-card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-search text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">Sélectionnez un produit pour commencer l'analyse</h4>
                    <p class="text-muted">Choisissez un produit dans la liste ci-dessus pour voir son analyse de prix détaillée.</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
{% if selected_product and price_analysis_data.price_history is not empty %}
// Configuration du graphique d'évolution des prix
const priceCtx = document.getElementById('priceHistoryChart').getContext('2d');
new Chart(priceCtx, {
    type: 'line',
    data: {
        labels: {{ price_analysis_data.price_history|map(h => h.date)|json_encode|raw }},
        datasets: [{
            label: 'Prix (€)',
            data: {{ price_analysis_data.price_history|map(h => h.price)|json_encode|raw }},
            borderColor: 'rgba(40, 167, 69, 1)',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: false,
                ticks: {
                    callback: function(value) {
                        return new Intl.NumberFormat('fr-FR', {
                            style: 'currency',
                            currency: 'EUR'
                        }).format(value);
                    }
                }
            }
        }
    }
});
{% endif %}

// Fonctions JavaScript
function analyzeProduct() {
    const productId = document.getElementById('productSelector').value;
    if (productId) {
        window.location.href = '{{ path('app_purchasing_dashboard_price_analysis') }}?product=' + productId;
    } else {
        alert('Veuillez sélectionner un produit');
    }
}

function exportAnalysis() {
    // Fonction d'export (à implémenter)
    alert('Fonctionnalité d\'export en cours de développement');
}

// Auto-submit du formulaire lors du changement de sélection
document.getElementById('productSelector').addEventListener('change', function() {
    if (this.value) {
        analyzeProduct();
    }
});
</script>
{% endblock %}
