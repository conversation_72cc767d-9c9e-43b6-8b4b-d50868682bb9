{% extends 'base.html.twig' %}

{% block title %}Tendances des Achats{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .trends-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .chart-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
            border: none;
            margin-bottom: 2rem;
        }
        
        .chart-card:hover {
            transform: translateY(-2px);
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            padding: 1rem;
        }
        
        .metric-card {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            margin-bottom: 1rem;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .trend-indicator {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .trend-up {
            background: #d4edda;
            color: #155724;
        }
        
        .trend-down {
            background: #f8d7da;
            color: #721c24;
        }
        
        .trend-stable {
            background: #fff3cd;
            color: #856404;
        }
        
        .period-selector {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .btn-period {
            border-radius: 20px;
            margin: 0.25rem;
            padding: 0.5rem 1rem;
        }
        
        .btn-period.active {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- En-tête -->
    <div class="trends-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="bi bi-graph-up"></i> Tendances des Achats
                </h1>
                <p class="mb-0 opacity-75">
                    Analyse des tendances d'achat par période, catégorie, fournisseur et projet
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{{ path('app_purchasing_dashboard') }}" class="btn btn-light">
                    <i class="bi bi-arrow-left"></i> Retour au Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Sélecteur de période -->
    <div class="period-selector">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h6 class="mb-0">Période d'analyse :</h6>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-period active" data-period="12m">12 mois</button>
                <button class="btn btn-period" data-period="6m">6 mois</button>
                <button class="btn btn-period" data-period="3m">3 mois</button>
                <button class="btn btn-period" data-period="1m">1 mois</button>
            </div>
        </div>
    </div>

    <!-- Métriques rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ monthly_trends.datasets[0].data|length }}</div>
                <div class="metric-label">Périodes analysées</div>
                <span class="trend-indicator trend-stable">Stable</span>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ category_trends.labels|length }}</div>
                <div class="metric-label">Catégories actives</div>
                <span class="trend-indicator trend-up">+12%</span>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ supplier_trends.labels|length }}</div>
                <div class="metric-label">Fournisseurs actifs</div>
                <span class="trend-indicator trend-up">+8%</span>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value">{{ project_trends.labels|length }}</div>
                <div class="metric-label">Projets avec achats</div>
                <span class="trend-indicator trend-stable">Stable</span>
            </div>
        </div>
    </div>

    <!-- Graphiques des tendances -->
    <div class="row">
        <!-- Tendances mensuelles -->
        <div class="col-md-12 mb-4">
            <div class="chart-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-month"></i> Tendances Mensuelles
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="monthlyTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tendances par catégorie -->
        <div class="col-md-6 mb-4">
            <div class="chart-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-tags"></i> Tendances par Catégorie
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="categoryTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tendances par fournisseur -->
        <div class="col-md-6 mb-4">
            <div class="chart-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-building"></i> Tendances par Fournisseur
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="supplierTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tendances par projet -->
        <div class="col-md-12 mb-4">
            <div class="chart-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-kanban"></i> Tendances par Projet
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="projectTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Insights et recommandations -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-lightbulb"></i> Insights
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            Les achats sont stables sur les 12 derniers mois
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-info-circle text-info me-2"></i>
                            Diversification des fournisseurs en cours
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                            Concentration sur certaines catégories
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-star"></i> Recommandations
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="bi bi-arrow-right text-primary me-2"></i>
                            Optimiser les négociations avec les fournisseurs principaux
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-arrow-right text-primary me-2"></i>
                            Planifier les achats saisonniers
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-arrow-right text-primary me-2"></i>
                            Évaluer les opportunités de groupement d'achats
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Configuration des graphiques
const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
        },
        title: {
            display: false
        }
    },
    scales: {
        y: {
            beginAtZero: true,
            ticks: {
                callback: function(value) {
                    return new Intl.NumberFormat('fr-FR', {
                        style: 'currency',
                        currency: 'EUR',
                        minimumFractionDigits: 0
                    }).format(value);
                }
            }
        }
    }
};

// Graphique des tendances mensuelles
const monthlyCtx = document.getElementById('monthlyTrendsChart').getContext('2d');
new Chart(monthlyCtx, {
    type: 'line',
    data: {{ monthly_trends|json_encode|raw }},
    options: chartOptions
});

// Graphique des tendances par catégorie
const categoryCtx = document.getElementById('categoryTrendsChart').getContext('2d');
new Chart(categoryCtx, {
    type: 'bar',
    data: {{ category_trends|json_encode|raw }},
    options: chartOptions
});

// Graphique des tendances par fournisseur
const supplierCtx = document.getElementById('supplierTrendsChart').getContext('2d');
new Chart(supplierCtx, {
    type: 'bar',
    data: {{ supplier_trends|json_encode|raw }},
    options: chartOptions
});

// Graphique des tendances par projet
const projectCtx = document.getElementById('projectTrendsChart').getContext('2d');
new Chart(projectCtx, {
    type: 'horizontalBar',
    data: {{ project_trends|json_encode|raw }},
    options: {
        ...chartOptions,
        indexAxis: 'y'
    }
});

// Gestion des boutons de période
document.querySelectorAll('.btn-period').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.btn-period').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        
        // Ici on pourrait recharger les données pour la nouvelle période
        const period = this.dataset.period;
        console.log('Période sélectionnée:', period);
    });
});
</script>
{% endblock %}
