{% extends 'base.html.twig' %}

{% block title %}Recherche globale{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .search-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .search-box {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .search-box:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }
        
        .search-box::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .search-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 50px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .result-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: none;
            margin-bottom: 1rem;
        }
        
        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .result-item {
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
            border-radius: 0 8px 8px 0;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: white;
        }
        
        .result-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }
        
        .result-item.type-project {
            border-left-color: #007bff;
        }
        
        .result-item.type-employee {
            border-left-color: #28a745;
        }
        
        .result-item.type-invoice {
            border-left-color: #ffc107;
        }
        
        .result-item.type-partner {
            border-left-color: #17a2b8;
        }
        
        .result-item.type-purchase {
            border-left-color: #6c757d;
        }
        
        .result-item.type-stock {
            border-left-color: #fd7e14;
        }
        
        .result-item.type-department {
            border-left-color: #6f42c1;
        }
        
        .result-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }
        
        .result-icon.primary {
            background: #007bff;
        }
        
        .result-icon.success {
            background: #28a745;
        }
        
        .result-icon.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .result-icon.info {
            background: #17a2b8;
        }
        
        .result-icon.secondary {
            background: #6c757d;
        }
        
        .result-icon.danger {
            background: #dc3545;
        }
        
        .module-section {
            margin-bottom: 2rem;
        }
        
        .module-header {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #007bff;
        }
        
        .search-stats {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            backdrop-filter: blur(10px);
        }
        
        .suggestion-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 0.5rem 1rem;
            margin: 0.25rem;
            display: inline-block;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .suggestion-item:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .search-filters {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .quick-actions {
            margin-top: 1rem;
        }
        
        .quick-action-btn {
            margin: 0.25rem;
            border-radius: 20px;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        class SearchManager {
            constructor() {
                this.searchInput = document.getElementById('searchInput');
                this.searchForm = document.getElementById('searchForm');
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.setupAutoComplete();
                
                // Focus sur le champ de recherche
                if (this.searchInput) {
                    this.searchInput.focus();
                }
            }

            setupEventListeners() {
                // Recherche en temps réel
                if (this.searchInput) {
                    let timeout;
                    this.searchInput.addEventListener('input', (e) => {
                        clearTimeout(timeout);
                        timeout = setTimeout(() => {
                            if (e.target.value.length >= 2) {
                                this.performQuickSearch(e.target.value);
                            }
                        }, 300);
                    });
                }

                // Soumission du formulaire
                if (this.searchForm) {
                    this.searchForm.addEventListener('submit', (e) => {
                        e.preventDefault();
                        this.performFullSearch();
                    });
                }

                // Raccourcis clavier
                document.addEventListener('keydown', (e) => {
                    // Ctrl+K ou Cmd+K pour focus sur la recherche
                    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                        e.preventDefault();
                        this.searchInput?.focus();
                    }
                    
                    // Escape pour vider la recherche
                    if (e.key === 'Escape' && document.activeElement === this.searchInput) {
                        this.searchInput.value = '';
                        this.clearResults();
                    }
                });
            }

            setupAutoComplete() {
                // Implémentation de l'auto-complétion
                if (this.searchInput) {
                    // Créer un conteneur pour les suggestions
                    const suggestionsContainer = document.createElement('div');
                    suggestionsContainer.className = 'suggestions-container position-absolute w-100 bg-white border rounded shadow-lg';
                    suggestionsContainer.style.zIndex = '1000';
                    suggestionsContainer.style.display = 'none';
                    
                    this.searchInput.parentNode.style.position = 'relative';
                    this.searchInput.parentNode.appendChild(suggestionsContainer);
                    
                    this.suggestionsContainer = suggestionsContainer;
                }
            }

            async performQuickSearch(query) {
                if (!query || query.length < 2) return;

                try {
                    const response = await fetch(`/search/quick?q=${encodeURIComponent(query)}`);
                    const data = await response.json();
                    
                    if (data.success && data.results.length > 0) {
                        this.showSuggestions(data.results);
                    } else {
                        this.hideSuggestions();
                    }
                } catch (error) {
                    console.error('Erreur lors de la recherche rapide:', error);
                }
            }

            showSuggestions(results) {
                if (!this.suggestionsContainer) return;

                let html = '';
                results.forEach(result => {
                    html += `
                        <div class="suggestion-result p-2 border-bottom" style="cursor: pointer;" 
                             onclick="window.location.href='${result.url}'">
                            <div class="d-flex align-items-center">
                                <div class="result-icon ${result.color} me-2">
                                    <i class="${result.icon}"></i>
                                </div>
                                <div>
                                    <div class="fw-bold">${result.title}</div>
                                    <small class="text-muted">${result.subtitle}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });

                this.suggestionsContainer.innerHTML = html;
                this.suggestionsContainer.style.display = 'block';
            }

            hideSuggestions() {
                if (this.suggestionsContainer) {
                    this.suggestionsContainer.style.display = 'none';
                }
            }

            performFullSearch() {
                const query = this.searchInput.value.trim();
                if (query.length >= 2) {
                    window.location.href = `/search?q=${encodeURIComponent(query)}`;
                }
            }

            clearResults() {
                this.hideSuggestions();
            }

            exportResults(format) {
                const query = this.searchInput.value.trim();
                if (!query) {
                    alert('Veuillez effectuer une recherche avant d\'exporter');
                    return;
                }

                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/search/export';
                
                const queryInput = document.createElement('input');
                queryInput.type = 'hidden';
                queryInput.name = 'query';
                queryInput.value = query;
                
                const formatInput = document.createElement('input');
                formatInput.type = 'hidden';
                formatInput.name = 'format';
                formatInput.value = format;
                
                form.appendChild(queryInput);
                form.appendChild(formatInput);
                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);
            }
        }

        // Initialiser le gestionnaire de recherche
        document.addEventListener('DOMContentLoaded', () => {
            window.searchManager = new SearchManager();
        });
    </script>
{% endblock %}

{% block body %}
<div class="container-fluid">
    <!-- En-tête de recherche -->
    <div class="search-header text-center">
        <div class="container">
            <h1 class="mb-4">
                <i class="bi bi-search"></i> Recherche globale
            </h1>
            <p class="lead mb-4">Trouvez rapidement ce que vous cherchez dans tout le système</p>
            
            <form id="searchForm" class="row justify-content-center">
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" 
                               id="searchInput" 
                               name="q" 
                               class="form-control search-box" 
                               placeholder="Rechercher des projets, employés, factures..." 
                               value="{{ query }}"
                               autocomplete="off">
                        <button type="submit" class="btn search-btn">
                            <i class="bi bi-search"></i> Rechercher
                        </button>
                    </div>
                    <small class="text-white-50 mt-2 d-block">
                        <i class="bi bi-lightbulb"></i> 
                        Astuce : Utilisez Ctrl+K pour accéder rapidement à la recherche
                    </small>
                </div>
            </form>
            
            {% if results and results.success %}
                <div class="search-stats">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <h4>{{ results.total_results }}</h4>
                            <small>Résultats trouvés</small>
                        </div>
                        <div class="col-md-4">
                            <h4>{{ (results.search_time * 1000)|round(0) }}ms</h4>
                            <small>Temps de recherche</small>
                        </div>
                        <div class="col-md-4">
                            <h4>{{ results.results|length }}</h4>
                            <small>Modules recherchés</small>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    {% if query and query|length >= 2 %}
        {% if results and results.success and results.total_results > 0 %}
            <!-- Actions rapides -->
            <div class="search-filters">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-0">Résultats pour "{{ query }}"</h6>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="quick-actions">
                            <a href="{{ path('app_search_advanced', {q: query}) }}" class="btn btn-outline-primary quick-action-btn btn-sm">
                                <i class="bi bi-funnel"></i> Recherche avancée
                            </a>
                            <button onclick="searchManager.exportResults('csv')" class="btn btn-outline-success quick-action-btn btn-sm">
                                <i class="bi bi-download"></i> CSV
                            </button>
                            <button onclick="searchManager.exportResults('json')" class="btn btn-outline-info quick-action-btn btn-sm">
                                <i class="bi bi-download"></i> JSON
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Résultats par module -->
            {% for module, moduleResults in results.results %}
                {% if moduleResults|length > 0 %}
                    <div class="module-section">
                        <div class="module-header">
                            <h5 class="mb-0">
                                {% set moduleNames = {
                                    'projects': 'Projets',
                                    'employees': 'Employés', 
                                    'invoices': 'Factures',
                                    'partners': 'Partenaires',
                                    'purchases': 'Achats',
                                    'stock': 'Stock',
                                    'departments': 'Départements'
                                } %}
                                {{ moduleNames[module] ?? module|title }}
                                <span class="badge bg-primary">{{ moduleResults|length }}</span>
                            </h5>
                        </div>
                        
                        {% for result in moduleResults %}
                            <div class="result-item type-{{ result.type }}">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        <div class="result-icon {{ result.color }}">
                                            <i class="{{ result.icon }}"></i>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <h6 class="mb-1">
                                            <a href="{{ result.url }}" class="text-decoration-none">
                                                {{ result.title }}
                                            </a>
                                        </h6>
                                        {% if result.subtitle %}
                                            <p class="mb-1 text-muted">{{ result.subtitle }}</p>
                                        {% endif %}
                                        <div class="d-flex gap-3">
                                            {% if result.status %}
                                                <small class="text-muted">
                                                    <i class="bi bi-flag"></i> {{ result.status|title }}
                                                </small>
                                            {% endif %}
                                            {% if result.created_at %}
                                                <small class="text-muted">
                                                    <i class="bi bi-calendar"></i> {{ result.created_at }}
                                                </small>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <a href="{{ result.url }}" class="btn btn-outline-primary btn-sm">
                                            <i class="bi bi-arrow-right"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endfor %}

            <!-- Suggestions -->
            {% if results.suggestions is defined and results.suggestions|length > 0 %}
                <div class="card result-card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="bi bi-lightbulb"></i> Suggestions de recherche
                        </h6>
                        <div>
                            {% for suggestion in results.suggestions %}
                                <a href="/search?q={{ suggestion|url_encode }}" class="suggestion-item">
                                    {{ suggestion }}
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}

        {% else %}
            <!-- Aucun résultat -->
            <div class="empty-state">
                <i class="bi bi-search"></i>
                <h5>Aucun résultat trouvé</h5>
                <p>Essayez avec des termes différents ou vérifiez l'orthographe</p>
                <div class="mt-3">
                    <a href="{{ path('app_search_advanced') }}" class="btn btn-primary">
                        <i class="bi bi-funnel"></i> Recherche avancée
                    </a>
                </div>
            </div>
        {% endif %}
    {% else %}
        <!-- État initial -->
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card result-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">
                            <i class="bi bi-compass"></i> Explorez le système
                        </h5>
                        <p class="card-text">Utilisez la recherche globale pour trouver rapidement :</p>
                        <div class="row text-start">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-kanban text-primary"></i> Projets et tâches</li>
                                    <li><i class="bi bi-people text-success"></i> Employés et équipes</li>
                                    <li><i class="bi bi-receipt text-warning"></i> Factures et paiements</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-building text-info"></i> Partenaires et clients</li>
                                    <li><i class="bi bi-box text-secondary"></i> Articles en stock</li>
                                    <li><i class="bi bi-cart text-primary"></i> Commandes et achats</li>
                                </ul>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="{{ path('app_search_advanced') }}" class="btn btn-outline-primary">
                                <i class="bi bi-gear"></i> Recherche avancée
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
