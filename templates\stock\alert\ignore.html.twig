{% extends 'base.html.twig' %}

{% block title %}Ignorer l'alerte{% endblock %}

{% block body %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Ignorer l'alerte</h1>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-ban me-1"></i>
                Ignorer l'alerte #{{ alert.id }}
            </div>
            <div>
                <a href="{{ path('app_stock_alert_show', {'id': alert.id}) }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left"></i> Retour à l'alerte
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-warning mb-4">
                <h5><i class="fas fa-exclamation-triangle"></i> Attention</h5>
                <p>Vous êtes sur le point d'ignorer cette alerte. Cela signifie qu'aucune action ne sera prise pour résoudre le problème signalé.</p>
                <p>Veuillez expliquer pourquoi cette alerte peut être ignorée.</p>
            </div>
            
            <div class="alert alert-info mb-4">
                <h5><i class="fas fa-info-circle"></i> Informations sur l'alerte</h5>
                <p><strong>Type :</strong> {{ alert.typeLabel }}</p>
                <p><strong>Titre :</strong> {{ alert.title }}</p>
                <p><strong>Description :</strong> {{ alert.description }}</p>
                {% if alert.stockItem and alert.stockItem.product %}
                    <p><strong>Produit :</strong> {{ alert.stockItem.product.code }} - {{ alert.stockItem.product.name }}</p>
                {% endif %}
            </div>
            
            {{ form_start(form) }}
                <div class="mb-3">
                    {{ form_label(form.notes, null, {'label_attr': {'class': 'form-label'}}) }}
                    {{ form_widget(form.notes, {'attr': {'class': 'form-control', 'rows': 5}}) }}
                    {{ form_errors(form.notes) }}
                    <small class="form-text text-muted">Expliquez pourquoi cette alerte peut être ignorée.</small>
                </div>
                
                <button type="submit" class="btn btn-secondary">
                    <i class="fas fa-ban"></i> Ignorer cette alerte
                </button>
            {{ form_end(form) }}
        </div>
    </div>
</div>
{% endblock %}
