{% extends 'base.html.twig' %}

{% block title %}Alertes de stock{% endblock %}

{% block body %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Alertes de stock</h1>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-bell me-1"></i>
                Liste des alertes
            </div>
            <div>
                <a href="{{ path('app_stock_check_expiring') }}" class="btn btn-warning btn-sm">
                    <i class="fas fa-calendar-times"></i> Vérifier les expirations
                </a>
                <a href="{{ path('app_stock_dashboard') }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left"></i> Retour au tableau de bord
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="alertsTable">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Titre</th>
                            <th>Produit</th>
                            <th>Emplacement</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for alert in alerts %}
                            <tr>
                                <td>{{ alert.createdAt|date('d/m/Y H:i') }}</td>
                                <td>
                                    {% if alert.type == 'low_stock' %}
                                        <span class="badge bg-primary">Stock bas</span>
                                    {% elseif alert.type == 'expiry' %}
                                        <span class="badge bg-warning">Expiration</span>
                                    {% elseif alert.type == 'discrepancy' %}
                                        <span class="badge bg-danger">Écart d'inventaire</span>
                                    {% endif %}
                                </td>
                                <td>{{ alert.title }}</td>
                                <td>
                                    {% if alert.stockItem and alert.stockItem.product %}
                                        <a href="{{ path('app_stock_product_show', {'id': alert.stockItem.product.id}) }}">
                                            {{ alert.stockItem.product.name }}
                                        </a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if alert.location %}
                                        <a href="{{ path('app_stock_location_show', {'id': alert.location.id}) }}">
                                            {{ alert.location.code }}
                                        </a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if alert.isResolved %}
                                        <span class="badge bg-success">Résolu</span>
                                    {% elseif alert.isActive %}
                                        <span class="badge bg-danger">Actif</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Inactif</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ path('app_stock_alert_show', {'id': alert.id}) }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    {% if alert.isActive and not alert.isResolved %}
                                        <form method="post" action="{{ path('app_stock_alert_acknowledge', {'id': alert.id}) }}" class="d-inline">
                                            <input type="hidden" name="_token" value="{{ csrf_token('acknowledge' ~ alert.id) }}">
                                            <button type="submit" class="btn btn-warning btn-sm" title="Reconnaître">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    {% endif %}

                                    {% if not alert.isResolved %}
                                        <a href="{{ path('app_stock_alert_resolve', {'id': alert.id}) }}" class="btn btn-success btn-sm" title="Résoudre">
                                            <i class="fas fa-check-double"></i>
                                        </a>
                                        <a href="{{ path('app_stock_alert_ignore', {'id': alert.id}) }}" class="btn btn-secondary btn-sm" title="Ignorer">
                                            <i class="fas fa-ban"></i>
                                        </a>
                                    {% endif %}
                                </td>
                            </tr>
                        {% else %}
                            <tr>
                                <td colspan="7" class="text-center">Aucune alerte trouvée</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            $('#alertsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/fr-FR.json'
                },
                order: [[0, 'desc']]
            });
        });
    </script>
{% endblock %}
