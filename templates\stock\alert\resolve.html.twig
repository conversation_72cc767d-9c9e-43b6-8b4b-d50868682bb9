{% extends 'base.html.twig' %}

{% block title %}Résoudre l'alerte{% endblock %}

{% block body %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Résoudre l'alerte</h1>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-check-double me-1"></i>
                Résoudre l'alerte #{{ alert.id }}
            </div>
            <div>
                <a href="{{ path('app_stock_alert_show', {'id': alert.id}) }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left"></i> Retour à l'alerte
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-info mb-4">
                <h5><i class="fas fa-info-circle"></i> Informations sur l'alerte</h5>
                <p><strong>Type :</strong> {{ alert.typeLabel }}</p>
                <p><strong>Titre :</strong> {{ alert.title }}</p>
                <p><strong>Description :</strong> {{ alert.description }}</p>
                {% if alert.stockItem and alert.stockItem.product %}
                    <p><strong>Produit :</strong> {{ alert.stockItem.product.code }} - {{ alert.stockItem.product.name }}</p>
                {% endif %}
            </div>
            
            {{ form_start(form) }}
                <div class="mb-3">
                    {{ form_label(form.notes, null, {'label_attr': {'class': 'form-label'}}) }}
                    {{ form_widget(form.notes, {'attr': {'class': 'form-control', 'rows': 5}}) }}
                    {{ form_errors(form.notes) }}
                    <small class="form-text text-muted">Décrivez les actions prises pour résoudre cette alerte.</small>
                </div>
                
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-check-double"></i> Marquer comme résolu
                </button>
            {{ form_end(form) }}
        </div>
    </div>
</div>
{% endblock %}
