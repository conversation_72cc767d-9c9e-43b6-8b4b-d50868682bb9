{% extends 'base.html.twig' %}

{% block title %}Alerte {{ alert.id }}{% endblock %}

{% block body %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Alerte #{{ alert.id }}</h1>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-bell me-1"></i>
                Détails de l'alerte
            </div>
            <div>
                <a href="{{ path('app_stock_alert_index') }}" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-left"></i> Retour à la liste
                </a>
                
                {% if alert.isActive and not alert.isResolved %}
                    <form method="post" action="{{ path('app_stock_alert_acknowledge', {'id': alert.id}) }}" class="d-inline">
                        <input type="hidden" name="_token" value="{{ csrf_token('acknowledge' ~ alert.id) }}">
                        <button type="submit" class="btn btn-warning btn-sm">
                            <i class="fas fa-check"></i> Reconnaître
                        </button>
                    </form>
                {% endif %}

                {% if not alert.isResolved %}
                    <a href="{{ path('app_stock_alert_resolve', {'id': alert.id}) }}" class="btn btn-success btn-sm">
                        <i class="fas fa-check-double"></i> Résoudre
                    </a>
                    <a href="{{ path('app_stock_alert_ignore', {'id': alert.id}) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-ban"></i> Ignorer
                    </a>
                {% endif %}
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-bordered">
                        <tr>
                            <th>Type</th>
                            <td>
                                {% if alert.type == 'low_stock' %}
                                    <span class="badge bg-primary">Stock bas</span>
                                {% elseif alert.type == 'expiry' %}
                                    <span class="badge bg-warning">Expiration</span>
                                {% elseif alert.type == 'discrepancy' %}
                                    <span class="badge bg-danger">Écart d'inventaire</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Titre</th>
                            <td>{{ alert.title }}</td>
                        </tr>
                        <tr>
                            <th>Statut</th>
                            <td>
                                {% if alert.isResolved %}
                                    <span class="badge bg-success">Résolu</span>
                                {% elseif alert.isActive %}
                                    <span class="badge bg-danger">Actif</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactif</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>Date de création</th>
                            <td>{{ alert.createdAt|date('d/m/Y H:i') }}</td>
                        </tr>

                        {% if alert.resolvedAt %}
                            <tr>
                                <th>Date de résolution</th>
                                <td>{{ alert.resolvedAt|date('d/m/Y H:i') }}</td>
                            </tr>
                        {% endif %}
                    </table>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Informations produit</h5>
                        </div>
                        <div class="card-body">
                            {% if alert.stockItem and alert.stockItem.product %}
                                <table class="table table-sm">
                                    <tr>
                                        <th>Produit</th>
                                        <td>
                                            <a href="{{ path('app_stock_product_show', {'id': alert.stockItem.product.id}) }}">
                                                {{ alert.stockItem.product.code }} - {{ alert.stockItem.product.name }}
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Emplacement</th>
                                        <td>
                                            {% if alert.stockItem and alert.stockItem.location %}
                                                <a href="{{ path('app_stock_location_show', {'id': alert.stockItem.location.id}) }}">
                                                    {{ alert.stockItem.location.code }} - {{ alert.stockItem.location.name }}
                                                </a>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% if alert.stockItem %}
                                        <tr>
                                            <th>Quantité actuelle</th>
                                            <td>{{ alert.stockItem.quantity|number_format(2, ',', ' ') }}</td>
                                        </tr>
                                        {% if alert.stockItem.lotNumber %}
                                            <tr>
                                                <th>Numéro de lot</th>
                                                <td>{{ alert.stockItem.lotNumber }}</td>
                                            </tr>
                                        {% endif %}
                                        {% if alert.stockItem.expiryDate %}
                                            <tr>
                                                <th>Date d'expiration</th>
                                                <td>{{ alert.stockItem.expiryDate|date('d/m/Y') }}</td>
                                            </tr>
                                        {% endif %}
                                    {% endif %}
                                </table>
                                
                                <div class="mt-3">
                                    <a href="{{ path('app_stock_product_show', {'id': alert.stockItem.product.id}) }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-box"></i> Voir le stock
                                    </a>
                                    <a href="{{ path('app_stock_movement_new') }}" class="btn btn-success btn-sm">
                                        <i class="fas fa-plus-circle"></i> Ajouter du stock
                                    </a>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    Aucune information produit disponible pour cette alerte.
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <h5>Description</h5>
                <div class="card">
                    <div class="card-body">
                        {{ alert.description|nl2br|default('Aucune description disponible.') }}
                    </div>
                </div>
            </div>
            
            {% if alert.resolutionNotes %}
                <div class="mt-3">
                    <h5>Notes de résolution</h5>
                    <div class="card">
                        <div class="card-body">
                            {{ alert.resolutionNotes|nl2br }}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
