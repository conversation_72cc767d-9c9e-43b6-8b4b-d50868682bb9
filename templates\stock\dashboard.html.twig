{% extends 'base.html.twig' %}

{% block title %}Tableau de bord - Gestion des stocks{% endblock %}

{% block body %}
<div class="container-fluid">
    <h1 class="mt-4 mb-4">Tableau de bord - Gestion des stocks</h1>
    
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">{{ dashboardData.lowStockCount }}</h5>
                            <div>Produits en stock bas</div>
                        </div>
                        <div>
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="{{ path('app_stock_item_index') }}">Voir les détails</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">{{ dashboardData.expiringCount }}</h5>
                            <div>Produits proches de l'expiration</div>
                        </div>
                        <div>
                            <i class="fas fa-calendar-times fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="{{ path('app_stock_check_expiring') }}">Vérifier les expirations</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">{{ dashboardData.activeInventories|length }}</h5>
                            <div>Inventaires actifs</div>
                        </div>
                        <div>
                            <i class="fas fa-clipboard-list fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="{{ path('app_stock_inventory_index') }}">Voir les inventaires</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">{{ activeAlerts|length }}</h5>
                            <div>Alertes actives</div>
                        </div>
                        <div>
                            <i class="fas fa-bell fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="{{ path('app_stock_alert_index') }}">Voir les alertes</a>
                    <div class="small text-white"><i class="fas fa-angle-right"></i></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-exchange-alt me-1"></i>
                    Mouvements récents
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Produit</th>
                                    <th>Type</th>
                                    <th>Quantité</th>
                                    <th>Emplacement</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for movement in dashboardData.recentMovements %}
                                    <tr>
                                        <td>{{ movement.movementDate|date('d/m/Y H:i') }}</td>
                                        <td>{{ movement.stockItem.product.name }}</td>
                                        <td>
                                            {% if movement.type == 'in' %}
                                                <span class="badge bg-success">Entrée</span>
                                            {% elseif movement.type == 'out' %}
                                                <span class="badge bg-danger">Sortie</span>
                                            {% elseif movement.type == 'transfer' %}
                                                <span class="badge bg-info">Transfert</span>
                                            {% elseif movement.type == 'adjustment' %}
                                                <span class="badge bg-warning">Ajustement</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if movement.quantity > 0 %}
                                                <span class="text-success">+{{ movement.quantity }}</span>
                                            {% else %}
                                                <span class="text-danger">{{ movement.quantity }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if movement.type == 'transfer' %}
                                                {{ movement.sourceLocation.code }} → {{ movement.destinationLocation.code }}
                                            {% elseif movement.type == 'in' %}
                                                {{ movement.destinationLocation.code }}
                                            {% else %}
                                                {{ movement.sourceLocation.code }}
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center">Aucun mouvement récent</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end mt-2">
                        <a href="{{ path('app_stock_movement_index') }}" class="btn btn-sm btn-primary">Voir tous les mouvements</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-bell me-1"></i>
                    Alertes actives
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Produit</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for alert in activeAlerts %}
                                    <tr>
                                        <td>{{ alert.createdAt|date('d/m/Y H:i') }}</td>
                                        <td>
                                            {% if alert.type == 'low_stock' %}
                                                <span class="badge bg-primary">Stock bas</span>
                                            {% elseif alert.type == 'expiry' %}
                                                <span class="badge bg-warning">Expiration</span>
                                            {% elseif alert.type == 'discrepancy' %}
                                                <span class="badge bg-danger">Écart d'inventaire</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ alert.stockItem.product.name }}</td>
                                        <td>
                                            {% if alert.isResolved %}
                                                <span class="badge bg-success">Résolu</span>
                                            {% elseif alert.isActive %}
                                                <span class="badge bg-danger">Actif</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Inactif</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ path('app_stock_alert_show', {'id': alert.id}) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% else %}
                                    <tr>
                                        <td colspan="5" class="text-center">Aucune alerte active</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-end mt-2">
                        <a href="{{ path('app_stock_alert_index') }}" class="btn btn-sm btn-primary">Voir toutes les alertes</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-tasks me-1"></i>
                    Actions rapides
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ path('app_stock_movement_new') }}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-plus-circle"></i><br>
                                Nouveau mouvement
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ path('app_stock_transfer_new') }}" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-exchange-alt"></i><br>
                                Nouveau transfert
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ path('app_stock_adjustment_new') }}" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-balance-scale"></i><br>
                                Nouvel ajustement
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ path('app_stock_inventory_new') }}" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-clipboard-list"></i><br>
                                Nouvel inventaire
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
