# Script pour tester tous les liens du système SI avec détection des erreurs Symfony
Write-Host "=== VERIFICATION COMPLETE DE TOUS LES LIENS DU SYSTEME SI ===" -ForegroundColor Green

# Fonction pour tester un lien avec détection d'erreurs Symfony
function Test-SymfonyLink {
    param($url, $description)
    
    try {
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing -TimeoutSec 10
        
        # Vérifier le contenu pour les erreurs Symfony
        if ($response.Content -like "*Symfony Exception*" -or 
            $response.Content -like "*RuntimeError*" -or 
            $response.Content -like "*Fatal error*" -or
            $response.Content -like "*Twig\Error*" -or
            $response.Content -like "*Neither the property*" -or
            $response.Content -like "*does not exist*") {
            Write-Host "❌ $description : ERREUR SYMFONY DETECTEE!" -ForegroundColor Red
            return $false
        } else {
            Write-Host "✅ $description : OK ($($response.StatusCode))" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "❌ $description : ERREUR - $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Liste complète des liens à tester
$links = @(
    # Menu principal
    @{ url = "http://localhost:8000/"; desc = "Accueil" },
    @{ url = "http://localhost:8000/dashboard"; desc = "Dashboard Principal" },
    
    # Module Stock
    @{ url = "http://localhost:8000/stock/"; desc = "Stock - Dashboard" },
    @{ url = "http://localhost:8000/stock/items"; desc = "Stock - Articles" },
    @{ url = "http://localhost:8000/stock/alerts"; desc = "Stock - Alertes" },
    @{ url = "http://localhost:8000/stock/locations"; desc = "Stock - Emplacements" },
    @{ url = "http://localhost:8000/stock/movements"; desc = "Stock - Mouvements" },
    @{ url = "http://localhost:8000/stock/inventories"; desc = "Stock - Inventaires" },
    @{ url = "http://localhost:8000/stock/movements/new"; desc = "Stock - Nouveau mouvement" },
    @{ url = "http://localhost:8000/stock/transfers/new"; desc = "Stock - Nouveau transfert" },
    @{ url = "http://localhost:8000/stock/adjustments/new"; desc = "Stock - Nouvel ajustement" },
    @{ url = "http://localhost:8000/stock/locations/new"; desc = "Stock - Nouvel emplacement" },
    @{ url = "http://localhost:8000/stock/inventories/new"; desc = "Stock - Nouvel inventaire" },
    
    # Module Purchasing
    @{ url = "http://localhost:8000/purchasing/dashboard/"; desc = "Purchasing - Dashboard" },
    @{ url = "http://localhost:8000/purchasing/requests"; desc = "Purchasing - Demandes" },
    @{ url = "http://localhost:8000/purchasing/orders"; desc = "Purchasing - Commandes" },
    @{ url = "http://localhost:8000/purchasing/suppliers"; desc = "Purchasing - Fournisseurs" },
    @{ url = "http://localhost:8000/purchasing/requests/new"; desc = "Purchasing - Nouvelle demande" },
    @{ url = "http://localhost:8000/purchasing/orders/new"; desc = "Purchasing - Nouvelle commande" },
    
    # Module Projets
    @{ url = "http://localhost:8000/project/dashboard/"; desc = "Projets - Dashboard" },
    @{ url = "http://localhost:8000/project/"; desc = "Projets - Liste" },
    @{ url = "http://localhost:8000/project/new"; desc = "Projets - Nouveau" },
    @{ url = "http://localhost:8000/project/calendar"; desc = "Projets - Calendrier" },
    @{ url = "http://localhost:8000/project/reports"; desc = "Projets - Rapports" },
    
    # Module Partenaires
    @{ url = "http://localhost:8000/partner/dashboard/"; desc = "Partenaires - Dashboard" },
    @{ url = "http://localhost:8000/partner/"; desc = "Partenaires - Liste" },
    @{ url = "http://localhost:8000/partner/new"; desc = "Partenaires - Nouveau" },
    
    # Module RH/Employés
    @{ url = "http://localhost:8000/employee/dashboard/"; desc = "RH - Dashboard" },
    @{ url = "http://localhost:8000/employee/"; desc = "RH - Employés" },
    @{ url = "http://localhost:8000/employee/new"; desc = "RH - Nouvel employé" },
    
    # Module Médical
    @{ url = "http://localhost:8000/medical/dashboard/"; desc = "Médical - Dashboard" },
    @{ url = "http://localhost:8000/medical/records"; desc = "Médical - Dossiers" },
    @{ url = "http://localhost:8000/medical/examinations"; desc = "Médical - Examens" },
    
    # Module Comptabilité
    @{ url = "http://localhost:8000/accounting/dashboard/"; desc = "Comptabilité - Dashboard" },
    @{ url = "http://localhost:8000/accounting/accounts"; desc = "Comptabilité - Comptes" },
    @{ url = "http://localhost:8000/accounting/journals"; desc = "Comptabilité - Journaux" },
    
    # Module Administration
    @{ url = "http://localhost:8000/admin/dashboard/"; desc = "Admin - Dashboard" },
    @{ url = "http://localhost:8000/admin/users"; desc = "Admin - Utilisateurs" },
    @{ url = "http://localhost:8000/admin/roles"; desc = "Admin - Rôles" },
    @{ url = "http://localhost:8000/admin/permissions"; desc = "Admin - Permissions" },
    @{ url = "http://localhost:8000/admin/departments"; desc = "Admin - Départements" },
    @{ url = "http://localhost:8000/admin/positions"; desc = "Admin - Postes" },
    @{ url = "http://localhost:8000/admin/integrated-workflow/"; desc = "Admin - Workflows intégrés" },
    @{ url = "http://localhost:8000/admin/system-settings"; desc = "Admin - Paramètres système" },
    @{ url = "http://localhost:8000/admin/logs"; desc = "Admin - Logs" },
    
    # Autres modules
    @{ url = "http://localhost:8000/product/"; desc = "Produits - Liste" },
    @{ url = "http://localhost:8000/product/new"; desc = "Produits - Nouveau" },
    @{ url = "http://localhost:8000/invoice/"; desc = "Factures - Liste" },
    @{ url = "http://localhost:8000/quote/"; desc = "Devis - Liste" },
    @{ url = "http://localhost:8000/contract/"; desc = "Contrats - Liste" }
)

Write-Host "`n=== DEBUT DES TESTS ===" -ForegroundColor Yellow
$totalLinks = $links.Count
$successCount = 0
$errorCount = 0

foreach ($link in $links) {
    $result = Test-SymfonyLink -url $link.url -description $link.desc
    if ($result) {
        $successCount++
    } else {
        $errorCount++
    }
    Start-Sleep -Milliseconds 500  # Pause pour éviter de surcharger le serveur
}

Write-Host "`n=== RESUME FINAL ===" -ForegroundColor Yellow
Write-Host "Total des liens testés: $totalLinks" -ForegroundColor White
Write-Host "Liens fonctionnels: $successCount" -ForegroundColor Green
Write-Host "Liens avec erreurs: $errorCount" -ForegroundColor Red

if ($errorCount -eq 0) {
    Write-Host "`n🎉 TOUS LES LIENS FONCTIONNENT PARFAITEMENT! 🎉" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  $errorCount LIENS NECESSITENT UNE CORRECTION ⚠️" -ForegroundColor Red
}
