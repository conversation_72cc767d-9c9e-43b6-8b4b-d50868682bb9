# Script détaillé pour identifier les liens problématiques
Write-Host "=== VERIFICATION DETAILLEE DES LIENS PROBLEMATIQUES ===" -ForegroundColor Green

function Test-LinkDetailed {
    param($url, $description)
    
    try {
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing -TimeoutSec 10
        
        # Vérifier le contenu pour les erreurs Symfony
        if ($response.Content -like "*Symfony Exception*" -or 
            $response.Content -like "*RuntimeError*" -or 
            $response.Content -like "*Fatal error*" -or
            $response.Content -like "*Twig\Error*" -or
            $response.Content -like "*Neither the property*" -or
            $response.Content -like "*does not exist*" -or
            $response.Content -like "*No route found*") {
            
            Write-Host "❌ $description" -ForegroundColor Red
            Write-Host "   URL: $url" -ForegroundColor Yellow
            
            # Extraire le type d'erreur
            if ($response.Content -like "*No route found*") {
                Write-Host "   ERREUR: Route inexistante (404)" -ForegroundColor Red
            } elseif ($response.Content -like "*Neither the property*") {
                Write-Host "   ERREUR: Propriété inexistante dans template" -ForegroundColor Red
            } elseif ($response.Content -like "*RuntimeError*") {
                Write-Host "   ERREUR: Erreur d'exécution Symfony" -ForegroundColor Red
            } else {
                Write-Host "   ERREUR: Exception Symfony détectée" -ForegroundColor Red
            }
            Write-Host ""
            return $false
        } else {
            Write-Host "✅ $description : OK" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "❌ $description" -ForegroundColor Red
        Write-Host "   URL: $url" -ForegroundColor Yellow
        Write-Host "   ERREUR: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        return $false
    }
}

# Liste des liens à tester avec les bonnes routes
$links = @(
    @{ url = "http://localhost:8000/"; desc = "Accueil" },
    @{ url = "http://localhost:8000/dashboard"; desc = "Dashboard Principal" },
    @{ url = "http://localhost:8000/stock/"; desc = "Stock - Dashboard" },
    @{ url = "http://localhost:8000/stock/items"; desc = "Stock - Articles" },
    @{ url = "http://localhost:8000/stock/alerts"; desc = "Stock - Alertes" },
    @{ url = "http://localhost:8000/purchasing/dashboard/"; desc = "Purchasing - Dashboard" },
    @{ url = "http://localhost:8000/purchase/request"; desc = "Purchasing - Demandes" },
    @{ url = "http://localhost:8000/purchase/order"; desc = "Purchasing - Commandes" },
    @{ url = "http://localhost:8000/project/dashboard/"; desc = "Projets - Dashboard" },
    @{ url = "http://localhost:8000/project/"; desc = "Projets - Liste" },
    @{ url = "http://localhost:8000/partner/dashboard/"; desc = "Partenaires - Dashboard" },
    @{ url = "http://localhost:8000/partner/"; desc = "Partenaires - Liste" },
    @{ url = "http://localhost:8000/hr/employee"; desc = "RH - Employés" },
    @{ url = "http://localhost:8000/hr/medical-dashboard"; desc = "Médical - Dashboard" },
    @{ url = "http://localhost:8000/hr/medical-record"; desc = "Médical - Dossiers" },
    @{ url = "http://localhost:8000/accounting/dashboard/"; desc = "Comptabilité - Dashboard" },
    @{ url = "http://localhost:8000/admin/dashboard/"; desc = "Admin - Dashboard" },
    @{ url = "http://localhost:8000/admin/user"; desc = "Admin - Utilisateurs" },
    @{ url = "http://localhost:8000/product/"; desc = "Produits - Liste" },
    @{ url = "http://localhost:8000/invoice/"; desc = "Factures - Liste" }
)

$errorCount = 0
$successCount = 0

foreach ($link in $links) {
    $result = Test-LinkDetailed -url $link.url -description $link.desc
    if ($result) {
        $successCount++
    } else {
        $errorCount++
    }
    Start-Sleep -Milliseconds 300
}

Write-Host "=== RESUME ===" -ForegroundColor Yellow
Write-Host "Liens OK: $successCount" -ForegroundColor Green
Write-Host "Liens avec erreurs: $errorCount" -ForegroundColor Red
