# Script PowerShell pour tester tous les liens du menu
Write-Host "=== TEST DES LIENS DU MENU ===" -ForegroundColor Green

$baseUrl = "http://localhost:8000"
$links = @(
    "/",
    "/dashboard",
    "/advanced-dashboard/",
    "/project-dashboard/",
    "/hr/dashboard/",
    "/purchasing/dashboard/",
    "/stock/",
    "/financial-dashboard/",
    "/accounting/dashboard",
    "/admin/dashboard",
    "/partner/",
    "/partner/crud",
    "/partner/dashboard/",
    "/message",
    "/project",
    "/project/list",
    "/task",
    "/notifications",
    "/employee",
    "/department",
    "/position",
    "/medical-dashboard",
    "/mission/order",
    "/expense/report",
    "/purchase/request",
    "/quote",
    "/purchase/order",
    "/contract",
    "/product",
    "/stock/item",
    "/invoice",
    "/accounting/account",
    "/admin/user",
    "/admin/role",
    "/admin/workflow-analysis/",
    "/admin/integrated-workflow/",
    "/profile/",
    "/integration"
)

$successCount = 0
$errorCount = 0
$errors = @()

foreach ($link in $links) {
    $url = $baseUrl + $link
    try {
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✓ $link" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "✗ $link (Status: $($response.StatusCode))" -ForegroundColor Red
            $errorCount++
            $errors += "$link - Status: $($response.StatusCode)"
        }
    } catch {
        Write-Host "✗ $link (Error: $($_.Exception.Message))" -ForegroundColor Red
        $errorCount++
        $errors += "$link - Error: $($_.Exception.Message)"
    }
    Start-Sleep -Milliseconds 500
}

Write-Host "`n=== RÉSULTATS ===" -ForegroundColor Yellow
Write-Host "Liens testés: $($links.Count)" -ForegroundColor White
Write-Host "Succès: $successCount" -ForegroundColor Green
Write-Host "Erreurs: $errorCount" -ForegroundColor Red

if ($errorCount -gt 0) {
    Write-Host "`n=== ERREURS DÉTAILLÉES ===" -ForegroundColor Red
    foreach ($error in $errors) {
        Write-Host "- $error" -ForegroundColor Red
    }
}

Write-Host "`nTest termine!" -ForegroundColor Yellow
