# Script complet pour tester TOUS les liens du menu avec détection d'erreurs Symfony
Write-Host "=== VERIFICATION COMPLETE DE TOUS LES LIENS DU MENU ===" -ForegroundColor Green

function Test-MenuLink {
    param($url, $description)
    
    try {
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing -TimeoutSec 8
        
        # Vérifier le contenu pour les erreurs Symfony
        if ($response.Content -like "*Symfony Exception*" -or 
            $response.Content -like "*RuntimeError*" -or 
            $response.Content -like "*Fatal error*" -or
            $response.Content -like "*Twig\Error*" -or
            $response.Content -like "*Neither the property*" -or
            $response.Content -like "*does not exist*" -or
            $response.Content -like "*No route found*") {
            
            Write-Host "❌ $description" -ForegroundColor Red
            Write-Host "   URL: $url" -ForegroundColor Yellow
            
            # Extraire le type d'erreur
            if ($response.Content -like "*No route found*") {
                Write-Host "   ERREUR: Route inexistante (404)" -ForegroundColor Red
            } elseif ($response.Content -like "*Neither the property*") {
                Write-Host "   ERREUR: Propriété inexistante dans template" -ForegroundColor Red
            } elseif ($response.Content -like "*RuntimeError*") {
                Write-Host "   ERREUR: Erreur d'exécution Symfony" -ForegroundColor Red
            } else {
                Write-Host "   ERREUR: Exception Symfony détectée" -ForegroundColor Red
            }
            Write-Host ""
            return $false
        } else {
            Write-Host "✅ $description : OK" -ForegroundColor Green
            return $true
        }
    } catch {
        Write-Host "❌ $description" -ForegroundColor Red
        Write-Host "   URL: $url" -ForegroundColor Yellow
        Write-Host "   ERREUR: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        return $false
    }
}

# Liste complète des liens du menu à tester
$menuLinks = @(
    # Navigation principale
    @{ url = "http://localhost:8000/"; desc = "Accueil" },
    @{ url = "http://localhost:8000/dashboard"; desc = "Dashboard Principal" },
    @{ url = "http://localhost:8000/advanced-dashboard"; desc = "Analytics Avancé" },
    
    # Dashboards
    @{ url = "http://localhost:8000/project/dashboard/"; desc = "Dashboard Projets" },
    @{ url = "http://localhost:8000/hr/dashboard/"; desc = "Dashboard RH" },
    @{ url = "http://localhost:8000/purchasing/dashboard/"; desc = "Dashboard Achats" },
    @{ url = "http://localhost:8000/stock/"; desc = "Dashboard Stock" },
    @{ url = "http://localhost:8000/accounting/dashboard/"; desc = "Dashboard Comptabilité" },
    @{ url = "http://localhost:8000/admin/dashboard/"; desc = "Dashboard Admin" },
    
    # Module Projets
    @{ url = "http://localhost:8000/project/"; desc = "Projets - Liste" },
    @{ url = "http://localhost:8000/project/new"; desc = "Projets - Nouveau" },
    @{ url = "http://localhost:8000/project/calendar"; desc = "Projets - Calendrier" },
    @{ url = "http://localhost:8000/project/reports"; desc = "Projets - Rapports" },
    
    # Module Partenaires
    @{ url = "http://localhost:8000/partner/dashboard/"; desc = "Partenaires - Dashboard" },
    @{ url = "http://localhost:8000/partner/"; desc = "Partenaires - Liste" },
    @{ url = "http://localhost:8000/partner/new"; desc = "Partenaires - Nouveau" },
    
    # Module RH
    @{ url = "http://localhost:8000/hr/employee"; desc = "RH - Employés" },
    @{ url = "http://localhost:8000/department/"; desc = "RH - Départements" },
    @{ url = "http://localhost:8000/position/"; desc = "RH - Postes" },
    
    # Module Médical
    @{ url = "http://localhost:8000/hr/medical-dashboard"; desc = "Médical - Dashboard" },
    @{ url = "http://localhost:8000/hr/medical-record"; desc = "Médical - Dossiers" },
    
    # Module Achats
    @{ url = "http://localhost:8000/purchase/request"; desc = "Achats - Demandes" },
    @{ url = "http://localhost:8000/purchase/request/new"; desc = "Achats - Nouvelle demande" },
    @{ url = "http://localhost:8000/purchase/order"; desc = "Achats - Commandes" },
    @{ url = "http://localhost:8000/purchase/order/new"; desc = "Achats - Nouvelle commande" },
    @{ url = "http://localhost:8000/quote/"; desc = "Achats - Devis" },
    @{ url = "http://localhost:8000/contract/"; desc = "Achats - Contrats" },
    @{ url = "http://localhost:8000/product/"; desc = "Achats - Produits" },
    @{ url = "http://localhost:8000/product/new"; desc = "Achats - Nouveau produit" },
    
    # Module Stock
    @{ url = "http://localhost:8000/stock/items"; desc = "Stock - Articles" },
    @{ url = "http://localhost:8000/stock/alerts"; desc = "Stock - Alertes" },
    @{ url = "http://localhost:8000/stock/locations"; desc = "Stock - Emplacements" },
    @{ url = "http://localhost:8000/stock/movements"; desc = "Stock - Mouvements" },
    @{ url = "http://localhost:8000/stock/inventories"; desc = "Stock - Inventaires" },
    @{ url = "http://localhost:8000/stock/movements/new"; desc = "Stock - Nouveau mouvement" },
    @{ url = "http://localhost:8000/stock/transfers/new"; desc = "Stock - Nouveau transfert" },
    @{ url = "http://localhost:8000/stock/adjustments/new"; desc = "Stock - Nouvel ajustement" },
    
    # Module Comptabilité
    @{ url = "http://localhost:8000/accounting/accounts"; desc = "Comptabilité - Comptes" },
    @{ url = "http://localhost:8000/accounting/journals"; desc = "Comptabilité - Journaux" },
    
    # Module Administration
    @{ url = "http://localhost:8000/admin/user"; desc = "Admin - Utilisateurs" },
    @{ url = "http://localhost:8000/admin/roles"; desc = "Admin - Rôles" },
    @{ url = "http://localhost:8000/admin/permissions"; desc = "Admin - Permissions" },
    @{ url = "http://localhost:8000/admin/departments"; desc = "Admin - Départements" },
    @{ url = "http://localhost:8000/admin/positions"; desc = "Admin - Postes" },
    @{ url = "http://localhost:8000/admin/integrated-workflow/"; desc = "Admin - Workflows" },
    @{ url = "http://localhost:8000/admin/system-settings"; desc = "Admin - Paramètres" },
    @{ url = "http://localhost:8000/admin/logs"; desc = "Admin - Logs" },
    
    # Autres modules
    @{ url = "http://localhost:8000/invoice/"; desc = "Factures - Liste" },
    @{ url = "http://localhost:8000/invoice/new"; desc = "Factures - Nouvelle" }
)

Write-Host "`n=== DEBUT DES TESTS COMPLETS ===" -ForegroundColor Yellow
$totalLinks = $menuLinks.Count
$successCount = 0
$errorCount = 0
$errorLinks = @()

foreach ($link in $menuLinks) {
    $result = Test-MenuLink -url $link.url -description $link.desc
    if ($result) {
        $successCount++
    } else {
        $errorCount++
        $errorLinks += $link
    }
    Start-Sleep -Milliseconds 200  # Pause pour éviter de surcharger le serveur
}

Write-Host "`n=== RESUME FINAL COMPLET ===" -ForegroundColor Yellow
Write-Host "Total des liens testés: $totalLinks" -ForegroundColor White
Write-Host "Liens fonctionnels: $successCount" -ForegroundColor Green
Write-Host "Liens avec erreurs: $errorCount" -ForegroundColor Red

if ($errorCount -eq 0) {
    Write-Host "`n🎉 TOUS LES LIENS DU MENU FONCTIONNENT PARFAITEMENT! 🎉" -ForegroundColor Green
    Write-Host "✅ AUCUNE ERREUR SYMFONY DÉTECTÉE!" -ForegroundColor Green
    Write-Host "✅ SYSTÈME 100% OPÉRATIONNEL!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  LIENS NÉCESSITANT UNE CORRECTION:" -ForegroundColor Red
    foreach ($errorLink in $errorLinks) {
        Write-Host "   - $($errorLink.desc): $($errorLink.url)" -ForegroundColor Yellow
    }
}
