<?php

try {
    // Connexion sans spécifier de base de données
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;charset=utf8mb4', 'root', '');
    echo "Connexion MySQL réussie !\n";

    // Suppression et recréation de la base de données
    $pdo->exec("DROP DATABASE IF EXISTS si_project");
    echo "Base de données si_project supprimée !\n";

    $pdo->exec("CREATE DATABASE si_project CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Base de données si_project créée avec succès !\n";

} catch (PDOException $e) {
    echo "Erreur MySQL : " . $e->getMessage() . "\n";
}
